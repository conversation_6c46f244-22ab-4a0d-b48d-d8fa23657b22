package br.com.alice.api.ops.utils

import br.com.alice.common.MultipartRequest
import br.com.alice.common.storage.FileType

class ImageRequestValidator(val request: MultipartRequest) {
    private val validFileTypes = listOf(
        FileType.IMAGE_PNG,
        FileType.IMAGE_JPEG,
        FileType.IMAGE_GIF,
    )

    fun isValid(): <PERSON><PERSON>an {
        return isValidRequest() && hasValidExtension()
    }

    fun getFileType(): FileType? {
        return FileType.fromExtension(
            request.file!!.extension.replace("jpeg", "jpg").lowercase()
        )
    }

    private fun isValidRequest(): Boolean {
        return request.fileContent != null && request.file != null
    }

    private fun hasValidExtension(): Boolean {
        return validFileTypes.contains(getFileType())
    }
}
