package br.com.alice.api.backoffice.controllers.staff

import br.com.alice.common.controllers.Controller
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.common.Response
import br.com.alice.common.MultipartRequest
import br.com.alice.common.ValidationErrorResponse
import br.com.alice.common.ErrorResponse
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileStoreRequest
import br.com.alice.common.storage.FileType
import br.com.alice.common.toResponse
import br.com.alice.api.backoffice.utils.ImageRequestValidator
import br.com.alice.api.backoffice.ServiceConfig
import br.com.alice.common.RangeUUID
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.api.backoffice.mappers.staff.StaffInputMapper
import br.com.alice.api.backoffice.mappers.staff.StaffOutputMapper
import br.com.alice.api.backoffice.transfers.staff.CreateStaffRequest
import br.com.alice.api.backoffice.transfers.staff.UpdateStaffRequest
import br.com.alice.api.backoffice.transfers.staff.StaffFormResponse
import br.com.alice.api.backoffice.validation.CreateStaffRequestValidator
import br.com.alice.api.backoffice.validation.UpdateStaffRequestValidator
import br.com.alice.common.core.StaffType
import br.com.alice.api.backoffice.mappers.staff.StaffFormInputMapper
import br.com.alice.api.backoffice.mappers.staff.StaffFormOutputMapper
import br.com.alice.api.backoffice.services.StaffBackofficeService
import br.com.alice.common.core.Status
import br.com.alice.data.layer.models.MedicalSpecialtyType.SPECIALTY
import br.com.alice.data.layer.models.MedicalSpecialtyType.SUBSPECIALTY
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.googlemaps.services.GoogleMapsService
import io.ktor.http.HttpHeaders.ContentRange
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitFilter
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.Result
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.TimeoutCancellationException
import java.util.concurrent.ConcurrentHashMap
import java.time.Duration
import java.time.Instant
import br.com.alice.api.backoffice.transfers.staff.StaffTiersResponse
import br.com.alice.api.backoffice.transfers.staff.StaffScoreResponse
import br.com.alice.api.backoffice.transfers.staff.CouncilTypeResponse
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.HealthProfessionalOpsProfile
import br.com.alice.data.layer.models.Staff
import br.com.alice.ehr.client.PrescriptionService
import br.com.alice.api.backoffice.services.HealthProfessionalOpsProfileInternalService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import java.util.*

class StaffController(
    private val staffService: StaffService,
    private val staffBackofficeService: StaffBackofficeService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val providerUnitService: ProviderUnitService,
    private val googleMapsService: GoogleMapsService,
    private val fileStorage: FileStorage,
    private val healthProfessionalService: HealthProfessionalService,
    private val prescriptionService: PrescriptionService,
    private val healthProfessionalOpsProfileInternalService: HealthProfessionalOpsProfileInternalService,
) : Controller() {

    private data class CacheEntry<T>(val data: T, val timestamp: Instant)

    private val specialtiesCache = ConcurrentHashMap<String, CacheEntry<List<MedicalSpecialty>>>()
    private val providerUnitsCache = ConcurrentHashMap<String, CacheEntry<List<ProviderUnit>>>()
    private val staffTiersCache = ConcurrentHashMap<String, CacheEntry<List<StaffTiersResponse>>>()
    private val staffScoreCache = ConcurrentHashMap<String, CacheEntry<List<StaffScoreResponse>>>()
    private val councilTypesCache = ConcurrentHashMap<String, CacheEntry<List<CouncilTypeResponse>>>()

    private val cacheTTL = Duration.ofHours(1)

    private val requestTimeout = 5000L

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = CommonInputMapper.getPaginationParams(queryParams)
        val filters = StaffFormInputMapper.toSearchFilters(queryParams)

        val (items, count) = when {
            filters.namePrefix != null || filters.active != null || filters.types != null || filters.roles != null -> {
                val countDeferred = async {
                    staffService.countByNameAndRoleWithRange(
                        ids = filters.ids,
                        roles = filters.roles,
                        namePrefix = filters.namePrefix,
                        active = filters.active,
                        types = filters.types
                    ).get()
                }

                staffService.searchByNameAndRoleWithRange(
                    ids = filters.ids,
                    range = range,
                    roles = filters.roles,
                    namePrefix = filters.namePrefix,
                    active = filters.active,
                    types = filters.types
                ).map { items -> Pair(items, countDeferred.await()) }
            }
            else -> {
                val countDeferred = async { staffService.count().get() }
                staffService.findByRange(range).map { items -> Pair(items, countDeferred.await()) }
            }
        }.get()

        Response(
            status = HttpStatusCode.OK,
            message = StaffOutputMapper.toPaginatedResponse(items, count, queryParams),
        )
    }

    suspend fun create(request: CreateStaffRequest): Response {
        val validation = CreateStaffRequestValidator.validate(request)

        if (!validation.isValid) {
            return ValidationErrorResponse(validation.errors)
        }

        val staff = StaffInputMapper.toCreate(request)
        val healthProfessional = StaffInputMapper.toHealthProfessional(request, staff.id)
        val contacts = healthProfessional?.contacts
        val healthProfessionalOpsProfile = buildHealthProfessionalOpsProfileForCreate(healthProfessional?.id, request)

        return staffService.add(staff, healthProfessional, contacts)
            .flatMap { createdStaff ->
                val pair = if (healthProfessionalOpsProfile != null) {
                    healthProfessionalOpsProfileInternalService.upsert(healthProfessionalOpsProfile).map { Pair(createdStaff, it) }
                } else {
                    Pair(createdStaff, null).success()
                }
                pair
            }.map { (createdStaff, createdHpOpsProfile) ->
                StaffOutputMapper.toFullResponse(createdStaff, healthProfessional, memedStatus = null, healthProfessionalOpsProfile = createdHpOpsProfile)
            }
            .foldResponse()
    }

    suspend fun getById(id: UUID): Response = coroutineScope {
        staffService.get(id)
            .map { staff ->
                val memedStatus = getMemedStatus(staff)
                val healthProfessional = getHealthProfessionalIfNecessary(staff)
                StaffOutputMapper.toFullResponse(staff, healthProfessional, memedStatus)
            }
            .fold(
                { Response(HttpStatusCode.OK, it) },
                { throwable ->
                    when (throwable) {
                        is NotFoundException -> Response(
                            HttpStatusCode.NotFound,
                            ErrorResponse("staff_not_found", "Staff not found")
                        )
                        else -> Response(
                            HttpStatusCode.InternalServerError,
                            ErrorResponse("internal_error", "Internal server error")
                        )
                    }
                }
            )
    }

    suspend fun update(id: UUID, request: UpdateStaffRequest): Response {
        val validation = UpdateStaffRequestValidator.validate(request)

        if (!validation.isValid) {
            return ValidationErrorResponse(validation.errors)
        }

        return staffService.get(id)
            .flatMap { existingStaff ->
                val staff = StaffInputMapper.toUpdate(id, request, existingStaff)
                val existingHealthProfessional = getHealthProfessionalIfNecessary(existingStaff)
                val healthProfessional = buildOrInactiveHealthProfessionalIfNecessary(id, request, existingHealthProfessional)
                val contacts = healthProfessional?.contacts
                val healthProfessionalOpsProfile = buildHealthProfessionalOpsProfileForUpdate(healthProfessional?.id, request)

                staffService.update(staff, healthProfessional, contacts)
                    .flatMap { updatedStaff ->
                        val pair = if (healthProfessionalOpsProfile != null) {
                            healthProfessionalOpsProfileInternalService.upsert(healthProfessionalOpsProfile).map { Pair(updatedStaff, it) }
                        } else {
                            Pair(updatedStaff, null).success()
                        }
                        pair
                    }.map { (updatedStaff, updatedHpOpsProfile) ->
                        val memedStatus = getMemedStatus(updatedStaff)
                        StaffOutputMapper.toFullResponse(updatedStaff, healthProfessional, memedStatus, healthProfessionalOpsProfile = updatedHpOpsProfile)
                    }
            }
            .foldResponse()
    }

    suspend fun searchProviderUnits(queryParams: Parameters): Response = coroutineScope {
        val filter = StaffFormInputMapper.toFilter(queryParams)
        val searchToken = filter.searchToken

        val result = providerUnitService.getByFilterWithRange(
            filter = ProviderUnitFilter(
                searchToken = searchToken,
                status = listOf(Status.ACTIVE),
                type = emptyList(),
                ids = emptyList()
            ),
            range = 0..100
        )

        val units = when (result) {
            is Result.Success -> result.get()
            else -> emptyList()
        }

        return@coroutineScope Response(
            status = HttpStatusCode.OK,
            message = units
        )
    }

    suspend fun buildFormByStaffType(queryParams: Parameters): Response = coroutineScope {
        val staffType = StaffFormInputMapper.getStaffType(queryParams)
        val response = buildResponseByStaffType(staffType)

        Response(
            status = HttpStatusCode.OK,
            message = response
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun buildResponseByStaffType(staffType: StaffType): StaffFormResponse = coroutineScope {
        val needsSpecialties = staffType in listOf(
            StaffType.COMMUNITY_SPECIALIST,
            StaffType.HEALTH_PROFESSIONAL,
            StaffType.PARTNER_HEALTH_PROFESSIONAL
        )

        val needsCouncilTypes = staffType in listOf(
            StaffType.COMMUNITY_SPECIALIST,
            StaffType.HEALTH_ADMINISTRATIVE,
            StaffType.HEALTH_PROFESSIONAL,
            StaffType.PARTNER_HEALTH_PROFESSIONAL,
            StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
        )
        val deferredResults = mutableListOf<Deferred<*>>()

        val specialtiesDeferred = if (needsSpecialties) {
            async {
                fetchWithCacheAndTimeout(
                    "specialties",
                    specialtiesCache,
                    { medicalSpecialtyService.getActivesByType(SPECIALTY, false).get().sortedBy { it.name } }
                )
            }.also { deferredResults.add(it) }
        } else null

        val providerUnitsDeferred = if (needsSpecialties) {
            async {
                fetchWithCacheAndTimeout(
                    "providerUnits",
                    providerUnitsCache,
                    {
                        providerUnitService.getByFilterWithRange(
                            filter = ProviderUnitFilter(
                                searchToken = null,
                                status = listOf(Status.ACTIVE),
                                type = listOf(ProviderUnit.Type.HOSPITAL),
                                ids = emptyList()
                            ),
                            range = 0..100
                        ).get()
                    }
                )
            }.also { deferredResults.add(it) }
        } else null

        val staffRolesDeferred = async {
            try {
                withTimeout(requestTimeout) {
                    staffBackofficeService.getStaffRoles(staffType)
                }
            } catch (e: TimeoutCancellationException) {
                emptyList()
            }
        }.also { deferredResults.add(it) }

        val staffTiersDeferred = if (needsSpecialties) {
            async {
                fetchWithCacheAndTimeout(
                    "staffTiers",
                    staffTiersCache,
                    { staffBackofficeService.getStaffTiers() }
                )
            }.also { deferredResults.add(it) }
        } else null

        val staffScoreDeferred = if (needsSpecialties) {
            async {
                fetchWithCacheAndTimeout(
                    "staffScore",
                    staffScoreCache,
                    { staffBackofficeService.getStaffScore() }
                )
            }.also { deferredResults.add(it) }
        } else null

        val councilTypesDeferred = if (needsCouncilTypes) {
            async {
                fetchWithCacheAndTimeout(
                    "councilTypes",
                    councilTypesCache,
                    { staffBackofficeService.getCouncilTypes() }
                )
            }.also { deferredResults.add(it) }
        } else null

        deferredResults.awaitAll()

        return@coroutineScope StaffFormOutputMapper.toResponse(
            specialties = specialtiesDeferred?.getCompleted(),
            providerUnits = providerUnitsDeferred?.getCompleted(),
            staffRoles = staffRolesDeferred.getCompleted(),
            staffTiers = staffTiersDeferred?.getCompleted(),
            staffScore = staffScoreDeferred?.getCompleted(),
            councilTypes = councilTypesDeferred?.getCompleted()
        )
    }

    /**
     * Search data with cache and timeout
     *
     * @param key
     * @param cache
     * @param fetcher
     */
    private suspend fun <T> fetchWithCacheAndTimeout(
        key: String,
        cache: ConcurrentHashMap<String, CacheEntry<T>>,
        fetcher: suspend () -> T
    ): T {
        val cachedEntry = cache[key]
        if (cachedEntry != null && Duration.between(cachedEntry.timestamp, Instant.now()) < cacheTTL) {
            return cachedEntry.data
        }

        return try {
            withTimeout(requestTimeout) {
                val data = fetcher()

                cache[key] = CacheEntry(data, Instant.now())
                data
            }
        } catch (e: TimeoutCancellationException) {
            @Suppress("UNCHECKED_CAST")
            cachedEntry?.data ?: (emptyList<Any>() as T)
        }
    }

    suspend fun getSubSpecialty(queryParams: Parameters): Response {
        val filterQuery = CommonInputMapper.getFilterParams<String>(queryParams, "q")
        val id = CommonInputMapper.getFilterParams<String>(queryParams, "parent_specialty_id")
        val ids = CommonInputMapper.getFilterParams<List<String>>(queryParams, "ids")

        val filtered = when {
            filterQuery != null -> medicalSpecialtyService.getByName(filterQuery, SUBSPECIALTY).get()
            id != null -> medicalSpecialtyService.getByParentId(id.toUUID()).get()
            ids != null -> medicalSpecialtyService.getByIds(ids.map { it.toUUID() }).get()
            else -> null
        }

        return if (filtered != null) {
            Response(
                status = HttpStatusCode.OK,
                message = filtered,
                headers = mapOf(ContentRange to filtered.size.toString())
            )
        } else {
            val range = CommonInputMapper.getPaginationParams(queryParams)
            val byRange = medicalSpecialtyService.getByRange(range, listOf(SUBSPECIALTY)).get()
            val totalCount = medicalSpecialtyService.count().get()

            Response(
                status = HttpStatusCode.OK,
                message = byRange,
                headers = mapOf(ContentRange to totalCount.toString())
            )
        }
    }

    suspend fun autocompleteAddress(queryParams: Parameters): Response {
        val session = header("Session-Id")?.toSafeUUID() ?: RangeUUID.generate()
        return googleMapsService.autocompleteByText(getQueryText(queryParams), session).foldResponse()
    }

    suspend fun getByPlaceId(placeId: String) =
        googleMapsService.getAddressById(placeId).foldResponse()

    private fun getQueryText(queryParams: Parameters): String =
        queryParams["q"] ?: throw InvalidArgumentException(code = "missing_params", message = "missing parameters q for the search")

    fun uploadProfileImage(multipartRequest: MultipartRequest): Response {
        val validator = ImageRequestValidator(multipartRequest)

        if (!validator.isValid()) {
            return ValidationErrorResponse("invalid_file")
        }

        return storeImage(
            multipartRequest, validator.getFileType()!!
        ).toResponse()
    }

    private fun storeImage(multipartRequest: MultipartRequest, extension: FileType): String {
        val originalFileName = multipartRequest.file!!.name
        val fileNameWithoutExtension = originalFileName.substringBeforeLast(".")
        val fileExtension = originalFileName.substringAfterLast(".")
        val uniqueId = RangeUUID.generate().toString().substring(0, 8)
        val uniqueFileName = "${fileNameWithoutExtension}_${uniqueId}.${fileExtension}"

        return fileStorage.store(
            FileStoreRequest(
                bucketName = ServiceConfig.bucket("publicAssetsBucket"),
                fileContent = multipartRequest.fileContent!!,
                filePath = "healthcare-team-assets/${uniqueFileName}",
                fileType = extension,
                pii = false,
                phi = false
            )
        )
    }

    private suspend fun buildOrInactiveHealthProfessionalIfNecessary(
        staffId: UUID,
        request: UpdateStaffRequest,
        existingHealthProfessional: HealthProfessional?
    ): HealthProfessional? {
        if (existingHealthProfessional != null && !request.type.isHealthProfessional()) {
            healthProfessionalService.inactivateById(existingHealthProfessional.id)
            return null
        }

        return if (request.type.isHealthProfessional()) {
            StaffInputMapper.toHealthProfessionalForUpdate(
                request = request,
                staffId = staffId,
                existingHealthProfessionalId = existingHealthProfessional?.id
            )
        } else null
    }

    private fun buildHealthProfessionalOpsProfileForUpdate(
        healthProfessionalId: UUID?,
        request: UpdateStaffRequest
    ): HealthProfessionalOpsProfile? {
        if (healthProfessionalId == null || request.attendsToOnCall == null) return null
        return HealthProfessionalOpsProfile(
            healthProfessionalId = healthProfessionalId,
            attendsToOnCall = request.attendsToOnCall,
            onCallPaymentMethod = request.onCallPaymentMethod
        )
    }

    private suspend fun getHealthProfessionalIfNecessary(staff: Staff): HealthProfessional? =
        if (staff.type.isHealthProfessional()) {
            healthProfessionalService.findByStaffId(
                staff.id,
                HealthProfessionalService.FindOptions(withStaff = false, withContact = true)
            ).getOrNullIfNotFound()
        } else null

    private fun StaffType?.isHealthProfessional(): Boolean =
        this in listOf(
            StaffType.COMMUNITY_SPECIALIST,
            StaffType.HEALTH_PROFESSIONAL,
            StaffType.PARTNER_HEALTH_PROFESSIONAL,
            StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
        )

    private suspend fun getMemedStatus(staff: Staff): String? =
        staff.nationalId?.let {
            if (staff.type.isHealthProfessional()) {
                prescriptionService.getPhysicianStatus(it).fold(
                    { status -> status },
                    { null }
                )
            } else null
        }

    private fun buildHealthProfessionalOpsProfileForCreate(
        healthProfessionalId: UUID?,
        request: CreateStaffRequest
    ): HealthProfessionalOpsProfile? {
        if (healthProfessionalId == null || request.attendsToOnCall == null) return null
        return HealthProfessionalOpsProfile(
            healthProfessionalId = healthProfessionalId,
            attendsToOnCall = request.attendsToOnCall,
            onCallPaymentMethod = request.onCallPaymentMethod
        )
    }

    fun getStaffRoles(queryParams: Parameters): Response {
        val staffTypeParam = queryParams["staffType"]
            ?: return Response(
                status = HttpStatusCode.BadRequest,
                message = ErrorResponse("missing_staff_type", "staffType parameter is required")
            )

        val staffType = try {
            StaffType.valueOf(staffTypeParam)
        } catch (e: IllegalArgumentException) {
            return Response(
                status = HttpStatusCode.BadRequest,
                message = ErrorResponse("invalid_staff_type", "Invalid staffType: $staffTypeParam")
            )
        }

        val roles = staffBackofficeService.getStaffRoles(staffType)
        return Response(
            status = HttpStatusCode.OK,
            message = roles
        )
    }

    suspend fun getSpecialties(): Response =
        medicalSpecialtyService.getActivesByType(SPECIALTY, true).foldResponse({ it.sortedBy { spec -> spec.name } })

    fun getTiers(): Response =
        staffBackofficeService.getStaffTiers().toResponse()
}
