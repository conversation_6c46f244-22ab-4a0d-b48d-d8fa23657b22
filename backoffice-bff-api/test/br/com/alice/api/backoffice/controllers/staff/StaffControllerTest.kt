package br.com.alice.api.backoffice.controllers.staff

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.mappers.staff.StaffOutputMapper
import br.com.alice.api.backoffice.mappers.staff.StaffFormOutputMapper
import br.com.alice.api.backoffice.transfers.staff.CouncilTypeResponse
import br.com.alice.api.backoffice.transfers.staff.CreateStaffRequest
import br.com.alice.api.backoffice.transfers.staff.UpdateStaffRequest
import br.com.alice.api.backoffice.transfers.staff.StaffFormResponse
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.common.core.Role
import io.ktor.http.HttpStatusCode
import br.com.alice.api.backoffice.transfers.staff.StaffRolesResponse
import br.com.alice.api.backoffice.transfers.staff.StaffTiersResponse
import br.com.alice.api.backoffice.transfers.staff.StaffScoreResponse
import br.com.alice.common.core.StaffType
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileType
import io.ktor.http.HttpMethod
import io.ktor.client.statement.bodyAsText
import br.com.alice.common.data.dsl.matchers.BFFResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsBFFJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.staff.client.StaffService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.ehr.client.PrescriptionService
import br.com.alice.api.backoffice.services.StaffBackofficeService
import br.com.alice.api.backoffice.services.HealthProfessionalOpsProfileInternalService
import br.com.alice.data.layer.models.OnCallPaymentMethod
import br.com.alice.data.layer.models.HealthProfessionalOpsProfile
import br.com.alice.data.layer.models.Staff
import br.com.alice.api.backoffice.transfers.staff.CouncilDTO
import br.com.alice.api.backoffice.transfers.staff.ContactDTO
import br.com.alice.api.backoffice.transfers.staff.AddressDTO
import br.com.alice.common.models.State
import br.com.alice.data.layer.models.ModalityType
import kotlinx.coroutines.runBlocking
import br.com.alice.api.backoffice.transfers.staff.StaffValidationErrorResponse
import io.mockk.coVerify
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.googlemaps.services.Address
import br.com.alice.common.googlemaps.services.AutocompleteTransport
import br.com.alice.common.googlemaps.services.GoogleMapsService
import com.github.kittinunf.result.success
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.Result
import br.com.alice.common.core.exceptions.NotFoundException
import io.ktor.http.Parameters
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.util.UUID

class StaffControllerTest : ControllerTestHelper() {

    private val staffService: StaffService = mockk()
    private val staffBackofficeService: StaffBackofficeService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val googleMapsService: GoogleMapsService = mockk()
    private val fileStorage: FileStorage = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val prescriptionService: PrescriptionService = mockk()
    private val healthProfessionalOpsProfileInternalService: HealthProfessionalOpsProfileInternalService = mockk()
    private val staffController = StaffController(
        staffService,
        staffBackofficeService,
        medicalSpecialtyService,
        providerUnitService,
        googleMapsService,
        fileStorage,
        healthProfessionalService,
        prescriptionService,
        healthProfessionalOpsProfileInternalService
    )

    private val staff1 = TestModelFactory.buildStaff()
    private val staffs = listOf(staff1)
    private val total = 2
    private val queryParams = Parameters.build {
        append("page", "1")
        append("pageSize", "10")
    }
    private val expectedResponse = StaffOutputMapper.toPaginatedResponse(staffs, total, queryParams)

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()
        module.single { staffController }

        // Default mocks for subspecialty tests
        coEvery { medicalSpecialtyService.getByRange(any(), any()) } returns emptyList<MedicalSpecialty>().success()
        coEvery { medicalSpecialtyService.count() } returns 0.success()
    }

    @BeforeTest
    fun confirmMocks() = confirmVerified(staffService, staffBackofficeService, medicalSpecialtyService, providerUnitService, googleMapsService, fileStorage, healthProfessionalOpsProfileInternalService)

    @Test
    fun `#index returns Staffs found by range`() {
        coEvery { staffService.findByRange(IntRange(0, 9)) } returns staffs.success()
        coEvery { staffService.count() } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={}&page=1&pageSize=10&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { staffService.findByRange(any()) }
        coVerifyOnce { staffService.count() }
    }

    @Test
    fun `#index returns Staffs found by term using searchByNameAndRoleWithRange`() {
        coEvery {
            staffService.searchByNameAndRoleWithRange(
                ids = null,
                range = IntRange(0, 9),
                roles = null,
                namePrefix = "STAFF",
                active = null,
                types = null
            )
        } returns staffs.success()
        coEvery {
            staffService.countByNameAndRoleWithRange(
                ids = null,
                roles = null,
                namePrefix = "STAFF",
                active = null,
                types = null
            )
        } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={\"q\":\"STAFF\"}&page=1&pageSize=10&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { staffService.searchByNameAndRoleWithRange(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { staffService.countByNameAndRoleWithRange(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#index returns Staffs found by multiple filters`() {
        coEvery {
            staffService.searchByNameAndRoleWithRange(
                ids = null,
                range = IntRange(0, 9),
                roles = listOf(Role.COMMUNITY),
                namePrefix = "John",
                active = true,
                types = listOf(StaffType.HEALTH_PROFESSIONAL)
            )
        } returns staffs.success()
        coEvery {
            staffService.countByNameAndRoleWithRange(
                ids = null,
                roles = listOf(Role.COMMUNITY),
                namePrefix = "John",
                active = true,
                types = listOf(StaffType.HEALTH_PROFESSIONAL)
            )
        } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={\"q\":\"John\",\"active\":\"true\",\"roles\":\"COMMUNITY\",\"types\":\"HEALTH_PROFESSIONAL\"}&page=1&pageSize=10") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            staffService.searchByNameAndRoleWithRange(
                ids = null,
                range = IntRange(0, 9),
                roles = listOf(Role.COMMUNITY),
                namePrefix = "John",
                active = true,
                types = listOf(StaffType.HEALTH_PROFESSIONAL)
            )
        }
        coVerifyOnce {
            staffService.countByNameAndRoleWithRange(
                ids = null,
                roles = listOf(Role.COMMUNITY),
                namePrefix = "John",
                active = true,
                types = listOf(StaffType.HEALTH_PROFESSIONAL)
            )
        }
    }

    @Test
    fun `#index returns Staffs found by nome parameter`() {
        coEvery {
            staffService.searchByNameAndRoleWithRange(
                ids = null,
                range = IntRange(0, 9),
                roles = null,
                namePrefix = "Maria",
                active = null,
                types = null
            )
        } returns staffs.success()
        coEvery {
            staffService.countByNameAndRoleWithRange(
                ids = null,
                roles = null,
                namePrefix = "Maria",
                active = null,
                types = null
            )
        } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={\"nome\":\"Maria\"}&page=1&pageSize=10") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { staffService.searchByNameAndRoleWithRange(any(), any(), any(), any(), any(), any()) }
        coVerifyOnce { staffService.countByNameAndRoleWithRange(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#index returns Staffs found by multiple roles and types`() {
        coEvery {
            staffService.searchByNameAndRoleWithRange(
                ids = null,
                range = IntRange(0, 9),
                roles = listOf(Role.COMMUNITY, Role.MANAGER_PHYSICIAN),
                namePrefix = null,
                active = null,
                types = listOf(StaffType.HEALTH_PROFESSIONAL, StaffType.COMMUNITY_SPECIALIST)
            )
        } returns staffs.success()
        coEvery {
            staffService.countByNameAndRoleWithRange(
                ids = null,
                roles = listOf(Role.COMMUNITY, Role.MANAGER_PHYSICIAN),
                namePrefix = null,
                active = null,
                types = listOf(StaffType.HEALTH_PROFESSIONAL, StaffType.COMMUNITY_SPECIALIST)
            )
        } returns total.success()

        authenticatedAs(idToken, staff) {
            get("/staff?filter={\"roles\":\"COMMUNITY,MANAGER_PHYSICIAN\",\"types\":\"HEALTH_PROFESSIONAL,COMMUNITY_SPECIALIST\"}&page=1&pageSize=10") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            staffService.searchByNameAndRoleWithRange(
                ids = null,
                range = IntRange(0, 9),
                roles = listOf(Role.COMMUNITY, Role.MANAGER_PHYSICIAN),
                namePrefix = null,
                active = null,
                types = listOf(StaffType.HEALTH_PROFESSIONAL, StaffType.COMMUNITY_SPECIALIST)
            )
        }
        coVerifyOnce {
            staffService.countByNameAndRoleWithRange(
                ids = null,
                roles = listOf(Role.COMMUNITY, Role.MANAGER_PHYSICIAN),
                namePrefix = null,
                active = null,
                types = listOf(StaffType.HEALTH_PROFESSIONAL, StaffType.COMMUNITY_SPECIALIST)
            )
        }
    }



    @Test
    fun `#buildFormByStaffType returns form for COMMUNITY_SPECIALIST`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )
        val staffTiers = listOf(
            StaffTiersResponse(
                id = "tier1",
                name = "Tier 1",
                value = "TIER1"
            )
        )
        val staffScore = listOf(
            StaffScoreResponse(
                id = "score1",
                name = "Score 1",
                value = "SCORE1"
            )
        )
        val councilTypes = listOf(
            CouncilTypeResponse(
                id = "CRM",
                name = "CRM"
            )
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.COMMUNITY_SPECIALIST) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore
        coEvery { staffBackofficeService.getCouncilTypes() } returns councilTypes

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore,
            councilTypes = councilTypes
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"COMMUNITY_SPECIALIST\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
                assertThat(responseBody.councilTypes).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.COMMUNITY_SPECIALIST) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
        coVerifyOnce { staffBackofficeService.getCouncilTypes() }
    }

    @Test
    fun `#buildFormByStaffType returns form for PITAYA`() {
        // Mock data
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )

        // Mock service calls
        coEvery { staffBackofficeService.getStaffRoles(StaffType.PITAYA) } returns staffRoles

        // Expected response
        val expectedResponse = StaffFormResponse(
            staffRoles = staffRoles
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"PITAYA\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNull()
                assertThat(responseBody.providerUnits).isNull()
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNull()
                assertThat(responseBody.staffScore).isNull()
            }
        }

        // Verify service calls
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.PITAYA) }
    }

    @Test
    fun `#buildFormByStaffType returns form for HEALTH_ADMINISTRATIVE`() {
        // Mock data
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )

        // Mock service calls
        coEvery { staffBackofficeService.getStaffRoles(StaffType.HEALTH_ADMINISTRATIVE) } returns staffRoles
        coEvery { staffBackofficeService.getCouncilTypes() } returns emptyList()

        // Expected response
        val expectedResponse = StaffFormResponse(
            staffRoles = staffRoles
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"HEALTH_ADMINISTRATIVE\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNull()
                assertThat(responseBody.providerUnits).isNull()
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNull()
                assertThat(responseBody.staffScore).isNull()
            }
        }

        // Verify service calls
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.HEALTH_ADMINISTRATIVE) }
    }

    @Test
    fun `#searchProviderUnits returns provider units by search token`() {
        // Mock data
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))

        // Mock service calls
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()

        authenticatedAs(idToken, staff) {
            get("/staff/search_provider_units?filter={\"q\":\"Hospital\"}") { response ->
                assertThat(response).isOKWithData(providerUnits)
            }
        }

        // Verify service calls
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
    }

    @Test
    fun `#searchProviderUnits returns empty list when search token is empty`() {
        // Mock data
        val providerUnits = emptyList<ProviderUnit>()

        // Mock service calls
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()

        authenticatedAs(idToken, staff) {
            get("/staff/search_provider_units?filter={\"q\":\"\"}") { response ->
                assertThat(response).isOKWithData(emptyList<ProviderUnit>())
            }
        }

        // Verify service calls
        coVerifyOnce { providerUnitService.getByFilterWithRange(match { it.searchToken == "" }, any()) }
    }

    @Test
    fun `#buildFormByStaffType returns form for HEALTH_PROFESSIONAL`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )
        val staffTiers = listOf(
            StaffTiersResponse(
                id = "tier1",
                name = "Tier 1",
                value = "TIER1"
            )
        )
        val staffScore = listOf(
            StaffScoreResponse(
                id = "score1",
                name = "Score 1",
                value = "SCORE1"
            )
        )
        val councilTypes = listOf(
            CouncilTypeResponse(
                id = "CRM",
                name = "CRM"
            )
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.HEALTH_PROFESSIONAL) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore
        coEvery { staffBackofficeService.getCouncilTypes() } returns councilTypes

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore,
            councilTypes = councilTypes
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"HEALTH_PROFESSIONAL\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
                assertThat(responseBody.councilTypes).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.HEALTH_PROFESSIONAL) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
        coVerifyOnce { staffBackofficeService.getCouncilTypes() }
    }

    @Test
    fun `#buildFormByStaffType returns form for PARTNER_HEALTH_PROFESSIONAL`() {
        // Mock data
        val specialties = listOf(MedicalSpecialty(
            name = "Specialty 1",
            type = MedicalSpecialtyType.SPECIALTY,
            urlSlug = "specialty-1"
        ))
        val providerUnits = listOf(ProviderUnit(
            name = "Provider Unit 1",
            id = UUID.randomUUID(),
            type = ProviderUnit.Type.HOSPITAL,
            site = "",
            cnpj = "",
            phones = emptyList(),
            workingPeriods = emptyList(),
            qualifications = emptyList(),
            imageUrl = null,
            providerId = UUID.randomUUID()
        ))
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )
        val staffTiers = listOf(
            StaffTiersResponse(
                id = "tier1",
                name = "Tier 1",
                value = "TIER1"
            )
        )
        val staffScore = listOf(
            StaffScoreResponse(
                id = "score1",
                name = "Score 1",
                value = "SCORE1"
            )
        )
        val councilTypes = listOf(
            CouncilTypeResponse(
                id = "CRM",
                name = "CRM"
            )
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getActivesByType(any(), any()) } returns specialties.success()
        coEvery { providerUnitService.getByFilterWithRange(any(), any()) } returns providerUnits.success()
        coEvery { staffBackofficeService.getStaffRoles(StaffType.PARTNER_HEALTH_PROFESSIONAL) } returns staffRoles
        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers
        coEvery { staffBackofficeService.getStaffScore() } returns staffScore
        coEvery { staffBackofficeService.getCouncilTypes() } returns councilTypes

        // Expected response
        val expectedResponse = StaffFormResponse(
            specialties = specialties,
            providerUnits = providerUnits,
            staffRoles = staffRoles,
            staffTiers = staffTiers,
            staffScore = staffScore,
            councilTypes = councilTypes
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"PARTNER_HEALTH_PROFESSIONAL\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNotNull
                assertThat(responseBody.providerUnits).isNotNull
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNotNull
                assertThat(responseBody.staffScore).isNotNull
                assertThat(responseBody.councilTypes).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }
        coVerifyOnce { providerUnitService.getByFilterWithRange(any(), any()) }
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.PARTNER_HEALTH_PROFESSIONAL) }
        coVerifyOnce { staffBackofficeService.getStaffTiers() }
        coVerifyOnce { staffBackofficeService.getStaffScore() }
        coVerifyOnce { staffBackofficeService.getCouncilTypes() }
    }

    @Test
    fun `#buildFormByStaffType returns form for EXTERNAL_PAID_HEALTH_PROFESSIONAL`() {
        // Mock data
        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )
        val councilTypes = listOf(
            CouncilTypeResponse(
                id = "CRM",
                name = "CRM"
            )
        )

        // Mock service calls
        coEvery { staffBackofficeService.getStaffRoles(StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL) } returns staffRoles
        coEvery { staffBackofficeService.getCouncilTypes() } returns councilTypes

        // Expected response
        val expectedResponse = StaffFormResponse(
            staffRoles = staffRoles,
            councilTypes = councilTypes
        )

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"EXTERNAL_PAID_HEALTH_PROFESSIONAL\"}") { response ->
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()
                assertThat(responseBody.specialties).isNull()
                assertThat(responseBody.providerUnits).isNull()
                assertThat(responseBody.staffRoles).isNotNull
                assertThat(responseBody.staffTiers).isNull()
                assertThat(responseBody.staffScore).isNull()
                assertThat(responseBody.councilTypes).isNotNull
            }
        }

        // Verify service calls
        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL) }
        coVerifyOnce { staffBackofficeService.getCouncilTypes() }
    }

    @Test
    fun `#getSubSpecialty should return subspecialties by name`() {
        // Mock data
        val subspecialties = listOf(
            TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY, name = "Joelho")
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getByName("Joelho", MedicalSpecialtyType.SUBSPECIALTY) } returns subspecialties.success()

        authenticatedAs(idToken, staff) {
            get("/staff/sub_specialties?q=Joelho") { response ->
                assertThat(response.status()).isEqualTo(HttpStatusCode.OK)
            }
        }

        // No verification needed
    }

    @Test
    fun `#getSubSpecialty should return subspecialties by parent specialty id`() {
        // Mock data
        val parentId = UUID.randomUUID()
        val subspecialties = listOf(
            TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY, parentSpecialtyId = parentId)
        )

        // Mock service calls
        coEvery { medicalSpecialtyService.getByParentId(parentId) } returns subspecialties.success()

        authenticatedAs(idToken, staff) {
            get("/staff/sub_specialties?parent_specialty_id=$parentId") { response ->
                assertThat(response.status()).isEqualTo(HttpStatusCode.OK)
            }
        }

        // No verification needed
    }

    @Test
    fun `#getSubSpecialty should return subspecialties by range when no filter is provided`() {
        // Mock data
        val range = IntRange(0, 9)
        val subspecialties = listOf(
            TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY),
            TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY)
        )
        val totalCount = 10

        // Mock service calls
        coEvery { medicalSpecialtyService.getByRange(range, listOf(MedicalSpecialtyType.SUBSPECIALTY)) } returns subspecialties.success()
        coEvery { medicalSpecialtyService.count() } returns totalCount.success()

        authenticatedAs(idToken, staff) {
            get("/staff/sub_specialties?page=1&pageSize=10") { response ->
                assertThat(response).isOKWithData(subspecialties)
                assertThat(response).containsHeaderWithValue("Content-Range", totalCount.toString())
            }
        }

        // Verify service calls
        coVerifyOnce { medicalSpecialtyService.getByRange(range, listOf(MedicalSpecialtyType.SUBSPECIALTY)) }
        coVerifyOnce { medicalSpecialtyService.count() }
    }

    @Test
    fun `#autocompleteAddress should return address suggestions`() {
        // Mock data
        val query = "Av. Rebouças"
        val placeId = "place_id_123"
        val autocompleteResults = listOf(
            AutocompleteTransport(
                placeId = placeId,
                description = "Av. Rebouças, São Paulo - SP, Brasil",
                mainText = "Av. Rebouças",
                secondaryText = "São Paulo - SP, Brasil"
            )
        )

        // Mock service calls
        coEvery { googleMapsService.autocompleteByText(query, any()) } returns autocompleteResults.success()

        authenticatedAs(idToken, staff) {
            get("/staff/address/search?q=$query") { response ->
                assertThat(response).isOKWithData(autocompleteResults)
            }
        }

        // Verify service calls
        coVerifyOnce { googleMapsService.autocompleteByText(query, any()) }
    }

    @Test
    fun `#autocompleteAddress should use session id from header if available`() {
        // Mock data
        val query = "Av. Rebouças"
        val sessionId = "session-id".toSafeUUID()
        val placeId = "place_id_123"
        val autocompleteResults = listOf(
            AutocompleteTransport(
                placeId = placeId,
                description = "Av. Rebouças, São Paulo - SP, Brasil",
                mainText = "Av. Rebouças",
                secondaryText = "São Paulo - SP, Brasil"
            )
        )

        // Mock service calls
        coEvery { googleMapsService.autocompleteByText(query, sessionId) } returns autocompleteResults.success()

        authenticatedAs(idToken, staff) {
            get("/staff/address/search?q=$query", mapOf("Session-Id" to sessionId.toString())) { response ->
                assertThat(response).isOKWithData(autocompleteResults)
            }
        }

        // Verify service calls
        coVerifyOnce { googleMapsService.autocompleteByText(query, sessionId) }
    }

    @Test
    fun `#getByPlaceId should return address details by place id`() {
        // Mock data
        val placeId = "ChIJN1t_tDeuEmsRUsoyG83frY4"
        val addressDetails = Address(
            id = placeId,
            street = "Avenida Rebouças",
            number = "3506",
            neighbourhood = "Pinheiros",
            city = "São Paulo",
            state = "SP",
            country = "Brasil",
            lat = -23.5718116,
            lng = -46.69273700000001,
            postalCode = "05402-600"
        )

        // Mock service calls
        coEvery { googleMapsService.getAddressById(placeId) } returns addressDetails.success()

        authenticatedAs(idToken, staff) {
            get("/staff/address/$placeId") { response ->
                assertThat(response).isOKWithData(addressDetails)
            }
        }

        // Verify service calls
        coVerifyOnce { googleMapsService.getAddressById(placeId) }
    }

    @Test
    fun `#getByPlaceId should return error when address not found`() {
        // Mock data
        val placeId = "invalid_place_id"

        // Mock service calls
        coEvery { googleMapsService.getAddressById(placeId) } returns NotFoundException("Address not found").failure()

        authenticatedAs(idToken, staff) {
            get("/staff/address/$placeId") { response ->
                assertThat(response).isNotFound()
            }
        }

        // Verify service calls
        coVerifyOnce { googleMapsService.getAddressById(placeId) }
    }

    @Test
    fun `#uploadProfileImage should return 200 OK with image URL and unique filename`() {
        val uploadedFileUrl = "http://image.url"

        coEvery {
            fileStorage.store(match {
                it.bucketName == "publicAssetsBucketTest"
                        && it.filePath.startsWith("healthcare-team-assets/apple_")
                        && it.filePath.endsWith(".png")
                        && it.fileType == FileType.IMAGE_PNG
            })
        } returns uploadedFileUrl

        authenticatedAs(idToken, staff) {
            multipart(HttpMethod.Post, "/staff/upload_profile_image", fileName = "apple.png", parameters = emptyMap()) { response ->
                assertThat(response).isOK()

                val resultUrl: String = response.bodyAsText()
                assertThat(resultUrl).isEqualTo(uploadedFileUrl)
                coVerifyOnce { fileStorage.store(any()) }
            }
        }
    }

    @Test
    fun `#create should create a new staff member`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val createRequest = CreateStaffRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } returns TestModelFactory.buildHealthProfessionalOpsProfile().success()

        authenticatedAs(idToken, staff) {
            post("/staff", createRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isNotNull()
            }
        }

        coVerifyOnce { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should handle minimal request with only required fields`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val minimalRequest = CreateStaffRequest(
            firstName = "Jane",
            lastName = "Smith",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.MANAGER_PHYSICIAN,
            type = StaffType.COMMUNITY_SPECIALIST,
            council = CouncilDTO(
                number = "123456",
                state = State.SP,
                type = CouncilType.CRM
            )
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } returns TestModelFactory.buildHealthProfessionalOpsProfile().success()

        authenticatedAs(idToken, staff) {
            post("/staff", minimalRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isNotNull()
            }
        }

        coVerifyOnce { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should handle PITAYA staff type correctly`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val pitayaRequest = CreateStaffRequest(
            firstName = "Carlos",
            lastName = "Santos",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.B2B_OPS,
            type = StaffType.PITAYA,
            active = true,
            nationalId = "11144477735"
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } returns TestModelFactory.buildHealthProfessionalOpsProfile().success()

        authenticatedAs(idToken, staff) {
            post("/staff", pitayaRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isNotNull()
            }
        }

        coVerifyOnce {
            staffService.add(
                match { staff ->
                    staff.type == StaffType.PITAYA &&
                    staff.role == Role.B2B_OPS &&
                    staff.firstName == "Carlos" &&
                    staff.lastName == "Santos"
                },
                isNull(),
                any()
            )
        }
    }

    @Test
    fun `#create should create HealthProfessionalOpsProfile when attendsToOnCall is provided`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val healthProfessionalOpsProfile = TestModelFactory.buildHealthProfessionalOpsProfile()
        val createRequest = CreateStaffRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL,
            attendsToOnCall = true,
            onCallPaymentMethod = OnCallPaymentMethod.ALICE,
            council = CouncilDTO(
                number = "123456",
                state = State.SP,
                type = CouncilType.CRM
            )
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } answers { firstArg<HealthProfessionalOpsProfile>().success() }

        authenticatedAs(idToken, staff) {
            post("/staff", createRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isNotNull()
                assertThat(responseBody["attendsToOnCall"]).isEqualTo(true)
                assertThat(responseBody["onCallPaymentMethod"]).isEqualTo("ALICE")
            }
        }

        coVerifyOnce { staffService.add(any(), any(), any()) }
        coVerifyOnce {
            healthProfessionalOpsProfileInternalService.upsert(
                match { profile ->
                    profile.attendsToOnCall == true && profile.onCallPaymentMethod == OnCallPaymentMethod.ALICE
                }
            )
        }
    }

    @Test
    fun `#getById should return staff details when staff exists`() {
        val staffId = RangeUUID.generate()
        val existingStaff = TestModelFactory.buildStaff().copy(
            id = staffId,
            firstName = "Maria",
            lastName = "Silva",
            email = "<EMAIL>",
            type = StaffType.HEALTH_PROFESSIONAL,
            role = Role.MANAGER_PHYSICIAN
        )

        coEvery { staffService.get(staffId) } returns existingStaff.success()
        val healthProfessional = TestModelFactory.buildHealthProfessional().copy(staffId = staffId)
        coEvery { healthProfessionalService.findByStaffId(staffId, any()) } returns healthProfessional.success()
        coEvery { prescriptionService.getPhysicianStatus(any()) } returns "ACTIVE".success()

        authenticatedAs(idToken, staff) {
            get("/staff/$staffId") { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isEqualTo(staffId.toString())
                assertThat(responseBody["firstName"]).isEqualTo("Maria")
                assertThat(responseBody["lastName"]).isEqualTo("Silva")
                assertThat(responseBody["email"]).isEqualTo("<EMAIL>")
                assertThat(responseBody["type"]).isEqualTo("HEALTH_PROFESSIONAL")
                assertThat(responseBody["role"]).isEqualTo("MANAGER_PHYSICIAN")
            }
        }

        coVerifyOnce { staffService.get(staffId) }
    }

    @Test
    fun `#getById should return 404 when staff does not exist`() {
        val nonExistentStaffId = RangeUUID.generate()

        coEvery { staffService.get(nonExistentStaffId) } returns Result.failure(NotFoundException("Staff not found"))

        authenticatedAs(idToken, staff) {
            get("/staff/$nonExistentStaffId") { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { staffService.get(nonExistentStaffId) }
    }

    @Test
    fun `#getById should return staff with PITAYA type correctly`() {
        val staffId = RangeUUID.generate()
        val pitayaStaff = TestModelFactory.buildStaff().copy(
            id = staffId,
            firstName = "João",
            lastName = "Oliveira",
            email = "<EMAIL>",
            type = StaffType.PITAYA,
            role = Role.FIN_OPS,
            active = true
        )

        coEvery { staffService.get(staffId) } returns pitayaStaff.success()

        authenticatedAs(idToken, staff) {
            get("/staff/$staffId") { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isEqualTo(staffId.toString())
                assertThat(responseBody["firstName"]).isEqualTo("João")
                assertThat(responseBody["lastName"]).isEqualTo("Oliveira")
                assertThat(responseBody["type"]).isEqualTo("PITAYA")
                assertThat(responseBody["role"]).isEqualTo("FIN_OPS")
                assertThat(responseBody["active"]).isEqualTo(true)
            }
        }

        coVerifyOnce { staffService.get(staffId) }
    }

    @Test
    fun `#uploadProfileImage should throw exception when file is not an image`() {
        authenticatedAs(idToken, staff) {
            multipart(
                HttpMethod.Post,
                "/staff/upload_profile_image",
                fileName = "invalid_image.txt",
                parameters = emptyMap()
            ) { response ->
                assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#uploadProfileImage should add unique identifier to filename to avoid conflicts`() {
        val uploadedFileUrl = "http://image.url"

        coEvery {
            fileStorage.store(any())
        } returns uploadedFileUrl

        authenticatedAs(idToken, staff) {
            multipart(HttpMethod.Post, "/staff/upload_profile_image", fileName = "apple.png", parameters = emptyMap()) { response ->
                assertThat(response).isOK()

                val resultUrl: String = response.bodyAsText()
                assertThat(resultUrl).isEqualTo(uploadedFileUrl)

                coVerifyOnce {
                    fileStorage.store(match { request ->
                        request.bucketName == "publicAssetsBucketTest" &&
                        request.filePath.startsWith("healthcare-team-assets/apple_") &&
                        request.filePath.endsWith(".png") &&
                        request.filePath != "healthcare-team-assets/apple.png" &&
                        request.fileType == FileType.IMAGE_PNG
                    })
                }
            }
        }
    }

    @Test
    fun `#create should return validation errors when firstName is missing`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "",
            lastName = "Doe",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasFirstNameError = errors.any { it["field"] == "firstName" && it["message"] == "Nome é obrigatório" }
                assertThat(hasFirstNameError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should return validation errors when email is invalid`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "John",
            lastName = "Doe",
            email = "invalid-email",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasEmailError = errors.any { it["field"] == "email" && it["message"] == "E-mail inválido" }
                assertThat(hasEmailError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should return validation errors when nationalId is invalid`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            nationalId = "12345",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasNationalIdError = errors.any { it["field"] == "nationalId" && it["message"] == "CPF inválido" }
                assertThat(hasNationalIdError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should return multiple validation errors when multiple fields are invalid`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "",
            lastName = "",
            email = "invalid-email",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                assertThat(errors.size).isGreaterThan(2)
                val hasFirstNameError = errors.any { it["field"] == "firstName" }
                val hasLastNameError = errors.any { it["field"] == "lastName" }
                val hasEmailError = errors.any { it["field"] == "email" }
                assertThat(hasFirstNameError).isTrue()
                assertThat(hasLastNameError).isTrue()
                assertThat(hasEmailError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `should update staff successfully`() = runBlocking {
        val staffId = UUID.randomUUID()
        val existingStaff = Staff(
            id = staffId,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL,
            active = true
        )

        val updateRequest = UpdateStaffRequest(
            firstName = "Jane",
            lastName = "Smith",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL,
            active = true,
            version = 1
        )

        val updatedStaff = existingStaff.copy(
            firstName = updateRequest.firstName,
            lastName = updateRequest.lastName,
            email = updateRequest.email,
            gender = updateRequest.gender
        )

        coEvery { staffService.get(staffId) } returns existingStaff.success()
        coEvery { healthProfessionalService.findByStaffId(staffId, any()) } returns NotFoundException("HealthProfessional not found").failure()
        coEvery { staffService.update(any(), any(), any()) } returns updatedStaff.success()

        authenticatedAs(idToken, staff) {
            put("/staff/$staffId", updateRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isEqualTo(staffId.toString())
                assertThat(responseBody["firstName"]).isEqualTo("Jane")
                assertThat(responseBody["lastName"]).isEqualTo("Smith")
                assertThat(responseBody["email"]).isEqualTo("<EMAIL>")
            }
        }

        coVerifyOnce { staffService.get(staffId) }
        coVerifyOnce { staffService.update(any(), any(), any()) }
    }

    @Test
    fun `should update staff with health professional ops profile when attendsToOnCall is provided`() = runBlocking {
        val staffId = UUID.randomUUID()
        val existingStaff = Staff(
            id = staffId,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL,
            active = true
        )

        val updateRequest = UpdateStaffRequest(
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL,
            active = true,
            attendsToOnCall = true,
            onCallPaymentMethod = OnCallPaymentMethod.ALICE,
            council = CouncilDTO(
                number = "123456",
                state = State.SP,
                type = CouncilType.CRM
            ),
            version = 1
        )

        val updatedStaff = existingStaff.copy()

        coEvery { staffService.get(staffId) } returns existingStaff.success()
        coEvery { healthProfessionalService.findByStaffId(staffId, any()) } returns NotFoundException("HealthProfessional not found").failure()
        coEvery { staffService.update(any(), any(), any()) } returns updatedStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } answers { firstArg<HealthProfessionalOpsProfile>().success() }

        authenticatedAs(idToken, staff) {
            put("/staff/$staffId", updateRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isEqualTo(staffId.toString())
                assertThat(responseBody["attendsToOnCall"]).isEqualTo(true)
                assertThat(responseBody["onCallPaymentMethod"]).isEqualTo("ALICE")
            }
        }

        coVerifyOnce { staffService.get(staffId) }
        coVerifyOnce { staffService.update(any(), any(), any()) }
        coVerifyOnce {
            healthProfessionalOpsProfileInternalService.upsert(
                match { profile ->
                    profile.attendsToOnCall == true && profile.onCallPaymentMethod == OnCallPaymentMethod.ALICE
                }
            )
        }
    }

    @Test
    fun `should return 404 when updating non-existent staff`() = runBlocking {
        val staffId = UUID.randomUUID()
        val updateRequest = UpdateStaffRequest(
            firstName = "Jane",
            lastName = "Smith",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL,
            active = true,
            version = 1
        )

        coEvery { staffService.get(staffId) } returns NotFoundException("Staff not found").failure()

        authenticatedAs(idToken, staff) {
            put("/staff/$staffId", updateRequest) { response ->
                assertThat(response).isNotFound()
            }
        }

        coVerifyOnce { staffService.get(staffId) }
        coVerify(exactly = 0) { staffService.update(any(), any(), any()) }
    }

    @Test
    fun `should return validation error when updating staff with invalid data`() = runBlocking {
        val staffId = UUID.randomUUID()
        val updateRequest = UpdateStaffRequest(
            firstName = "", // Invalid: empty firstName
            lastName = "Smith",
            email = "invalid-email", // Invalid: not a valid email
            gender = Gender.FEMALE,
            role = Role.COMMUNITY,
            type = StaffType.HEALTH_PROFESSIONAL,
            active = true,
            version = 1
        )

        authenticatedAs(idToken, staff) {
            put("/staff/$staffId", updateRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["code"]).isEqualTo("validation_error")
                assertThat(responseBody["data"]).isNotNull()
                val errors = responseBody["data"] as? List<*>
                assertThat(errors).isNotEmpty()
            }
        }

        coVerify(exactly = 0) { staffService.get(any()) }
        coVerify(exactly = 0) { staffService.update(any(), any(), any()) }
    }



    @Test
    fun `#create should return validation error when council is missing for PARTNER_HEALTH_PROFESSIONAL`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "Maria",
            lastName = "Silva",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.CHIEF_PHYSICIAN,
            type = StaffType.PARTNER_HEALTH_PROFESSIONAL
            // council is missing
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasCouncilError = errors.any { it["field"] == "council" && it["message"] == "Conselho é obrigatório para este tipo de staff" }
                assertThat(hasCouncilError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should succeed when council is provided for PARTNER_HEALTH_PROFESSIONAL`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val validRequest = CreateStaffRequest(
            firstName = "Carlos",
            lastName = "Santos",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.CHIEF_PHYSICIAN,
            type = StaffType.PARTNER_HEALTH_PROFESSIONAL,
            council = CouncilDTO(
                number = "123456",
                state = State.SP,
                type = CouncilType.CRM
            )
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()

        authenticatedAs(idToken, staff) {
            post("/staff", validRequest) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should succeed when council is provided for EXTERNAL_PAID_HEALTH_PROFESSIONAL`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val validRequest = CreateStaffRequest(
            firstName = "Ana",
            lastName = "Costa",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.ANESTHETIST,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            nationalId = "11144477735",
            birthdate = LocalDate.of(1985, 5, 15),
            council = CouncilDTO(
                number = "654321",
                state = State.RJ,
                type = CouncilType.CRM
            ),
            deAccreditationDate = LocalDate.of(2025, 12, 31)
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()

        authenticatedAs(idToken, staff) {
            post("/staff", validRequest) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            staffService.add(
                match { staff ->
                    staff.type == StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL &&
                    staff.nationalId == "11144477735" &&
                    staff.birthdate == LocalDate.of(1985, 5, 15)
                },
                match { healthProfessional ->
                    healthProfessional != null &&
                    healthProfessional.nationalId == "11144477735" &&
                    healthProfessional.council.number == "654321" &&
                    healthProfessional.deAccreditationDate == LocalDate.of(2025, 12, 31)
                },
                any()
            )
        }
    }

    @Test
    fun `#update should return validation error when council is missing for PARTNER_HEALTH_PROFESSIONAL`() = runBlocking {
        val staffId = UUID.randomUUID()
        val invalidRequest = UpdateStaffRequest(
            firstName = "Ana",
            lastName = "Costa",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.CHIEF_PHYSICIAN,
            type = StaffType.PARTNER_HEALTH_PROFESSIONAL,
            active = true,
            version = 1
        )

        authenticatedAs(idToken, staff) {
            put("/staff/$staffId", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasCouncilError = errors.any { it["field"] == "council" && it["message"] == "Conselho é obrigatório para este tipo de staff" }
                assertThat(hasCouncilError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.get(any()) }
        coVerify(exactly = 0) { staffService.update(any(), any(), any()) }
    }

    @Test
    fun `#create should return validation error when council is missing for EXTERNAL_PAID_HEALTH_PROFESSIONAL`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "Carlos",
            lastName = "Silva",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.ANESTHETIST,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            nationalId = "98765432100",
            birthdate = LocalDate.of(1980, 3, 10)
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasCouncilError = errors.any { it["field"] == "council" && it["message"] == "Conselho é obrigatório para este tipo de staff" }
                assertThat(hasCouncilError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should return validation error when council is missing for COMMUNITY_SPECIALIST`() {
        val invalidRequest = CreateStaffRequest(
            firstName = "Pedro",
            lastName = "Oliveira",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.COMMUNITY,
            type = StaffType.COMMUNITY_SPECIALIST
        )

        authenticatedAs(idToken, staff) {
            post("/staff", invalidRequest) { response ->
                assertThat(response).isBadRequest()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                @Suppress("UNCHECKED_CAST")
                val errors = responseBody["data"] as List<Map<String, String>>
                val hasCouncilError = errors.any { it["field"] == "council" && it["message"] == "Conselho é obrigatório para este tipo de staff" }
                assertThat(hasCouncilError).isTrue()
            }
        }

        coVerify(exactly = 0) { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#create should succeed when council is provided for COMMUNITY_SPECIALIST`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val validRequest = CreateStaffRequest(
            firstName = "Lucia",
            lastName = "Fernandes",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.COMMUNITY,
            type = StaffType.COMMUNITY_SPECIALIST,
            council = CouncilDTO(
                number = "789012",
                state = State.MG,
                type = CouncilType.CRM
            )
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()

        authenticatedAs(idToken, staff) {
            post("/staff", validRequest) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce { staffService.add(any(), any(), any()) }
    }

    @Test
    fun `#update should save birthdate and nationalId for EXTERNAL_PAID_HEALTH_PROFESSIONAL`() {
        val staffId = RangeUUID.generate()
        val existingStaff = TestModelFactory.buildStaff().copy(
            id = staffId,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            nationalId = "11144477735",
            birthdate = LocalDate.of(1980, 1, 1)
        )
        val updatedStaff = existingStaff.copy(
            nationalId = "98765432100",
            birthdate = LocalDate.of(1985, 5, 15)
        )

        val updateRequest = UpdateStaffRequest(
            firstName = "Ana",
            lastName = "Costa",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.ANESTHETIST,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            nationalId = "98765432100",
            birthdate = LocalDate.of(1985, 5, 15),
            council = CouncilDTO(
                number = "654321",
                state = State.RJ,
                type = CouncilType.CRM
            ),
            deAccreditationDate = LocalDate.of(2025, 12, 31)
        )

        coEvery { staffService.get(staffId) } returns existingStaff.success()
        coEvery { healthProfessionalService.findByStaffId(staffId, any()) } returns Result.failure(NotFoundException("Health professional not found"))
        coEvery { prescriptionService.getPhysicianStatus(any()) } returns "ACTIVE".success()
        coEvery { staffService.update(any(), any(), any()) } returns updatedStaff.success()

        authenticatedAs(idToken, staff) {
            put("/staff/$staffId", updateRequest) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            staffService.update(
                match { staff ->
                    staff.type == StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL &&
                    staff.nationalId == "98765432100" &&
                    staff.birthdate == LocalDate.of(1985, 5, 15)
                },
                match { healthProfessional ->
                    healthProfessional != null &&
                    healthProfessional.nationalId == "98765432100" &&
                    healthProfessional.council.number == "654321" &&
                    healthProfessional.deAccreditationDate == LocalDate.of(2025, 12, 31)
                },
                any()
            )
        }
    }

    @Test
    fun `#getStaffRoles returns roles for valid staffType`() {
        val expectedRoles = listOf(
            StaffRolesResponse(
                id = "COMMUNITY",
                name = "Especialista",
                value = "COMMUNITY"
            )
        )

        coEvery { staffBackofficeService.getStaffRoles(StaffType.COMMUNITY_SPECIALIST) } returns expectedRoles

        authenticatedAs(idToken, staff) {
            get("/staff/roles?staffType=COMMUNITY_SPECIALIST") { response ->
                assertThat(response).isOKWithData(expectedRoles)
            }
        }

        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.COMMUNITY_SPECIALIST) }
    }

    @Test
    fun `#getStaffRoles returns bad request when staffType is missing`() {
        authenticatedAs(idToken, staff) {
            get("/staff/roles") { response ->
                assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#getStaffRoles returns bad request when staffType is invalid`() {
        authenticatedAs(idToken, staff) {
            get("/staff/roles?staffType=INVALID_TYPE") { response ->
                assertThat(response).isBadRequest()
            }
        }
    }

    @Test
    fun `#getTiers returns all tiers`() {
        val staffTiers = listOf(
            StaffTiersResponse(
                id = "tier1",
                name = "Tier 1",
                value = "TIER1"
            )
        )

        coEvery { staffBackofficeService.getStaffTiers() } returns staffTiers

        authenticatedAs(idToken, staff) {
            get("/staff/tiers") { response ->
                assertThat(response).isOKWithData(staffTiers)
            }
        }

        coVerifyOnce { staffBackofficeService.getStaffTiers() }
    }

    @Test
    fun `#getSpecialty returns all specialties`() {
        val specialties = listOf(
            MedicalSpecialty(
                name = "Specialty 1",
                type = MedicalSpecialtyType.SPECIALTY,
                urlSlug = "specialty-1"
            )
        )

        coEvery { medicalSpecialtyService.getActivesByType(MedicalSpecialtyType.SPECIALTY, true) } returns specialties.success()

        authenticatedAs(idToken, staff) {
            get("/staff/specialties") { response ->
                assertThat(response).isOKWithData(specialties)
            }
        }
        coVerifyOnce { medicalSpecialtyService.getActivesByType(any(), any()) }

    }

    @Test
    fun `#buildFormByStaffType should return council types with string ids instead of numeric codes`() {
        val councilTypes = listOf(
            CouncilTypeResponse(
                id = "CRM",
                name = "CRM"
            ),
            CouncilTypeResponse(
                id = "COREN",
                name = "COREN"
            ),
            CouncilTypeResponse(
                id = "CRF",
                name = "CRF"
            )
        )

        val staffRoles = listOf(
            StaffRolesResponse(
                id = "role1",
                name = "Role 1",
                value = "ROLE1"
            )
        )

        coEvery { staffBackofficeService.getStaffRoles(StaffType.HEALTH_ADMINISTRATIVE) } returns staffRoles
        coEvery { staffBackofficeService.getCouncilTypes() } returns councilTypes

        authenticatedAs(idToken, staff) {
            get("/staff/build_staff?filter={\"type\":\"HEALTH_ADMINISTRATIVE\"}") { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<StaffFormResponse>()

                assertThat(responseBody.councilTypes).isNotNull
                assertThat(responseBody.councilTypes!!.size).isEqualTo(3)

                val crmType = responseBody.councilTypes!!.find { it.id == "CRM" }
                val corenType = responseBody.councilTypes!!.find { it.id == "COREN" }
                val crfType = responseBody.councilTypes!!.find { it.id == "CRF" }

                assertThat(crmType).isNotNull()
                assertThat(crmType!!.name).isEqualTo("CRM")

                assertThat(corenType).isNotNull()
                assertThat(corenType!!.name).isEqualTo("COREN")

                assertThat(crfType).isNotNull()
                assertThat(crfType!!.name).isEqualTo("CRF")
            }
        }

        coVerifyOnce { staffBackofficeService.getStaffRoles(StaffType.HEALTH_ADMINISTRATIVE) }
        coVerifyOnce { staffBackofficeService.getCouncilTypes() }
    }

    @Test
    fun `#create should accept CouncilType enum in council field`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)

        val createRequest = CreateStaffRequest(
            firstName = "Maria",
            lastName = "Santos",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.CHIEF_PHYSICIAN,
            type = StaffType.HEALTH_PROFESSIONAL,
            council = CouncilDTO(
                number = "123456",
                state = State.SP,
                type = CouncilType.COREN
            )
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } returns TestModelFactory.buildHealthProfessionalOpsProfile().success()

        authenticatedAs(idToken, staff) {
            post("/staff", createRequest) { response ->
                assertThat(response).isOK()
                val responseBody = response.bodyAsBFFJson<Map<String, Any>>()
                assertThat(responseBody["id"]).isNotNull()
            }
        }

        coVerifyOnce {
            staffService.add(
                any(),
                match { healthProfessional ->
                    healthProfessional?.council?.type == CouncilType.COREN &&
                    healthProfessional.council.number == "123456" &&
                    healthProfessional.council.state == State.SP
                },
                any()
            )
        }
    }

    @Test
    fun `#create should create staff with presential contact and address references`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val contactId = RangeUUID.generate()
        val addressId = RangeUUID.generate()

        val addressDTO = AddressDTO(
            id = addressId,
            street = "Rua das Flores",
            number = "123",
            complement = "Sala 10",
            neighborhood = "Centro",
            state = "SP",
            city = "São Paulo",
            zipcode = "01234-567",
            label = "Consultório Principal",
            active = true,
            latitude = "-23.5505",
            longitude = "-46.6333"
        )

        val contactDTO = ContactDTO(
            id = contactId,
            address = addressDTO,
            phones = emptyList(),
            scheduleAvailabilityDays = 127,
            modality = ModalityType.PRESENTIAL,
            availableDays = emptyList(),
            website = "https://clinica.com"
        )

        val createRequest = CreateStaffRequest(
            firstName = "João",
            lastName = "Silva",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.MANAGER_PHYSICIAN,
            type = StaffType.PARTNER_HEALTH_PROFESSIONAL,
            active = true,
            contacts = listOf(contactDTO),
            council = CouncilDTO(
                number = "123456",
                state = State.SP,
                type = CouncilType.CRM
            )
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } returns TestModelFactory.buildHealthProfessionalOpsProfile().success()

        authenticatedAs(idToken, staff) {
            post("/staff", createRequest) { response ->
                assertThat(response).isOK()
            }
        }

        // Verify that the staff service was called with the correct data
        coVerifyOnce {
            staffService.add(
                match { staff ->
                    staff.firstName == "João" &&
                    staff.lastName == "Silva" &&
                    staff.email == "<EMAIL>"
                },
                match { healthProfessional ->
                    healthProfessional != null &&
                    healthProfessional.contacts != null &&
                    healthProfessional.contacts!!.size == 1 &&
                    healthProfessional.contacts!!.first().modality == ModalityType.PRESENTIAL &&
                    healthProfessional.contacts!!.first().address != null &&
                    healthProfessional.contacts!!.first().address!!.street == "Rua das Flores" &&
                    healthProfessional.contacts!!.first().address!!.referencedModelClass != null &&
                    healthProfessional.contacts!!.first().address!!.referencedModelId != null
                },
                any()
            )
        }
    }

    @Test
    fun `#create should create staff with remote contact without address`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val contactId = RangeUUID.generate()

        val contactDTO = ContactDTO(
            id = contactId,
            address = null,
            phones = emptyList(),
            scheduleAvailabilityDays = 31,
            modality = ModalityType.REMOTE,
            availableDays = emptyList(),
            website = "https://telemedicina.com"
        )

        val createRequest = CreateStaffRequest(
            firstName = "Maria",
            lastName = "Santos",
            email = "<EMAIL>",
            gender = Gender.FEMALE,
            role = Role.MANAGER_PHYSICIAN,
            type = StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL,
            active = true,
            contacts = listOf(contactDTO),
            council = CouncilDTO(
                number = "654321",
                state = State.SP,
                type = CouncilType.CRM
            )
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } returns TestModelFactory.buildHealthProfessionalOpsProfile().success()

        authenticatedAs(idToken, staff) {
            post("/staff", createRequest) { response ->
                assertThat(response).isOK()
            }
        }

        // Verify that the staff service was called with remote contact without address
        coVerifyOnce {
            staffService.add(
                match { staff ->
                    staff.firstName == "Maria" &&
                    staff.lastName == "Santos"
                },
                match { healthProfessional ->
                    healthProfessional != null &&
                    healthProfessional.contacts != null &&
                    healthProfessional.contacts!!.size == 1 &&
                    healthProfessional.contacts!!.first().modality == ModalityType.REMOTE &&
                    healthProfessional.contacts!!.first().address == null
                },
                any()
            )
        }
    }

    @Test
    fun `#create should create staff with multiple contacts of different modalities`() {
        val staffId = RangeUUID.generate()
        val createdStaff = TestModelFactory.buildStaff().copy(id = staffId)
        val presentialContactId = RangeUUID.generate()
        val remoteContactId = RangeUUID.generate()
        val addressId = RangeUUID.generate()

        val addressDTO = AddressDTO(
            id = addressId,
            street = "Avenida Paulista",
            number = "1000",
            complement = "Andar 15",
            neighborhood = "Bela Vista",
            state = "SP",
            city = "São Paulo",
            zipcode = "01310-100",
            label = "Consultório Presencial",
            active = true,
            latitude = "-23.5489",
            longitude = "-46.6388"
        )

        val presentialContactDTO = ContactDTO(
            id = presentialContactId,
            address = addressDTO,
            phones = emptyList(),
            scheduleAvailabilityDays = 62,
            modality = ModalityType.PRESENTIAL,
            availableDays = emptyList(),
            website = "https://presencial.com"
        )

        val remoteContactDTO = ContactDTO(
            id = remoteContactId,
            address = null,
            phones = emptyList(),
            scheduleAvailabilityDays = 31,
            modality = ModalityType.REMOTE,
            availableDays = emptyList(),
            website = "https://telemedicina.com"
        )

        val createRequest = CreateStaffRequest(
            firstName = "Carlos",
            lastName = "Oliveira",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.MANAGER_PHYSICIAN,
            type = StaffType.PARTNER_HEALTH_PROFESSIONAL,
            active = true,
            contacts = listOf(presentialContactDTO, remoteContactDTO),
            council = CouncilDTO(
                number = "789012",
                state = State.SP,
                type = CouncilType.CRM
            )
        )

        coEvery { staffService.add(any(), any(), any()) } returns createdStaff.success()
        coEvery { healthProfessionalOpsProfileInternalService.upsert(any()) } returns TestModelFactory.buildHealthProfessionalOpsProfile().success()

        authenticatedAs(idToken, staff) {
            post("/staff", createRequest) { response ->
                assertThat(response).isOK()
            }
        }

        // Verify that both contacts were processed correctly
        coVerifyOnce {
            staffService.add(
                any(),
                match { healthProfessional ->
                    healthProfessional != null &&
                    healthProfessional.contacts != null &&
                    healthProfessional.contacts!!.size == 2 &&
                    // Check presential contact has address with references
                    healthProfessional.contacts!!.any { contact ->
                        contact.modality == ModalityType.PRESENTIAL &&
                        contact.address != null &&
                        contact.address!!.referencedModelClass != null &&
                        contact.address!!.referencedModelId != null
                    } &&
                    // Check remote contact has no address
                    healthProfessional.contacts!!.any { contact ->
                        contact.modality == ModalityType.REMOTE &&
                        contact.address == null
                    }
                },
                any()
            )
        }
    }

    @Test
    fun `#update should update staff with presential contact and address references`() = runBlocking {
        val staffId = UUID.randomUUID()
        val healthProfessionalId = UUID.randomUUID()
        val contactId = RangeUUID.generate()
        val addressId = RangeUUID.generate()

        val existingStaff = Staff(
            id = staffId,
            firstName = "João",
            lastName = "Silva",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.MANAGER_PHYSICIAN,
            type = StaffType.PARTNER_HEALTH_PROFESSIONAL,
            active = true
        )

        val existingHealthProfessional = TestModelFactory.buildHealthProfessional().copy(
            id = healthProfessionalId,
            staffId = staffId
        )

        val addressDTO = AddressDTO(
            id = addressId,
            street = "Rua Nova",
            number = "456",
            complement = "Sala 20",
            neighborhood = "Vila Nova",
            state = "RJ",
            city = "Rio de Janeiro",
            zipcode = "20000-000",
            label = "Novo Consultório",
            active = true,
            latitude = "-22.9068",
            longitude = "-43.1729"
        )

        val contactDTO = ContactDTO(
            id = contactId,
            address = addressDTO,
            phones = emptyList(),
            scheduleAvailabilityDays = 255,
            modality = ModalityType.PRESENTIAL,
            availableDays = emptyList(),
            website = "https://novo-consultorio.com"
        )

        val updateRequest = UpdateStaffRequest(
            firstName = "João Updated",
            lastName = "Silva Updated",
            email = "<EMAIL>",
            gender = Gender.MALE,
            role = Role.MANAGER_PHYSICIAN,
            type = StaffType.PARTNER_HEALTH_PROFESSIONAL,
            active = true,
            contacts = listOf(contactDTO),
            council = CouncilDTO(
                number = "123456",
                state = State.SP,
                type = CouncilType.CRM
            ),
            version = 1
        )

        val updatedStaff = existingStaff.copy(
            firstName = updateRequest.firstName,
            lastName = updateRequest.lastName,
            email = updateRequest.email
        )

        coEvery { staffService.get(staffId) } returns existingStaff.success()
        coEvery { healthProfessionalService.findByStaffId(staffId, any()) } returns existingHealthProfessional.success()
        coEvery { staffService.update(any(), any(), any()) } returns updatedStaff.success()

        authenticatedAs(idToken, staff) {
            put("/staff/$staffId", updateRequest) { response ->
                assertThat(response).isOK()
            }
        }

        // Verify that the staff service was called with updated contact data including address references
        coVerifyOnce {
            staffService.update(
                match { staff ->
                    staff.firstName == "João Updated" &&
                    staff.lastName == "Silva Updated"
                },
                match { healthProfessional ->
                    healthProfessional != null &&
                    healthProfessional.contacts != null &&
                    healthProfessional.contacts!!.size == 1 &&
                    healthProfessional.contacts!!.first().modality == ModalityType.PRESENTIAL &&
                    healthProfessional.contacts!!.first().address != null &&
                    healthProfessional.contacts!!.first().address!!.street == "Rua Nova" &&
                    healthProfessional.contacts!!.first().address!!.referencedModelClass != null &&
                    healthProfessional.contacts!!.first().address!!.referencedModelId == healthProfessionalId
                },
                any()
            )
        }
        coVerifyOnce { staffService.get(staffId) }
        coVerifyOnce { healthProfessionalService.findByStaffId(staffId, any()) }
    }
}
