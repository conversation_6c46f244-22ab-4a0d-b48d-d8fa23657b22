package br.com.alice.member.api.extensions

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.member.api.models.HealthPlanItemNavigation
import br.com.alice.member.api.models.HealthPlanItemNavigationTag
import br.com.alice.member.api.models.HealthPlanItemNavigationTagColor
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.procedureAuthorization.ActionPlanTaskAuthorization
import br.com.alice.member.api.models.v2.ActionNavigationConfirmationModal
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import kotlin.test.Test

class ActionPlanTaskAuthorizationExtensionsTest {

    val totvsGuiaId = RangeUUID.generate()

    @Nested
    inner class ToHealthPlanItemNavigation {

        @Test
        fun `#toHealthPlanItemNavigation should return correct response when status is AUTHORIZED`() {
            val actionPlanTaskAuthorization = ActionPlanTaskAuthorization(
                totvsGuiaStatus = TotvsGuiaStatus.AUTHORIZED,
                totvsGuiaId = totvsGuiaId
            )

            val expectedResult = HealthPlanItemNavigation(
                name = "Autorização de procedimentos",
                description = "Acompanhe os detalhes da sua guia e procedimentos",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/paper.png",
                icon = "paper",
                tag = HealthPlanItemNavigationTag(
                    icon = "check_outlined",
                    text = "Autorizada",
                    color = HealthPlanItemNavigationTagColor.GREEN
                ),
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                    properties = mapOf("id" to totvsGuiaId)
                ),
            )

            val result = actionPlanTaskAuthorization.toHealthPlanItemNavigation()
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

        @Test
        fun `#toHealthPlanItemNavigation should return correct response when status is CANCELLED`() {
            val actionPlanTaskAuthorization = ActionPlanTaskAuthorization(
                totvsGuiaStatus = TotvsGuiaStatus.CANCELLED,
                totvsGuiaId = totvsGuiaId
            )

            val expectedResult = HealthPlanItemNavigation(
                name = "Autorização de procedimentos",
                description = "Acompanhe os detalhes da sua guia e procedimentos",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/paper.png",
                icon = "paper",
                tag = HealthPlanItemNavigationTag(
                    icon = "alert_circle_outlined",
                    text = "Cancelada",
                    color = HealthPlanItemNavigationTagColor.GRAY
                ),
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                    properties = mapOf("id" to totvsGuiaId)
                ),
            )

            val result = actionPlanTaskAuthorization.toHealthPlanItemNavigation()
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

        @Test
        fun `#toHealthPlanItemNavigation should return correct response when status is PARTIALLY_AUTHORIZED`() {
            val actionPlanTaskAuthorization = ActionPlanTaskAuthorization(
                totvsGuiaStatus = TotvsGuiaStatus.PARTIALLY_AUTHORIZED,
                totvsGuiaId = totvsGuiaId
            )

            val expectedResult = HealthPlanItemNavigation(
                name = "Autorização de procedimentos",
                description = "Acompanhe os detalhes da sua guia e procedimentos",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/paper.png",
                icon = "paper",
                tag = HealthPlanItemNavigationTag(
                    icon = "chat_sent",
                    text = "Parcialmente autorizada",
                    color = HealthPlanItemNavigationTagColor.BLUE
                ),
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                    properties = mapOf("id" to totvsGuiaId)
                ),
            )

            val result = actionPlanTaskAuthorization.toHealthPlanItemNavigation()
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

        @Test
        fun `#toHealthPlanItemNavigation should return correct response when status is PENDING`() {
            val actionPlanTaskAuthorization = ActionPlanTaskAuthorization(
                totvsGuiaStatus = TotvsGuiaStatus.PENDING,
                totvsGuiaId = totvsGuiaId
            )

            val expectedResult = HealthPlanItemNavigation(
                name = "Autorização de procedimentos",
                description = "Acompanhe os detalhes da sua guia e procedimentos",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/paper.png",
                icon = "paper",
                tag = HealthPlanItemNavigationTag(
                    icon = "clock",
                    text = "Análise em andamento",
                    color = HealthPlanItemNavigationTagColor.YELLOW
                ),
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                    properties = mapOf("id" to totvsGuiaId)
                ),
            )

            val result = actionPlanTaskAuthorization.toHealthPlanItemNavigation()
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

        @Test
        fun `#toHealthPlanItemNavigation should return correct response when status is UNAUTHORIZED`() {
            val actionPlanTaskAuthorization = ActionPlanTaskAuthorization(
                totvsGuiaStatus = TotvsGuiaStatus.UNAUTHORIZED,
                totvsGuiaId = totvsGuiaId
            )

            val expectedResult = HealthPlanItemNavigation(
                name = "Autorização de procedimentos",
                description = "Acompanhe os detalhes da sua guia e procedimentos",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/paper.png",
                icon = "paper",
                tag = HealthPlanItemNavigationTag(
                    icon = "block",
                    text = "Não autorizada",
                    color = HealthPlanItemNavigationTagColor.RED
                ),
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                    properties = mapOf("id" to totvsGuiaId)
                ),
            )

            val result = actionPlanTaskAuthorization.toHealthPlanItemNavigation()
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

        @Test
        fun `#toHealthPlanItemNavigation should return correct response when there is no totvsGuiaId`() {
            val actionPlanTaskAuthorization = ActionPlanTaskAuthorization(
                totvsGuiaStatus = TotvsGuiaStatus.AUTHORIZED,
                totvsGuiaId = null
            )

            val expectedResult = HealthPlanItemNavigation(
                name = "Autorização de procedimentos",
                description = "Acompanhe os detalhes da sua guia e procedimentos",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/paper.png",
                icon = "paper",
                tag = HealthPlanItemNavigationTag(
                    icon = "check_outlined",
                    text = "Autorizada",
                    color = HealthPlanItemNavigationTagColor.GREEN
                ),
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_LIST,
                    properties = null
                ),
            )

            val result = actionPlanTaskAuthorization.toHealthPlanItemNavigation()
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

    }


    @Nested
    inner class ToActionNavigationConfirmationModal {

        @Test
        fun `#toActionNavigationConfirmationModal should return correct response when status is not PENDING`() {
            val actionPlanTaskAuthorization = ActionPlanTaskAuthorization(
                totvsGuiaStatus = TotvsGuiaStatus.AUTHORIZED,
                totvsGuiaId = totvsGuiaId
            )

            val result = actionPlanTaskAuthorization.toActionNavigationConfirmationModal()
            Assertions.assertThat(result).isEqualTo(null)
        }

        @Test
        fun `#toActionNavigationConfirmationModal should return correct response when status is PENDING`() {
            val actionPlanTaskAuthorization = ActionPlanTaskAuthorization(
                totvsGuiaStatus = TotvsGuiaStatus.PENDING,
                totvsGuiaId = totvsGuiaId
            )

            val expectedResult = ActionNavigationConfirmationModal(
                title = "Os procedimentos da sua guia ainda não foram autorizados.\n" +
                        "Quer agendar mesmo assim?",
                imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/alert.svg",
                description = "A análise pode levar até 3 dias úteis.",
                confirmButtonLabel = "Sim",
                cancelButtonLabel = "Cancelar"
            )

            val result = actionPlanTaskAuthorization.toActionNavigationConfirmationModal()
            Assertions.assertThat(result).isEqualTo(expectedResult)
        }

    }
}
