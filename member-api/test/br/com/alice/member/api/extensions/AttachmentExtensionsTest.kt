package br.com.alice.member.api.extensions

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.models.Attachment
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.HealthPlanItemAttachment
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import kotlin.test.Test

class AttachmentExtensionsTest {

    @Nested
    inner class ToHealthPlanItemAttachment {

        @Test
        fun `#toHealthPlanItemAttachment should return HealthPlanItemAttachment when is prescription`() {

            val attachment = Attachment(
                id = RangeUUID.generate(),
                type = "pdf",
                fileName = "Prescrição",
            )

            val result = attachment.toHealthPlanItemAttachment()
            Assertions.assertThat(result).isEqualTo(
                HealthPlanItemAttachment(
                    name = "Prescrição",
                    description = "Confira a rotina de cuidado recomendada para você",
                    imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/att.png",
                    icon = "bullets",
                    link = ServiceConfig.url("/health_plan/attachments/${attachment.id}")
                )
            )

        }

        @Test
        fun `#toHealthPlanItemAttachment should return HealthPlanItemAttachment when is test request`() {

            val attachment = Attachment(
                id = RangeUUID.generate(),
                type = "pdf",
                fileName = "Pedido_de_exame",
            )

            val result = attachment.toHealthPlanItemAttachment()
            Assertions.assertThat(result).isEqualTo(
                HealthPlanItemAttachment(
                    name = "Veja o Pedido do exame",
                    description = "Confira detalhes e compartilhe",
                    imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/signature.png",
                    icon = "lab",
                    link = ServiceConfig.url("/health_plan/attachments/${attachment.id}")
                )
            )

        }

        @Test
        fun `#toHealthPlanItemAttachment should return HealthPlanItemAttachment when is unknown case`() {

            val attachment = Attachment(
                id = RangeUUID.generate(),
                type = "pdf",
                fileName = "unknown",
            )

            val result = attachment.toHealthPlanItemAttachment()
            Assertions.assertThat(result).isEqualTo(
                HealthPlanItemAttachment(
                    name = "unknown",
                    icon = "paper",
                    link = ServiceConfig.url("/health_plan/attachments/${attachment.id}")
                )
            )

        }

    }

    @Nested
    inner class ToItemAttachment {

        @Test
        fun `#toItemAttachment should return list of HealthPlanItemAttachment correctly`() {

            val attachment1 = Attachment(
                id = RangeUUID.generate(),
                type = "pdf",
                fileName = "Prescrição",
            )

            val attachment2 = Attachment(
                id = RangeUUID.generate(),
                type = "pdf",
                fileName = "Pedido_de_exame",
            )

            val result = listOf(attachment1, attachment2).toItemAttachment()
            Assertions.assertThat(result).isEqualTo(
                listOf(
                    HealthPlanItemAttachment(
                        name = "Prescrição",
                        description = "Confira a rotina de cuidado recomendada para você",
                        imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/att.png",
                        icon = "bullets",
                        link = ServiceConfig.url("/health_plan/attachments/${attachment1.id}")
                    ),
                    HealthPlanItemAttachment(
                        name = "Veja o Pedido do exame",
                        description = "Confira detalhes e compartilhe",
                        imageUrl = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/signature.png",
                        icon = "lab",
                        link = ServiceConfig.url("/health_plan/attachments/${attachment2.id}")
                    )
                )
            )

        }

    }

}
