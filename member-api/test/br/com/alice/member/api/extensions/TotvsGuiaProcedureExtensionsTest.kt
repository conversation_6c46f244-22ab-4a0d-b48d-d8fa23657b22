package br.com.alice.member.api.extensions

import br.com.alice.common.MvUtil
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TotvsGuiaStatus
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertFailsWith

class TotvsGuiaProcedureExtensionsTest {

    @Nested
    inner class MergedStatus {

        @Test
        fun `#mergedStatus should throw InvalidArgumentException when list is empty`() {
            val statuses = emptyList<TotvsGuiaStatus>()

            assertFailsWith(InvalidArgumentException::class) {
                statuses.mergedStatus()
            }
        }

        @Test
        fun `#mergedStatus should return PENDING when all status is equal to PENDING`() {
            val statuses = listOf(
                TotvsGuiaStatus.PENDING,
                TotvsGuiaStatus.PENDING,
                TotvsGuiaStatus.PENDING
            )

            val result = statuses.mergedStatus()
            Assertions.assertThat(result).isEqualTo(TotvsGuiaStatus.PENDING)
        }

        @Test
        fun `#mergedStatus should return PENDING when there is one PENDING on list`() {
            val statuses = listOf(
                TotvsGuiaStatus.AUTHORIZED,
                TotvsGuiaStatus.PENDING,
                TotvsGuiaStatus.CANCELLED
            )

            val result = statuses.mergedStatus()
            Assertions.assertThat(result).isEqualTo(TotvsGuiaStatus.PENDING)
        }

        @Test
        fun `#mergedStatus should return PARTIALLY_AUTHORIZED when there is no PENDING and one PARTIALLY_AUTHORIZED on list`() {
            val statuses = listOf(
                TotvsGuiaStatus.AUTHORIZED,
                TotvsGuiaStatus.PARTIALLY_AUTHORIZED,
                TotvsGuiaStatus.UNAUTHORIZED
            )

            val result = statuses.mergedStatus()
            Assertions.assertThat(result).isEqualTo(TotvsGuiaStatus.PARTIALLY_AUTHORIZED)
        }

        @Test
        fun `#mergedStatus should return AUTHORIZED when there is no PENDING, PARTIALLY_AUTHORIZED, UNAUTHORIZED and one AUTHORIZED on list`() {
            val statuses = listOf(
                TotvsGuiaStatus.AUTHORIZED,
                TotvsGuiaStatus.CANCELLED,
                TotvsGuiaStatus.CANCELLED
            )

            val result = statuses.mergedStatus()
            Assertions.assertThat(result).isEqualTo(TotvsGuiaStatus.AUTHORIZED)
        }

        @Test
        fun `#mergedStatus should return UNAUTHORIZED when there is no PENDING, PARTIALLY_AUTHORIZED, AUTHORIZED and one UNAUTHORIZED on list`() {
            val statuses = listOf(
                TotvsGuiaStatus.UNAUTHORIZED,
                TotvsGuiaStatus.UNAUTHORIZED,
                TotvsGuiaStatus.CANCELLED
            )

            val result = statuses.mergedStatus()
            Assertions.assertThat(result).isEqualTo(TotvsGuiaStatus.UNAUTHORIZED)
        }

        @Test
        fun `#mergedStatus should return PARTIALLY_AUTHORIZED when there is one AUTHORIZED and UNAUTHORIZED on list`() {
            val statuses = listOf(
                TotvsGuiaStatus.UNAUTHORIZED,
                TotvsGuiaStatus.AUTHORIZED,
                TotvsGuiaStatus.CANCELLED,
            )

            val result = statuses.mergedStatus()
            Assertions.assertThat(result).isEqualTo(TotvsGuiaStatus.PARTIALLY_AUTHORIZED)
        }
    }

    @Nested
    inner class ProcedureLabel {

        @Test
        fun `#procedureLabel should return correct label for PS`() {

            val totvsGuia = TestModelFactory.buildTotvsGuia(type = MvUtil.TISS.PS)

            val result = totvsGuia.procedureLabel()
            Assertions.assertThat(result).isEqualTo("Número da guia do Pronto-Socorro")
        }

        @Test
        fun `#procedureLabel should return correct label for HOSPITALIZATION`() {

            val totvsGuia = TestModelFactory.buildTotvsGuia(type = MvUtil.TISS.HOSPITALIZATION)

            val result = totvsGuia.procedureLabel()
            Assertions.assertThat(result).isEqualTo("Número da guia da Internação")
        }

        @Test
        fun `#procedureLabel should return correct label for EXTENSION`() {

            val totvsGuia = TestModelFactory.buildTotvsGuia(type = MvUtil.TISS.EXTENSION)

            val result = totvsGuia.procedureLabel()
            Assertions.assertThat(result).isEqualTo("Prorrogação da Internação")
        }

        @Test
        fun `#procedureLabel should return correct label for EXAM`() {

            val totvsGuia = TestModelFactory.buildTotvsGuia(type = MvUtil.TISS.EXAM)

            val result = totvsGuia.procedureLabel()
            Assertions.assertThat(result).isEqualTo("Número da guia do Exame")
        }

        @Test
        fun `#procedureLabel should return correct label for THERAPY`() {

            val totvsGuia = TestModelFactory.buildTotvsGuia(type = MvUtil.TISS.THERAPY)

            val result = totvsGuia.procedureLabel()
            Assertions.assertThat(result).isEqualTo("Número da guia da Terapia")
        }

        @Test
        fun `#procedureLabel should return correct label for APPOINTMENT`() {

            val totvsGuia = TestModelFactory.buildTotvsGuia(type = MvUtil.TISS.APPOINTMENT)

            val result = totvsGuia.procedureLabel()
            Assertions.assertThat(result).isEqualTo("Número da guia da Consulta")
        }

    }
}
