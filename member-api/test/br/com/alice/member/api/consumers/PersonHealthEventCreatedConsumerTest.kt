package br.com.alice.member.api.consumers

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.wanda.event.PersonHealthEventCreatedEvent
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class PersonHealthEventCreatedConsumerTest {

    private val consumer = PersonHealthEventCreatedConsumer()

    @Test
    fun `#updateAppState should call AppStateNotifier#updateAppState as expected`() = runBlocking {
        val personHealthEvent = TestModelFactory.buildPersonHealthEvent(category = PersonHealthEventCategory.TIMELINE_EVENT)
        val event = PersonHealthEventCreatedEvent(personHealthEvent)

        mockkObject(AppStateNotifier) {
            every { AppStateNotifier.updateAppState(event.payload.personHealthEvent.personId, AppState.PERSON_HEALTH_EVENT) } returns Unit

            consumer.updateAppState(event)

            coVerify(exactly = 1) { AppStateNotifier.updateAppState(event.payload.personHealthEvent.personId, AppState.PERSON_HEALTH_EVENT) }
        }
    }

    @Test
    fun `#updateAppState should not call AppStateNotifier#updateAppState if category should not be shown on mobile`() = runBlocking {
        val personHealthEvent = TestModelFactory.buildPersonHealthEvent(category = PersonHealthEventCategory.APPOINTMENT_IMMERSION)
        val event = PersonHealthEventCreatedEvent(personHealthEvent)

        mockkObject(AppStateNotifier) {
            every { AppStateNotifier.updateAppState(event.payload.personHealthEvent.personId, AppState.PERSON_HEALTH_EVENT) } returns Unit

            consumer.updateAppState(event)

            coVerify(exactly = 0) { AppStateNotifier.updateAppState(event.payload.personHealthEvent.personId, AppState.PERSON_HEALTH_EVENT) }
        }
    }
}
