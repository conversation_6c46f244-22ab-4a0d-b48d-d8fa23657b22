package br.com.alice.member.api.consumers

import br.com.alice.authentication.Authenticator
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.person.model.events.MemberActivatedEvent
import com.google.firebase.ErrorCode
import com.google.firebase.auth.AuthErrorCode
import com.google.firebase.auth.FirebaseAuthException
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class MemberActivatedConsumerTest {

    private val consumer = MemberActivatedConsumer()

    @Test
    fun `#expireToken should expire token and call update app state`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val event = MemberActivatedEvent(member)

        mockkObject(AppStateNotifier) {
            mockkObject(Authenticator) {
                every { AppStateNotifier.updateAppState(event.payload.personId, AppState.TOKEN_EXPIRED) } returns Unit
                every { AppStateNotifier.updateAppState(event.payload.personId, AppState.BENEFICIARY_MEMBERSHIP_ACTIVATED) } returns Unit
                every { Authenticator.revokeTokenByUserId(event.payload.personId.toString()) } returns mockk()

                consumer.expireTokenAndUpdateAppState(event)

                coVerify(exactly = 2) { AppStateNotifier.updateAppState(any(), any()) }
                coVerifyOnce { Authenticator.revokeTokenByUserId(any()) }
            }
        }
    }

    @Test
    fun `#expireToken should ignore expire token firebase error and update app state`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val event = MemberActivatedEvent(member)

        mockkObject(AppStateNotifier) {
            mockkObject(Authenticator) {
                every { AppStateNotifier.updateAppState(event.payload.personId, AppState.TOKEN_EXPIRED) } returns Unit
                every { AppStateNotifier.updateAppState(event.payload.personId, AppState.BENEFICIARY_MEMBERSHIP_ACTIVATED) } returns Unit
                every {
                    Authenticator.revokeTokenByUserId(event.payload.personId.toString())
                } throws FirebaseAuthException(
                    ErrorCode.NOT_FOUND,
                    "message",
                    Exception("details"),
                    null,
                    AuthErrorCode.USER_NOT_FOUND
                )

                consumer.expireTokenAndUpdateAppState(event)

                coVerify(exactly = 2) { AppStateNotifier.updateAppState(any(), any()) }
                coVerifyOnce { Authenticator.revokeTokenByUserId(any()) }
            }
        }
    }

    @Test
    fun `#expireToken should throw error when expire throws error`() = runBlocking {
        val member = TestModelFactory.buildMember()
        val event = MemberActivatedEvent(member)

        mockkObject(AppStateNotifier) {
            mockkObject(Authenticator) {
                every {
                    Authenticator.revokeTokenByUserId(event.payload.personId.toString())
                } throws FirebaseAuthException(
                    ErrorCode.UNAVAILABLE,
                    "message",
                    Exception("details"),
                    null,
                    AuthErrorCode.REVOKED_SESSION_COOKIE
                )

                val result = consumer.expireTokenAndUpdateAppState(event)
                assertThat(result).isFailureOfType(FirebaseAuthException::class)

                coVerifyNone { AppStateNotifier.updateAppState(any(), any()) }
                coVerifyOnce { Authenticator.revokeTokenByUserId(any()) }
            }
        }
    }
}
