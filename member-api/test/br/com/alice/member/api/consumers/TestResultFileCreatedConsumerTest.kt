package br.com.alice.member.api.consumers

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.ehr.event.TestResultFileCreatedEvent
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class TestResultFileCreatedConsumerTest {

    private val consumer = TestResultFileCreatedConsumer()

    @Test
    fun `#updateAppState should call AppStateNotifier#updateAppState as expected`() = runBlocking {
        val testResultFile = TestModelFactory.buildTestResultFile()
        val event = TestResultFileCreatedEvent(testResultFile)

        mockkObject(AppStateNotifier) {
            every { AppStateNotifier.updateAppState(event.payload.personId, AppState.HOME_TEST_RESULTS) } returns Unit

            consumer.updateAppState(event)

            coVerify(exactly = 1) {
                AppStateNotifier.updateAppState(
                    event.payload.personId,
                    AppState.HOME_TEST_RESULTS
                )
            }
        }
    }
}
