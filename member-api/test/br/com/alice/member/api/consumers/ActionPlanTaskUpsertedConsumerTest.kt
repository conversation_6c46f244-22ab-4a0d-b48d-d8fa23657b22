package br.com.alice.member.api.consumers

import br.com.alice.action.plan.notifier.ActionPlanTaskUpsertedEvent
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.api.services.PushNotificationInternalService
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ActionPlanTaskUpsertedConsumerTest {

    private val pushNotificationService: PushNotificationInternalService = mockk()
    private val consumer = ActionPlanTaskUpsertedConsumer(pushNotificationService)

    @Test
    fun `#updateAppState should call AppStateNotifier#updateAppState as expected`() = runBlocking {
        val task = TestModelFactory.buildActionPlanTask(status = ActionPlanTaskStatus.SCHEDULED)
        val event = ActionPlanTaskUpsertedEvent(task)

        mockkObject(AppStateNotifier) {
            every {
                AppStateNotifier.updateAppState(
                    event.payload.task.personId,
                    AppState.HEALTH_PLAN,
                    AppState.UNIFIED_HEALTH,
                    AppState.HEALTH_ALL_DEMANDS,
                    AppState.REDESIGN_HEALTH_PLAN_HOME_TASK_LIST,
                    AppState.REDESIGN_UNIFIED_HEALTH,
                )
            } returns Unit

            consumer.updateAppState(event)

            coVerify(exactly = 1) {
                AppStateNotifier.updateAppState(
                    event.payload.task.personId,
                    AppState.HEALTH_PLAN,
                    AppState.UNIFIED_HEALTH,
                    AppState.HEALTH_ALL_DEMANDS,
                    AppState.REDESIGN_HEALTH_PLAN_HOME_TASK_LIST,
                    AppState.REDESIGN_UNIFIED_HEALTH,
                )
            }
            coVerifyNone { pushNotificationService.sendNewTasksPushNotification(any()) }
        }
    }

    @Test
    fun `#updateAppState should send push notification if task is QUESTIONNAIRE`() = runBlocking {
        val task = TestModelFactory.buildActionPlanTask(type = ActionPlanTaskType.QUESTIONNAIRE)
        val event = ActionPlanTaskUpsertedEvent(task)

        mockkObject(AppStateNotifier) {
            every {
                AppStateNotifier.updateAppState(
                    event.payload.task.personId,
                    AppState.HEALTH_PLAN,
                    AppState.UNIFIED_HEALTH,
                    AppState.HEALTH_ALL_DEMANDS,
                    AppState.REDESIGN_HEALTH_PLAN_HOME_TASK_LIST,
                    AppState.REDESIGN_UNIFIED_HEALTH,
                )
            } returns Unit

            consumer.updateAppState(event)

            coVerifyOnce { pushNotificationService.sendNewTasksPushNotification(event.payload.task.personId) }
        }
    }
}
