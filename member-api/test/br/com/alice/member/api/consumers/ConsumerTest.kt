package br.com.alice.member.api.consumers

import br.com.alice.authentication.Authenticator
import br.com.alice.data.layer.MEMBER_API_SUBSCRIBERS_ROOT_SERVICE_NAME
import br.com.alice.data.layer.MEMBER_ROOT_SERVICE_NAME
import io.mockk.coEvery
import io.mockk.mockkObject
import kotlin.test.BeforeTest

abstract class ConsumerTest {

    @BeforeTest
    open fun setup() { }

    init {
        mockkObject(Authenticator)
        coEvery { Authenticator.generateRootServiceToken(MEMBER_ROOT_SERVICE_NAME) } returns "123"
        coEvery { Authenticator.generateRootServiceToken(MEMBER_API_SUBSCRIBERS_ROOT_SERVICE_NAME) } returns "123"
        coEvery { Authenticator.generateCustomToken(any(), "Unauthenticated") } returns "456"
    }
}
