package br.com.alice.member.api.consumers

import br.com.alice.authentication.Authenticator
import br.com.alice.business.events.BeneficiaryCreatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class BeneficiaryCreatedConsumerTest : ConsumerTest() {
    private val consumer = BeneficiaryCreatedConsumer()

    @Test
    fun `#expireToken - consume event and expire token`() = runBlocking<Unit> {
        val beneficiary = TestModelFactory.buildBeneficiary()
        val event = BeneficiaryCreatedEvent(
            beneficiary,
            RangeUUID.generate(),
            BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
        )

        mockkObject(AppStateNotifier) {
            mockkObject(Authenticator) {
                every {
                    AppStateNotifier.updateAppState(
                        event.payload.beneficiary.personId,
                        AppState.TOKEN_EXPIRED
                    )
                } returns Unit
                every { Authenticator.revokeTokenByUserId(event.payload.beneficiary.personId.toString()) } returns mockk()

                consumer.expireToken(event)

                coVerify(exactly = 1) {
                    AppStateNotifier.updateAppState(
                        event.payload.beneficiary.personId,
                        AppState.TOKEN_EXPIRED
                    )
                }
            }
        }
    }
}
