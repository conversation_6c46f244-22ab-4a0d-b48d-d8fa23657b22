package br.com.alice.member.api.consumers

import br.com.alice.app.content.notifier.AppContentScreenDetailUpsertedEvent
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ScreenDetailType
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import io.mockk.every
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class AppContentScreenDetailUpsertedConsumerTest {

    private val consumer = AppContentScreenDetailUpsertedConsumer()

    private val person = TestModelFactory.buildPerson()
    private val appContentScreenDetail = TestModelFactory.buildAppContentScreenDetail(
        personId = person.id
    )

    @Test
    fun `#notifyAppContentScreenDetailUpsertion should call AppStateNotifier#updateAppState to CHESIRE_HOME when screenType is HOME`() = runBlocking {
        val event = AppContentScreenDetailUpsertedEvent(appContentScreenDetail.copy(screenType = ScreenDetailType.HOME))
        mockkObject(AppStateNotifier) {
            every {
                AppStateNotifier.updateAppState(
                    event.payload.screenDetail.personId,
                    AppState.CHESHIRE_HOME
                )
            } returns Unit

            val result = consumer.notifyAppContentScreenDetailUpsertion(event)
            assertThat(result).isSuccessWithData(Unit)

            coVerifyOnce { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

    @Test
    fun `#notifyAppContentScreenDetailUpsertion should call AppStateNotifier#updateAppState to CHESIRE_ALICE_NOW when screenType is ALICE_NOW`() = runBlocking {
        val event = AppContentScreenDetailUpsertedEvent(appContentScreenDetail.copy(screenType = ScreenDetailType.ALICE_NOW))
        mockkObject(AppStateNotifier) {
            every {
                AppStateNotifier.updateAppState(
                    event.payload.screenDetail.personId,
                    AppState.CHESHIRE_ALICE_NOW
                )
            } returns Unit

            val result = consumer.notifyAppContentScreenDetailUpsertion(event)

            assertThat(result).isSuccessWithData(Unit)

            coVerifyOnce { AppStateNotifier.updateAppState(any(), any()) }
        }
    }


    @Test
    fun `#notifyAppContentScreenDetailUpsertion should call AppStateNotifier#updateAppState to CHESIRE_HEALTH when screenType is HEALTH`() = runBlocking {
        val event = AppContentScreenDetailUpsertedEvent(appContentScreenDetail.copy(screenType = ScreenDetailType.HEALTH))
        mockkObject(AppStateNotifier) {
            every {
                AppStateNotifier.updateAppState(
                    event.payload.screenDetail.personId,
                    AppState.CHESHIRE_HEALTH
                )
            } returns Unit

            val result = consumer.notifyAppContentScreenDetailUpsertion(event)

            assertThat(result).isSuccessWithData(Unit)

            coVerifyOnce { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

    @Test
    fun `#notifyAppContentScreenDetailUpsertion should call AppStateNotifier#updateAppState to CHESIRE_CHANNELS when screenType is CHANNELS`() = runBlocking {
        val event = AppContentScreenDetailUpsertedEvent(appContentScreenDetail.copy(screenType = ScreenDetailType.CHANNELS))
        mockkObject(AppStateNotifier) {
            every {
                AppStateNotifier.updateAppState(
                    event.payload.screenDetail.personId,
                    AppState.CHESHIRE_CHANNELS
                )
            } returns Unit

            val result = consumer.notifyAppContentScreenDetailUpsertion(event)

            assertThat(result).isSuccessWithData(Unit)

            coVerifyOnce { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

    @Test
    fun `#notifyAppContentScreenDetailUpsertion should call AppStateNotifier#updateAppState to CHESIRE_SCHEDULE when screenType is SCHEDULE`() = runBlocking {
        val event = AppContentScreenDetailUpsertedEvent(appContentScreenDetail.copy(screenType = ScreenDetailType.SCHEDULE))
        mockkObject(AppStateNotifier) {
            every {
                AppStateNotifier.updateAppState(
                    event.payload.screenDetail.personId,
                    AppState.CHESHIRE_SCHEDULE
                )
            } returns Unit

            val result = consumer.notifyAppContentScreenDetailUpsertion(event)

            assertThat(result).isSuccessWithData(Unit)

            coVerifyOnce { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

    @Test
    fun `#notifyAppContentScreenDetailUpsertion should call AppStateNotifier#updateAppState to UNIFIED_HEALTH when screenType is UNIFIED_HEALTH`() = runBlocking {
        val event = AppContentScreenDetailUpsertedEvent(appContentScreenDetail.copy(screenType = ScreenDetailType.UNIFIED_HEALTH))
        mockkObject(AppStateNotifier) {
            every {
                AppStateNotifier.updateAppState(
                    event.payload.screenDetail.personId,
                    AppState.UNIFIED_HEALTH
                )
            } returns Unit

            val result = consumer.notifyAppContentScreenDetailUpsertion(event)
            assertThat(result).isSuccessWithData(Unit)

            coVerifyOnce { AppStateNotifier.updateAppState(any(), any()) }
        }
    }
}
