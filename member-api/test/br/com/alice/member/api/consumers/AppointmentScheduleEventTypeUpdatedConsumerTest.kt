package br.com.alice.member.api.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.schedule.model.events.AppointmentScheduleEventTypeUpdated
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentScheduleEventTypeUpdatedConsumerTest : ConsumerTest() {

    private val cache: GenericCache = mockk()
    private val consumer = AppointmentScheduleEventTypeUpdatedConsumer(cache)

    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType()
    private val event = AppointmentScheduleEventTypeUpdated(appointmentScheduleEventType)
    private val cacheKeyToInvalidate = "appointmentScheduleEventTypeById#${appointmentScheduleEventType.id}"
    private val invalidatedCacheKeys = mutableSetOf(cacheKeyToInvalidate)

    @BeforeTest
    override fun setup() {
        super.setup()

        coEvery { cache.invalidateAndReturnKeys(cacheKeyToInvalidate) } returns invalidatedCacheKeys
    }

    @Test
    fun `#updateAppState should call AppStateNotifier with expected parameters`() = runBlocking {
        val result = consumer.invalidateCache(event)
        assertThat(result).isSuccessWithData(invalidatedCacheKeys)
        coVerifyOnce { cache.invalidateAndReturnKeys(cacheKeyToInvalidate) }
    }
}
