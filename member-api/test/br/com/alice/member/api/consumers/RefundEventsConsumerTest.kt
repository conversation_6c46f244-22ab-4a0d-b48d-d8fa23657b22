package br.com.alice.member.api.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.AnalyticsTrackerResult
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.RefundHealthEventType
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.person.client.PersonService
import br.com.alice.refund.notifier.RefundCreatedEvent
import br.com.alice.refund.notifier.RefundPayload
import br.com.alice.refund.notifier.RefundRequestedEvent
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class RefundEventsConsumerTest : ConsumerTest() {

    private val crmAnalyticsTracker: CrmAnalyticsTracker = mockk()
    private val personService: PersonService = mockk()

    private val consumer = RefundEventsConsumer(
        crmAnalyticsTracker,
        personService
    )

    private val person = TestModelFactory.buildPerson()

    private val refund = TestModelFactory.buildRefund(
        healthEventType = RefundHealthEventType.APPOINTMENT,
    )
    private val refundCreatedEvent = RefundCreatedEvent(RefundPayload(refund))
    private val refundRequestedEvent = RefundRequestedEvent(RefundPayload(refund))

    @Test
    fun `#consumeRefundCreated should update REFUND_HISTORY screen app state properly`() = runBlocking {
        mockkObject(AppStateNotifier) {
            every { AppStateNotifier.updateAppState(refund.personId, AppState.REFUND_HISTORY) } returns Unit

            consumer.consumeRefundCreated(refundCreatedEvent)

            coVerifyOnce { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

    @Test
    fun `#consumeRefundRequested should update REFUND_HISTORY screen app state properly`() = runBlocking {
        mockkObject(AppStateNotifier) {
            every { AppStateNotifier.updateAppState(refund.personId, AppState.REFUND_HISTORY) } returns Unit

            consumer.consumeRefundRequested(refundRequestedEvent)

            coVerifyOnce { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

    @Test
    fun `#sendTriggerToCESQuestionnaire should send braze event to trigger ces questionnaire`() =
        mockLocalDateTime { localDateTimeNow ->
            coEvery {
                personService.get(refund.personId)
            } returns person.success()

            val expectedResult = AnalyticsTrackerResult(true)
            coEvery {
                crmAnalyticsTracker.sendEvent(
                    person.nationalId,
                    AnalyticsEvent(
                        name = AnalyticsEventName.REFUND_REQUESTED,
                        timestamp = localDateTimeNow,
                        properties = mapOf(
                            "type" to "MEMBER_CHOICE",
                            "health_event_type" to "APPOINTMENT",
                        )
                    )
                )
            } returns expectedResult

            val result = consumer.sendTriggerToCESQuestionnaire(refundRequestedEvent)

            ResultAssert.assertThat(result).isSuccessWithData(expectedResult)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { crmAnalyticsTracker.sendEvent(any(), any()) }
        }
}
