package br.com.alice.member.api.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthPlanTaskType.PRESCRIPTION
import br.com.alice.healthplan.events.HealthPlanTaskGroupPublishedEvent
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.api.services.PushNotificationInternalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class HealthPlanTaskGroupPublishedConsumerTest : ConsumerTest() {

    private val person = TestModelFactory.buildPerson()
    private val healthPlanTasks = listOf(TestModelFactory.buildHealthPlanTask(personId = person.id))

    private val pushNotificationService: PushNotificationInternalService = mockk()
    private val consumer = HealthPlanTaskGroupPublishedConsumer(pushNotificationService)

    @Test
    fun `#updateAppState should call AppStateNotifier with expected parameters`() = runBlocking {
        mockkObject(AppStateNotifier)

        coEvery { AppStateNotifier.updateAppState(person.id, AppState.UNIFIED_HEALTH) } returns Unit
        coEvery { AppStateNotifier.updateAppState(person.id, AppState.HEALTH_ALL_DEMANDS) } returns Unit
        coEvery { AppStateNotifier.updateAppState(person.id, AppState.HEALTH_PLAN) } returns Unit

        val event = HealthPlanTaskGroupPublishedEvent(healthPlanTasks)

        val result = consumer.updateAppState(event)
        assertThat(result).isSuccessWithData(Unit)

        coVerifyOnce { AppStateNotifier.updateAppState(person.id, AppState.HEALTH_PLAN) }
        coVerifyOnce { AppStateNotifier.updateAppState(person.id, AppState.UNIFIED_HEALTH) }
        coVerifyOnce { AppStateNotifier.updateAppState(person.id, AppState.HEALTH_ALL_DEMANDS) }
    }

    @Test
    fun `#updateAppState should do nothing if task list are empty`() = runBlocking {
        mockkObject(AppStateNotifier)

        val event = HealthPlanTaskGroupPublishedEvent(emptyList())

        val result = consumer.updateAppState(event)
        assertThat(result).isSuccessWithData(Unit)

        coVerify { AppStateNotifier wasNot called }
    }

    @Test
    fun `#sendPush with multiple tasks should send a notification with expected parameters`() = runBlocking {
        val tasks = listOf(
            TestModelFactory.buildHealthPlanTask(personId = person.id, type = PRESCRIPTION),
            TestModelFactory.buildHealthPlanTask(personId = person.id, type = PRESCRIPTION)
        )

        coEvery {
            pushNotificationService.sendNewTasksPushNotification(
                personId = person.id,
                taskCount = tasks.count(),
            )
        } returns "message-id".success()

        val event = HealthPlanTaskGroupPublishedEvent(tasks)

        val result = consumer.sendPush(event)
        assertThat(result).isSuccessWithData("message-id")

        coVerifyOnce { pushNotificationService.sendNewTasksPushNotification(any(), any()) }
    }

    @Test
    fun `#sendPush should do nothing if task list are empty`() = runBlocking {
        val event = HealthPlanTaskGroupPublishedEvent(emptyList())

        val result = consumer.sendPush(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyNone { pushNotificationService.sendNewTasksPushNotification(any(), any()) }
    }

    @Test
    fun `#sendPush should return success if push service return NotFoundException`() = runBlocking {
        val tasks = listOf(
            TestModelFactory.buildHealthPlanTask(personId = person.id, type = PRESCRIPTION),
            TestModelFactory.buildHealthPlanTask(personId = person.id, type = PRESCRIPTION)
        )

        coEvery {
            pushNotificationService.sendNewTasksPushNotification(
                personId = person.id,
                taskCount = tasks.count(),
            )
        } returns NotFoundException("Result has empty list").failure()

        val event = HealthPlanTaskGroupPublishedEvent(tasks)

        val result = consumer.sendPush(event)

        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { pushNotificationService.sendNewTasksPushNotification(any(), any()) }
    }
}
