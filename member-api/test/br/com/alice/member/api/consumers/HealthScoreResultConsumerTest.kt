package br.com.alice.member.api.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.Step
import br.com.alice.healthlogic.event.ClinicalOutcomeRecordCreatedEvent
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppState.MEMBER_ONBOARDING_V2
import br.com.alice.member.api.services.AppState.REDESIGN_UNIFIED_HEALTH
import br.com.alice.member.api.services.AppState.UNIFIED_HEALTH
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.onboarding.client.MemberOnboardingService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthScoreResultConsumerTest: ConsumerTest() {
    private val memberOnboardingService: MemberOnboardingService = mockk()
    private val consumer = HealthScoreResultConsumer(memberOnboardingService)
    private val scoreMagentaOutcomeConfId = "bec90d96-4d94-4f66-9cb3-5a8956062600".toUUID()

    private val person = TestModelFactory.buildPerson()
    private val healthScoreResultMock = TestModelFactory.buildClinicalOutcomeRecord(
        personId = person.id,
        outcomeConfId = scoreMagentaOutcomeConfId,
    )

    private val genericClinicalOutcomeRecordMock = TestModelFactory.buildClinicalOutcomeRecord(
        personId = person.id,
    )

    private val healthScoreResultEvent = ClinicalOutcomeRecordCreatedEvent(healthScoreResultMock)
    private val genericClinicalOutcomeRecordEvent = ClinicalOutcomeRecordCreatedEvent(genericClinicalOutcomeRecordMock)

    @BeforeTest
    override fun setup() {
        super.setup()

        mockkObject(AppStateNotifier)
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#notifyHealthScoreResult should call AppStateNotifier#updateAppState as expected when is HealthScoreResult`() =
        runBlocking {
            coEvery {
                AppStateNotifier.updateAppState(
                    person.id,
                    AppState.SCORE_MAGENTA_RESULT_EVENT
                )
            } returns Unit

            val result = consumer.notifyHealthScoreResult(healthScoreResultEvent)

            assertThat(result).isSuccessWithData(Unit)

            coVerifyOnce {
                AppStateNotifier.updateAppState(
                    person.id,
                    AppState.SCORE_MAGENTA_RESULT_EVENT
                )
            }
        }

    @Test
    fun `#notifyHealthScoreResult should not call AppStateNotifier#updateAppState as expected when is not HealthScoreResult`() =
        runBlocking {
            val result = consumer.notifyHealthScoreResult(genericClinicalOutcomeRecordEvent)

            assertThat(result).isSuccessWithData(Unit)

            coVerifyNone {
                AppStateNotifier.updateAppState(any(), any())
            }
        }

    @Test
    fun `#finishHealthScoreOnboardingStep should set health score step as completed when flag shouldUseOnboardingV2 is on and is HealthScoreResult`() = runBlocking {
        withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = true) {
            val step = Step(
                templateType = MemberOnboardingStepType.SCORE_MAGENTA,
                status = MemberOnboardingStepStatus.PENDING
            )
            val memberOnboarding = TestModelFactory.buildMemberOnboarding(personId = person.id, steps = listOf(step))
            val memberOnboardingUpdated = memberOnboarding.copy(
                steps = listOf(
                    step.copy(status = MemberOnboardingStepStatus.COMPLETED)
                )
            )

            coEvery { memberOnboardingService.getByPersonIdAndCompleted(person.id, false) } returns memberOnboarding.success()
            coEvery {
                memberOnboardingService.updateStepStatus(
                    memberOnboarding.id,
                    MemberOnboardingStepType.SCORE_MAGENTA
                )
            } returns memberOnboardingUpdated.success()
            coEvery {
                AppStateNotifier.updateAppState(
                    person.id,
                    MEMBER_ONBOARDING_V2, UNIFIED_HEALTH, REDESIGN_UNIFIED_HEALTH
                )
            } returns Unit

            val result = consumer.finishHealthScoreOnboardingStep(healthScoreResultEvent)

            assertThat(result).isSuccessWithData(memberOnboardingUpdated)

            coVerifyOnce { memberOnboardingService.getByPersonIdAndCompleted(any(), any()) }
            coVerifyOnce { memberOnboardingService.updateStepStatus(any(), any()) }
            coVerifyOnce { AppStateNotifier.updateAppState(any(), any(), any(), any()) }
        }
    }

    @Test
    fun `#finishHealthScoreOnboardingStep should return false when flag shouldUseOnboardingV2 is on and is not HealthScoreResult`() = runBlocking {
        withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = true) {
            val result = consumer.finishHealthScoreOnboardingStep(genericClinicalOutcomeRecordEvent)

            assertThat(result).isSuccessWithData(false)

            coVerifyNone { memberOnboardingService.getByPersonIdAndCompleted(any(), any()) }
            coVerifyNone { memberOnboardingService.updateStepStatus(any(), any()) }
            coVerifyNone { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

    @Test
    fun `#finishHealthScoreOnboardingStep should return false when flag shouldUseOnboardingV2 is on and there is not memberOnboarding`() = runBlocking {
        withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = true) {

            coEvery { memberOnboardingService.getByPersonIdAndCompleted(person.id, false) } returns NotFoundException().failure()

            val result = consumer.finishHealthScoreOnboardingStep(healthScoreResultEvent)

            assertThat(result).isSuccessWithData(false)

            coVerifyOnce { memberOnboardingService.getByPersonIdAndCompleted(any(), any()) }
            coVerifyNone { memberOnboardingService.updateStepStatus(any(), any()) }
            coVerifyNone { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

    @Test
    fun `#finishHealthScoreOnboardingStep should return false when flag shouldUseOnboardingV2 is off`() = runBlocking {
        withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = false) {
            val result = consumer.finishHealthScoreOnboardingStep(healthScoreResultEvent)

            assertThat(result).isSuccessWithData(false)

            coVerifyNone { memberOnboardingService.getByPersonIdAndCompleted(any(), any()) }
            coVerifyNone { memberOnboardingService.updateStepStatus(any(), any()) }
            coVerifyNone { AppStateNotifier.updateAppState(any(), any()) }
        }
    }
}
