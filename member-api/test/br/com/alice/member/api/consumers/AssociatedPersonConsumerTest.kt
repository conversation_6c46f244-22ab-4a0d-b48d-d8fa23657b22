package br.com.alice.member.api.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.sortinghat.event.PersonTeamAssociationEvent
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class AssociatedPersonConsumerTest: ConsumerTest()  {
    val consumer = AssociatedPersonConsumer()
    val person = TestModelFactory.buildPerson()
    val healthCareTeam = TestModelFactory.buildHealthcareTeam()
    val additionalTeamId = TestModelFactory.buildHealthcareTeam()
    val risk = TestModelFactory.buildRisk()

    @Test
    fun `#updateAppState should call AppStateNotifier with expected app state`() = runBlocking {
        mockkObject(AppStateNotifier)

        coEvery { AppStateNotifier.updateAppState(person.id, AppState.MEMBER_ONBOARDING) } returns Unit

        val event = PersonTeamAssociationEvent(
            personId = person.id,
            healthcareTeamId = healthCareTeam.id,
            additionalTeamId = additionalTeamId.id,
            risk = risk.riskDescription,
        )

        val result = consumer.updateAppState(event)
        ResultAssert.assertThat(result).isSuccessWithData(Unit)

        coVerify(exactly = 1) { AppStateNotifier.updateAppState(person.id, AppState.MEMBER_ONBOARDING) }
    }
}
