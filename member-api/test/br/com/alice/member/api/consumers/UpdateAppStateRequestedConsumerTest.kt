package br.com.alice.member.api.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.membership.model.events.UpdateAppStateRequestedEvent
import br.com.alice.membership.model.events.UpdateAppStateRequestedPayload
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class UpdateAppStateRequestedConsumerTest {

    private val consumer = UpdateAppStateRequestedConsumer()

    private val person = TestModelFactory.buildPerson()
    private val updateAppStateRequestedPayload = UpdateAppStateRequestedPayload(
        personId = person.id,
        appState = AppState.HEALTH_PLAN.name
    )

    @Test
    fun `#updateAppState should call AppStateNotifier with expected parameters`() = runBlocking {
        mockkObject(AppStateNotifier) {
            coEvery {
                AppStateNotifier.updateAppState(
                    personId = updateAppStateRequestedPayload.personId,
                    AppState.HEALTH_PLAN
                )
            } returns Unit

            val event = UpdateAppStateRequestedEvent(updateAppStateRequestedPayload)

            val result = consumer.updateAppState(event)
            assertThat(result).isSuccessWithData(Unit)

            coVerify(exactly = 1) { AppStateNotifier.updateAppState(any(), any()) }
        }
    }

}
