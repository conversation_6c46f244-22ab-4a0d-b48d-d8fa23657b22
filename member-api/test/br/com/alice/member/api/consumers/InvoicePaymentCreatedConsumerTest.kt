package br.com.alice.member.api.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class InvoicePaymentCreatedConsumerTest: ConsumerTest()  {
    val consumer = InvoicePaymentCreatedConsumer()
    val person = TestModelFactory.buildPerson()
    val member = TestModelFactory.buildMember(personId = person.id)
    val invoicePayment = TestModelFactory.buildInvoicePayment()

    @Test
    fun `#updateAppState should call AppStateNotifier with expected app state`() = runBlocking {
        mockkObject(AppStateNotifier)

        coEvery { AppStateNotifier.updateAppState(person.id, AppState.FIRST_PAYMENT_CREATED) } returns Unit

        val event = InvoicePaymentCreatedEvent(
            member = member,
            invoicePayment = invoicePayment,
            paymentReason = PaymentReason.FIRST_PAYMENT,
        )

        val result = consumer.updateAppState(event)
        ResultAssert.assertThat(result).isSuccess()

        coVerify(exactly = 1) { AppStateNotifier.updateAppState(person.id, AppState.FIRST_PAYMENT_CREATED) }
    }

    @Test
    fun `#updateAppState should not call AppStateNotifier when the paymentReason is not FIRST_PAYMENT`() = runBlocking {
        mockkObject(AppStateNotifier)

        val event = InvoicePaymentCreatedEvent(
            member = member,
            invoicePayment = invoicePayment,
            paymentReason = PaymentReason.OVERDUE_PAYMENT,
        )

        val result = consumer.updateAppState(event)
        ResultAssert.assertThat(result).isSuccess()

        coVerify(exactly = 0) { AppStateNotifier.updateAppState(person.id, AppState.FIRST_PAYMENT_CREATED) }
    }

    @Test
    fun `#updateAppState should not call AppStateNotifier when there is not personId`() = runBlocking {
        mockkObject(AppStateNotifier)

        val event = InvoicePaymentCreatedEvent(
            member = null,
            invoicePayment = invoicePayment,
            paymentReason = PaymentReason.OVERDUE_PAYMENT,
        )

        val result = consumer.updateAppState(event)
        ResultAssert.assertThat(result).isSuccess()

        coVerify(exactly = 0) { AppStateNotifier.updateAppState(person.id, AppState.FIRST_PAYMENT_CREATED) }
    }
}
