package br.com.alice.member.api.consumers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.notifier.ActionPlanTasksAcknowledgedEvent
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class ActionPlanTasksAcknowledgedConsumerTest: ConsumerTest() {
    private val actionPlanTaskService: ActionPlanTaskService = mockk()
    private val consumer = ActionPlanTasksAcknowledgedConsumer(actionPlanTaskService)

    @Test
    fun `#updateAppState should call AppStateNotifier#updateAppState as expected`() = runBlocking {
        val now = LocalDateTime.now()
        val task = TestModelFactory.buildActionPlanTask(acknowledgedAt = now)
        val event = ActionPlanTasksAcknowledgedEvent(listOf(task), now)

        mockkObject(AppStateNotifier) {
            mockkStatic(LocalDateTime::class)
            every { LocalDateTime.now() } returns now
            every { AppStateNotifier.updateAppState(event.payload.tasks[0].personId, AppState.HEALTH_ALL_DEMANDS) } returns Unit
            every { AppStateNotifier.updateAppState(event.payload.tasks[0].personId, AppState.UNIFIED_HEALTH) } returns Unit
            every { AppStateNotifier.updateAppState(event.payload.tasks[0].personId, AppState.REDESIGN_UNIFIED_HEALTH) } returns Unit

            coEvery {
                actionPlanTaskService.getUnacknowlegedTasksByDate(event.payload.tasks[0].personId, now.atBeginningOfTheDay())
            } returns emptyList<ActionPlanTask>().success()

            consumer.updateAppState(event)

            coVerifyOnce { AppStateNotifier.updateAppState(event.payload.tasks[0].personId, AppState.HEALTH_ALL_DEMANDS) }
            coVerifyOnce { AppStateNotifier.updateAppState(event.payload.tasks[0].personId, AppState.UNIFIED_HEALTH) }
            coVerifyOnce { AppStateNotifier.updateAppState(event.payload.tasks[0].personId, AppState.REDESIGN_UNIFIED_HEALTH) }
        }
    }

    @Test
    fun `#updateAppState should call AppStateNotifier#updateAppState as expected and not updated anything`() = runBlocking {
        val event = ActionPlanTasksAcknowledgedEvent(emptyList(), LocalDateTime.now())

        mockkObject(AppStateNotifier) {

            consumer.updateAppState(event)

            coVerifyNone { AppStateNotifier.updateAppState(any(), AppState.HEALTH_ALL_DEMANDS) }
            coVerifyNone { AppStateNotifier.updateAppState(any(), AppState.UNIFIED_HEALTH) }
            coVerifyNone { AppStateNotifier.updateAppState(any(), AppState.REDESIGN_UNIFIED_HEALTH) }
        }
    }
}
