package br.com.alice.member.api.consumers

import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.moneyin.event.PaymentDetailCreatedEvent
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class PaymentDetailCreatedConsumerTest: ConsumerTest()  {
    val consumer = PaymentDetailCreatedConsumer()
    val person = TestModelFactory.buildPerson()
    val member = TestModelFactory.buildMember(personId = person.id)

    @Test
    fun `#updateAppState should call AppStateNotifier with expected app state`() = runBlocking {
        mockkObject(AppStateNotifier)

        coEvery { AppStateNotifier.updateAppState(person.id, AppState.PAYMENT_UPDATED) } returns Unit

        val event = PaymentDetailCreatedEvent(
            personId = person.id
        )

        val result = consumer.updateAppState(event)
        ResultAssert.assertThat(result).isSuccess()

        coVerify(exactly = 1) { AppStateNotifier.updateAppState(person.id, AppState.PAYMENT_UPDATED) }
    }

    @Test
    fun `#updateAppState should not call AppStateNotifier when there is not personId`() = runBlocking {
        mockkObject(AppStateNotifier)

        val event = PaymentDetailCreatedEvent(
            personId = null
        )

        val result = consumer.updateAppState(event)
        ResultAssert.assertThat(result).isSuccess()

        coVerify(exactly = 0) { AppStateNotifier.updateAppState(person.id, AppState.PAYMENT_UPDATED) }
    }
}
