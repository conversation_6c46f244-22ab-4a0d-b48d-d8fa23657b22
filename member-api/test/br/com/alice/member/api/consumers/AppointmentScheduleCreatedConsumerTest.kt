package br.com.alice.member.api.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.member.api.services.AppState.APPOINTMENT_HUB
import br.com.alice.member.api.services.AppState.APPOINTMENT_SCHEDULE
import br.com.alice.member.api.services.AppState.HEALTH_PLAN
import br.com.alice.member.api.services.AppState.HOME
import br.com.alice.member.api.services.AppState.REDESIGN_UNIFIED_HEALTH
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import io.mockk.coEvery
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class AppointmentScheduleCreatedConsumerTest {

    private val person = TestModelFactory.buildPerson()
    private val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
        personId = person.id,
        status = AppointmentScheduleStatus.SCHEDULED,
        taskId = RangeUUID.generate()
    )

    private val consumer = AppointmentScheduleCreatedConsumer()

    @Test
    fun `#updateAppState should call AppStateNotifier with expected parameters`() = runBlocking {
        mockkObject(AppStateNotifier)
        coEvery {
            AppStateNotifier.updateAppState(
                person.id,
                HOME,
                APPOINTMENT_SCHEDULE,
                REDESIGN_UNIFIED_HEALTH,
                APPOINTMENT_HUB,
                HEALTH_PLAN
            )
        } returns Unit


        val event = AppointmentScheduleCreatedEvent(person, appointmentSchedule)

        val result = consumer.updateAppState(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce {
            AppStateNotifier.updateAppState(
                person.id,
                HOME,
                APPOINTMENT_SCHEDULE,
                REDESIGN_UNIFIED_HEALTH,
                APPOINTMENT_HUB,
                HEALTH_PLAN
            )
        }
    }
}
