package br.com.alice.member.api.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.member.api.services.AppState.APPOINTMENT_HUB
import br.com.alice.member.api.services.AppState.APPOINTMENT_SCHEDULE
import br.com.alice.member.api.services.AppState.HEALTH_PLAN
import br.com.alice.member.api.services.AppState.HOME
import br.com.alice.member.api.services.AppState.REDESIGN_UNIFIED_HEALTH
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import io.mockk.coEvery
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test

class AppointmentScheduleCancelledConsumerTest {

    private val person = TestModelFactory.buildPerson()

    private val consumer = AppointmentScheduleCancelledConsumer()

    @Test
    fun `#updateAppState should call AppStateNotifier with expected parameters`() = runBlocking {
        mockkObject(AppStateNotifier) {
            coEvery {
                AppStateNotifier.updateAppState(
                    person.id,
                    HOME,
                    APPOINTMENT_SCHEDULE,
                    REDESIGN_UNIFIED_HEALTH,
                    APPOINTMENT_HUB,
                    HEALTH_PLAN
                )
            } returns Unit

            val appointmentSchedule = AppointmentSchedule(
                id = RangeUUID.generate(),
                personId = person.id,
                eventId = RangeUUID.generate().toString(),
                eventName = "Conversa com Enfermeira(o) Alice",
                healthcareTeamId = null,
                location = "https://meeting.app.com.br/**********",
                startTime = LocalDateTime.now().plusDays(1),
                status = AppointmentScheduleStatus.CANCELED,
                version = 1,
                createdAt = LocalDateTime.now(),
                type = AppointmentScheduleType.HEALTH_DECLARATION,
                healthPlanTaskId = RangeUUID.generate(),
                eventUuid = null
            )

            val event = AppointmentScheduleCancelledEvent(appointmentSchedule)

            val result = consumer.updateAppState(event)
            assertThat(result).isSuccessWithData(Unit)

            coVerifyOnce {
                AppStateNotifier.updateAppState(
                    person.id,
                    HOME,
                    APPOINTMENT_SCHEDULE,
                    REDESIGN_UNIFIED_HEALTH,
                    APPOINTMENT_HUB,
                    HEALTH_PLAN
                )
            }

        }
    }
}
