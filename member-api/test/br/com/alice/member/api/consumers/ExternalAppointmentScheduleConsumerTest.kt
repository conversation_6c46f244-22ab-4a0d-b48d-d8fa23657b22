package br.com.alice.member.api.consumers

import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ScheduleStatus
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.schedule.model.events.ExternalAppointmentScheduleUpsertEvent
import io.mockk.every
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class ExternalAppointmentScheduleConsumerTest {
    val consumer = ExternalAppointmentScheduleConsumer()

    @Test
    fun `#updateAppState should update appState`() = runBlocking {
        val schedule = TestModelFactory.buildExternalAppointmentSchedule(status = ScheduleStatus.SCHEDULED)
        val event = ExternalAppointmentScheduleUpsertEvent(schedule)

        mockkObject(AppStateNotifier) {
            every { AppStateNotifier.updateAppState(schedule.personId, AppState.DUQUESA_HOME) } returns Unit

            consumer.updateAppState(event)

            coVerifyOnce {
                AppStateNotifier.updateAppState(schedule.personId, AppState.DUQUESA_HOME)
            }
        }
    }
}
