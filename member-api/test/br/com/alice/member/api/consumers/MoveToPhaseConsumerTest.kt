package br.com.alice.member.api.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class MoveToPhaseConsumerTest: ConsumerTest() {
   private val memberOnboardingService: MemberOnboardingService = mockk()
   private val beneficiaryService: BeneficiaryService = mockk()
   private val ongoingCompanyDealService: OngoingCompanyDealService = mockk()

   private val consumer = MoveToPhaseConsumer(
      memberOnboardingService,
      beneficiaryService,
      ongoingCompanyDealService
   )

   private val beneficiary = TestModelFactory.buildBeneficiary()
   private val event = MoveToPhaseEvent(
      beneficiaryId = beneficiary.id,
      requiredPhaseType = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
   )
   private val ongoingCompanyDeal = TestModelFactory.buildOngoingCompanyDeal(
      companyId = beneficiary.companyId,
      channel = DealChannel.INTERNAL
   )

   @BeforeTest
   override fun setup() {
      super.setup()
   }

   @Test
   fun `#createOnboardingWhenWaitingForReview should create onboarding when flag is on and phase is WAITING_FOR_REVIEW`() = runBlocking {
      withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = true) {
         coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary.success()
         coEvery {
            memberOnboardingService.createMemberOnboardingIfNecessary(personId = beneficiary.personId)
         } returns true.success()
         coEvery { ongoingCompanyDealService.getByCompanyId(beneficiary.companyId) } returns listOf(ongoingCompanyDeal).success()

         val result = consumer.createOnboardingWhenWaitingForReview(event)

         assertThat(result).isSuccessWithData(true)

         coVerifyOnce { beneficiaryService.get(any()) }
         coVerifyOnce { memberOnboardingService.createMemberOnboardingIfNecessary(any()) }
         coVerifyOnce { ongoingCompanyDealService.getByCompanyId(any()) }
      }
   }

   @Test
   fun `#createOnboardingWhenWaitingForReview should return false if beneficiary not exists`() = runBlocking {
      withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = true) {

         coEvery { beneficiaryService.get(beneficiary.id) } returns NotFoundException("").failure()

         val result = consumer.createOnboardingWhenWaitingForReview(event)

         assertThat(result).isSuccessWithData(false)

         coVerifyOnce { beneficiaryService.get(any()) }

         coVerifyNone { memberOnboardingService.createMemberOnboardingIfNecessary(any()) }
         coVerify { ongoingCompanyDealService wasNot called }
      }
   }

   @Test
   fun `#createOnboardingWhenWaitingForReview should return false if createMemberOnboarding return false`() = runBlocking {
      withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = true) {

         coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary.success()
         coEvery { memberOnboardingService.createMemberOnboardingIfNecessary(personId = beneficiary.personId) } returns false.success()
         coEvery { ongoingCompanyDealService.getByCompanyId(beneficiary.companyId) } returns listOf(ongoingCompanyDeal).success()

         val result = consumer.createOnboardingWhenWaitingForReview(event)

         assertThat(result).isSuccessWithData(false)

         coVerifyOnce { beneficiaryService.get(any()) }
         coVerifyOnce { memberOnboardingService.createMemberOnboardingIfNecessary(any()) }
         coVerifyOnce { ongoingCompanyDealService.getByCompanyId(any()) }
      }
   }

   @Test
   fun `#createOnboardingWhenWaitingForReview should return false when phase is not WAITING_FOR_REVIEW`() = runBlocking {
      withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = true) {
         val event = MoveToPhaseEvent(
            beneficiaryId = beneficiary.id,
            requiredPhaseType = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE
         )

         val result = consumer.createOnboardingWhenWaitingForReview(event)

         assertThat(result).isSuccessWithData(false)

         coVerifyNone { beneficiaryService.get(any()) }
         coVerifyNone { memberOnboardingService.createMemberOnboardingIfNecessary(any()) }
         coVerify { ongoingCompanyDealService wasNot called }
      }
   }

   @Test
   fun `#createOnboardingWhenWaitingForReview should return false when flag is off`() = runBlocking {
      withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = false) {
         val result = consumer.createOnboardingWhenWaitingForReview(event)

         assertThat(result).isSuccessWithData(false)

         coVerifyNone { beneficiaryService.get(any()) }
         coVerifyNone { memberOnboardingService.createMemberOnboardingIfNecessary(any()) }
         coVerify { ongoingCompanyDealService wasNot called }
      }
   }

   @Test
   fun `#createOnboardingWhenWaitingForReview should return false when member is from broker`() = runBlocking {
      withFeatureFlag(namespace = FeatureNamespace.MEMBERSHIP, key = "should-use-onboarding-v2", value = true) {
         val ongoingCompanyDeal = ongoingCompanyDeal.copy(channel = DealChannel.BROKER)

         coEvery { beneficiaryService.get(beneficiary.id) } returns beneficiary.success()
         coEvery { ongoingCompanyDealService.getByCompanyId(beneficiary.companyId) } returns listOf(ongoingCompanyDeal).success()

         val result = consumer.createOnboardingWhenWaitingForReview(event)

         assertThat(result).isSuccessWithData(false)

         coVerifyOnce { beneficiaryService.get(any()) }
         coVerifyOnce { ongoingCompanyDealService.getByCompanyId(any()) }
         coVerify { memberOnboardingService wasNot called }
      }
   }

   @Test
   fun `#createOnboardingWhenWaitingForReview should return false when should use new consumer`() = runBlocking {
      withFeatureFlags(
         namespace = FeatureNamespace.MEMBERSHIP,
         mapOf("should-use-new-onboarding-consumer" to true, "should-use-onboarding-v2" to true)
      ) {

         val result = consumer.createOnboardingWhenWaitingForReview(event)

         assertThat(result).isSuccessWithData(false)

         coVerify {
            beneficiaryService wasNot called
            ongoingCompanyDealService wasNot called
            memberOnboardingService wasNot called
         }
      }
   }
}
