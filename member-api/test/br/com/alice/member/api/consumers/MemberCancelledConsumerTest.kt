package br.com.alice.member.api.consumers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.MemberCancelledEvent
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class MemberCancelledConsumerTest: ConsumerTest() {
    private val personService: PersonService = mockk()
    private val consumer = MemberCancelledConsumer(personService)

    private val member = TestModelFactory.buildMember()
    private val event = MemberCancelledEvent(member)

    @Test
    fun `#updateAppState should call AppStateNotifier#updateAppState as expected`() = runBlocking {
        coEvery { personService.terminateSession(event.payload.member.personId) } returns true.success()

        val result = consumer.expireToken(event)
        assertThat(result).isSuccessWithData(true)

        coVerify(exactly = 1) { personService.terminateSession(event.payload.member.personId) }
    }

    @Test
    fun `#updateAppState should call AppStateNotifier#updateAppState as expected and returns false when not found`() = runBlocking {
        coEvery { personService.terminateSession(event.payload.member.personId) } returns NotFoundException().failure()

        val result = consumer.expireToken(event)
        assertThat(result).isSuccessWithData(false)

        coVerify(exactly = 1) { personService.terminateSession(event.payload.member.personId) }
    }
}
