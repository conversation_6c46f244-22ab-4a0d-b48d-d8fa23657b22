package br.com.alice.member.api.webhooks

import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.webhooks.idwall.ReportWebhookData
import br.com.alice.member.api.models.webhooks.idwall.ReportWebhookPayload
import br.com.alice.member.api.models.webhooks.idwall.ReportingStatus
import br.com.alice.onboarding.client.BackgroundCheckReporter
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class IdwallWebhooksReceiverTest : RoutesTestHelper() {

    private val backgroundCheckReporter: BackgroundCheckReporter = mockk()
    private val idwallWebhooksReceiver = IdwallWebhooksReceiver(backgroundCheckReporter)

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { idwallWebhooksReceiver }
    }

    @Test
    fun `#receive return error when body is empty`() {
        val body = ReportWebhookPayload(
            tipo = "tipo",
            dados = ReportWebhookData(
                protocolo = "protocolo",
                status = ReportingStatus.PENDENTE,
            )
        )

        coEvery { backgroundCheckReporter.generateReport("protocolo") } returns mockk()

        internalAuthentication {
            post( "/webhooks/idwall/report_status", body = body) {
                ResponseAssert.assertThat(it).isOK()
            }
        }
    }
}
