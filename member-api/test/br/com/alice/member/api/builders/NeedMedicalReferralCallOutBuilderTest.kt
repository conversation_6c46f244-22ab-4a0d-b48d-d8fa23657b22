package br.com.alice.member.api.builders

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.CalloutAction
import br.com.alice.app.content.model.CalloutType
import br.com.alice.app.content.model.CalloutVariant
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.member.api.models.ContactCallOutResponse
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class NeedMedicalReferralCallOutBuilderTest {
    @Test
    fun `#buildCalloutSection - should build CalloutSection with main values`() = runBlocking<Unit> {
        val expected = CalloutSection(
            title = "Quer agendar um atendimento?",
            calloutBody = "Para agendar com essa especialidade você precisa de indicação médica.",
            calloutVariant = CalloutVariant.INFORMATION,
            calloutAction = CalloutAction(
                type = CalloutType.NAVIGATION,
                label = "Falar com Alice Agora",
                onClickAction = RemoteAction(mobileRoute = ActionRouting.ALICE_AGORA)
            )
        )
        val callout = NeedMedicalReferralCallOutBuilder.buildCalloutSection(
            type = NeedMedicalReferralCallOutBuilder.ScreenType.SPECIALIST
        )

        assertThat(callout).isEqualTo(expected)
    }

    @Test
    fun `#buildCalloutSection - should build CalloutSection with details values`() = runBlocking<Unit> {
        val expected = CalloutSection(
            title = "É necessário encaminhamento",
            calloutBody = "Para agendar um atendimento você precisa de indicação médica.",
            calloutVariant = CalloutVariant.INFORMATION,
            calloutAction = CalloutAction(
                type = CalloutType.NAVIGATION,
                label = "Falar com Alice Agora",
                onClickAction = RemoteAction(mobileRoute = ActionRouting.ALICE_AGORA)
            )
        )
        val callout = NeedMedicalReferralCallOutBuilder.buildCalloutSection(
            type = NeedMedicalReferralCallOutBuilder.ScreenType.PROVIDER
        )

        assertThat(callout).isEqualTo(expected)
    }

    @Test
    fun `#buildContactCallOut - should build ContactCallOutResponse with main values`() = runBlocking<Unit> {
        val expected = ContactCallOutResponse(
            title = "Quer agendar um atendimento?",
            body = "Para agendar com essa especialidade você precisa de indicação médica.",
            variant = ContactCallOutResponse.Variant.INFORMATION,
            action = ContactCallOutResponse.Action(
                type = CalloutType.NAVIGATION,
                label = "Falar com Alice Agora",
                onClickAction = RemoteAction(mobileRoute = ActionRouting.ALICE_AGORA)
            )
        )
        val callout = NeedMedicalReferralCallOutBuilder.buildContactCallOut(
            type = NeedMedicalReferralCallOutBuilder.ScreenType.SPECIALIST
        )

        assertThat(callout).isEqualTo(expected)
    }

    @Test
    fun `#buildContactCallOut - should build ContactCallOutResponse with details values`() = runBlocking<Unit> {
        val expected = ContactCallOutResponse(
            title = "É necessário encaminhamento",
            body = "Para agendar um atendimento você precisa de indicação médica.",
            variant = ContactCallOutResponse.Variant.INFORMATION,
            action = ContactCallOutResponse.Action(
                type = CalloutType.NAVIGATION,
                label = "Falar com Alice Agora",
                onClickAction = RemoteAction(mobileRoute = ActionRouting.ALICE_AGORA)
            )
        )
        val callout = NeedMedicalReferralCallOutBuilder.buildContactCallOut(
            type = NeedMedicalReferralCallOutBuilder.ScreenType.PROVIDER
        )

        assertThat(callout).isEqualTo(expected)
    }
}
