package br.com.alice.member.api.builders

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.HealthFormAnswerSourceType
import kotlin.test.Test
import kotlin.test.assertEquals

class DeepLinkBuilderTest {
    @Test
    fun `#buildLinkForQuestionnaireFromTask should return a deep link for a questionnaire task`() {
        val questionnaireKey = "Q_KEY"
        val healthFormAnswerSource = HealthFormAnswerSource(
            id = RangeUUID.generate().toString(),
            type = HealthFormAnswerSourceType.OUTCOME_REQUEST_SCHEDULING,
        )
        val task = TestModelFactory.buildActionPlanTask(
            type = ActionPlanTaskType.QUESTIONNAIRE,
            content = mapOf(
                "questionnaireKey" to questionnaire<PERSON><PERSON>,
                "healthFormAnswerSource" to mapOf(
                    "id" to healthFormAnswerSource.id,
                    "type" to healthFormAnswerSource.type.name,
                )
            )
        )

        val deepLink = DeepLinkBuilder.buildLinkForQuestionnaireFromTask(task.specialize())

        assertEquals(
            "https://alice-saude.webflow.io/app/questionnaire?type=${questionnaireKey}&source_type=${healthFormAnswerSource.type}&source_id=${healthFormAnswerSource.id}",
            deepLink
        )
    }
}
