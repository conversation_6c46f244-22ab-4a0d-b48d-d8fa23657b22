package br.com.alice.member.api.builders

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.core.extensions.toUrlEncoded
import br.com.alice.common.models.Gender
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.Staff
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentAction
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentCard
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentResponse
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentSchedule
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentStaff
import br.com.alice.member.api.models.onboarding.OnboardingPhaseDescription
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthDeclarationAppointmentBuilderTest {

    @BeforeTest
    fun setup() {
        mockkObject(OnboardingPhaseDescriptionBuilder)
        every { OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(any()) } returns OnboardingPhaseDescription(
            title = "Fase do onboarding",
            subtitle = "Subtítulo da fase do onboarding"
        )
    }

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#buildHealthDeclarationAppointmentResponse should return Response with schedule when Appointment with a Staff is valid`() =
        runBlocking<Unit> {
            val scheduleUrl = "https://calendar.alice.com.br/time-alice/fale-com-alice"

            val person = TestModelFactory.buildPerson()
            val personCalendly = TestModelFactory.buildPersonCalendly(person.id)
            val utmTerm = gson.toJson(mapOf("person_calendly_id" to personCalendly.id)).toUrlEncoded()
            val utmParam = "&utm_term=$utmTerm"

            val appointmentSchedule = AppointmentSchedule(
                personId = person.id,
                eventId = "EVENT_ID",
                eventName = "Primeira consulta com seu time de saúde",
                location = "Rua Rebouças, 3506",
                healthcareTeamId = TestModelFactory.buildHealthcareTeam().id,
                startTime = LocalDateTime.of(2020, 3, 3, 3, 3),
                status = AppointmentScheduleStatus.SCHEDULED,
                type = AppointmentScheduleType.HEALTH_DECLARATION,
                eventUuid = null,
            )

            val staff = Staff(
                email = "<EMAIL>",
                firstName = "Péricles",
                lastName = "Farias",
                gender = Gender.MALE,
                role = Role.DIGITAL_CARE_NURSE,
                profileImageUrl = "https://alice.com.br/foto_do_periclao.jpg",
                type = StaffType.PITAYA
            )

            val appointments = listOf(
                AppointmentScheduleWithStaff(
                    appointmentSchedule = appointmentSchedule,
                    staff = staff
                )
            )

            val nextPhases = listOf(
                OnboardingPhase.WAITING_FOR_REVIEW,
                OnboardingPhase.CONTRACT,
                OnboardingPhase.PAYMENT,
                OnboardingPhase.FINISHED
            )

            val cancelUrl =
                "https://calendly.com/cancellations/EVENT_ID?text_color=000000&primary_color=e01f80&health_plan_task_id=74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b&staff_id=null&event_name=Primeira+consulta+com+seu+time+de+sa%C3%BAde&appointment_schedule_id=${appointmentSchedule.id}$utmParam"
            val rescheduleUrl =
                "https://webview.alice.com.br/schedule-first-appointment/?schedule_url=https://calendly.com/reschedulings/EVENT_ID?text_color=000000&primary_color=e01f80$utmParam"

            val expectedHealthDeclarationAppointment = HealthDeclarationAppointmentResponse(
                card = HealthDeclarationAppointmentCard(
                    title = "Sua video call está marcada!",
                    schedule = HealthDeclarationAppointmentSchedule(
                        startTime = LocalDateTime.of(2020, 3, 3, 3, 3).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                        location = "Rua Rebouças, 3506",
                        cancelUrl = cancelUrl,
                        rescheduleUrl = rescheduleUrl,
                        staff = HealthDeclarationAppointmentStaff(
                            id = staff.id,
                            description = "Enfermeiro(a) do seu time de saúde - Atendimento Imediato",
                            firstName = "Péricles",
                            profileImageUrl = "https://alice.com.br/foto_do_periclao.jpg"
                        )
                    )
                ),
                nextPhases = listOf(
                    OnboardingPhaseDescription(
                        title = "Fase do onboarding",
                        subtitle = "Subtítulo da fase do onboarding"
                    ),
                    OnboardingPhaseDescription(
                        title = "Fase do onboarding",
                        subtitle = "Subtítulo da fase do onboarding"
                    ),
                    OnboardingPhaseDescription(
                        title = "Fase do onboarding",
                        subtitle = "Subtítulo da fase do onboarding"
                    ),
                    OnboardingPhaseDescription(
                        title = "Fase do onboarding",
                        subtitle = "Subtítulo da fase do onboarding"
                    )
                )
            )

            val actualHealthDeclarationAppointment =
                HealthDeclarationAppointmentBuilder.buildHealthDeclarationAppointmentResponse(
                    appointments,
                    nextPhases,
                    scheduleUrl,
                    personCalendly
                )

            assertThat(actualHealthDeclarationAppointment).isEqualTo(expectedHealthDeclarationAppointment)
        }

    @Test
    fun `#buildHealthDeclarationAppointmentResponse should return Response with action when an appointment is null`() =
        runBlocking<Unit> {
            val scheduleUrl = "https://calendar.alice.com.br/time-alice/fale-com-alice"
            val appointments = emptyList<AppointmentScheduleWithStaff>()

            val nextPhases = listOf(
                OnboardingPhase.WAITING_FOR_REVIEW,
                OnboardingPhase.CONTRACT,
                OnboardingPhase.PAYMENT,
                OnboardingPhase.FINISHED
            )
            val personCalendly = TestModelFactory.buildPersonCalendly(PersonId())


            val expectedHealthDeclarationAppointment = HealthDeclarationAppointmentResponse(
                card = HealthDeclarationAppointmentCard(
                    title = "Videocall com enfermeiras(os)",
                    action = HealthDeclarationAppointmentAction(
                        link = Link(
                            href = scheduleUrl
                        )
                    )
                ),
                nextPhases = listOf(
                    OnboardingPhaseDescription(
                        title = "Fase do onboarding",
                        subtitle = "Subtítulo da fase do onboarding"
                    ),
                    OnboardingPhaseDescription(
                        title = "Fase do onboarding",
                        subtitle = "Subtítulo da fase do onboarding"
                    ),
                    OnboardingPhaseDescription(
                        title = "Fase do onboarding",
                        subtitle = "Subtítulo da fase do onboarding"
                    ),
                    OnboardingPhaseDescription(
                        title = "Fase do onboarding",
                        subtitle = "Subtítulo da fase do onboarding"
                    )
                )
            )

            val actualHealthDeclarationAppointment =
                HealthDeclarationAppointmentBuilder.buildHealthDeclarationAppointmentResponse(
                    appointments,
                    nextPhases,
                    scheduleUrl,
                    personCalendly
                )

            assertThat(actualHealthDeclarationAppointment).isEqualTo(expectedHealthDeclarationAppointment)
        }
}
