package br.com.alice.member.api.builders

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.builders.PersonMissingDataNavigationBuilder
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.PersonData
import br.com.alice.member.api.models.PersonMissingData
import br.com.alice.member.api.models.PersonMissingDataNavigationResponse
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test


class PersonMissingDataNavigationBuilderTest {

    @Test
    fun `#PersonMissedDataNavigationBuilder`() {
        val person = TestModelFactory.buildPerson(
            dateOfBirth = null,
            phoneNumber = "***********",
            addresses = emptyList()
        )

        val expected = PersonMissingDataNavigationResponse(
            mobileRoute = MobileRouting.DATA_REGISTRATION,
            properties = PersonMissingData(
                missingData = listOf("date_of_birth", "postal_code"),
                person = PersonData(
                    firstName = person.firstName,
                    lastName = person.lastName,
                    nickName = person.nickName,
                    email = person.email,
                    phone = person.phoneNumber,
                    dateOfBirth = null,
                    nationalId = person.nationalId,
                    postalCode = null
                )
            )
        )

        val result = PersonMissingDataNavigationBuilder.buildNavigation(MobileRouting.GAS_INIT, person)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#PersonMissedDataNavigationBuilder with portability steps`() {
        val person = TestModelFactory.buildPerson(
            dateOfBirth = null,
            phoneNumber = "***********",
            addresses = emptyList()
        )

        val expected = PersonMissingDataNavigationResponse(
            mobileRoute = MobileRouting.DATA_REGISTRATION,
            properties = PersonMissingData(
                missingData = listOf("date_of_birth", "postal_code"),
                person = PersonData(
                    firstName = person.firstName,
                    lastName = person.lastName,
                    nickName = person.nickName,
                    email = person.email,
                    phone = person.phoneNumber,
                    dateOfBirth = null,
                    nationalId = person.nationalId,
                    postalCode = null
                )
            )
        )

        val result = PersonMissingDataNavigationBuilder.buildNavigation(MobileRouting.PORTABILITY_STEPS, person)

        assertThat(result).isEqualTo(expected)
    }
}
