package br.com.alice.member.api.builders

import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.member.api.builders.OnboardingPhaseDescriptionBuilder
import br.com.alice.member.api.models.onboarding.OnboardingPhaseDescription
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class OnboardingPhaseDescriptionBuilderTest {

    @Test
    fun `#buildOnboardingPhaseDescription - given a SHOPPING phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("Escolha seu plano")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.SHOPPING)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }

    @Test
    fun `#buildOnboardingPhaseDescription - given a PORTABILITY phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("Escolher sobre portabilidade")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.PORTABILITY)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }

    @Test
    fun `#buildOnboardingPhaseDescription - given a PORTABILITY_REVIEW phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("Aguardando análise de portabilidade")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.PORTABILITY_REVIEW)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }

    @Test
    fun `#buildOnboardingPhaseDescription - given a REGISTRATION phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("Preencha seus dados")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.REGISTRATION)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }

    @Test
    fun `#buildOnboardingPhaseDescription - given a HEALTH_DECLARATION_APPOINTMENT phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("Video call com enfermeiras(os)")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }

    @Test
    fun `#buildOnboardingPhaseDescription - given a WAITING_FOR_REVIEW phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("Análise das informações", "Entraremos em contato se precisarmos de mais alguma informação")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.WAITING_FOR_REVIEW)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }

    @Test
    fun `#buildOnboardingPhaseDescription - given a CONTRACT phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("Assinatura do contrato", "Tudo feito pelo app {nada de papelada}")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.CONTRACT)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }

    @Test
    fun `#buildOnboardingPhaseDescription - given a PAYMENT phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("Pagamento", "Disponível imediatamente após assinatura do contrato")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.PAYMENT)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }

    @Test
    fun `#buildOnboardingPhaseDescription - given a FINISHED phase, should return an expected description`() = runBlocking<Unit> {
        val expectedOnboardingPhaseDescription = OnboardingPhaseDescription("App Liberado", "Disponível após confirmação do pagamento")
        val actualOnboardingPhaseDescription = OnboardingPhaseDescriptionBuilder.buildOnboardingPhaseDescription(OnboardingPhase.FINISHED)

        assertThat(actualOnboardingPhaseDescription).isEqualTo(expectedOnboardingPhaseDescription)
    }
}
