package br.com.alice.member.api.builders

import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.reminder.ReminderActionResponse
import br.com.alice.member.api.models.reminder.ReminderImage
import br.com.alice.member.api.models.reminder.ReminderImageType
import br.com.alice.member.api.models.reminder.ReminderResponse
import br.com.alice.member.api.helpers.I18nHelper.i18n
import io.mockk.clearAllMocks
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.Test

class ReminderResponseBuilderTest {

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#buildHealthDocumentsReminder should return a health documents upload reminder as expected`() = runBlocking<Unit> {
        val reminder = ReminderResponseBuilder.buildHealthDocumentsReminder(i18n)

        val expectedReminder = ReminderResponse(
            title = "Organize sua Saúde",
            message = "Carteira de vacinação, exames antigos, receitas passadas...",
            style = "tertiary",
            image = ReminderImage(
                url = "https://alice-member-app-assets.s3.amazonaws.com/reminder/health_documents.png",
                type = ReminderImageType.STATIC
            ),
            action = ReminderActionResponse(
                text = "Subir arquivos",
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.HEALTH_DOCUMENTS,
                )
            )
        )

        assertThat(reminder).isEqualTo(expectedReminder)
    }

}
