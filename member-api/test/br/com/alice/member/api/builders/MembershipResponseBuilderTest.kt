package br.com.alice.member.api.builders

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.withCassiMember
import br.com.alice.member.api.models.MemberCardResponse
import br.com.alice.member.api.models.MemberCardType
import br.com.alice.member.api.models.MembershipResponse
import br.com.alice.person.model.MemberWithProduct
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class MembershipResponseBuilderTest {
    private val person = TestModelFactory.buildPerson()
    private val product = TestModelFactory.buildProduct(displayName = "Plano Alice")
    private val member = TestModelFactory.buildMember(person.id)

    @Test
    fun `#buildMembershipResponse - should return alice card`() {
        val memberWithProduct = MemberWithProduct(member, product)

        val expectedMembershipResponse = MembershipResponse(
            card = MemberCardResponse(
                number = person.nationalId,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                type = MemberCardType.ALICE,
                ansNumber = "-",
            ),
            cards = listOf(
                MemberCardResponse(
                    number = person.nationalId,
                    type = MemberCardType.ALICE,
                    productDisplayName = product.title,
                    displayName = product.displayName,
                    complementName = product.complementName,
                    ansNumber = "-",
                )
            ),
        )

        val membershipResponse =
            MembershipResponseBuilder.buildMembershipResponse(person, memberWithProduct)
        assertThat(membershipResponse).isEqualTo(expectedMembershipResponse)
    }

    @Test
    fun `#buildMembershipResponse - duquesa should use default alice card`() {
        val externalBrandAccountNumber = "********"
        val member = member.copy(brand = Brand.DUQUESA, externalBrandAccountNumber = externalBrandAccountNumber)
        val memberWithProduct = MemberWithProduct(member, product)
        val expectedMembershipResponse = MembershipResponse(
            card = MemberCardResponse(
                number = person.nationalId,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                type = MemberCardType.ALICE,
                ansNumber = "-",
            ),
            cards = listOf(
                MemberCardResponse(
                    number = person.nationalId,
                    type = MemberCardType.ALICE,
                    productDisplayName = product.title,
                    displayName = product.displayName,
                    complementName = product.complementName,
                    ansNumber = "-",
                )
            ),
        )

        val membershipResponse =
            MembershipResponseBuilder.buildMembershipResponse(person, memberWithProduct)
        assertThat(membershipResponse).isEqualTo(expectedMembershipResponse)
    }

    @Test
    fun `#buildMembershipResponse - should return alice and cassi card`() {
        val cassiMember = TestModelFactory.buildCassiMember()
        val memberWithCassi = member.withCassiMember(cassiMember)
        val memberWithProduct = MemberWithProduct(memberWithCassi, product)

        val expectedMembershipResponse = MembershipResponse(
            card = MemberCardResponse(
                number = person.nationalId,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                type = MemberCardType.ALICE,
                ansNumber = "-",
            ),
            cards = listOf(
                MemberCardResponse(
                    number = person.nationalId,
                    type = MemberCardType.ALICE,
                    productDisplayName = product.title,
                    displayName = product.displayName,
                    complementName = product.complementName,
                    ansNumber = "-",
                ),
                MemberCardResponse(
                    number = cassiMember.accountNumber!!,
                    type = MemberCardType.CASSI,
                    ansNumber = "34665-9",
                    startDate = cassiMember.startDate,
                    expirationDate = cassiMember.expirationDate,
                ),
            ),
        )

        val membershipResponse =
            MembershipResponseBuilder.buildMembershipResponse(person, memberWithProduct)
        assertThat(membershipResponse).isEqualTo(expectedMembershipResponse)
    }

    @Test
    fun `#buildMembershipResponse - duquesa should use alice card when shouldUseAliceCardForDuquesa`() {
        val externalBrandAccountNumber = "********"
        val member = member.copy(brand = Brand.DUQUESA, externalBrandAccountNumber = externalBrandAccountNumber)
        val memberWithProduct = MemberWithProduct(member, product)
        val expectedMembershipResponse = MembershipResponse(
            card = MemberCardResponse(
                number = person.nationalId,
                productDisplayName = product.title,
                displayName = product.displayName,
                complementName = product.complementName,
                type = MemberCardType.ALICE,
                ansNumber = "-",
            ),
            cards = listOf(
                MemberCardResponse(
                    number = person.nationalId,
                    productDisplayName = product.title,
                    displayName = product.displayName,
                    complementName = product.complementName,
                    type = MemberCardType.ALICE,
                    ansNumber = "-",
                )
            ),
        )

        val membershipResponse =
            MembershipResponseBuilder.buildMembershipResponse(person, memberWithProduct)
        assertThat(membershipResponse).isEqualTo(expectedMembershipResponse)
    }
}
