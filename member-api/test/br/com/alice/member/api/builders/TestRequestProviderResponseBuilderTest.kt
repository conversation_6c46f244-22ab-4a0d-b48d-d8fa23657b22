package br.com.alice.member.api.builders

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.converters.ProviderConverter
import br.com.alice.member.api.models.TestRequestProviderResponse
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class TestRequestProviderResponseBuilderTest {
    private val provider = TestModelFactory.buildProvider()

    @Test
    fun `#build a test request provider response`() {
        val providers = listOf(provider.copy(name = "Fleury"), provider.copy(name = "a+"))
        val testRequestProviderResponse = TestRequestProviderResponseBuilder.build(providers)

        val expectedResponse = TestRequestProviderResponse(
            title = "Agende diretamente com um dos laboratórios abaixo:",
            providers = providers.map { ProviderConverter.convert(it) }
        )

        assertThat(testRequestProviderResponse).isEqualTo(expectedResponse)
    }
}
