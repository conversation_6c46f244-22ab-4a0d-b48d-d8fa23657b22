package br.com.alice.member.api.builders

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Cpt
import br.com.alice.membership.model.onboarding.GracePeriod
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus
import br.com.alice.member.api.helpers.ContractDetailsResponseFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test


class ContractDetailsResponseBuilderTest {

    @Test
    fun `#buildContractDetailsResponse when user has CPTs`() {
        val expected = ContractDetailsResponseFactory.buildWithCPTsResponse()
        val cpts = listOf(
            Cpt("Miopia", cids = listOf()),
            Cpt("Obesidade", cids = listOf()),
            Cpt("Hipertensão", cids = listOf()),
        )
        val personId = PersonId()
        val gracePeriod = GracePeriod(
            personId = personId,
            cpts = cpts,
            personGracePeriods = emptyList(),
            portabilityRequest = null,
            isFullRiskB2B = false,
        )

        val result = ContractDetailsResponseBuilder.buildContractDetailsResponse(gracePeriod)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildContractDetailsResponse when user has CPTs and it has 3p grace period`() {
        val expected = ContractDetailsResponseFactory.buildWithCPTsResponse(true)
        val cpts = listOf(
            Cpt("Miopia", cids = listOf()),
            Cpt("Obesidade", cids = listOf()),
            Cpt("Hipertensão", cids = listOf()),
        )
        val personId = PersonId()
        val gracePeriod = GracePeriod(
            personId = personId,
            cpts = cpts,
            personGracePeriods = emptyList(),
            portabilityRequest = null,
            isFullRiskB2B = true,
        )

        val result = ContractDetailsResponseBuilder.buildContractDetailsResponse(gracePeriod)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildContractDetailsResponse when user has no CPTs and declined portability`() {
        val personId = PersonId()
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            personId = personId,
            status = InsurancePortabilityRequestStatus.DECLINED
        )
        val gracePeriod = GracePeriod(
            personId = personId,
            cpts = emptyList(),
            personGracePeriods = emptyList(),
            portabilityRequest = portabilityRequest,
            isFullRiskB2B = false,
        )
        val expected = ContractDetailsResponseFactory.buildWithGracePeriodWithoutCPTsResponse()
        val result = ContractDetailsResponseBuilder.buildContractDetailsResponse(gracePeriod)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildContractDetailsResponse when user has no CPTs, has 3p grace period and declined portability`() {
        val personId = PersonId()
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            personId = personId,
            status = InsurancePortabilityRequestStatus.DECLINED
        )
        val gracePeriod = GracePeriod(
            personId = personId,
            cpts = emptyList(),
            personGracePeriods = emptyList(),
            portabilityRequest = portabilityRequest,
            isFullRiskB2B = true,
        )
        val expected = ContractDetailsResponseFactory.buildWithThirdPartyGracePeriodResponse()
        val result = ContractDetailsResponseBuilder.buildContractDetailsResponse(gracePeriod)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildContractDetailsResponse when user has approved portability`() {
        val personId = PersonId()
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            personId = personId,
            status = InsurancePortabilityRequestStatus.APPROVED
        )
        val gracePeriod = GracePeriod(
            personId = personId,
            cpts = listOf(Cpt("Miopia", cids = listOf())),
            personGracePeriods = emptyList(),
            portabilityRequest = portabilityRequest,
            isFullRiskB2B = false
        )
        val expected = ContractDetailsResponseFactory.buildWithoutGracePeriodResponse()
        val result = ContractDetailsResponseBuilder.buildContractDetailsResponse(gracePeriod)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildContractDetailsResponse when user has approved portability and it has 3p grace period`() {
        val personId = PersonId()
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            personId = personId,
            status = InsurancePortabilityRequestStatus.APPROVED
        )
        val gracePeriod = GracePeriod(
            personId = personId,
            cpts = listOf(Cpt("Miopia", cids = listOf())),
            personGracePeriods = emptyList(),
            portabilityRequest = portabilityRequest,
            isFullRiskB2B = true,
        )
        val expected = ContractDetailsResponseFactory.buildWithoutGracePeriodWithThirdGracePeriodResponse()
        val result = ContractDetailsResponseBuilder.buildContractDetailsResponse(gracePeriod)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildContractDetailsResponse when user has not approved portability`() {
        val personId = PersonId()
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            personId = personId,
            status = InsurancePortabilityRequestStatus.PENDING
        )
        val gracePeriod = GracePeriod(
            personId = personId,
            cpts = emptyList(),
            personGracePeriods = emptyList(),
            portabilityRequest = portabilityRequest,
            isFullRiskB2B = false,
        )
        val expected = ContractDetailsResponseFactory.buildWithGracePeriodWithoutCPTsResponse()
        val result = ContractDetailsResponseBuilder.buildContractDetailsResponse(gracePeriod)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildContractDetailsResponse when user has not approved portability and it has 3p grace period`() {
        val personId = PersonId()
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            personId = personId,
            status = InsurancePortabilityRequestStatus.PENDING
        )
        val gracePeriod = GracePeriod(
            personId = personId,
            cpts = emptyList(),
            personGracePeriods = emptyList(),
            portabilityRequest = portabilityRequest,
            isFullRiskB2B = true,
        )
        val expected = ContractDetailsResponseFactory.buildWithThirdPartyGracePeriodResponse()
        val result = ContractDetailsResponseBuilder.buildContractDetailsResponse(gracePeriod)

        assertThat(result).isEqualTo(expected)
    }
}
