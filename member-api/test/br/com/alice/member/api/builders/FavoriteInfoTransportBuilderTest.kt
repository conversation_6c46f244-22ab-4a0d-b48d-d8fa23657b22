package br.com.alice.member.api.builders

import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.accreditedNetwork.FavoriteInfoTransport
import br.com.alice.member.api.models.appContent.Navigation
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class FavoriteInfoTransportBuilderTest {
    @Test
    fun `Should return FavoriteInfoTransport when specialist is not favorited`() = runBlocking {
        withFeatureFlags(
            FeatureNamespace.MEMBERSHIP,
            mapOf(
                "show_accredited_network_favorites" to true,
                "specialty_is_not_allowed_to_favorite" to emptyList<String>()
            ),
        ) {
            val specialist = TestModelFactory.buildHealthProfessional()
            val favorite = TestModelFactory.buildAccreditedNetworkFavorite()
            val isFavorite = false

            val result = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                isFavorite = isFavorite,
                favoriteId = favorite.id,
                appVersion = SemanticVersion("4.20.0"),
            )

            val expectedResult = FavoriteInfoTransport(
                isFavorite = false,
                action = Navigation(
                    method = RemoteActionMethod.POST,
                    endpoint = ServiceConfig.url("/accredited_network/favorites?provider_id=${specialist.id}")
                )
            )

            assertEquals(expectedResult, result)
        }
    }

    @Test
    fun `Should return FavoriteInfoTransport when specialist is favorited`() = runBlocking {
        withFeatureFlags(
            FeatureNamespace.MEMBERSHIP,
            mapOf(
                "show_accredited_network_favorites" to true,
                "specialty_is_not_allowed_to_favorite" to emptyList<String>()
            ),
        ) {
            val specialist = TestModelFactory.buildHealthProfessional()
            val favorite = TestModelFactory.buildAccreditedNetworkFavorite()
            val isFavorite = true

            val result = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                isFavorite = isFavorite,
                favoriteId = favorite.id,
                appVersion = SemanticVersion("4.20.0"),
            )

            val expectedResult = FavoriteInfoTransport(
                isFavorite = true,
                action = Navigation(
                    method = RemoteActionMethod.DELETE,
                    endpoint = ServiceConfig.url("/accredited_network/favorites/${favorite.id}?provider_id=${specialist.id}")
                )
            )

            assertEquals(expectedResult, result)
        }
    }

    @Test
    fun `Should return null when show_accredited_network_favorites is false`() = runBlocking {
        withFeatureFlags(
            FeatureNamespace.MEMBERSHIP,
            mapOf(
                "version_with_accredited_network_favorites" to "4.20.0",
                "specialty_is_not_allowed_to_favorite" to emptyList<String>()
            ),
        ) {
            val specialist = TestModelFactory.buildHealthProfessional()
            val favorite = TestModelFactory.buildAccreditedNetworkFavorite()
            val isFavorite = true

            val result = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                isFavorite = isFavorite,
                favoriteId = favorite.id,
                appVersion = SemanticVersion("4.0.0"),
            )

            val expectedResult = null

            assertEquals(expectedResult, result)
        }
    }

    @Test
    fun `Should return null when specialty_is_not_allowed_to_favorite contains specialist specialtyId`() = runBlocking {
        val specialtyId = RangeUUID.generate()

        withFeatureFlags(
            FeatureNamespace.MEMBERSHIP,
            mapOf(
                "show_accredited_network_favorites" to true,
                "specialty_is_not_allowed_to_favorite" to listOf(specialtyId.toString())
            ),
        ) {
            val specialist = TestModelFactory.buildHealthProfessional(specialtyId = specialtyId)
            val favorite = TestModelFactory.buildAccreditedNetworkFavorite()
            val isFavorite = true

            val result = FavoriteInfoTransportBuilder.build(
                referenceId = specialist.id,
                specialtyIds = specialist.specialtyId?.let { listOf(it) } ?: emptyList(),
                referenceType = ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
                isFavorite = isFavorite,
                favoriteId = favorite.id,
                appVersion = SemanticVersion("4.20.0"),
            )

            val expectedResult = null

            assertEquals(expectedResult, result)
        }
    }

    @Test
    fun `Should return null when referenceType is not one allowed`() = runBlocking {
        val specialtyId = RangeUUID.generate()

        withFeatureFlags(
            FeatureNamespace.MEMBERSHIP,
            mapOf(
                "show_accredited_network_favorites" to true,
                "specialty_is_not_allowed_to_favorite" to listOf(specialtyId.toString())
            ),
        ) {
            val providerUnit = TestModelFactory.buildProviderUnit(type = ProviderUnit.Type.HOSPITAL)
            val favorite = TestModelFactory.buildAccreditedNetworkFavorite()
            val isFavorite = true

            val result = FavoriteInfoTransportBuilder.build(
                referenceId = providerUnit.id,
                specialtyIds = providerUnit.medicalSpecialtyIds!!,
                referenceType = ConsolidatedAccreditedNetworkType.HOSPITAL,
                isFavorite = isFavorite,
                favoriteId = favorite.id,
                appVersion = SemanticVersion("4.20.0"),
            )

            val expectedResult = null

            assertEquals(expectedResult, result)
        }
    }

    @Test
    fun `#buildTransport should return FavoriteInfoTransport with POST method when isFavorite is false`() {
        val referenceId = RangeUUID.generate()
        val favoriteId = RangeUUID.generate()
        val isFavorite = false

        val result = FavoriteInfoTransportBuilder.buildTransport(referenceId, isFavorite, favoriteId)

        val expectedResult = FavoriteInfoTransport(
            isFavorite = false,
            action = Navigation(
                method = RemoteActionMethod.POST,
                endpoint = ServiceConfig.url("/accredited_network/favorites?provider_id=${referenceId}")
            )
        )

        assertEquals(expectedResult, result)
    }

    @Test
    fun `#buildTransport should return FavoriteInfoTransport with DELETE method when isFavorite is true`() {
        val referenceId = RangeUUID.generate()
        val favoriteId = RangeUUID.generate()
        val isFavorite = true

        val result = FavoriteInfoTransportBuilder.buildTransport(referenceId, isFavorite, favoriteId)

        val expectedResult = FavoriteInfoTransport(
            isFavorite = true,
            action = Navigation(
                method = RemoteActionMethod.DELETE,
                endpoint = ServiceConfig.url("/accredited_network/favorites/${favoriteId}?provider_id=${referenceId}")
            )
        )

        assertEquals(expectedResult, result)
    }
}
