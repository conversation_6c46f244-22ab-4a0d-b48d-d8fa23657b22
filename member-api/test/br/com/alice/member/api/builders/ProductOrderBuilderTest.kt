package br.com.alice.member.api.builders

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.member.api.models.onboarding.ProductOrderDetailsResponse
import br.com.alice.member.api.models.onboarding.ProductOrderDetailsSectionItemResponse
import br.com.alice.member.api.models.onboarding.ProductOrderDetailsSectionResponse
import br.com.alice.member.api.models.onboarding.ProductOrderResponse
import br.com.alice.membership.model.OrderWithProductWithProviders
import br.com.alice.product.model.ProductWithProviders
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ProductOrderBuilderTest {

    private val person = TestModelFactory.buildPerson()

    private val productWithProviders = ProductWithProviders(
        product = TestModelFactory.buildProduct(),
        providers = TestModelFactory.buildProviders()
    )

    private val productOrder = TestModelFactory.buildProductOrder(
        product = productWithProviders.product,
        promoCode = TestModelFactory.buildPromoCode()
    )

    private val discount = productOrder.getPromoCode()?.discountPercentage?.toInt()

    private val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

    @Test
    fun `#buildProductOrderResponse with PromoCode should build as expected`() {
        val (order, productWithProviders) = orderWithProduct
        val (product, providers) = productWithProviders

        val expectedProductOrderResponse = ProductOrderResponse(
            id = product.id,
            title = product.title,
            priceOriginal = order.getPrice(person.age),
            originalPrice = order.getPrice(person.age),
            price = order.getPriceWithDiscount(person.age, (discount ?: 0)),
            discountPercentage = discount,
            promoCode = order.getPromoCode()?.code,
            productDetails = ProductOrderDetailsResponse(listOf(
                ProductOrderDetailsSectionResponse(
                    title = "Time de Saúde Alice",
                    items = listOf(
                        ProductOrderDetailsSectionItemResponse("Médica(o) pessoal"),
                        ProductOrderDetailsSectionItemResponse("Enfermeiras(os)"),
                        ProductOrderDetailsSectionItemResponse("Nutricionista"),
                        ProductOrderDetailsSectionItemResponse("Preparador físico"),
                    )
                ),
                ProductOrderDetailsSectionResponse(
                    title = "Rede de Especialistas",
                    items = listOf(
                        ProductOrderDetailsSectionItemResponse("Especialistas escolhidas(os) a dedo {experiência de consulta particular pelo plano}."),
                    )
                ),
                ProductOrderDetailsSectionResponse(
                    title = "Laboratórios",
                    items = providers.filter { it.type == ProviderType.LABORATORY }
                        .map { ProductOrderDetailsSectionItemResponse(title = it.name) }
                ),
                ProductOrderDetailsSectionResponse(
                    title = "Hospitais",
                    items = providers.filter {
                        it.type in listOf(ProviderType.HOSPITAL, ProviderType.MATERNITY, ProviderType.CHILDREN)
                    }.map { ProductOrderDetailsSectionItemResponse(title = it.name) }
                ),
            ))
        )

        val actualProductOrderResponse = ProductOrderBuilder.buildProductOrderResponse(person, orderWithProduct)
        assertThat(actualProductOrderResponse).isEqualTo(expectedProductOrderResponse)
    }

    @Test
    fun `#buildProductOrderResponse without PromoCode should build as expected`() {
        val productOrderWithoutPromoCode = productOrder.setPromoCode(null)
        val orderWithProduct = OrderWithProductWithProviders(productOrderWithoutPromoCode, productWithProviders)

        val (order, productWithProviders) = orderWithProduct
        val (product, providers) = productWithProviders

        val person = TestModelFactory.buildPerson()
        val discount = productOrderWithoutPromoCode.getPromoCode()?.discountPercentage?.toInt()

        val expectedProductOrderResponse = ProductOrderResponse(
            id = product.id,
            title = product.title,
            priceOriginal = null,
            originalPrice = null,
            price = order.getPriceWithDiscount(person.age, (discount ?: 0)),
            discountPercentage = discount,
            promoCode = productOrderWithoutPromoCode.getPromoCode()?.code,
            productDetails = ProductOrderDetailsResponse(listOf(
                ProductOrderDetailsSectionResponse(
                    title = "Time de Saúde Alice",
                    items = listOf(
                        ProductOrderDetailsSectionItemResponse("Médica(o) pessoal"),
                        ProductOrderDetailsSectionItemResponse("Enfermeiras(os)"),
                        ProductOrderDetailsSectionItemResponse("Nutricionista"),
                        ProductOrderDetailsSectionItemResponse("Preparador físico"),
                    )
                ),
                ProductOrderDetailsSectionResponse(
                    title = "Rede de Especialistas",
                    items = listOf(
                        ProductOrderDetailsSectionItemResponse("Especialistas escolhidas(os) a dedo {experiência de consulta particular pelo plano}."),
                    )
                ),
                ProductOrderDetailsSectionResponse(
                    title = "Laboratórios",
                    items = providers.filter { it.type == ProviderType.LABORATORY }
                        .map { ProductOrderDetailsSectionItemResponse(title = it.name) }
                ),
                ProductOrderDetailsSectionResponse(
                    title = "Hospitais",
                    items = providers.filter {
                        it.type in listOf(ProviderType.HOSPITAL, ProviderType.MATERNITY, ProviderType.CHILDREN)
                    }.map { ProductOrderDetailsSectionItemResponse(title = it.name) }
                ),
            ))
        )

        val actualProductOrderResponse = ProductOrderBuilder.buildProductOrderResponse(person, orderWithProduct)
        assertThat(actualProductOrderResponse).isEqualTo(expectedProductOrderResponse)
    }
}
