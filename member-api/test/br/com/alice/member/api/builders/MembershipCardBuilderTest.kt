package br.com.alice.member.api.builders

import br.com.alice.common.Brand
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.models.MemberCardResponse
import br.com.alice.member.api.models.MemberCardType
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class MembershipCardBuilderTest {

    private val person = TestModelFactory.buildPerson()
    private val ansNumber = "ansNumber"
    private val product = TestModelFactory.buildProduct(ansNumber = ansNumber)
    private val member = TestModelFactory.buildMember(cassiMember = TestModelFactory.buildCassiMember())
    private val duquesaMember = TestModelFactory.buildMember(brand = Brand.DUQUESA)
    private val aliceMemberCard = MemberCardResponse(
        number = person.nationalId,
        productDisplayName = product.title,
        displayName = product.displayName,
        complementName = product.complementName,
        type = MemberCardType.ALICE,
        ansNumber = ansNumber
    )
    private val cassiMemberCard = MemberCardResponse(
        number = member.cassiMember!!.accountNumber!!,
        type = MemberCardType.CASSI,
        ansNumber = "34665-9",
        startDate = member.cassiMember!!.startDate,
        expirationDate = member.cassiMember!!.expirationDate,
    )

    @Test
    fun `#buildCassiMembershipCard - return card response`() {
        val result = MembershipCardBuilder.buildCassiMembershipCard(member)

        assertThat(result).isEqualTo(cassiMemberCard)
    }

    @Test
    fun `#buildCassiMembershipCard - return card response when cassiMember is empty`() {
        val cassiMemberEmpty =
            TestModelFactory.buildCassiMember(accountNumber = null, startDate = null, expirationDate = null)
        val memberAliceWithoutCassi = member.copy(cassiMember = cassiMemberEmpty)
        val result = MembershipCardBuilder.buildCassiMembershipCard(memberAliceWithoutCassi)

        val expectedCassiMemberCard = MemberCardResponse(
            number = "-",
            type = MemberCardType.CASSI,
            ansNumber = "34665-9",
            startDate = null,
            expirationDate = null,
        )

        assertThat(result).isEqualTo(expectedCassiMemberCard)
    }

    @Test
    fun `#buildCassiMembershipCard - return card response when cassiMember is null`() {
        val memberAliceWithoutCassi = member.copy(cassiMember = null)
        val result = MembershipCardBuilder.buildCassiMembershipCard(memberAliceWithoutCassi)

        val expectedCassiMemberCard = MemberCardResponse(
            number = "-",
            type = MemberCardType.CASSI,
            ansNumber = "34665-9",
            startDate = null,
            expirationDate = null,
        )

        assertThat(result).isEqualTo(expectedCassiMemberCard)
    }

    @Test
    fun `#buildAliceMembershipCard - return card response`() {
        val result = MembershipCardBuilder.buildAliceMembershipCard(person, product)

        assertThat(result).isEqualTo(aliceMemberCard)
    }

    @Test
    fun `#buildAliceMembershipCard - return card response when ansNumber is null`() {
        val product = product.copy(ansNumber = null)
        val result = MembershipCardBuilder.buildAliceMembershipCard(person, product)

        val expected = aliceMemberCard.copy(ansNumber = "-")

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildMembershipCards - return card responses`() {
        val result = MembershipCardBuilder.buildMembershipCards(person, member, product)

        val expected = listOf(aliceMemberCard, cassiMemberCard)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildMembershipCards - return card response for duquesa member`() {

        val result = MembershipCardBuilder.buildMembershipCards(person, duquesaMember, product)

        val expected = listOf(aliceMemberCard)

        assertThat(result).isEqualTo(expected)
    }
}
