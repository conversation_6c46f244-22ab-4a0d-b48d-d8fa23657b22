package br.com.alice.member.api.factory

import br.com.alice.member.api.models.ScoreMagentaOverviewPillarItem
import br.com.alice.member.api.models.ScoreMagentaOverviewRangeLabel
import br.com.alice.member.api.models.ScoreTypes
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.assertThrows
import kotlin.test.Test
import kotlin.test.assertEquals

class ScoreMagentaOverviewFactoryTest {

    @Test
    fun `#buildLabels should build successfully`() = runBlocking {
        val expected = listOf(
            ScoreMagentaOverviewRangeLabel(
                title = "Ruim",
                value = 0
            ),
            ScoreMagentaOverviewRangeLabel(
                title = "Regular",
                value = 500
            ),
            ScoreMagentaOverviewRangeLabel(
                title = "Excelente",
                value = 1000
            )
        )

        assertEquals(expected, ScoreMagentaOverviewFactory.buildLabels())
    }

    @Test
    fun `#buildSection should build food section successfully`() = runBlocking {
        val expected = ScoreMagentaOverviewPillarItem(
            title = "Alimentação",
            description = "Avaliamos a qualidade da sua alimentação através de um questionário baseado nos " +
                    "marcadores de consumo alimentar do Ministério da Saúde. Queremos saber sobre aquelas " +
                    "escapadinhas da dieta e também tudo que você fez certinho e que vai deixar a sua Nutri " +
                    "muito feliz e orgulhosa.",
            value = 10,
            icon = ScoreTypes.FOOD.icon,
        )

        assertEquals(expected, ScoreMagentaOverviewFactory.buildSection(ScoreTypes.FOOD, expected.value))
    }

    @Test
    fun `#buildSection should build mental section successfully`() = runBlocking {
        val expected = ScoreMagentaOverviewPillarItem(
            title = "Saúde Mental",
            description = "Usamos dois questionários que avaliam seus níveis de ansiedade e depressão, " +
                    "o GAD (Generalized Anxiety Disorder scale) e PHQ (Patient Health Questionnaire). " +
                    "Com essas respostas, identificamos quando você precisa da gente mais pertinho de você para te " +
                    "apoiar, ajudar e indicar os melhores especialistas!",
            value = 10,
            icon = ScoreTypes.MENTAL.icon,
        )

        assertEquals(expected, ScoreMagentaOverviewFactory.buildSection(ScoreTypes.MENTAL, expected.value))
    }

    @Test
    fun `#buildSection should build habit section successfully`() = runBlocking {
        val expected = ScoreMagentaOverviewPillarItem(
            title = "Hábitos",
            description = "Aqui a gente quer saber sobre aqueles hábitos que muitas vezes temos, mas sequer " +
                    "gostamos de falar. E utilizamos escalas reconhecidas, como o AUDIT " +
                    "(Alcohol Use Disorders Identification Test), para calcular o impacto desses hábitos na " +
                    "sua saúde. Mas fica em paz que só nós vamos saber.",
            value = 10,
            icon = ScoreTypes.ALCOHOL_AND_SMOKE.icon,
        )

        assertEquals(expected, ScoreMagentaOverviewFactory.buildSection(ScoreTypes.ALCOHOL_AND_SMOKE, expected.value))
    }

    @Test
    fun `#buildSection should build quality of life section successfully`() = runBlocking {
        val expected = ScoreMagentaOverviewPillarItem(
            title = "Qualidade de vida",
            description = "Medimos através do EuroQoL, um protocolo internacional que avalia a sua qualidade de " +
                    "vida a partir de como você enxerga a sua saúde. Até porque, a sua opinião é a que " +
                    "realmente importa.",
            value = 10,
            icon = ScoreTypes.QUALITY_OF_LIFE.icon,
        )

        assertEquals(expected, ScoreMagentaOverviewFactory.buildSection(ScoreTypes.QUALITY_OF_LIFE, expected.value))
    }

    @Test
    fun `#buildSection should build sleep section successfully`() = runBlocking {
        val expected = ScoreMagentaOverviewPillarItem(
            title = "Sono",
            description = "Com base no MSQ (Mini-Sleep Questionnaire), entendemos como está a qualidade do seu sono," +
                    " até porque não adianta nada dormir bastante e acordar parecendo que tirou só um cochilo. " +
                    "Assim, podemos identificar distúrbios do sono e propor o melhor plano de cuidados para que " +
                    "você durma igual a um anjinho!",
            value = 10,
            icon = ScoreTypes.SLEEP.icon,
        )

        assertEquals(expected, ScoreMagentaOverviewFactory.buildSection(ScoreTypes.SLEEP, expected.value))
    }

    @Test
    fun `#buildSection should build physical activity section successfully`() = runBlocking {
        val expected = ScoreMagentaOverviewPillarItem(
            title = "Atividade física",
            description = "Vamos somar cada minutinho das suas atividades físicas, divididas em 3 intensidades: " +
                    "leve, moderada e alta. Assim, entendemos melhor o quanto você está se movimentando, " +
                    "a partir do MET (Equivalente Metabólico da Tarefa), de acordo com as diretrizes internacionais " +
                    "de atividade física e saúde.",
            value = 10,
            icon = ScoreTypes.PHYSICAL_ACTIVITY.icon,
        )

        assertEquals(expected, ScoreMagentaOverviewFactory.buildSection(ScoreTypes.PHYSICAL_ACTIVITY, expected.value))
    }

    @Test
    fun `#buildSection should throw error when is unsupported type`() {
        assertThrows<UnsupportedOperationException> {
            ScoreMagentaOverviewFactory.buildSection(ScoreTypes.GENERAL, 0)
        }
    }

}
