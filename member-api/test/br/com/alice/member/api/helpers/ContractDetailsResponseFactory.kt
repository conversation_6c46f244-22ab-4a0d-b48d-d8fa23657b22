package br.com.alice.member.api.helpers

import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.onboarding.ActionResponse
import br.com.alice.member.api.models.onboarding.ContentResponse
import br.com.alice.member.api.models.onboarding.ContractDetailsResponse
import br.com.alice.member.api.models.onboarding.SectionResponse

object ContractDetailsResponseFactory {

    private fun buildWithoutGracePeriodSection() = SectionResponse(
        id = "CONTRACT_GENERAL_GRACE_PERIOD",
        headline = "An<PERSON>, vamos falar sobre as suas carências.",
        description = "**Seu plano não tem carência!** Logo após assinar o contrato, todos os procedimentos já estarão liberados:",
        buttonLabel = "Avançar",
        content = listOf(
            ContentResponse(title = "Parto", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Internações & Cirurgias Eletivas", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Consultas em Pronto-Socorro", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Consultas", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Terapias", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Exames", subtitle = "<success>Liberado</success>")
        ),
        actions = null
    )

    private fun buildWithThirdPartyGracePeriodSection() = SectionResponse(
        id = "CONTRACT_GENERAL_GRACE_PERIOD",
        headline = "Antes, vamos falar sobre as suas carências.",
        description = "Você terá as seguintes carências:",
        buttonLabel = "Avançar",
        content = listOf(
            ContentResponse(title = "Parto", subtitle = "300 dias"),
            ContentResponse(title = "Internações & Cirurgias Eletivas", subtitle = "6 meses"),
            ContentResponse(title = "Consultas em Pronto-Socorro", subtitle = "24 horas"),
            ContentResponse(title = "Consultas", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Terapias", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Exames", subtitle = "<success>Liberado</success>")
        ),
        actions = listOf(
            ActionResponse(
                title = "Fale com a gente pelo Whatsapp",
                action = NavigationResponse(
                    mobileRoute = MobileRouting.EXTERNAL_APP,
                    link = Links.CPTS_DOUBTS_WHATSAPP
                )
            )
        )
    )

    private fun buildWithGracePeriodWithoutCPTsSection() = SectionResponse(
        id = "CONTRACT_GENERAL_GRACE_PERIOD",
        headline = "Antes, vamos falar sobre as suas carências.",
        description = "Você terá as seguintes carências:",
        buttonLabel = "Avançar",
        content = listOf(
            ContentResponse(title = "Parto", subtitle = "300 dias"),
            ContentResponse(title = "Internações & Cirurgias Eletivas", subtitle = "6 meses"),
            ContentResponse(title = "Consultas em Pronto-Socorro", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Consultas", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Terapias", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Exames", subtitle = "<success>Liberado</success>")
        ),
        actions = listOf(
            ActionResponse(
                title = "Fale com a gente pelo Whatsapp",
                action = NavigationResponse(
                    mobileRoute = MobileRouting.EXTERNAL_APP,
                    link = Links.CPTS_DOUBTS_WHATSAPP
                )
            )
        )
    )

    private fun buildWithCPTsSection() = SectionResponse(
        id = "CONTRACT_GENERAL_GRACE_PERIOD",
        headline = "Antes, vamos falar sobre as suas carências e coberturas.",
        description = "Você terá as seguintes carências:",
        buttonLabel = "Avançar",
        content = listOf(
            ContentResponse(title = "Parto", subtitle = "300 dias"),
            ContentResponse(title = "Internações & Cirurgias Eletivas", subtitle = "6 meses"),
            ContentResponse(title = "Consultas em Pronto-Socorro", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Consultas", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Terapias", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Exames", subtitle = "<success>Liberado</success>")
        ),
        actions = listOf(
            ActionResponse(
                title = "Fale com a gente pelo Whatsapp",
                action = NavigationResponse(mobileRoute = MobileRouting.EXTERNAL_APP, link = Links.CPTS_DOUBTS_WHATSAPP)
            )
        )
    )

    private fun buildWithThirdPartyGracePeriodWithCPTsSection() = SectionResponse(
        id = "CONTRACT_GENERAL_GRACE_PERIOD",
        headline = "Antes, vamos falar sobre as suas carências e coberturas.",
        description = "Você terá as seguintes carências:",
        buttonLabel = "Avançar",
        content = listOf(
            ContentResponse(title = "Parto", subtitle = "300 dias"),
            ContentResponse(title = "Internações & Cirurgias Eletivas", subtitle = "6 meses"),
            ContentResponse(title = "Consultas em Pronto-Socorro", subtitle = "24 horas"),
            ContentResponse(title = "Consultas", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Terapias", subtitle = "<success>Liberado</success>"),
            ContentResponse(title = "Exames", subtitle = "<success>Liberado</success>")
        ),
        actions = listOf(
            ActionResponse(
                title = "Fale com a gente pelo Whatsapp",
                action = NavigationResponse(mobileRoute = MobileRouting.EXTERNAL_APP, link = Links.CPTS_DOUBTS_WHATSAPP)
            )
        )
    )

    fun buildWithoutGracePeriodResponse() = ContractDetailsResponse(listOf(buildWithoutGracePeriodSection()))

    fun buildWithoutGracePeriodWithThirdGracePeriodResponse() = ContractDetailsResponse(listOf(
        buildWithThirdPartyGracePeriodSection()
    ))

    fun buildWithThirdPartyGracePeriodResponse() =
        ContractDetailsResponse(listOf(buildWithThirdPartyGracePeriodSection()))

    fun buildWithGracePeriodWithoutCPTsResponse() =
        ContractDetailsResponse(listOf(buildWithGracePeriodWithoutCPTsSection()))

    fun buildWithCPTsResponse(withThirdPartyGracePeriod: Boolean = false) = ContractDetailsResponse(
        listOf(
            if (withThirdPartyGracePeriod) buildWithThirdPartyGracePeriodWithCPTsSection() else buildWithCPTsSection(),
            SectionResponse(
                id = "CONTRACT_PRE_CONDITION_MEMBER",
                headline = "Algumas condições de saúde %%interferem na cobertura%% do seu plano.",
                description = "Abaixo, as condições que você disse ter na declaração de saúde e no bate-papo com a enfermeira:",
                caption = "IMPORTANTE:",
                buttonLabel = "Ver procedimentos",
                footnote = "Isso significa que alguns **procedimentos referentes a essas condições** não estarão cobertos.",
                content = listOf(
                    ContentResponse(title = "Miopia"),
                    ContentResponse(title = "Obesidade"),
                    ContentResponse(title = "Hipertensão")
                ),
                actions = listOf(
                    ActionResponse(
                        title = "Fale com a gente pelo Whatsapp",
                        action = NavigationResponse(
                            mobileRoute = MobileRouting.EXTERNAL_APP,
                            link = Links.CPTS_DOUBTS_WHATSAPP
                        )
                    )
                )
            ),
            SectionResponse(
                id = "CONTRACT_PRE_CONDITION_GRACE_PERIOD",
                headline = "Procedimentos que %%não%% estarão cobertos durante os 24 meses %%(Referentes às condições da tela anterior)%%:",
                buttonLabel = "Assinar contrato",
                content = listOf(
                    ContentResponse(
                        title = "Exames de alta complexidade",
                        subtitle = "Ex: Ressonância magnética, tomografia computadorizada, entre outros."
                    ),
                    ContentResponse(
                        title = "Procedimentos cirúrgicos",
                        subtitle = "Seja em casos pré-agendados, de urgência ou emergência."
                    ),
                    ContentResponse(
                        title = "Internações em leitos de CTI e/ou UTI",
                        subtitle = "Seja em casos pré-agendados, de urgência ou emergência."
                    ),
                    ContentResponse(
                        title = "Outros procedimentos de alta complexidade",
                        subtitle = "Ex: Sessões de quimioterapia, radioterapia, hemodiálise crônica, entre outros."
                    )
                ),
                actions = listOf(
                    ActionResponse(
                        title = "Veja a lista completa de procedimentos",
                        action = NavigationResponse(mobileRoute = MobileRouting.EXTERNAL_APP, link = Links.ALL_CPTS)
                    ),
                    ActionResponse(
                        title = "Fale com a gente pelo Whatsapp",
                        action = NavigationResponse(
                            mobileRoute = MobileRouting.EXTERNAL_APP,
                            link = Links.CPTS_DOUBTS_WHATSAPP
                        )
                    )
                )
            )
        )
    )
}
