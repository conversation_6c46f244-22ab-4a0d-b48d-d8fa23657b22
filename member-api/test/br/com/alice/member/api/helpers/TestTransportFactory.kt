package br.com.alice.member.api.helpers

import br.com.alice.action.plan.model.ActionPlanTaskTransport
import br.com.alice.action.plan.model.ActionPlanTasksTransport
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.GenericTaskNew
import br.com.alice.data.layer.models.HealthPlan
import br.com.alice.data.layer.models.PrescriptionNew
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.TestRequestNew
import br.com.alice.healthplan.models.HealthPlanTransport
import br.com.alice.healthplan.models.TaskRequesterRequest
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.reflect.full.memberProperties
import br.com.alice.action.plan.model.GenericTaskTransport as ActionPlanGenericTaskTransport
import br.com.alice.action.plan.model.PrescriptionTransport as ActionPlanPrescriptionTransport
import br.com.alice.action.plan.model.ReferralTransport as ActionPlanReferralTransport
import br.com.alice.action.plan.model.TestRequestTransport as ActionPlanTestRequestTransport

object TestTransportFactory {

    fun buildHealthPlanTransport(
        personId: PersonId? = PersonId(),
        id: UUID = RangeUUID.generate(),
        healthPlan: HealthPlan = TestModelFactory.buildHealthPlan(
            personId = personId!!,
            id = id
        ),
        version: Int = healthPlan.version
    ) =
        HealthPlanTransport(
            id = healthPlan.id,
            personId = healthPlan.personId.toString(),
            description = healthPlan.description,
            healthGoal = healthPlan.healthGoal,
            version = version,
            createdAt = DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(healthPlan.createdAt)
        )

    fun buildActionPlanTasksTransport(
        staff: Staff,
        prescription: List<PrescriptionNew> = emptyList(),
        eating: List<GenericTaskNew> = emptyList(),
        physicalActivity: List<GenericTaskNew> = emptyList(),
        sleep: List<GenericTaskNew> = emptyList(),
        mood: List<GenericTaskNew> = emptyList(),
        others: List<GenericTaskNew> = emptyList(),
        testRequest: List<TestRequestNew> = emptyList(),
        referral: List<ReferralNew> = emptyList()
    ) =
        ActionPlanTasksTransport(
            prescription = convertActionPlanTaskTransport<PrescriptionNew, ActionPlanPrescriptionTransport>(
                prescription,
                mapOf(staff.id to staff),
                ActionPlanTaskType.PRESCRIPTION,
            ),
            eating = convertActionPlanTaskTransport<GenericTaskNew, ActionPlanGenericTaskTransport>(
                eating,
                mapOf(staff.id to staff),
                ActionPlanTaskType.EATING,
            ),
            physicalActivity = convertActionPlanTaskTransport<GenericTaskNew, ActionPlanGenericTaskTransport>(
                physicalActivity,
                mapOf(staff.id to staff),
                ActionPlanTaskType.PHYSICAL_ACTIVITY,
            ),
            sleep = convertActionPlanTaskTransport<GenericTaskNew, ActionPlanGenericTaskTransport>(
                sleep,
                mapOf(staff.id to staff),
                ActionPlanTaskType.SLEEP,
            ),
            mood = convertActionPlanTaskTransport<GenericTaskNew, ActionPlanGenericTaskTransport>(
                mood,
                mapOf(staff.id to staff),
                ActionPlanTaskType.MOOD,
            ),
            others = convertActionPlanTaskTransport<GenericTaskNew, ActionPlanGenericTaskTransport>(
                others,
                mapOf(staff.id to staff),
                ActionPlanTaskType.OTHERS,
            ),
            testRequest = convertActionPlanTaskTransport<TestRequestNew, ActionPlanTestRequestTransport>(
                testRequest,
                mapOf(staff.id to staff),
                ActionPlanTaskType.TEST_REQUEST,
            ),
            referral = convertActionPlanTaskTransport<ReferralNew, ActionPlanReferralTransport>(
                referral,
                mapOf(staff.id to staff),
                ActionPlanTaskType.REFERRAL,
            ),
        )

    private inline fun <O : ActionPlanTask, reified D : ActionPlanTaskTransport> convertActionPlanTaskTransport(
        tasks: List<ActionPlanTask>,
        staffMap: Map<UUID, Staff>,
        type: ActionPlanTaskType,
    ) =
        tasks.filter { it.type == type }.map { it.specialize<O>() }.map { origin ->
            val constructor = D::class.constructors.last()

            val values = origin::class.memberProperties.associate {
                it.name to it.getter.call(origin)
            }

            val parameters = constructor.parameters.associateWith { param ->
                val name = param.name!!
                val fieldValue = values[name]

                val value = when {
                    name == "lastRequester" -> {
                        createTaskRequester(
                            origin.lastRequesterStaffId,
                            staffMap.getValue(origin.lastRequesterStaffId),
                            origin.updatedAt
                        )
                    }

                    name == "requesters" -> {
                        origin.requestersStaffIds.map {
                            createTaskRequester(it, staffMap.getValue(it), null)
                        }.toSet()
                    }

                    fieldValue is LocalDateTime -> fieldValue.toString()
                    fieldValue is LocalDate -> fieldValue.toString()
                    fieldValue is PersonId -> fieldValue.id
                    else -> fieldValue
                }

                value
            }

            constructor.callBy(parameters)
        }

    private fun createTaskRequester(
        staffId: UUID,
        staff: Staff,
        requestedAt: LocalDateTime? = null
    ): TaskRequesterRequest {
        return TaskRequesterRequest(
            staffId = staffId,
            name = staff.firstName,
            profileImageUrl = staff.profileImageUrl,
            requestedAt = requestedAt.toString()
        )
    }
}
