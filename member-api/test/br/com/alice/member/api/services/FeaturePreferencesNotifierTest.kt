package br.com.alice.member.api.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.logging.logger
import com.google.api.core.ApiFuture
import com.google.cloud.Timestamp
import com.google.cloud.firestore.CollectionReference
import com.google.cloud.firestore.DocumentReference
import com.google.cloud.firestore.DocumentSnapshot
import com.google.cloud.firestore.Firestore
import com.google.cloud.firestore.SetOptions
import com.google.cloud.firestore.WriteResult
import com.google.firebase.FirebaseApp
import com.google.firebase.cloud.FirestoreClient
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FeaturePreferencesNotifierTest {

    private val firestore: Firestore = mockk()
    private val collectionName = "feature_preferences"
    private val collectionReference: CollectionReference = mockk()
    private val timestamp = "2021-09-01T00:00:00.000Z"
    private val personId = PersonId(id = UUID.fromString("f47ac10b-58cc-4372-a567-0e02b2c3d401"))
    private val documentReference: DocumentReference = mockk()
    private val documentSnapshot: DocumentSnapshot = mockk()

    @BeforeTest
    fun setup() {
        mockkObject(logger)
        mockkStatic(FirebaseApp::class)
        mockkStatic(FirestoreClient::class)

        every { FirestoreClient.getFirestore() } returns firestore
        every { firestore.collection(collectionName) } returns collectionReference
        every { collectionReference.document(personId.toString()) } returns documentReference
    }

    @Test
    fun `#getFeaturePreference - given valid parameters, should call FireStore as expected`() = runBlocking {
        mockkStatic(Timestamp::class) {
            val featurePreferences = FeaturePreferences.APP_BAR_ITEM_POPOVER
            val apiFutureDocumentSnapshot: ApiFuture<DocumentSnapshot> = mockk()

            val expectedData = mapOf(
                "test" to true,
                "timestamp" to timestamp
            )

            val documentSetMap = mapOf(
                "app_bar_item_popover" to expectedData
            )

            every { Timestamp.now().toString() } returns timestamp
            every { documentReference.get() } returns apiFutureDocumentSnapshot
            every { apiFutureDocumentSnapshot.get() } returns documentSnapshot
            every { documentSnapshot.exists() } returns true
            every { documentSnapshot.data } returns documentSetMap

            val result = FeaturePreferencesNotifier.getFeaturePreferences(personId, featurePreferences)

            assert(result == expectedData)

            verify { documentReference.get() }
            verify { documentSnapshot.data }
        }
    }

    @Test
    fun `#getFeaturePreference - when get a error, should return null`() = runBlocking {
        mockkStatic(Timestamp::class) {
            val featurePreferences = FeaturePreferences.APP_BAR_ITEM_POPOVER
            val apiFutureDocumentSnapshot: ApiFuture<DocumentSnapshot> = mockk()
            val exception = Exception()

            every { Timestamp.now().toString() } returns timestamp
            every { documentReference.get() } returns apiFutureDocumentSnapshot
            every { apiFutureDocumentSnapshot.get() } throws exception

            val result = FeaturePreferencesNotifier.getFeaturePreferences(personId, featurePreferences)

            assert(result == null)

            verify { documentReference.get() }
            verify(exactly = 1) {
                logger.error(
                    "Error getting FeaturePreferences",
                    "person_id" to personId.id,
                    "feature_preferences" to "APP_BAR_ITEM_POPOVER",
                    exception
                )
            }
        }
    }

    @Test
    fun `#updateFeaturePreference - given valid parameters, should call FireStore as expected`() = runBlocking {
        mockkStatic(Timestamp::class) {
            val featurePreferences = FeaturePreferences.APP_BAR_ITEM_POPOVER
            val apiFutureWriteResult: ApiFuture<WriteResult> = mockk()

            val documentSetMap = mapOf(
                "app_bar_item_popover" to mapOf(
                    "test" to true,
                    "timestamp" to timestamp
                )
            )

            every { Timestamp.now().toString() } returns timestamp
            every { documentReference.set(any(), SetOptions.merge()) } returns apiFutureWriteResult
            every { apiFutureWriteResult.get().updateTime } returns mockk()

            FeaturePreferencesNotifier.updateFeaturePreferences(personId, featurePreferences, mapOf("test" to true))

            verify { documentReference.set(documentSetMap, SetOptions.merge()) }
            verify { apiFutureWriteResult.get().updateTime }
        }
    }

    @Test
    fun `#updateFeaturePreference - when get a error`() = runBlocking {
        mockkStatic(Timestamp::class) {
            val featurePreferences = FeaturePreferences.APP_BAR_ITEM_POPOVER
            val exception = Exception()
            val data = mapOf("test" to true)

            every { Timestamp.now().toString() } returns timestamp
            every { documentReference.set(any(), SetOptions.merge()) } throws exception

            FeaturePreferencesNotifier.updateFeaturePreferences(personId, featurePreferences, data)

            verify { documentReference.set(any(), SetOptions.merge()) }
            verify(exactly = 1) {
                logger.error(
                    "Error updating FeaturePreferences",
                    "person_id" to personId.id,
                    "feature_preferences" to "APP_BAR_ITEM_POPOVER",
                    "data" to data,
                    exception
                )
            }
        }
    }
}
