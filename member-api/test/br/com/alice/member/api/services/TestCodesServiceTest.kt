package br.com.alice.member.api.services

import br.com.alice.common.helpers.returns
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProviderUnit.Type.ALICE_HOUSE
import br.com.alice.data.layer.models.ProviderUnitTestCode
import br.com.alice.ehr.client.TestPreparationService
import br.com.alice.member.api.models.TestCode
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.provider.client.ProviderUnitTestCodeService
import br.com.alice.provider.client.TestCodeService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class TestCodesServiceTest {

    private val providerUnitTestCodeService: ProviderUnitTestCodeService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val testCodeService: TestCodeService = mockk()
    private val testPreparationService: TestPreparationService = mockk()
    private val cache: GenericCache = mockk()

    private val testCodesService = TestCodesService(
        providerUnitTestCodeService,
        providerUnitService,
        testCodeService,
        testPreparationService,
        cache
    )

    private val testPreparation = TestModelFactory.buildTestPreparation()
    private val testPreparation2 = TestModelFactory.buildTestPreparation()
    private val testCode = TestModelFactory.buildTestCode(
        code = "98040001",
        preparationId = testPreparation.id
    )
    private val testCode2 = TestModelFactory.buildTestCode(
        code = "98040002",
        preparationId = testPreparation2.id
    )
    private val testCode3 = TestModelFactory.buildTestCode(
        code = "98040003",
        preparationId = testPreparation2.id
    )
    private val testCode4 = TestModelFactory.buildTestCode(
        code = "98040004",
        preparationId = null
    )
    private val casaAliceProviderUnit = TestModelFactory.buildProviderUnit(type = ALICE_HOUSE)
    private val providerUnitIds = listOf(casaAliceProviderUnit.id)
    private val providerUnitTestCode = TestModelFactory.buildProviderUnitTestCode(
        providerUnitId = casaAliceProviderUnit.id,
        testCodeId = testCode.id,
        title = "Bioimpedância"
    )
    private val providerUnitTestCode2 = TestModelFactory.buildProviderUnitTestCode(
        providerUnitId = casaAliceProviderUnit.id,
        testCodeId = testCode2.id,
        title = "Exames Lab"
    )
    private val providerUnitTestCode3 = TestModelFactory.buildProviderUnitTestCode(
        providerUnitId = casaAliceProviderUnit.id,
        testCodeId = testCode3.id,
        title = "Exames Lab"
    )
    private val providerUnitTestCode4 =
        TestModelFactory.buildProviderUnitTestCode(providerUnitId = casaAliceProviderUnit.id)

    @BeforeTest
    fun setup() {
        clearAllMocks()

        coEvery { cache.getList(any<String>(), TestCode::class, any(), any(), any()) } coAnswers {
            arg<suspend () -> List<TestCode>>(4).invoke()
        }
        coEvery { providerUnitService.getByType(ALICE_HOUSE) } returns listOf(casaAliceProviderUnit)
    }

    @Test
    fun `#getTestCodes should return test codes`() = runBlocking<Unit> {
        val providerUnitTestCodes = listOf(
            providerUnitTestCode,
            providerUnitTestCode2,
            providerUnitTestCode3,
            providerUnitTestCode4,
        )
        val testCodeListMock = listOf(
            testCode,
            testCode2,
            testCode3,
        )
        val testCodeIds = providerUnitTestCodes.filter { it.title != null }.map { it.testCodeId }

        coEvery {
            providerUnitTestCodeService.findByProviderUnitIds(providerUnitIds)
        } returns providerUnitTestCodes
        coEvery {
            testCodeService.findByIds(testCodeIds)
        } returns testCodeListMock

        val testCodes = testCodesService.getTestCodes()

        assertThat(testCodes).containsExactlyInAnyOrder(
            TestCode(
                title = "Bioimpedância",
                codes = listOf("98040001")
            ),
            TestCode(
                title = "Exames Lab",
                codes = listOf("98040002", "98040003")
            )
        )
    }

    @Test
    fun `#getTestCodes should return empty list when provider unit has no test codes`() = runBlocking<Unit> {
        val providerUnitTestCodes = emptyList<ProviderUnitTestCode>()

        coEvery { providerUnitTestCodeService.findByProviderUnitIds(providerUnitIds) } returns providerUnitTestCodes

        val testCodes = testCodesService.getTestCodes()

        assertThat(testCodes).isEqualTo(emptyList<TestCode>())
    }

    @Test
    fun `#getTestCodes should return test codes sorted`() = runBlocking<Unit> {
        val providerUnitTestCodes = listOf(
            providerUnitTestCode4,
            providerUnitTestCode3,
            providerUnitTestCode2,
            providerUnitTestCode
        )
        val testCodeListMock = listOf(
            testCode3,
            testCode2,
            testCode,
        )

        coEvery { providerUnitTestCodeService.findByProviderUnitIds(providerUnitIds) } returns providerUnitTestCodes
        coEvery {
            testCodeService.findByIds(providerUnitTestCodes.filter { it.title != null }.map { it.testCodeId })
        } returns testCodeListMock

        val testCodes = testCodesService.getTestCodes()

        assertThat(testCodes).containsExactlyInAnyOrder(
            TestCode(
                title = "Bioimpedância",
                codes = listOf("98040001")
            ), TestCode(
                title = "Exames Lab",
                codes = listOf("98040002", "98040003")
            )
        )
    }

    @Test
    fun `#getTestCodes should return test codes with cache`() = runBlocking {
        val testCodeListMock = listOf(
            TestCode(
                title = "test",
                codes = listOf("98040001")
            )
        )

        coEvery { cache.getList(any<String>(), TestCode::class, any(), any(), any()) } returns testCodeListMock

        val testCodes = testCodesService.getTestCodes()

        assertThat(testCodes.size).isEqualTo(1)

        coVerify { providerUnitService wasNot called }
        coVerify { providerUnitTestCodeService wasNot called }
        coVerify { testCodeService wasNot called }
    }

    @Test
    fun `#getPreparations should return map with test preparations`() = runBlocking<Unit> {
        val codes = listOf(
            testCode.code,
            testCode2.code,
            testCode3.code,
            testCode4.code
        )
        val testCodes = listOf(
            testCode,
            testCode2,
            testCode3,
            testCode4
        )
        val testPreparations = listOf(
            testPreparation,
            testPreparation2
        )

        coEvery { testCodeService.findByProcedures(codes) } returns testCodes.success()
        coEvery {
            testPreparationService.findByIds(testCodes.mapNotNull { it.preparationId })
        } returns testPreparations.success()

        val preparationsMap = testCodesService.getPreparations(codes)

        assertThat(preparationsMap).containsAllEntriesOf(
            mapOf(
                testCode.code to testPreparation,
                testCode2.code to testPreparation2,
                testCode3.code to testPreparation2
            )
        )
    }

}
