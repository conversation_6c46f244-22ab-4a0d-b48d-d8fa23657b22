package br.com.alice.member.api.services

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.member.api.models.AllowedUsersList
import br.com.alice.membership.client.SessionsService
import br.com.alice.membership.client.onboarding.GasProcessService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class SessionNavigationServiceTest {
    private val gasProcessService: GasProcessService = mockk()
    private val sessionService: SessionsService = mockk()

    private val sessionsService = SessionNavigationService(
        sessionService,
        gasProcessService,
    )

    private val person = TestModelFactory.buildPerson()
    private val personOnboarding = TestModelFactory.buildPersonOnboarding(person.id)

    @Test
    fun `#getOnboardingPhase should return current onboarding phase without modifications`() = runBlocking<Unit> {
        mockkObject(AllowedUsersList)

        coEvery { AllowedUsersList.isGasProcessTester(person.nationalId) } returns false
        val currentPhase = sessionsService.getOnboardingPhase(person, personOnboarding)
        assertThat(currentPhase).isEqualTo(personOnboarding.currentPhase)
    }

    @Test
    fun `#getOnboardingPhase should return SHOPPING when user is on allowed list`() = runBlocking<Unit> {
        mockkObject(AllowedUsersList)

        val expected = personOnboarding.copy(
            currentPhase = OnboardingPhase.SHOPPING,
            finishedAt = null
        )

        coEvery { AllowedUsersList.isGasProcessTester(person.nationalId) } returns true
        coEvery { gasProcessService.clearGasProcess(person, personOnboarding) } returns expected.success()

        val currentPhase = sessionsService.getOnboardingPhase(person, personOnboarding)
        assertThat(currentPhase).isEqualTo(OnboardingPhase.SHOPPING)
    }

    @Test
    fun `#getCrmKey should return empty when a person is not found`() = runBlocking {
        coEvery { sessionService.getCrmKey(person.nationalId) } returns NotFoundException("person_not_found").failure()

        val crmKey = sessionsService.getCrmKey(person.nationalId).getOrNullIfNotFound()
        assertThat(crmKey).isNull()
    }

    @Test
    fun `#getCrmKey should return a CRM key when a person exists`() = runBlocking<Unit> {
        val key = "SOME_KEY"

        coEvery { sessionService.getCrmKey(person.nationalId) } returns key.success()

        val crmKey = sessionsService.getCrmKey(person.nationalId).getOrNullIfNotFound()
        assertThat(crmKey).isEqualTo(key)
    }

}
