package br.com.alice.member.api.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.coverage.client.AccreditedNetworkFavoriteService
import br.com.alice.coverage.client.ConsolidatedAccreditedNetworkService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.member.api.builders.FavoriteInfoTransportBuilder
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.util.UUID
import kotlin.test.Test

class AccreditedNetworkFavoriteInternalServiceTest {
    private val accreditedNetworkFavoriteService: AccreditedNetworkFavoriteService = mockk()
    private val consolidatedAccreditedNetworkService: ConsolidatedAccreditedNetworkService = mockk()

    private val accreditedNetworkFavoriteInternalService = AccreditedNetworkFavoriteInternalService(
        accreditedNetworkFavoriteService,
        consolidatedAccreditedNetworkService
    )

    @Test
    fun `addFavorite should return a FavoriteInfoTransport`() = runBlocking {
        val personId = PersonId()
        val referenceId = UUID.randomUUID()
        val referenceType = ConsolidatedAccreditedNetworkType.CASSI_SPECIALIST
        val specialtyIds = listOf(UUID.randomUUID())
        val consolidatedAccreditedNetwork = listOf(
            TestModelFactory.buildConsolidatedAccreditedNetwork(
                type = referenceType,
                specialtyIds = specialtyIds
            )
        )

        val favorite = TestModelFactory.buildAccreditedNetworkFavorite(
            personId = personId,
            referenceId = referenceId,
            referenceType = referenceType,
            specialtyIds = specialtyIds
        )

        coEvery {
            accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                personId,
                referenceId
            )
        } returns NotFoundException().failure()
        coEvery { consolidatedAccreditedNetworkService.getByReferencedId(referenceId) } returns consolidatedAccreditedNetwork.success()
        coEvery { accreditedNetworkFavoriteService.add(any()) } returns favorite.success()

        val result = accreditedNetworkFavoriteInternalService.addFavorite(personId, referenceId)
        val favoriteInfoTransportExpected = FavoriteInfoTransportBuilder.buildTransport(referenceId, true, favorite.id)

        assertThat(result).isSuccessWithData(favoriteInfoTransportExpected)

        coVerifyOnce { accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(personId, referenceId) }
        coVerifyOnce { consolidatedAccreditedNetworkService.getByReferencedId(any()) }
        coVerifyOnce { accreditedNetworkFavoriteService.add(any()) }
    }

    @Test
    fun `addFavorite should return the same FavoriteInfoTransport if Favorite already exist`() = runBlocking {
        val personId = PersonId()
        val referenceId = UUID.randomUUID()
        val referenceType = ConsolidatedAccreditedNetworkType.CASSI_SPECIALIST
        val specialtyIds = listOf(UUID.randomUUID())

        val favorite = TestModelFactory.buildAccreditedNetworkFavorite(
            personId = personId,
            referenceId = referenceId,
            referenceType = referenceType,
            specialtyIds = specialtyIds
        )

        coEvery {
            accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(
                personId,
                referenceId
            )
        } returns favorite.success()

        val result = accreditedNetworkFavoriteInternalService.addFavorite(personId, referenceId)
        val favoriteInfoTransportExpected = FavoriteInfoTransportBuilder.buildTransport(favorite.id, true)

        assertThat(result).isSuccessWithData(favoriteInfoTransportExpected)

        coVerifyOnce { accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(personId, referenceId) }
    }

    @Test
    fun `removeFavorite should return a FavoriteInfoTransport`() = runBlocking {
        val favoriteId = UUID.randomUUID()
        val referenceId = UUID.randomUUID()

        coEvery { accreditedNetworkFavoriteService.deleteById(favoriteId) } returns true.success()

        val result = accreditedNetworkFavoriteInternalService.removeFavorite(favoriteId, referenceId)
        val favoriteInfoTransportExpected = FavoriteInfoTransportBuilder.buildTransport(referenceId, false, favoriteId)

        assertThat(result).isSuccessWithData(favoriteInfoTransportExpected)

        coVerifyOnce { accreditedNetworkFavoriteService.deleteById(favoriteId) }
    }

    @Test
    fun `removeFavorite should return a FavoriteInfoTransport if result is false`() = runBlocking {
        val favoriteId = UUID.randomUUID()
        val referenceId = UUID.randomUUID()

        coEvery { accreditedNetworkFavoriteService.deleteById(favoriteId) } returns false.success()

        val result = accreditedNetworkFavoriteInternalService.removeFavorite(favoriteId, referenceId)
        val favoriteInfoTransportExpected = FavoriteInfoTransportBuilder.buildTransport(referenceId, false, favoriteId)

        assertThat(result).isSuccessWithData(favoriteInfoTransportExpected)

        coVerifyOnce { accreditedNetworkFavoriteService.deleteById(favoriteId) }
    }
}
