package br.com.alice.member.api.services

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonPreferences
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting.*
import br.com.alice.member.api.models.NavigationResponse
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class OnboardingMobileRouterTest {

    @Test
    fun `#withoutTrialNavigator should map phases to mobile routing with onboarding options`() = runBlocking {
        val navigator = BaselineNavigator()
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId = personId, phoneNumber = "***********", dateOfBirth = LocalDateTime.now().minusYears(1))

        assertMapping(navigator, OnboardingPhase.SHOPPING, expectedNavigation = NavigationResponse(SHOPPING, Links.NEW_SHOPPING_CART), person = person)
        assertMapping(navigator, OnboardingPhase.LEGAL_GUARDIAN_REGISTER, expectedNavigation = NavigationResponse(LEGAL_GUARDIAN_REGISTER, properties = mapOf("nickName" to person.contactName)), person = person)
        assertMapping(navigator, OnboardingPhase.LEGAL_GUARDIAN_RESPONSIBILITY_TERM_SIGNING, expectedNavigation = NavigationResponse(LEGAL_GUARDIAN_RESPONSIBILITY_TERM, properties = mapOf("email" to person.email)), person = person)
        assertMapping(navigator, OnboardingPhase.CHILD_VIDEOS_REQUEST, expectedNavigation = NavigationResponse(CHILD_VIDEOS_REQUEST, properties = mapOf("name" to person.contactName, "cpf" to person.nationalId, "videos" to listOf(
            mapOf(
                "description" to "Andando",
                "url" to "https://alice-member-app-assets.s3.amazonaws.com/child_onboarding/1_ano_01.mp4"
            ),
            mapOf(
                "description" to "Falando",
                "url" to "https://alice-member-app-assets.s3.amazonaws.com/child_onboarding/1_ano_02.mp4"
            )
        ))), person = person)
        assertMapping(navigator, OnboardingPhase.PORTABILITY, expectedNavigation = NavigationResponse(PORTABILITY_QUESTIONS, Links.PORTABILITY_QUESTIONS_V2), person = person)
        assertMapping(navigator, OnboardingPhase.REGISTRATION, expectedNavigation = NavigationResponse(REGISTRATION, Links.REGISTRATION), person = person)
        assertMapping(navigator, OnboardingPhase.REGISTRATION, portabilityRequested = true, expectedNavigation = NavigationResponse(REGISTRATION, Links.REGISTRATION), person = person)
        assertMapping(navigator, OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT, expectedNavigation = NavigationResponse(HEALTH_DECLARATION_APPOINTMENT, Links.HEALTH_DECLARATION_APPOINTMENT), person = person)
        assertMapping(navigator, OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT, portabilityRequested = true, expectedNavigation = NavigationResponse(HEALTH_DECLARATION_APPOINTMENT, Links.HEALTH_DECLARATION_APPOINTMENT), person = person)
        assertMapping(navigator, OnboardingPhase.WAITING_FOR_REVIEW, expectedNavigation = NavigationResponse(HEALTH_DECLARATION_APPOINTMENT, Links.HEALTH_DECLARATION_APPOINTMENT), person = person)
        assertMapping(navigator, OnboardingPhase.WAITING_FOR_REVIEW, portabilityRequested = true, expectedNavigation = NavigationResponse(HEALTH_DECLARATION_APPOINTMENT, Links.HEALTH_DECLARATION_APPOINTMENT), person = person)
        assertMapping(navigator, OnboardingPhase.CONTRACT, expectedNavigation = NavigationResponse(CONTRACT_SIGNING, Links.CONTRACT), person = person)
        assertMapping(navigator, OnboardingPhase.PAYMENT, expectedNavigation = NavigationResponse(GAS_PAYMENT, properties = mapOf("payment_method" to PaymentMethod.PIX)), person = person)
        assertMapping(navigator, OnboardingPhase.FINISHED, expectedNavigation = NavigationResponse(HOME), person = person)
    }

    @Test
    fun `#getNavigation should return WithoutTrialNavigator when app version gte WIHOUT_TRAIL`() = runBlocking<Unit> {
        val personId = PersonId()
        val person = TestModelFactory.buildPerson(personId = personId, phoneNumber = "***********", dateOfBirth = LocalDateTime.now())

        val onboardingPhase = OnboardingPhase.SHOPPING
        val expected = NavigationResponse(SHOPPING, Links.NEW_SHOPPING_CART)

        val actual = OnboardingMobileRouter.getNavigation(person, onboardingPhase, portabilityRequested = false, source = NavigationSource.START_SESSION)
        assertThat(actual).isEqualTo(expected)
    }

    private suspend fun assertMapping(
        onboardingAppNavigator: OnboardingAppNavigator,
        phase: OnboardingPhase,
        source: NavigationSource = NavigationSource.OTHER,
        portabilityRequested: Boolean = false,
        expectedNavigation: NavigationResponse,
        person: Person,
        preferences: PersonPreferences? = null,
    ) {
        val actual = onboardingAppNavigator.getNavigation(phase, portabilityRequested, source, person, preferences)
        assertThat(actual).isEqualTo(expectedNavigation)
    }
}
