package br.com.alice.member.api.services

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.models.procedureAuthorization.AuthorizationTransport
import br.com.alice.member.api.models.procedureAuthorization.InstructionTransport
import br.com.alice.member.api.models.procedureAuthorization.ProcedureTransport
import br.com.alice.member.api.models.procedureAuthorization.TimelineEventTransport
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import kotlin.test.AfterTest
import kotlin.test.Test

class ProcedureAuthorizationServiceTest {

    private val totvsGuiaService: TotvsGuiaService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()

    val service =
        ProcedureAuthorizationService(totvsGuiaService, mvAuthorizedProcedureService, healthcareResourceService)

    private val now = LocalDate.now()
    private val nowTime = now.atEndOfTheDay()
    private val personId = PersonId()
    private val totvsGuiaId = RangeUUID.generate()

    private val totvsGuia = TestModelFactory.buildTotvsGuia(
        id = totvsGuiaId,
        externalCode = "externalCode",
        status = TotvsGuiaStatus.PENDING,
        type = MvUtil.TISS.EXAM,
        requestedAt = now
    )

    private val procedure = TestModelFactory.buildHealthcareResource(
        code = "alice_code_1",
        tussCode = "tuss_code_1",
        description = "description_1"
    )

    private val mvAuthorizedProcedure = TestModelFactory.buildMvAuthorizedProcedure(
        totvsGuiaId = totvsGuiaId,
        procedureId = procedure.tussCode,
        opmeId = null,
        status = MvAuthorizedProcedureStatus.UNAUTHORIZED,
        authorizedAt = nowTime.minusDays(2L),
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Nested
    inner class GetList {

        @Test
        fun `#getList should throw InvalidArgumentException when FF not found`(): Unit =
            runBlocking {
                withFeatureFlag(
                    FeatureNamespace.ALICE_APP,
                    "procedure_authorization_release_date",
                    ""
                ) {
                    val result = service.getList(personId)
                    ResultAssert.assertThat(result).isFailureOfType(InvalidArgumentException::class)
                }
            }

        @Test
        fun `#getList should throw InvalidArgumentException when invalid FF date`(): Unit =
            runBlocking {
                withFeatureFlag(
                    FeatureNamespace.ALICE_APP,
                    "procedure_authorization_release_date",
                    "2023-aa10-05"
                ) {
                    val result = service.getList(personId)
                    ResultAssert.assertThat(result).isFailureOfType(InvalidArgumentException::class)
                }
            }

        @Test
        fun `#getList should return data with guias`(): Unit =
            runBlocking {
                withFeatureFlag(
                    FeatureNamespace.ALICE_APP,
                    "procedure_authorization_release_date",
                    "2023-10-05"
                ) {
                    val expectedResponse = ProcedureAuthorizationListResponse(
                        title = "Acompanhe a autorização das suas guias",
                        instructions = listOf(
                            InstructionTransport("calendar", "A análise pode levar até 3 dias úteis;"),
                            InstructionTransport(
                                "check_outlined",
                                "Só vá até ao local com o pedido autorizado;"
                            ),
                            InstructionTransport(
                                "attachment",
                                "Informe o número da guia no laboratório ou hospital para um atendimento mais rápido"
                            )
                        ),
                        procedureAuthorizations = listOf(
                            AuthorizationTransport(
                                id = totvsGuia.id.toString(),
                                externalCode = totvsGuia.externalCode!!,
                                status = "PENDING",
                                label = "Número da guia do Exame",
                                requestedBy = "Marco Antonio",
                                requestedAt = nowTime,
                                clickAction = RemoteAction(
                                    mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                                    params = mapOf("id" to totvsGuia.id.toString())
                                )
                            )
                        )
                    )

                    coEvery {
                        totvsGuiaService.findActivesByPersonIdRequestedAfter(personId, LocalDate.parse("2023-10-05"))
                    } returns listOf(
                        totvsGuia.copy(
                            requestedByProfessional = ProfessionalIdentification(
                                fullName = "MARCO ANTONIO",
                                councilNumber = "NULL",
                                council = CouncilType.OUT,
                                councilState = State.SP,
                                specialty = null
                            )
                        )
                    ).success()

                    val result = service.getList(personId)
                    ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

                    coVerifyOnce {
                        totvsGuiaService.findActivesByPersonIdRequestedAfter(personId, LocalDate.parse("2023-10-05"))
                    }
                }
            }

        @Test
        fun `#getList should return empty list when no guia found`(): Unit =
            runBlocking {
                withFeatureFlag(
                    FeatureNamespace.ALICE_APP,
                    "procedure_authorization_release_date",
                    "2023-10-05"
                ) {
                    val expectedResponse = ProcedureAuthorizationListResponse(
                        title = "Acompanhe a autorização das suas guias",
                        instructions = listOf(
                            InstructionTransport("calendar", "A análise pode levar até 3 dias úteis;"),
                            InstructionTransport("check_outlined", "Só vá até ao local com o pedido autorizado;"),
                            InstructionTransport(
                                "attachment",
                                "Informe o número da guia no laboratório ou hospital para um atendimento mais rápido"
                            )
                        ),
                        procedureAuthorizations = emptyList()
                    )

                    coEvery {
                        totvsGuiaService.findActivesByPersonIdRequestedAfter(personId, LocalDate.parse("2023-10-05"))
                    } returns emptyList<TotvsGuia>().success()

                    val result = service.getList(personId)
                    ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

                    coVerifyOnce {
                        totvsGuiaService.findActivesByPersonIdRequestedAfter(personId, LocalDate.parse("2023-10-05"))
                    }
                }
            }

        @Test
        fun `#getList should return failure when error on totvsGuiaService`(): Unit =
            runBlocking {
                withFeatureFlag(
                    FeatureNamespace.ALICE_APP,
                    "procedure_authorization_release_date",
                    "2023-10-05"
                ) {
                    coEvery {
                        totvsGuiaService.findActivesByPersonIdRequestedAfter(personId, LocalDate.parse("2023-10-05"))
                    } returns Exception("server error").failure()

                    val result = service.getList(personId)
                    ResultAssert.assertThat(result).isFailureOfType(Exception::class)

                    coVerifyOnce {
                        totvsGuiaService.findActivesByPersonIdRequestedAfter(personId, LocalDate.parse("2023-10-05"))
                    }
                }
            }
    }

    @Nested
    inner class GetDetail {

        @Test
        fun `#getDetail should return authorization and events timeline for PENDING guia`(): Unit =
            runBlocking {

                val expectedResponse = ProcedureAuthorizationDetailResponse(
                    authorization = AuthorizationTransport(
                        id = totvsGuiaId.toString(),
                        externalCode = "externalCode",
                        status = "PENDING",
                        label = "Número da guia do Exame",
                        requestedBy = "Marco Antonio",
                        requestedAt = nowTime,
                        clickAction = RemoteAction(
                            mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                            params = mapOf("id" to totvsGuiaId.toString())
                        )
                    ),
                    events = listOf(
                        TimelineEventTransport.finished("Solicitação recebida", nowTime),
                        TimelineEventTransport.inProgress(
                            "Análise de procedimentos",
                            "Conclusão em até 3 dias úteis",
                            nowTime.minusDays(2L),
                            listOf(
                                ProcedureTransport(
                                    status = "PENDING",
                                    tussCode = "tuss_code_1",
                                    description = "description_1"
                                )
                            )
                        ),
                        TimelineEventTransport.notStarted("Análise concluída")
                    )
                )

                coEvery {
                    totvsGuiaService.get(totvsGuiaId)
                } returns totvsGuia.copy(
                    requestedByProfessional = ProfessionalIdentification(
                        fullName = "marco antonio",
                        councilNumber = "NULL",
                        council = CouncilType.OUT,
                        councilState = State.SP,
                        specialty = null
                    )
                ).success()

                coEvery {
                    mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuiaId)
                } returns listOf(mvAuthorizedProcedure).success()

                coEvery {
                    healthcareResourceService.getByCode(mvAuthorizedProcedure.procedureId!!)
                } returns procedure.success()

                val result = service.getDetail(totvsGuiaId)
                ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce {
                    totvsGuiaService.get(any())
                    mvAuthorizedProcedureService.findByTotvsGuiaId(any())
                    healthcareResourceService.getByCode(any())
                }
            }

        @Test
        fun `#getDetail should return authorization and events timeline for AUTHORIZED guia`(): Unit =
            runBlocking {

                val totvsGuiaCopied = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED)

                val expectedResponse = ProcedureAuthorizationDetailResponse(
                    authorization = AuthorizationTransport(
                        id = totvsGuiaId.toString(),
                        externalCode = "externalCode",
                        status = "AUTHORIZED",
                        label = "Número da guia do Exame",
                        requestedBy = "Alice",
                        requestedAt = nowTime,
                        clickAction = RemoteAction(
                            mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                            params = mapOf("id" to totvsGuiaId.toString())
                        )
                    ),
                    events = listOf(
                        TimelineEventTransport.finished("Solicitação recebida", nowTime),
                        TimelineEventTransport.finished(
                            "Análise de procedimentos",
                            nowTime.minusDays(2L),
                        ),
                        TimelineEventTransport.finished(
                            "Análise concluída e autorizada",
                            nowTime.minusDays(2L),
                            listOf(
                                ProcedureTransport(
                                    status = "UNAUTHORIZED",
                                    tussCode = "tuss_code_1",
                                    description = "description_1"
                                )
                            )
                        )
                    )
                )

                coEvery {
                    totvsGuiaService.get(totvsGuiaId)
                } returns totvsGuiaCopied.success()

                coEvery {
                    mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuiaId)
                } returns listOf(mvAuthorizedProcedure).success()

                coEvery {
                    healthcareResourceService.getByCode(mvAuthorizedProcedure.procedureId!!)
                } returns procedure.success()

                val result = service.getDetail(totvsGuiaId)
                ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce {
                    totvsGuiaService.get(any())
                    mvAuthorizedProcedureService.findByTotvsGuiaId(any())
                    healthcareResourceService.getByCode(any())
                }

            }

        @Test
        fun `#getDetail should return authorization and events timeline with placeholder external code`(): Unit =
            runBlocking {

                val totvsGuiaCopied = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED, externalCode = null)

                val expectedResponse = ProcedureAuthorizationDetailResponse(
                    authorization = AuthorizationTransport(
                        id = totvsGuiaId.toString(),
                        externalCode = "Estamos criando sua guia e em breve o número estará aqui",
                        status = "AUTHORIZED",
                        label = "Número da guia do Exame",
                        requestedBy = "Alice",
                        requestedAt = nowTime,
                        clickAction = RemoteAction(
                            mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                            params = mapOf("id" to totvsGuiaId.toString())
                        )
                    ),
                    events = listOf(
                        TimelineEventTransport.finished("Solicitação recebida", nowTime),
                        TimelineEventTransport.finished(
                            "Análise de procedimentos",
                            nowTime.minusDays(2L),
                        ),
                        TimelineEventTransport.finished(
                            "Análise concluída e autorizada",
                            nowTime.minusDays(2L),
                            listOf(
                                ProcedureTransport(
                                    status = "UNAUTHORIZED",
                                    tussCode = "tuss_code_1",
                                    description = "description_1"
                                )
                            )
                        )
                    )
                )

                coEvery {
                    totvsGuiaService.get(totvsGuiaId)
                } returns totvsGuiaCopied.success()

                coEvery {
                    mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuiaId)
                } returns listOf(mvAuthorizedProcedure).success()

                coEvery {
                    healthcareResourceService.getByCode(mvAuthorizedProcedure.procedureId!!)
                } returns procedure.success()

                val result = service.getDetail(totvsGuiaId)
                ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce {
                    totvsGuiaService.get(any())
                    mvAuthorizedProcedureService.findByTotvsGuiaId(any())
                    healthcareResourceService.getByCode(any())
                }

            }

        @Test
        fun `#getDetail should return authorization and events timeline with empty tuss information`(): Unit =
            runBlocking {

                val totvsGuiaCopied = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED, externalCode = null)

                val expectedResponse = ProcedureAuthorizationDetailResponse(
                    authorization = AuthorizationTransport(
                        id = totvsGuiaId.toString(),
                        externalCode = "Estamos criando sua guia e em breve o número estará aqui",
                        status = "AUTHORIZED",
                        label = "Número da guia do Exame",
                        requestedBy = "Alice",
                        requestedAt = nowTime,
                        clickAction = RemoteAction(
                            mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                            params = mapOf("id" to totvsGuiaId.toString())
                        )
                    ),
                    events = listOf(
                        TimelineEventTransport.finished("Solicitação recebida", nowTime),
                        TimelineEventTransport.finished(
                            "Análise de procedimentos",
                            nowTime.minusDays(2L),
                        ),
                        TimelineEventTransport.finished(
                            "Análise concluída e autorizada",
                            nowTime.minusDays(2L),
                            listOf(
                                ProcedureTransport(
                                    status = "UNAUTHORIZED",
                                    tussCode = "Código não encontrado",
                                    description = "Procedimento não encontrado"
                                )
                            )
                        )
                    )
                )

                coEvery {
                    totvsGuiaService.get(totvsGuiaId)
                } returns totvsGuiaCopied.success()

                coEvery {
                    mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuiaId)
                } returns listOf(mvAuthorizedProcedure).success()

                coEvery {
                    healthcareResourceService.getByCode(mvAuthorizedProcedure.procedureId!!)
                } returns NotFoundException("Not found").failure()

                val result = service.getDetail(totvsGuiaId)
                ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce {
                    totvsGuiaService.get(any())
                    mvAuthorizedProcedureService.findByTotvsGuiaId(any())
                    healthcareResourceService.getByCode(any())
                }

            }

        @Test
        fun `#getDetail should return authorization and events timeline with TussProcedure matching with alice procedure description`(): Unit =
            runBlocking {

                val mvAuthorizedProcedure = TestModelFactory.buildMvAuthorizedProcedure(
                    totvsGuiaId = totvsGuiaId,
                    procedureId = procedure.code,
                    opmeId = null,
                    status = MvAuthorizedProcedureStatus.UNAUTHORIZED,
                    authorizationRequestedAt = nowTime.minusDays(2L),
                    authorizedAt = nowTime.minusDays(1L),
                )

                val totvsGuiaCopied = totvsGuia.copy(status = TotvsGuiaStatus.AUTHORIZED, externalCode = null)

                val expectedResponse = ProcedureAuthorizationDetailResponse(
                    authorization = AuthorizationTransport(
                        id = totvsGuiaId.toString(),
                        externalCode = "Estamos criando sua guia e em breve o número estará aqui",
                        status = "AUTHORIZED",
                        label = "Número da guia do Exame",
                        requestedBy = "Alice",
                        requestedAt = nowTime,
                        clickAction = RemoteAction(
                            mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
                            params = mapOf("id" to totvsGuiaId.toString())
                        )
                    ),
                    events = listOf(
                        TimelineEventTransport.finished("Solicitação recebida", nowTime),
                        TimelineEventTransport.finished(
                            "Análise de procedimentos",
                            nowTime.minusDays(1L),
                        ),
                        TimelineEventTransport.finished(
                            "Análise concluída e autorizada",
                            nowTime.minusDays(1L),
                            listOf(
                                ProcedureTransport(
                                    status = "UNAUTHORIZED",
                                    tussCode = "tuss_code_1",
                                    description = "description_1"
                                )
                            )
                        )
                    )
                )

                coEvery {
                    totvsGuiaService.get(totvsGuiaId)
                } returns totvsGuiaCopied.success()

                coEvery {
                    mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuiaId)
                } returns listOf(mvAuthorizedProcedure).success()

                coEvery {
                    healthcareResourceService.getByCode(mvAuthorizedProcedure.procedureId!!)
                } returns procedure.success()

                val result = service.getDetail(totvsGuiaId)
                ResultAssert.assertThat(result).isSuccessWithData(expectedResponse)

                coVerifyOnce {
                    totvsGuiaService.get(any())
                    mvAuthorizedProcedureService.findByTotvsGuiaId(any())
                    healthcareResourceService.getByCode(any())
                }

            }

    }

}
