package br.com.alice.member.api.services

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TestResultFile
import br.com.alice.ehr.client.TestResultFileService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class ReminderServiceTest {
    private val testResultFileService: TestResultFileService = mockk()

    private val person = TestModelFactory.buildPerson(dateOfBirth = LocalDateTime.now().minusYears(20))
    private val testResultFiles = listOf(
        TestModelFactory.buildTestResultFile()
    )

    private val reminderService = ReminderService(
        testResultFileService
    )

    @Test
    fun `#personHasHealthDocuments should return true if there are health documents`() = runBlocking<Unit> {
        coEvery {
            testResultFileService.findByPersonId(person.id.toString())
        } returns testResultFiles.success()

        val personHasHealthDocuments = reminderService.personHasHealthDocuments(person.id.toString())

        assertThat(personHasHealthDocuments).isTrue()
    }

    @Test
    fun `#personHasHealthDocuments should return false if there isn't any health documents`() = runBlocking<Unit> {
        coEvery {
            testResultFileService.findByPersonId(person.id.toString())
        } returns emptyList<TestResultFile>().success()

        val personHasHealthDocuments = reminderService.personHasHealthDocuments(person.id.toString())

        assertThat(personHasHealthDocuments).isFalse()
    }

}
