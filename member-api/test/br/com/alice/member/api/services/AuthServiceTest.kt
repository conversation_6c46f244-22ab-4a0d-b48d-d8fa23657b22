package br.com.alice.member.api.services

import br.com.alice.authentication.Authenticator
import br.com.alice.authentication.UserType
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.kafka.internals.LocalProducer
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonInternalReference
import br.com.alice.data.layer.models.PersonLogin
import br.com.alice.member.api.models.AuthResponse
import br.com.alice.membership.client.InvalidCredentialsException
import br.com.alice.membership.client.LoginNotFoundException
import br.com.alice.membership.client.PersonLoginService
import br.com.alice.membership.model.events.UserAuthenticatedEvent
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.client.UserTypeService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class AuthServiceTest {

    private val personService: PersonService = mockk()
    private val personLoginService: PersonLoginService = mockk()
    private val personInternalReferenceService: PersonInternalReferenceService = mockk()
    private val userTypeService: UserTypeService = mockk()
    private val memberService: MemberService = mockk()

    private val authService = AuthService(
        personService,
        personLoginService,
        personInternalReferenceService,
        userTypeService,
        LocalProducer,
        memberService
    )

    private val person = TestModelFactory.buildPerson()

    private val someNationalId = RangeUUID.generate().toString()

    private val personLogin = PersonLogin(
        nationalId = someNationalId,
        personId = person.id,
        accessCode = "3488923",
        saltAccessCode = "398483434",
        expirationDate = LocalDateTime.now().plusDays(10)
    )
    private val nowDateTime = LocalDateTime.now()

    @BeforeTest
    fun setup() {
        clearAllMocks()
        mockkObject(Authenticator)
        every { Authenticator.generateCustomToken(any(), eq("Unauthenticated")) } returns "SomeToken"
        coEvery { personService.get(person.id) } returns person.success()

        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now() } returns nowDateTime

        LocalProducer.clearMessages()
    }


    @Test
    fun `#getPersonAndSendNewAccessCodeByNationalId should return person response and generate new access code`() =
        runBlocking<Unit> {
            coEvery { personLoginService.getLoginInformation(someNationalId) } returns Result.success(person)

            val loginResponse = authService.getPersonAndSendNewAccessCodeByNationalId(someNationalId)

            verify(exactly = 1) { Authenticator.generateCustomToken(someNationalId, "Unauthenticated") }
            assertThat(loginResponse).isNotNull
        }


    @Test
    fun `#authenticateByNationalIdAndGetPerson should return authenticated when sign in was successful and got channel_person_id`() =
        runBlocking<Unit> {
            val someNationalId = "01234567890"
            val otp = "12345"
            val personWithLeadId = person.copy(leadId = RangeUUID.generate())
            val accessToken = RangeUUID.generate().toString()
            val personInternalReference = PersonInternalReference(personLogin.personId)

            coEvery {
                personInternalReferenceService.getForPerson(personLogin.personId)
            } returns Result.success(personInternalReference)

            coEvery {
                personService.get(personLogin.personId)
            } returns personWithLeadId.success()

            val member = TestModelFactory.buildMember(personId = personWithLeadId.id)
            coEvery { memberService.findFirstMembership(personWithLeadId.id) } returns member.success()

            every {
                Authenticator.generateCustomToken(
                    userId = personLogin.personId.toString(),
                    kClass = Person::class,
                    extraClaims = mapOf(
                        Authenticator.CHANNEL_PERSON_ID_KEY to personInternalReference.channelPersonId.toString(),
                        Authenticator.USER_SUB_TYPE_KEY to "MEMBER",
                        Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
                        Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString(),
                        Authenticator.LEAD_ID to personWithLeadId.leadId.toString(),
                    )
                )
            } returns accessToken

            val expectedAuthResult = AuthResponse(
                customToken = accessToken,
                person = personWithLeadId
            )

            coEvery { personLoginService.signInByNationalId(someNationalId, otp) } returns Result.success(personLogin)
            coEvery { userTypeService.get(personWithLeadId.id, refreshed = true) } returns UserType.MEMBER.success()

            val authResult = authService.authenticateByNationalIdAndGetPerson(someNationalId, otp)
            assertThat(authResult).isSuccessWithData(expectedAuthResult)

            LocalProducer.hasEvent(UserAuthenticatedEvent.name)
        }

    @Test
    fun `#authenticateByNationalIdAndGetPerson should return authenticated when sign in was successful and did not get channel_person_id and lead_id`() =
        runBlocking<Unit> {
            val someNationalId = "01234567890"
            val otp = "12345"
            val accessToken = RangeUUID.generate().toString()

            coEvery {
                personInternalReferenceService.getForPerson(personLogin.personId)
            } returns Result.failure(Exception())

            val member = TestModelFactory.buildMember(personId = person.id)
            coEvery { memberService.findFirstMembership(person.id) } returns member.success()

            every {
                Authenticator.generateCustomToken(
                    userId = personLogin.personId.toString(),
                    kClass = Person::class,
                    extraClaims = mapOf(
                        Authenticator.USER_SUB_TYPE_KEY to "MEMBER",
                        Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
                        Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString()
                    )
                )
            } returns accessToken

            coEvery { personLoginService.signInByNationalId(someNationalId, otp) } returns Result.success(
                personLogin
            )
            coEvery { userTypeService.get(person.id, refreshed = true) } returns UserType.MEMBER.success()

            val expectedAuthResult = AuthResponse(
                customToken = accessToken,
                person = person
            )
            val authResult = authService.authenticateByNationalIdAndGetPerson(someNationalId, otp)
            assertThat(authResult).isSuccessWithData(expectedAuthResult)

            LocalProducer.hasEvent(UserAuthenticatedEvent.name)
        }

    @Test
    fun `#authenticateByNationalIdAndGetPerson should return invalid credentials when login process failed`() = runBlocking<Unit> {
        val someNationalId = "01234567890"
        val otp = "12345"

        coEvery { personLoginService.signInByNationalId(someNationalId, otp) } returns Result.failure(
            LoginNotFoundException("Invalid credentials for national id '$someNationalId'")
        )
        val authResult = authService.authenticateByNationalIdAndGetPerson(someNationalId, otp)

        coVerifyNone {
            Authenticator.generateCustomToken(
                userId = any(),
                kClass = any()
            )
        }
        coVerifyNone {
            personService.get(any())
        }

        assertThat(authResult).isFailureOfType(LoginNotFoundException::class)
    }

    @Test
    fun `#authenticateByNationalIdAndGetPerson should find Person, generate and return token`() = runBlocking<Unit> {
        val otp = "**********"
        coEvery { personLoginService.signInByNationalId(someNationalId, otp) } returns personLogin.success()

        val userType = UserType.NON_MEMBER
        coEvery { userTypeService.get(personLogin.personId, refreshed = true) } returns userType.success()

        val personInternalReference = TestModelFactory.buildPersonInternalReference(personId = person.id)
        coEvery { personInternalReferenceService.getForPerson(person.id) } returns personInternalReference.success()

        val member = TestModelFactory.buildMember(personId = person.id)
        coEvery { memberService.findFirstMembership(person.id) } returns member.success()

        val claims = mapOf(
            Authenticator.USER_SUB_TYPE_KEY to userType.toString(),
            Authenticator.CHANNEL_PERSON_ID_KEY to personInternalReference.channelPersonId.toString(),
            Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
            Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString(),
        )

        val expectedToken = "SomeToken"
        every {
            Authenticator.generateCustomToken(
                person.id.toString(),
                Person::class,
                claims
            )
        } returns expectedToken

        val expectedAuthResult = AuthResponse(
            customToken = expectedToken,
            person = person
        )

        val actualToken = authService.authenticateByNationalIdAndGetPerson(someNationalId, otp)
        assertThat(actualToken).isSuccessWithData(expectedAuthResult)

        LocalProducer.hasEvent(UserAuthenticatedEvent.name)

    }

    @Test
    fun `#authenticateByNationalIdAndGetPerson shouldn't find Person and return exception`() = runBlocking<Unit> {
        val otp = "**********"
        coEvery {
            personLoginService.signInByNationalId(
                someNationalId,
                otp
            )
        } returns NotFoundException("Credentials not found").failure()

        val actualToken = authService.authenticateByNationalIdAndGetPerson(someNationalId, otp)
        assertThat(actualToken).isFailureOfType(NotFoundException::class)

        coVerify(exactly = 0) { userTypeService.get(any(), any()) }
        coVerify(exactly = 0) { personInternalReferenceService.getForPerson(any()) }
        coVerify(exactly = 0) { Authenticator.generateCustomToken(person.id.toString(), Person::class, any()) }
    }

    @Test
    fun `#getPersonInfoUnauthenticated should return person info`() = runBlocking<Unit> {
        val personId = PersonId()

        coEvery { personService.get(personId) } returns person.success()

        val authResponse = authService.getPersonInfoUnauthenticated(personId.toString())

        coVerify(exactly = 0) {
            Authenticator.generateCustomToken(
                userId = any(),
                kClass = any()
            )
        }

        assertThat(authResponse.name).isEqualTo("José")
        assertThat(authResponse.email).isEqualTo("<EMAIL>")
        assertThat(authResponse.nationalId).isEqualTo("609.048.950-68")
    }

    @Test
    fun `#generateToken generate and return a token with HEALTHCARE_TEAM_TYPE defined as LEAGUE when Member activationDate is null`() =
        runBlocking<Unit> {
            val userType = UserType.NON_MEMBER
            coEvery { userTypeService.get(person.id, refreshed = true) } returns userType.success()

            val personInternalReference = TestModelFactory.buildPersonInternalReference(personId = person.id)
            coEvery { personInternalReferenceService.getForPerson(person.id) } returns personInternalReference.success()

            val member = TestModelFactory.buildMember(personId = person.id)
            coEvery { memberService.findFirstMembership(person.id) } returns member.success()

            val claims = mapOf(
                Authenticator.USER_SUB_TYPE_KEY to userType.toString(),
                Authenticator.CHANNEL_PERSON_ID_KEY to personInternalReference.channelPersonId.toString(),
                Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
                Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString(),
            )

            val expectedToken = "SomeToken"
            every {
                Authenticator.generateCustomToken(
                    person.id.toString(),
                    Person::class,
                    claims
                )
            } returns expectedToken

            val actualToken = authService.generateToken(person.id)
            assertThat(actualToken).isSuccessWithData(expectedToken)

            LocalProducer.hasEvent(UserAuthenticatedEvent.name)
        }

    @Test
    fun `#generateToken generate and return a token with HEALTHCARE_TEAM_TYPE defined as LEAGUE when findFirstMembership throws an exception`() =
        runBlocking<Unit> {
            val userType = UserType.NON_MEMBER
            coEvery { userTypeService.get(person.id, refreshed = true) } returns userType.success()

            val personInternalReference = TestModelFactory.buildPersonInternalReference(personId = person.id)
            coEvery { personInternalReferenceService.getForPerson(person.id) } returns personInternalReference.success()

            coEvery { memberService.findFirstMembership(person.id) } returns Result.failure(Exception())

            val claims = mapOf(
                Authenticator.USER_SUB_TYPE_KEY to userType.toString(),
                Authenticator.CHANNEL_PERSON_ID_KEY to personInternalReference.channelPersonId.toString(),
                Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
                Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString(),
            )

            val expectedToken = "SomeToken"
            every {
                Authenticator.generateCustomToken(
                    person.id.toString(),
                    Person::class,
                    claims
                )
            } returns expectedToken

            val actualToken = authService.generateToken(person.id)
            assertThat(actualToken).isSuccessWithData(expectedToken)

            LocalProducer.hasEvent(UserAuthenticatedEvent.name)

        }

    @Test
    fun `#generateToken generate and return a token with HEALTHCARE_TEAM_TYPE defined as LEAGUE when Member activationDate is after the date defined in the feature flag`() =
        runBlocking<Unit> {
            val userType = UserType.NON_MEMBER
            coEvery { userTypeService.get(person.id, refreshed = true) } returns userType.success()

            val personInternalReference = TestModelFactory.buildPersonInternalReference(personId = person.id)
            coEvery { personInternalReferenceService.getForPerson(person.id) } returns personInternalReference.success()

            val member = TestModelFactory.buildMember(
                personId = person.id,
                status = MemberStatus.ACTIVE,
                activationDate = LocalDateTime.parse("2022-09-26T18:07:57")
            )
            coEvery { memberService.findFirstMembership(person.id) } returns member.success()

            val claims = mapOf(
                Authenticator.USER_SUB_TYPE_KEY to userType.toString(),
                Authenticator.CHANNEL_PERSON_ID_KEY to personInternalReference.channelPersonId.toString(),
                Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
                Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString(),
            )

            val expectedToken = "SomeToken"
            every {
                Authenticator.generateCustomToken(
                    person.id.toString(),
                    Person::class,
                    claims
                )
            } returns expectedToken

            withFeatureFlag(FeatureNamespace.MEMBERSHIP, "channel_segmentation_for_new_journey_started_at", "2022-09-19") {
                val actualToken = authService.generateToken(person.id)
                assertThat(actualToken).isSuccessWithData(expectedToken)
            }

            LocalProducer.hasEvent(UserAuthenticatedEvent.name)
        }


    @Test
    fun `#generateToken generate and return a token with HEALTHCARE_TEAM_TYPE defined as STANDARD when Member activationDate is before the date defined in the feature flag`() =
        runBlocking<Unit> {
            val userType = UserType.NON_MEMBER
            coEvery { userTypeService.get(person.id, refreshed = true) } returns userType.success()

            val personInternalReference = TestModelFactory.buildPersonInternalReference(personId = person.id)
            coEvery { personInternalReferenceService.getForPerson(person.id) } returns personInternalReference.success()

            val member = TestModelFactory.buildMember(
                personId = person.id,
                status = MemberStatus.ACTIVE,
                activationDate = LocalDateTime.parse("2022-09-26T18:27:13")
            )
            coEvery { memberService.findFirstMembership(person.id) } returns member.success()

            val claims = mapOf(
                Authenticator.USER_SUB_TYPE_KEY to userType.toString(),
                Authenticator.CHANNEL_PERSON_ID_KEY to personInternalReference.channelPersonId.toString(),
                Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
                Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.STANDARD.toString(),
            )

            val expectedToken = "SomeToken"
            every {
                Authenticator.generateCustomToken(
                    person.id.toString(),
                    Person::class,
                    claims
                )
            } returns expectedToken

            val actualToken = authService.generateToken(person.id)
            assertThat(actualToken).isSuccessWithData(expectedToken)

            LocalProducer.hasEvent(UserAuthenticatedEvent.name)
        }

    @Test
    fun `#authenticate should return authenticated when sign in was successful and got channel_person_id`() =
        runBlocking<Unit> {
            val someNationalId = "01234567890"
            val otp = "12345"
            val accessToken = RangeUUID.generate().toString()
            val personInternalReference = PersonInternalReference(personLogin.personId)

            coEvery {
                personInternalReferenceService.getForPerson(personLogin.personId)
            } returns Result.success(personInternalReference)

            val member = TestModelFactory.buildMember(personId = person.id)
            coEvery { memberService.findFirstMembership(person.id) } returns member.success()

            every {
                Authenticator.generateCustomToken(
                    userId = personLogin.personId.toString(),
                    kClass = Person::class,
                    extraClaims = mapOf(
                        Authenticator.CHANNEL_PERSON_ID_KEY to personInternalReference.channelPersonId.toString(),
                        Authenticator.USER_SUB_TYPE_KEY to "MEMBER",
                        Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
                        Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString(),
                    )
                )
            } returns accessToken


            coEvery { personLoginService.signInByNationalId(someNationalId, otp) } returns Result.success(personLogin)
            coEvery { userTypeService.get(person.id, refreshed = true) } returns UserType.MEMBER.success()

            val authResult = authService.authenticateByNationalId(someNationalId, otp)
            assertThat(authResult).isSuccessWithData(accessToken)

            LocalProducer.hasEvent(UserAuthenticatedEvent.name)
        }

    @Test
    fun `#authenticate should return authenticated when sign in was successful and did not get channel_person_id`() =
        runBlocking<Unit> {
            val someNationalId = "01234567890"
            val otp = "12345"
            val accessToken = RangeUUID.generate().toString()

            coEvery {
                personInternalReferenceService.getForPerson(personLogin.personId)
            } returns Result.failure(Exception())

            val member = TestModelFactory.buildMember(personId = person.id)
            coEvery { memberService.findFirstMembership(person.id) } returns member.success()

            every {
                Authenticator.generateCustomToken(
                    userId = personLogin.personId.toString(),
                    kClass = Person::class,
                    extraClaims = mapOf(
                        Authenticator.USER_SUB_TYPE_KEY to "MEMBER",
                        Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
                        Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString()
                    )
                )
            } returns accessToken

            coEvery { personLoginService.signInByNationalId(someNationalId, otp) } returns Result.success(
                personLogin
            )
            coEvery { userTypeService.get(person.id, refreshed = true) } returns UserType.MEMBER.success()

            val authResult = authService.authenticateByNationalId(someNationalId, otp)
            assertThat(authResult).isSuccessWithData(accessToken)

            LocalProducer.hasEvent(UserAuthenticatedEvent.name)
        }

    @Test
    fun `#authenticate should return invalid credentials when login process failed`() = runBlocking<Unit> {
        val someNationalId = "01234567890"
        val otp = "12345"

        coEvery { personLoginService.signInByNationalId(someNationalId, otp) } returns Result.failure(
            InvalidCredentialsException("Invalid credentials for national id '$someNationalId'")
        )
        val authResult = authService.authenticateByNationalId(someNationalId, otp)

        coVerify(exactly = 0) {
            Authenticator.generateCustomToken(
                userId = any(),
                kClass = any()
            )
        }

        assertThat(authResult).isFailureOfType(InvalidCredentialsException::class)
    }

    @Test
    fun `#authenticateByNationalId should find Person, generate and return token`() = runBlocking<Unit> {
        val otp = "**********"
        coEvery { personLoginService.signInByNationalId(someNationalId, otp) } returns personLogin.success()

        val userType = UserType.NON_MEMBER
        coEvery { userTypeService.get(personLogin.personId, refreshed = true) } returns userType.success()

        val personInternalReference = TestModelFactory.buildPersonInternalReference(personId = person.id)
        coEvery { personInternalReferenceService.getForPerson(person.id) } returns personInternalReference.success()

        val member = TestModelFactory.buildMember(personId = person.id)
        coEvery { memberService.findFirstMembership(person.id) } returns member.success()

        val claims = mapOf(
            Authenticator.USER_SUB_TYPE_KEY to userType.toString(),
            Authenticator.CHANNEL_PERSON_ID_KEY to personInternalReference.channelPersonId.toString(),
            Authenticator.CREATED_AT_KEY to nowDateTime.toString(),
            Authenticator.HEALTHCARE_TEAM_TYPE to HealthcareTeam.Type.LEAGUE.toString(),
        )

        val expectedToken = "SomeToken"
        every {
            Authenticator.generateCustomToken(
                person.id.toString(),
                Person::class,
                claims
            )
        } returns expectedToken

        val actualToken = authService.authenticateByNationalId(someNationalId, otp)
        assertThat(actualToken).isSuccessWithData(expectedToken)

        LocalProducer.hasEvent(UserAuthenticatedEvent.name)

    }

    @Test
    fun `#authenticateByNationalId shouldn't find Person and return exception`() = runBlocking<Unit> {
        val otp = "**********"
        coEvery {
            personLoginService.signInByNationalId(
                someNationalId,
                otp
            )
        } returns NotFoundException("Credentials not found").failure()

        val actualToken = authService.authenticateByNationalId(someNationalId, otp)
        assertThat(actualToken).isFailureOfType(NotFoundException::class)

        coVerify(exactly = 0) { userTypeService.get(any(), any()) }
        coVerify(exactly = 0) { personInternalReferenceService.getForPerson(any()) }
        coVerify(exactly = 0) { Authenticator.generateCustomToken(person.id.toString(), Person::class, any()) }
    }
}
