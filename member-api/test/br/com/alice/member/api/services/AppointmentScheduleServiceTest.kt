package br.com.alice.member.api.services

import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.ChannelDocument
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDate
import br.com.alice.common.helpers.returns
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.CalendarProviderUnit
import br.com.alice.data.layer.models.FeatureConfig
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import br.com.alice.data.layer.models.PersonTask
import br.com.alice.data.layer.models.TaskType
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.ProviderScheduleOption
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.PersonCalendlyService
import br.com.alice.schedule.client.StaffAvailabilityService
import br.com.alice.schedule.model.SlotForSpecificDuration
import br.com.alice.schedule.model.StaffAvailabilityResponse
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertFailsWith
import kotlin.test.assertEquals

class AppointmentScheduleServiceTest {

    private val personService: PersonService = mockk()
    private val staffService: StaffService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService = mockk()
    private val channelService: ChannelService = mockk()
    private val schedulingUrlBuilder: SchedulingUrlBuilder = mockk()
    private val personCalendlyService: PersonCalendlyService = mockk()
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService = mockk()
    private val staffAvailabilityService: StaffAvailabilityService = mockk()

    private val appointmentScheduleService = AppointmentScheduleService(
        personService,
        staffService,
        healthProfessionalService,
        appointmentScheduleOptionService,
        channelService,
        schedulingUrlBuilder,
        personCalendlyService,
        appointmentScheduleEventTypeService,
        staffAvailabilityService
    )

    private val person = TestModelFactory.buildPerson()
    private val staff = TestModelFactory.buildStaff()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(staffId = staff.id)
    private val adultDigitalPoolAppointmentScheduleEventTypeId = RangeUUID.generate().toString()
    private val childOnSitePoolAppointmentScheduleEventTypeId = RangeUUID.generate().toString()
    private val personCalendly = TestModelFactory.buildPersonCalendly(person.id)
    private val options = listOf(
        TestModelFactory.buildAppointmentScheduleOption(
            title = "Bioimpedancia",
            description = null,
            type = AppointmentScheduleType.TEST,
            calendarUrl = "calendarUrl"
        )
    )
    private val structuredAddress = TestModelFactory.buildStructuredAddress()
    private val appointmentScheduleEventType = TestModelFactory.buildAppointmentScheduleEventType()
    private val providerUnit1 = TestModelFactory.buildProviderUnit().copy(address = structuredAddress)
    private val providerUnit2 = TestModelFactory.buildProviderUnit().copy(address = structuredAddress)
    private val optionsWithCalendarProviderUnits = listOf(
        TestModelFactory.buildAppointmentScheduleOption(
            title = "Bioimpedancia",
            description = null,
            type = AppointmentScheduleType.TEST,
            calendarUrl = "calendarUrl",
            calendarProviderUnits = listOf(
                CalendarProviderUnit(
                    providerUnit1.id,
                    "url1",
                    address = "address",
                    name = "Casa Alice",
                ),
                CalendarProviderUnit(
                    providerUnit2.id,
                    "url2",
                    address = "address",
                    name = "Casa Alice",
                ),
            ),
        )
    )

    private fun feature(value: String) = FeatureConfig(
        namespace = FeatureNamespace.PAYMENTS,
        key = "return_calendar_provider_units",
        type = FeatureType.BOOLEAN,
        description = "description",
        active = true,
        value = value
    )

    companion object {
        @JvmStatic
        @BeforeAll
        fun setup() {
            FeatureService.FeatureConfigCache.put(
                FeatureConfig(
                    namespace = FeatureNamespace.SCHEDULE,
                    key = "should-check-member-age-to-get-appointment-schedule-options",
                    type = FeatureType.BOOLEAN,
                    value = "true",
                    description = "",
                    active = true
                )
            )
        }

        @JvmStatic
        @AfterAll
        fun tearDown() {
            FeatureService.FeatureConfigCache.clear()
        }

    }

    @BeforeTest
    fun before() {
        coEvery { personService.get(person.id) } returns person
        coEvery { staffService.get(staff.id) } returns staff
        coEvery { appointmentScheduleEventTypeService.get(appointmentScheduleEventType.id) } returns appointmentScheduleEventType
    }

    @AfterTest
    fun after() = confirmVerified(
        personService,
        staffService,
        healthProfessionalService,
        appointmentScheduleOptionService,
        channelService,
        schedulingUrlBuilder,
        personCalendlyService,
        appointmentScheduleEventTypeService,
        staffAvailabilityService
    )

    @Test
    fun `#getAppointmentScheduleCalendar should return calendar with URL filled`() = runBlocking {

        val task = PersonTask(person.id, "Bioimpedancia", null, TaskType.TEST_REQUEST)
        val file = AliceFile(RangeUUID.generate(), "test", "url", "jpg")
        val expectedFilePath = "${file.id}.${file.type}"
        coEvery { appointmentScheduleOptionService.listByPerson(person.id) } returns options

        coEvery { schedulingUrlBuilder.buildScheduleUrl(any(), any(), any(), any()) } returns "calendarUrl"
        coEvery {
            schedulingUrlBuilder.buildScheduleOptions(
                any(),
                any(),
                any(),
                any(),
            )
        } returns null
        coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

        val calendar = appointmentScheduleService.getAppointmentScheduleCalendar(
            person.id,
            AppointmentScheduleType.TEST,
            task,
            file
        )

        assertThat(calendar.calendarUrl).contains("calendarUrl")
        assertThat(calendar.filePath).isEqualTo(expectedFilePath)
        coVerifyOnce {
            personService.get(any())
            personCalendlyService.getOrCreate(any())
            appointmentScheduleOptionService.listByPerson(any())
            schedulingUrlBuilder.buildScheduleUrl(any(), any(), any(), any())
            schedulingUrlBuilder.buildScheduleOptions(any(), any(), any(), any())
        }

    }

    @Test
    fun `#getAppointmentScheduleCalendar should return calendar with channel created`() = runBlocking {

        val task = PersonTask(person.id, "Exame que não possui Calendar", null, TaskType.TEST_REQUEST)
        val file = AliceFile(RangeUUID.generate(), "test", "url", "jpg")
        val expectedFilePath = "${file.id}.${file.type}"
        coEvery { appointmentScheduleOptionService.listByPerson(person.id) } returns options
        coEvery { channelService.addChatV2(CreateChatRequest(personId = person.id)) } returns ChannelDocument(
            id = "channelId",
            personId = person.id.toString(),
            channelPersonId = ""
        )
        coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

        val calendar = appointmentScheduleService.getAppointmentScheduleCalendar(
            person.id,
            AppointmentScheduleType.TEST,
            task,
            file
        )

        assertThat(calendar.calendarUrl).isNull()
        assertThat(calendar.filePath).isEqualTo(expectedFilePath)
        assertThat(calendar.navigation).isNotNull
        assertThat(calendar.navigation!!.mobileRoute).isEqualTo(MobileRouting.CHANNEL)
        assertThat(calendar.navigation!!.link).isNull()
        assertThat(calendar.navigation!!.properties).containsKey("channel_id")
        assertThat(calendar.navigation!!.properties).containsKey("url")
        assertThat(calendar.navigation!!.properties?.get("channel_id")).isEqualTo("channelId")
        assertThat(calendar.navigation!!.properties?.get("url")).isEqualTo(file.url)
        assertThat(calendar.navigation!!.properties?.get("type")).isEqualTo(file.type)

        coVerifyOnce {
            personService.get(any())
            personCalendlyService.getOrCreate(any())
            appointmentScheduleOptionService.listByPerson(any())
            channelService.addChatV2(any())
        }
    }

    @Test
    fun `#getAppointmentScheduleCalendar should return calendar with schedule options if it have calendarProviderUnits`() =
        runBlocking {

            val task = PersonTask(person.id, "Bioimpedancia", null, TaskType.TEST_REQUEST)
            val file = AliceFile(RangeUUID.generate(), "test", "url", "jpg")
            val expectedFilePath = "${file.id}.${file.type}"
            coEvery { appointmentScheduleOptionService.listByPerson(person.id) } returns optionsWithCalendarProviderUnits
            coEvery { schedulingUrlBuilder.buildScheduleUrl(any(), any(), any(), any()) } returns "calendarUrl"
            coEvery { schedulingUrlBuilder.buildScheduleUrl(any(), "url1", any(), any()) } returns "url1"
            coEvery { schedulingUrlBuilder.buildScheduleUrl(any(), "url2", any(), any()) } returns "url2"
            coEvery {
                schedulingUrlBuilder.buildScheduleOptions(
                    any(),
                    any(),
                    any(),
                    any(),
                )
            } returns listOf(
                ProviderScheduleOption(
                    name = providerUnit1.name,
                    address = providerUnit1.address!!.formattedAddress(),
                    calendarUrl = optionsWithCalendarProviderUnits.first().calendarProviderUnits?.first()!!.calendarUrl
                ),
                ProviderScheduleOption(
                    name = providerUnit2.name,
                    address = providerUnit2.address!!.formattedAddress(),
                    calendarUrl = optionsWithCalendarProviderUnits.first().calendarProviderUnits?.last()!!.calendarUrl
                ),
            )

            FeatureService.FeatureConfigCache.put(feature("true"))

            coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

            val calendar = appointmentScheduleService.getAppointmentScheduleCalendar(
                person.id,
                AppointmentScheduleType.TEST,
                task,
                file
            )

            assertThat(calendar.filePath).isEqualTo(expectedFilePath)
            assertThat(calendar.scheduleOptions).isEqualTo(
                listOf(
                    ProviderScheduleOption(
                        name = providerUnit1.name,
                        address = providerUnit1.address!!.formattedAddress(),
                        calendarUrl = optionsWithCalendarProviderUnits.first().calendarProviderUnits?.first()!!.calendarUrl
                    ),
                    ProviderScheduleOption(
                        name = providerUnit2.name,
                        address = providerUnit1.address!!.formattedAddress(),
                        calendarUrl = optionsWithCalendarProviderUnits.first().calendarProviderUnits?.last()!!.calendarUrl
                    ),
                )
            )
            coVerifyOnce {
                personService.get(any())
                personCalendlyService.getOrCreate(any())
                appointmentScheduleOptionService.listByPerson(any())
                schedulingUrlBuilder.buildScheduleUrl(any(), any(), any(), any())
                schedulingUrlBuilder.buildScheduleOptions(any(), any(), any(), any())
            }
        }

    @Test
    fun `#getAppointmentScheduleCalendar should return calendar with no schedule options if FF is off`() = runBlocking {

        val task = PersonTask(person.id, "Bioimpedancia", null, TaskType.TEST_REQUEST)
        val file = AliceFile(RangeUUID.generate(), "test", "url", "jpg")
        val expectedFilePath = "${file.id}.${file.type}"
        coEvery { appointmentScheduleOptionService.listByPerson(person.id) } returns optionsWithCalendarProviderUnits
        coEvery { schedulingUrlBuilder.buildScheduleUrl(any(), any(), any(), any()) } returns "calendarUrl"
        coEvery {
            schedulingUrlBuilder.buildScheduleOptions(
                any(),
                any(),
                any(),
                any(),
            )
        } returns emptyList()

        FeatureService.FeatureConfigCache.put(feature("false"))

        coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly

        val calendar = appointmentScheduleService.getAppointmentScheduleCalendar(
            person.id,
            AppointmentScheduleType.TEST,
            task,
            file
        )

        assertThat(calendar.filePath).isEqualTo(expectedFilePath)
        assertThat(calendar.scheduleOptions).isEqualTo(emptyList<CalendarProviderUnit>())
        coVerifyOnce {
            personService.get(any())
            personCalendlyService.getOrCreate(any())
            appointmentScheduleOptionService.listByPerson(any())
            schedulingUrlBuilder.buildScheduleUrl(any(), any(), any(), any())
            schedulingUrlBuilder.buildScheduleOptions(any(), any(), any(), any())
        }
    }

    @Test
    fun `#getStaffAvailabilityForStaffForPeriod should get availability for staff who not have booked vacation`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "adult_digital_pool_appointment_schedule_event",
                value = adultDigitalPoolAppointmentScheduleEventTypeId
            ) {
                val fromDate = LocalDate.of(2025, 1, 1)
                val toDate = LocalDate.of(2025, 1, 31)
                val slots = buildSlots(fromDate, toDate)

                coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional

                buildMocksForSlots(
                    startDateTime = fromDate.atBeginningOfTheDay(),
                    endDateTime = toDate.atEndOfTheDay(),
                    slots = slots,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    appointmentScheduleEventType = appointmentScheduleEventType
                )

                val availability = appointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                    staffId = staff.id,
                    personId = person.id.toString(),
                    fromDate = fromDate,
                    toDate = toDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    providerUnitId = providerUnit1.id.toString()
                )

                val expected = StaffAvailabilityResponse(slots.sortedBy { it.startTime })
                assertThat(availability).isSuccessWithData(expected)
                coVerifyOnce {
                    staffService.get(any())
                    healthProfessionalService.findByStaffId(any())
                    appointmentScheduleEventTypeService.get(any())
                    staffAvailabilityService.getForStaffAndEventTypeAndPeriod(any(), any(), any(), any(), any(), any())
                }
                coVerifyNone {
                    staffAvailabilityService.getForEventTypeAndPeriod(
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                }
            }
        }

    @Test
    fun `#getStaffAvailabilityForStaffForPeriod should get availability for staff who have booked vacation with period overlap`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "child_on_site_pool_appointment_schedule_event",
                value = childOnSitePoolAppointmentScheduleEventTypeId
            ) {
                val fromDate = LocalDate.of(2025, 1, 1)
                val toDate = LocalDate.of(2025, 1, 31)
                val hpOnVacationStart = LocalDate.of(2025, 1, 10)
                val hpOnVacationUntil = LocalDate.of(2025, 1, 15)
                val firstSlots = buildSlots(fromDate, hpOnVacationStart.minusDays(1))
                val lastSlots = buildSlots(hpOnVacationUntil.plusDays(1), toDate)
                val onVacationSlots =
                    buildOnVacationSlots(
                        hpOnVacationStart,
                        hpOnVacationUntil,
                        childOnSitePoolAppointmentScheduleEventTypeId.toUUID()
                    )

                coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional.copy(
                    onVacationStart = hpOnVacationStart.atBeginningOfTheDay(),
                    onVacationUntil = hpOnVacationUntil.atEndOfTheDay()
                )

                buildMocksForSlots(
                    startDateTime = fromDate.atBeginningOfTheDay(),
                    endDateTime = hpOnVacationStart.atEndOfTheDay().minusDays(1),
                    slots = firstSlots,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    appointmentScheduleEventType = appointmentScheduleEventType
                )
                buildMocksForSlots(
                    startDateTime = hpOnVacationStart.atBeginningOfTheDay(),
                    endDateTime = hpOnVacationUntil.atEndOfTheDay(),
                    slots = onVacationSlots,
                    appointmentScheduleEventTypeId = childOnSitePoolAppointmentScheduleEventTypeId.toUUID(),
                    appointmentScheduleEventType = null,
                    isVacationSlots = true
                )
                buildMocksForSlots(
                    startDateTime = hpOnVacationUntil.atBeginningOfTheDay().plusDays(1),
                    endDateTime = toDate.atEndOfTheDay(),
                    slots = lastSlots,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    appointmentScheduleEventType = appointmentScheduleEventType
                )

                val availability = appointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                    staffId = staff.id,
                    personId = person.id.toString(),
                    fromDate = fromDate,
                    toDate = toDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    providerUnitId = providerUnit1.id.toString()
                )

                val allSlots = firstSlots.plus(onVacationSlots).plus(lastSlots).sortedBy { it.startTime }
                val expected = StaffAvailabilityResponse(allSlots)
                assertThat(availability).isSuccessWithData(expected)
                coVerifyOnce {
                    personService.get(any())
                    staffService.get(any())
                    healthProfessionalService.findByStaffId(any())
                    appointmentScheduleEventTypeService.get(any())
                    staffAvailabilityService.getForEventTypeAndPeriod(any(), any(), any(), any(), any(), any())
                }
                coVerify(exactly = 2) {
                    staffAvailabilityService.getForStaffAndEventTypeAndPeriod(any(), any(), any(), any(), any(), any())
                }
            }
        }

    @Test
    fun `#getStaffAvailabilityForStaffForPeriod should get availability for staff who have booked vacation without period overlap`() =
        runBlocking {
            withFeatureFlag(
                namespace = FeatureNamespace.SCHEDULE,
                key = "child_on_site_pool_appointment_schedule_event",
                value = childOnSitePoolAppointmentScheduleEventTypeId
            ) {
                val fromDate = LocalDate.of(2025, 1, 1)
                val toDate = LocalDate.of(2025, 1, 31)
                val hpOnVacationStart = LocalDate.of(2025, 2, 10)
                val hpOnVacationUntil = LocalDate.of(2025, 2, 15)
                val slots = buildSlots(fromDate, toDate)

                coEvery { healthProfessionalService.findByStaffId(staff.id) } returns healthProfessional.copy(
                    onVacationStart = hpOnVacationStart.atBeginningOfTheDay(),
                    onVacationUntil = hpOnVacationUntil.atEndOfTheDay()
                )

                buildMocksForSlots(
                    startDateTime = fromDate.atBeginningOfTheDay(),
                    endDateTime = toDate.atEndOfTheDay(),
                    slots = slots,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    appointmentScheduleEventType = appointmentScheduleEventType
                )

                val availability = appointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                    staffId = staff.id,
                    personId = person.id.toString(),
                    fromDate = fromDate,
                    toDate = toDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    providerUnitId = providerUnit1.id.toString()
                )

                val expected = StaffAvailabilityResponse(slots.sortedBy { it.startTime })
                assertThat(availability).isSuccessWithData(expected)
                coVerifyOnce {
                    staffService.get(any())
                    healthProfessionalService.findByStaffId(any())
                    appointmentScheduleEventTypeService.get(any())
                    staffAvailabilityService.getForStaffAndEventTypeAndPeriod(any(), any(), any(), any(), any(), any())
                }
                coVerifyNone {
                    staffAvailabilityService.getForEventTypeAndPeriod(
                        any(),
                        any(),
                        any(),
                        any(),
                        any(),
                        any()
                    )
                }
            }
        }

    @Test
    fun `#getStaffAvailabilityForStaffForPeriod should get empty availability for staff when it is not active`() =
        runBlocking {
            val fromDate = LocalDate.of(2025, 1, 1)
            val toDate = LocalDate.of(2025, 1, 31)

            coEvery { staffService.get(staff.id) } returns staff.copy(active = false)

            val availability = appointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                staffId = staff.id,
                personId = person.id.toString(),
                fromDate = fromDate,
                toDate = toDate,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                providerUnitId = providerUnit1.id.toString()
            )

            val expected = StaffAvailabilityResponse(listOf())
            assertThat(availability).isSuccessWithData(expected)

            coVerifyOnce { staffService.get(any()) }
            coVerify {
                healthProfessionalService wasNot called
                appointmentScheduleEventTypeService wasNot called
                staffAvailabilityService wasNot called
            }
        }

    @Test
    fun `#getStaffAvailabilityForStaffForPeriod should get error getting staff`() = runBlocking {
        val fromDate = LocalDate.of(2025, 1, 1)
        val toDate = LocalDate.of(2025, 1, 31)

        assertFailsWith<Exception> {
            coEvery { staffService.get(staff.id) } returns Exception("error")

            appointmentScheduleService.getStaffAvailabilityForStaffForPeriod(
                staffId = staff.id,
                personId = person.id.toString(),
                fromDate = fromDate,
                toDate = toDate,
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                providerUnitId = providerUnit1.id.toString()
            ).get()
        }

        coVerifyOnce { staffService.get(any()) }
        coVerify {
            healthProfessionalService wasNot called
            appointmentScheduleEventTypeService wasNot called
            staffAvailabilityService wasNot called
        }
    }

    @Test
    fun `#getEventIdIfHealthProfessionalIsOnVacation should return vacation id event if health professional is in vacation period`() = mockLocalDate { fromDate ->
        withFeatureFlag(
            namespace = FeatureNamespace.SCHEDULE,
            key = "child_on_site_pool_appointment_schedule_event",
            value = childOnSitePoolAppointmentScheduleEventTypeId
        ) {
            val toDate = fromDate.plusDays(15)

            val healthProfessional = TestModelFactory.buildHealthProfessional(
                onVacationStart = LocalDateTime.of(fromDate, LocalTime.now()),
                onVacationUntil = LocalDateTime.of(toDate, LocalTime.now()),
            )

            coEvery { healthProfessionalService.findByStaffId(healthProfessional.staffId) } returns healthProfessional
            coEvery { personService.get(person.id) } returns person

            val result = appointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(person.id, healthProfessional.staffId, 30)

            assertEquals(childOnSitePoolAppointmentScheduleEventTypeId.toUUID(), result.get())

            coVerifyOnce {
                healthProfessionalService.findByStaffId(any())
                personService.get(any())
            }
        }
    }

    @Test
    fun `#getEventIdIfHealthProfessionalIsOnVacation should return null if health professional is NOT in vacation period`() = mockLocalDate { fromDate ->
        withFeatureFlag(
            namespace = FeatureNamespace.SCHEDULE,
            key = "child_on_site_pool_appointment_schedule_event",
            value = childOnSitePoolAppointmentScheduleEventTypeId
        ) {
            val toDate = fromDate.plusDays(15)

            val healthProfessional = TestModelFactory.buildHealthProfessional(
                onVacationStart = LocalDateTime.of(fromDate.plusMonths(3), LocalTime.now()),
                onVacationUntil = LocalDateTime.of(toDate.plusMonths(3), LocalTime.now()),
            )

            coEvery { healthProfessionalService.findByStaffId(healthProfessional.staffId) } returns healthProfessional

            val result = appointmentScheduleService.getEventIdIfHealthProfessionalIsOnVacation(person.id, healthProfessional.staffId, 30)

            assertEquals(null, result.get())

            coVerifyOnce {
                healthProfessionalService.findByStaffId(any())
            }
            coVerifyNone {
                personService.get(any())
            }
        }
    }

    private fun buildMocksForSlots(
        startDateTime: LocalDateTime,
        endDateTime: LocalDateTime,
        slots: List<SlotForSpecificDuration>,
        appointmentScheduleEventTypeId: UUID,
        appointmentScheduleEventType: AppointmentScheduleEventType?,
        isVacationSlots: Boolean = false
    ) = if (isVacationSlots) {
        coEvery {
            staffAvailabilityService.getForEventTypeAndPeriod(
                startDate = startDateTime.toLocalDate(),
                endDate = endDateTime.toLocalDate(),
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                appointmentScheduleEventType = appointmentScheduleEventType,
                providerUnitIds = listOf(providerUnit1.id),
                personId = person.id
            )
        } returns slots
    } else {
        coEvery {
            staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                staffId = staff.id,
                startDateTime = startDateTime,
                endDateTime = endDateTime,
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                appointmentScheduleEventType = appointmentScheduleEventType,
                providerUnitId = providerUnit1.id
            )
        } returns slots
    }

    private fun buildOnVacationSlots(
        onVacationStart: LocalDate,
        onVacationUntil: LocalDate,
        appointmentScheduleEventTypeId: UUID
    ): List<SlotForSpecificDuration> =
        buildSlots(
            onVacationStart,
            onVacationUntil
        ).map { slot -> slot.copy(poolEventTypeId = appointmentScheduleEventTypeId) }

    private fun buildSlots(fromDate: LocalDate, toDate: LocalDate) = listOf(
        SlotForSpecificDuration(
            startTime = fromDate.atBeginningOfTheDay(),
            endTime = toDate.atEndOfTheDay(),
            durationInMinutes = 30
        )
    )

}
