package br.com.alice.member.api.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CoPaymentChargeType
import br.com.alice.data.layer.models.CoPaymentTierPrice
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.RefundEventType
import br.com.alice.data.layer.models.RefundType
import br.com.alice.data.layer.models.TierType
import br.com.alice.member.api.models.MemberProductDetails
import br.com.alice.person.client.MemberService
import br.com.alice.product.client.CoPaymentCostInfoService
import br.com.alice.product.client.PersonCoPaymentCostInfo
import br.com.alice.product.client.ProductService
import br.com.alice.product.client.RefundCostService
import br.com.alice.product.model.ProductWithProviders
import br.com.alice.product.transport_model.RefundCost
import br.com.alice.product.transport_model.RefundCostPrice
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import java.math.BigDecimal
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.Test

class MemberProductServiceTest {
    private val memberService: MemberService = mockk()
    private val productService: ProductService = mockk()
    private val coPaymentCostInfoService: CoPaymentCostInfoService = mockk()
    private val refundCostService: RefundCostService = mockk()

    private val service = MemberProductService(
        memberService,
        productService,
        coPaymentCostInfoService,
        refundCostService
    )

    private val personId = PersonId()
    private val product = TestModelFactory.buildProduct(
        tier = TierType.TIER_1,
        refund = RefundType.FULL,
        coPayment = CoPaymentType.FULL
    )
    private val member = TestModelFactory.buildMember(personId = personId, product = product)
    private val providers = TestModelFactory.buildProviders()

    private val coPaymentCostInfo = PersonCoPaymentCostInfo(
        event = "Consulta",
        chargeType = CoPaymentChargeType.FIXED_VALUE,
        price = CoPaymentTierPrice(
            tier = product.tier!!,
            value = BigDecimal.TEN
        )
    )
    private val refundCostInfo = RefundCost(
        tier = product.tier!!,
        prices = listOf(
            RefundCostPrice(
                eventType = RefundEventType.APPOINTMENT,
                event = "Consulta",
                value = BigDecimal.TEN
            )
        )
    )

    @AfterTest
    fun setup() = confirmVerified(
        memberService,
        productService,
        coPaymentCostInfoService,
        refundCostService
    )

    @Test
    fun `#getProductDetails should return valid data when it has co payment and refund`(): Unit = runBlocking {
        val productWithProviders = ProductWithProviders(
            product = product,
            providers = providers
        )

        coEvery {
            memberService.getCurrent(personId)
        } returns member.success()
        coEvery {
            productService.getProductWithProviders(product.id)
        } returns productWithProviders.success()
        coEvery {
            coPaymentCostInfoService.getCoPaymentByProductTier(product.tier!!)
        } returns listOf(coPaymentCostInfo).success()
        coEvery {
            refundCostService.getRefundCostInfoByMemberId(member.id)
        } returns refundCostInfo.success()

        val expected = MemberProductDetails(
            product = product,
            providers = providers,
            coPayment = listOf(coPaymentCostInfo),
            refundCost = refundCostInfo
        )

        val result = service.getProductDetails(personId)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { productService.getProductWithProviders(any()) }
        coVerifyOnce { coPaymentCostInfoService.getCoPaymentByProductTier(any()) }
        coVerifyOnce { refundCostService.getRefundCostInfoByMemberId(any()) }
    }

    @Test
    fun `#getProductDetails should return valid data when it has no co payment or refund`(): Unit = runBlocking {
        val product = product.copy(
            refund = RefundType.NONE,
            coPayment = CoPaymentType.NONE
        )

        val productWithProviders = ProductWithProviders(
            product = product,
            providers = providers
        )

        coEvery {
            memberService.getCurrent(personId)
        } returns member.success()
        coEvery {
            productService.getProductWithProviders(product.id)
        } returns productWithProviders.success()

        val expected = MemberProductDetails(
            product = product,
            providers = providers,
            coPayment = emptyList(),
            refund = emptyList()
        )

        val result = service.getProductDetails(personId)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { productService.getProductWithProviders(any()) }
        coVerifyNone { coPaymentCostInfoService.getCoPaymentByProductTier(any()) }
        coVerifyNone { refundCostService.getRefundCostInfoByMemberId(any()) }
    }

    @Test
    fun `#getProductDetails should return error when there is no current membership available`(): Unit = runBlocking {
        coEvery {
            memberService.getCurrent(personId)
        } returns NotFoundException("not_found").failure()

        val result = service.getProductDetails(personId)

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyNone { productService.getProductWithProviders(any()) }
        coVerifyNone { coPaymentCostInfoService.getCoPaymentByProductTier(any()) }
        coVerifyNone { refundCostService.getRefundCostInfoByMemberId(any()) }
    }

    @Test
    fun `#getMembershipWithProduct should return valid data when membership and product are found`(): Unit = runBlocking {
        val findOptions = MemberService.FindOptions()

        coEvery {
            memberService.findActiveOrPendingMembership(personId, findOptions)
        } returns member.success()
        coEvery {
            productService.getProduct(product.id)
        } returns product.success()

        val result = service.getMembershipWithProduct(personId, findOptions).get()
        val expected = MemberProductService.MembershipWithProduct(member, product)

        assertThat(result).isEqualTo(expected)

        coVerifyOnce { memberService.findActiveOrPendingMembership(any(), any()) }
        coVerifyOnce { productService.getProduct(any()) }
    }

    @Test
    fun `#getMembershipWithProduct should throw exception when membership is not found`(): Unit = runBlocking {
        val findOptions = MemberService.FindOptions()

        coEvery {
            memberService.findActiveOrPendingMembership(personId, findOptions)
        } returns NotFoundException("membership_not_found").failure()

        val result = service.getMembershipWithProduct(personId, findOptions)

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { memberService.findActiveOrPendingMembership(any(), any()) }
        coVerifyNone { productService.getProduct(any()) }
    }

    @Test
    fun `#getMembershipWithProduct should throw exception when product is not found`(): Unit = runBlocking {
        val findOptions = MemberService.FindOptions()

        coEvery {
            memberService.findActiveOrPendingMembership(personId, findOptions)
        } returns member.success()
        coEvery {
            productService.getProduct(product.id)
        } returns NotFoundException("product_not_found").failure()

        val result = service.getMembershipWithProduct(personId, findOptions)

        assertThat(result).isFailureOfType(NotFoundException::class)

        coVerifyOnce { memberService.findActiveOrPendingMembership(any(), any()) }
        coVerifyOnce { productService.getProduct(any()) }
    }
}
