package br.com.alice.member.api.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.CalendarProviderUnit
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.TestPreparation
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.converters.HealthPlanTasksTransportConverter
import br.com.alice.member.api.converters.HealthPlanResponseConverter
import br.com.alice.member.api.models.HealthHabitResponse
import br.com.alice.member.api.models.HealthPlanItemResponse
import br.com.alice.member.api.models.HealthPlanSectionName
import br.com.alice.member.api.models.PrescriptionResponse
import br.com.alice.member.api.models.ReferralsResponse
import br.com.alice.member.api.models.TestCode
import br.com.alice.member.api.models.TestRequestResponse
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.PersonCalendlyService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class TestResultFeedbackHealthPlanTaskServiceTest {

    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val healthPlanResponseConverter: HealthPlanResponseConverter = mockk()
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService = mockk()
    private val personService: PersonService = mockk()
    private val personCalendlyService: PersonCalendlyService = mockk()
    private val testCodesService: TestCodesService = mockk()

    private val testResultFeedbackHealthPlanTaskService = TestResultFeedbackHealthPlanTaskService(
        healthPlanTaskService,
        healthPlanResponseConverter,
        appointmentScheduleOptionService,
        personService,
        personCalendlyService,
        testCodesService,
    )
    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId)
    private val staff = TestModelFactory.buildStaff()
    private val healthPlanTaskMood =
        TestModelFactory.buildHealthPlanTask(personId, staffId = staff.id, type = HealthPlanTaskType.MOOD)
    private val tasksTransport = HealthPlanTasksTransportConverter.convert(
        mapOf(healthPlanTaskMood.type to listOf(healthPlanTaskMood)),
        mapOf(staff.id to staff),
        emptyMap()
    )
    private val healthHabitResponse = HealthPlanSectionName.healthHabits()
        .flatMap { section ->
            val items = section.get(tasksTransport) ?: emptyList()
            items.map { HealthHabitResponse(section, it) }
        }

    private val calendarProviderUnitOneId = RangeUUID.generate()
    private val calendarProviderUnits = listOf(
        CalendarProviderUnit(
            providerUnitId = calendarProviderUnitOneId,
            calendarUrl = "calendarUrl",
            address = "address",
            name = "Casa Alice",
        )
    )

    private val scheduleOptions = listOf(
        TestModelFactory.buildAppointmentScheduleOption(
            title = "Retorno com Taline",
            description = null,
            type = AppointmentScheduleType.HEALTHCARE_TEAM,
            calendarProviderUnits = calendarProviderUnits,
        )
    )
    private val personCalendly = TestModelFactory.buildPersonCalendly(person.id)
    private val testCodes = listOf(
        TestCode(
            title = "Bioimpedância",
            codes = listOf("98010028")
        )
    )
    private val preparations = emptyMap<String, TestPreparation>()

    @BeforeTest
    fun setup() {
        coEvery { personService.get(personId) } returns person.success()
    }

    @Test
    fun `#getHealthPlanTask - should return mood healthPlanTask response`(): Unit = runBlocking {

        coEvery { healthPlanTaskService.getAllByIds(listOf(healthPlanTaskMood.id)) } returns listOf(healthPlanTaskMood).success()
        coEvery { healthPlanResponseConverter.convertInHealthHabitResponses(tasksTransport) } returns healthHabitResponse

        val result =
            testResultFeedbackHealthPlanTaskService.getHealthPlanTask(listOf(healthPlanTaskMood.id), staff, personId)
        assertThat(result).isSuccess()

        coVerify(exactly = 1) { healthPlanTaskService.getAllByIds(any()) }
        coVerify(exactly = 1) { healthPlanResponseConverter.convertInHealthHabitResponses(any()) }
        coVerify(exactly = 0) { healthPlanResponseConverter.convertInPrescriptionResponses(any()) }
        coVerify(exactly = 0) { appointmentScheduleOptionService.listByPerson(any()) }
        coVerify(exactly = 0) { personService.get(any()) }
        coVerify(exactly = 0) { personCalendlyService.getOrCreate(any()) }
        coVerify(exactly = 0) { testCodesService.getTestCodes() }
        coVerify(exactly = 0) {
            healthPlanResponseConverter.convertInTestRequestResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        coVerify(exactly = 0) {
            healthPlanResponseConverter.convertInReferralsResponses(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }

        val sections = result.get().map(HealthPlanItemResponse::section)

        assertThat(result.get()).hasSize(1)
        assertThat(sections).containsExactly(HealthPlanSectionName.MOOD)
    }


    @Test
    fun `#getHealthPlanTask - should return prescription healthPlanTask response`(): Unit = runBlocking {
        val taskPrescription = TestModelFactory.buildHealthPlanTaskPrescription(personId, staffId = staff.id)
        val transportPrescription = HealthPlanTasksTransportConverter.convert(
            mapOf(taskPrescription.type to listOf(taskPrescription)),
            mapOf(staff.id to staff),
            emptyMap()
        )
        val prescriptionResponse = transportPrescription.prescription?.map(::PrescriptionResponse).orEmpty()


        coEvery { healthPlanTaskService.getAllByIds(listOf(taskPrescription.id)) } returns listOf(taskPrescription).success()
        coEvery { healthPlanResponseConverter.convertInHealthHabitResponses(transportPrescription) } returns emptyList()
        coEvery { healthPlanResponseConverter.convertInPrescriptionResponses(transportPrescription.prescription) } returns prescriptionResponse

        val result =
            testResultFeedbackHealthPlanTaskService.getHealthPlanTask(listOf(taskPrescription.id), staff, personId)
        assertThat(result).isSuccess()

        coVerify(exactly = 1) { healthPlanTaskService.getAllByIds(any()) }
        coVerify(exactly = 1) { healthPlanResponseConverter.convertInHealthHabitResponses(any()) }
        coVerify(exactly = 1) { healthPlanResponseConverter.convertInPrescriptionResponses(any()) }
        coVerify(exactly = 0) { appointmentScheduleOptionService.listByPerson(any()) }
        coVerify(exactly = 0) { personService.get(any()) }
        coVerify(exactly = 0) { personCalendlyService.getOrCreate(any()) }
        coVerify(exactly = 0) { testCodesService.getTestCodes() }
        coVerify(exactly = 0) {
            healthPlanResponseConverter.convertInTestRequestResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        coVerify(exactly = 0) {
            healthPlanResponseConverter.convertInReferralsResponses(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }

        val sections = result.get().map(HealthPlanItemResponse::section)

        assertThat(result.get()).hasSize(1)
        assertThat(sections).containsExactly(HealthPlanSectionName.PRESCRIPTIONS)
    }

    @Test
    fun `#getHealthPlanTask - should return testRequest healthPlanTask response`(): Unit = runBlocking {
        val taskTestRequest = TestModelFactory.buildHealthPlanTaskTestRequest(personId = personId)
            .copy(lastRequesterStaffId = staff.id)
        val transportTestRequest = HealthPlanTasksTransportConverter.convert(
            mapOf(taskTestRequest.type to listOf(taskTestRequest)),
            mapOf(staff.id to staff),
            emptyMap()
        )
        val codes = transportTestRequest.testRequest?.mapNotNull { it.code }
        val testRequestResponse = listOf(TestRequestResponse(transportTestRequest.testRequest!![0], null))


        coEvery { healthPlanTaskService.getAllByIds(listOf(taskTestRequest.id)) } returns listOf(taskTestRequest).success()
        coEvery { healthPlanResponseConverter.convertInHealthHabitResponses(transportTestRequest) } returns emptyList()
        coEvery { appointmentScheduleOptionService.listByPerson(personId) } returns scheduleOptions.success()
        coEvery { personCalendlyService.getOrCreate(personId) } returns personCalendly.success()
        coEvery { testCodesService.getTestCodes() } returns testCodes
        coEvery { testCodesService.getPreparations(codes!!) } returns emptyMap()
        coEvery {
            healthPlanResponseConverter.convertInTestRequestResponse(
                transportTestRequest.testRequest,
                scheduleOptions,
                person,
                personCalendly,
                testCodes,
                preparations,
                false
            )
        } returns testRequestResponse

        val result =
            testResultFeedbackHealthPlanTaskService.getHealthPlanTask(listOf(taskTestRequest.id), staff, personId)
        assertThat(result).isSuccess()

        coVerify(exactly = 1) { healthPlanTaskService.getAllByIds(any()) }
        coVerify(exactly = 1) { healthPlanResponseConverter.convertInHealthHabitResponses(any()) }
        coVerify(exactly = 0) { healthPlanResponseConverter.convertInPrescriptionResponses(any()) }
        coVerify(exactly = 1) { appointmentScheduleOptionService.listByPerson(any()) }
        coVerify(exactly = 1) { personService.get(any()) }
        coVerify(exactly = 1) { personCalendlyService.getOrCreate(any()) }
        coVerify(exactly = 1) { testCodesService.getTestCodes() }
        coVerify(exactly = 1) { testCodesService.getPreparations(any()) }
        coVerify(exactly = 1) {
            healthPlanResponseConverter.convertInTestRequestResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }
        coVerify(exactly = 0) {
            healthPlanResponseConverter.convertInReferralsResponses(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }

        val sections = result.get().map(HealthPlanItemResponse::section)

        assertThat(result.get()).hasSize(1)
        assertThat(sections).containsExactly(HealthPlanSectionName.TEST_REQUESTS)
    }

    @Test
    fun `#getHealthPlanTask - should return referral healthPlanTask response`(): Unit = runBlocking {
        val taskReferral = TestModelFactory.buildHealthPlanTaskReferral(personId = personId)
            .copy(lastRequesterStaffId = staff.id)
        val transportReferral = HealthPlanTasksTransportConverter.convert(
            mapOf(taskReferral.type to listOf(taskReferral)),
            mapOf(staff.id to staff),
            emptyMap()
        )

        val testRequestResponse = listOf(ReferralsResponse(transportReferral.referral!![0], null))

        coEvery { healthPlanTaskService.getAllByIds(listOf(taskReferral.id)) } returns listOf(taskReferral).success()
        coEvery { healthPlanResponseConverter.convertInHealthHabitResponses(transportReferral) } returns emptyList()
        coEvery { appointmentScheduleOptionService.listByPerson(personId) } returns scheduleOptions.success()
        coEvery { personCalendlyService.getOrCreate(personId) } returns personCalendly.success()
        coEvery {
            healthPlanResponseConverter.convertInReferralsResponses(
                transportReferral.referral,
                scheduleOptions,
                person,
                personCalendly,
            )
        } returns testRequestResponse

        val result = testResultFeedbackHealthPlanTaskService.getHealthPlanTask(listOf(taskReferral.id), staff, personId)
        assertThat(result).isSuccess()

        coVerify(exactly = 1) { healthPlanTaskService.getAllByIds(any()) }
        coVerify(exactly = 1) { healthPlanResponseConverter.convertInHealthHabitResponses(any()) }
        coVerify(exactly = 0) { healthPlanResponseConverter.convertInPrescriptionResponses(any()) }
        coVerify(exactly = 1) { appointmentScheduleOptionService.listByPerson(any()) }
        coVerify(exactly = 1) { personService.get(any()) }
        coVerify(exactly = 1) { personCalendlyService.getOrCreate(any()) }
        coVerify(exactly = 0) { testCodesService.getTestCodes() }
        coVerify(exactly = 0) { testCodesService.getPreparations(any()) }
        coVerify(exactly = 0) {
            healthPlanResponseConverter.convertInTestRequestResponse(
                any(),
                any(),
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        coVerify(exactly = 1) {
            healthPlanResponseConverter.convertInReferralsResponses(
                any(),
                any(),
                any(),
                any(),
                any(),
            )
        }

        val sections = result.get().map(HealthPlanItemResponse::section)

        assertThat(result.get()).hasSize(1)
        assertThat(sections).containsExactly(HealthPlanSectionName.REFERRALS)
    }

}
