package br.com.alice.member.api.services

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.storage.FileResponse
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.member.api.services.MemberDocuments
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.Test

class MemberDocumentsTest {

    private val fileStorage: FileStorage = mockk()
    private val fileVaultStorage: FileVaultStorage = mockk()

    private val memberDocuments = MemberDocuments(fileStorage, fileVaultStorage)

    @Test
    fun `#getExternalUrl should get document from file vault when contains schema`() = runBlocking<Unit> {
        val documentId = "2f712046-25cc-4fa2-a4e7-1f0bb1e03900"
        val fileVaultDocumentUrl = "https://prod-file-vault-service.wonderland.engineering/file/$documentId"

        val fileResponse = FileResponse(
            id = documentId.toUUID(),
            url = fileVaultDocumentUrl,
        )

        coEvery { fileVaultStorage.getFileById(documentId) } returns fileResponse

        val actual = memberDocuments.getExternalUrl(fileVaultDocumentUrl)
        Assertions.assertThat(actual).isEqualTo(fileVaultDocumentUrl)
    }

    @Test
    fun `#getExternalUrl should get document from file storage when does not contain schema`() = runBlocking<Unit> {
        val documentUrl = "https://s3.alice.com.br/document.pdf"
        val securedLink = "https://s3.alice.com.br/2f712046-25cc-4fa2-a4e7-1f0bb1e03900"

        coEvery { fileStorage.getSecuredLink(documentUrl) } returns securedLink

        val actual = memberDocuments.getExternalUrl(documentUrl)
        Assertions.assertThat(actual).isEqualTo(securedLink)
    }
}
