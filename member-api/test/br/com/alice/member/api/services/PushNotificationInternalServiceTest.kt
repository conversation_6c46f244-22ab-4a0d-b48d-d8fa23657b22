package br.com.alice.member.api.services

import br.com.alice.app.content.client.AppContentABTestService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PushService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.membership.client.DeviceService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

class PushNotificationInternalServiceTest {
    private val personService: PersonService = mockk()
    private val deviceService: DeviceService = mockk()
    private val pushService: PushService = mockk()
    private val appContentABTestService: AppContentABTestService = mockk()

    private val pushNotificationService = PushNotificationInternalService(
        personService,
        deviceService,
        pushService,
        appContentABTestService,
    )

    private val person = TestModelFactory.buildPerson()
    private val device = TestModelFactory.buildDevice(
        personId = person.id,
        deviceId = RangeUUID.generate().toString(),
    )

    companion object {
        @JvmStatic
        private fun provideSendNewTasksPushNotificationScenarios() = Stream.of(
            // Task count, redesign enabled, expected body, expected endpoint
            Arguments.of(1, false, "Você tem uma tarefa nova no seu Plano de Ação.", "http://localhost/app_content/screen/health_all_demands"),
            Arguments.of(2, false, "Você tem 2 tarefas novas no seu Plano de Ação.", "http://localhost/app_content/screen/health_all_demands"),
            Arguments.of(1, true, "Você tem uma tarefa nova no seu Plano de Ação.", "http://localhost/app_content/screen/redesign_health_plan_home"),
            Arguments.of(2, true, "Você tem 2 tarefas novas no seu Plano de Ação.", "http://localhost/app_content/screen/redesign_health_plan_home"),
        )
    }

    @ParameterizedTest
    @MethodSource("provideSendNewTasksPushNotificationScenarios")
    fun `#sendNewTasksPushNotification should send push for added tasks`(
        taskCount: Int,
        redesignEnabled: Boolean,
        expectedBody: String,
        expectedEndpoint: String,
    ): Unit = runBlocking {
        val expectedPush = FirebasePush(
            deviceToken = device.deviceId,
            title = "Plano de Ação.",
            body = expectedBody,
            data = mapOf(
                "path_to_navigate" to "cheshire_screen",
                "properties" to "{\"action\": {\"method\": \"GET\", \"endpoint\": \"$expectedEndpoint\"}}"
            )
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { deviceService.getDeviceByPerson(person.id.toString()) } returns device.success()
        coEvery { appContentABTestService.isRedesignEnabledForUser(person.id) } returns redesignEnabled.success()
        coEvery {
            pushService.send(expectedPush)
        } returns "message-id".success()

        val result = pushNotificationService.sendNewTasksPushNotification(person.id, taskCount)

        ResultAssert.assertThat(result).isSuccessWithData("message-id")
    }
}
