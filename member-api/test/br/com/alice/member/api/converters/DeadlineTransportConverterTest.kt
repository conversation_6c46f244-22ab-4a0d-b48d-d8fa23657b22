package br.com.alice.member.api.converters

import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.member.api.models.DeadlineTransport
import br.com.alice.member.api.models.DeadlineTypeTransport
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class DeadlineTransportConverterTest {

    @Test
    fun `#convert should return a continuous deadline as expected`() {
        val createdAt = LocalDateTime.now().minusDays(1L)
        val deadline = Deadline(
            unit = PeriodUnit.CONTINUOUS,
            date = LocalDateTime.now(),
            quantity = 1
        )

        val deadlineTransport = DeadlineTransportConverter.convert(deadline, createdAt.toString())
        val expectedDeadline = DeadlineTransport(
            type = DeadlineTypeTransport.CONTINUOUS,
            description = "contínuo",
            date = deadline.date!!.toLocalDate().toString()
        )

        assertThat(deadlineTransport).isEqualTo(expectedDeadline)
    }

    @Test
    fun `#convert should return a periodic deadline as expected`() {
        val createdAt = LocalDateTime.now().minusDays(1L)
        val deadline = Deadline(
            unit = PeriodUnit.UNDEFINED,
            quantity = 1
        )

        val deadlineTransport = DeadlineTransportConverter.convert(deadline, createdAt.toString())
        val expectedDeadline = DeadlineTransport(
            type = DeadlineTypeTransport.PERIODIC,
            description = null,
            date = null
        )

        assertThat(deadlineTransport).isEqualTo(expectedDeadline)
    }

    @Test
    fun `#convert should return a timestamp deadline as expected`() {
        val createdAt = LocalDateTime.now().minusDays(1L)
        val deadline = Deadline(
            date = LocalDateTime.now(),
            unit = PeriodUnit.WEEK,
            quantity = 1
        )

        val deadlineTransport = DeadlineTransportConverter.convert(deadline, createdAt.toString())
        val expectedDeadline = DeadlineTransport(
            type = DeadlineTypeTransport.TIMESTAMP,
            description = "até ${deadline.date!!.toBrazilianDateFormat()}",
            date = deadline.date!!.toLocalDate().toString()
        )

        assertThat(deadlineTransport).isEqualTo(expectedDeadline)
    }

    @Test
    fun `#friendlyDeadline should return a message containing the deadline date`() {
        val deadline = Deadline(
            unit = PeriodUnit.DAY,
            date = LocalDateTime.now(),
            quantity = 1
        )

        val createdAt = LocalDateTime.now().minusDays(1L)

        val friendlyDeadline = DeadlineTransportConverter.friendlyDeadline(deadline, createdAt.toString())

        assertThat(friendlyDeadline).isEqualTo("até ${deadline.date!!.toBrazilianDateFormat()}")
    }

    @Test
    fun `#friendlyDeadline should return a message containing the deadline date calculated from unit and quantity in cases of deadline date is missing`() {
        val deadline = Deadline(
            unit = PeriodUnit.DAY,
            date = LocalDateTime.now(),
            quantity = 1
        )

        val createdAt = LocalDateTime.now().minusDays(1L)

        val friendlyDeadline = DeadlineTransportConverter.friendlyDeadline(deadline, createdAt.toString())

        assertThat(friendlyDeadline).isEqualTo("até ${createdAt.plusDays(deadline.quantity.toLong()).toBrazilianDateFormat()}")
    }

    @Test
    fun `#friendlyDeadline should return a message return continious based on the period unit`() {
        val deadline = Deadline(
            unit = PeriodUnit.CONTINUOUS,
            quantity = 1
        )

        val createdAt = LocalDateTime.now().minusDays(1L)

        val friendlyDeadline = DeadlineTransportConverter.friendlyDeadline(deadline, createdAt.toString())

        assertThat(friendlyDeadline).isEqualTo("contínuo")
    }

    @Test
    fun `#friendlyDeadline should return an empty message if the unit is undefined`() {
        val deadline = Deadline(
            unit = PeriodUnit.UNDEFINED,
            quantity = 1
        )

        val createdAt = LocalDateTime.now().minusDays(1L)

        val friendlyDeadline = DeadlineTransportConverter.friendlyDeadline(deadline, createdAt.toString())

        assertThat(friendlyDeadline).isNull()
    }

    @Test
    fun `#friendlyDeadline should return an empty message if the start date and createdAt are null`() {
        val deadline = Deadline(
            unit = PeriodUnit.DAY,
            quantity = 1
        )

        val friendlyDeadline = DeadlineTransportConverter.friendlyDeadline(deadline, null)

        assertThat(friendlyDeadline).isNull()
    }

    @Test
    fun `#friendlyDeadline should return an empty message if deadline is null`() {
        val friendlyDeadline = DeadlineTransportConverter.friendlyDeadline(null, null)

        assertThat(friendlyDeadline).isNull()
    }
}
