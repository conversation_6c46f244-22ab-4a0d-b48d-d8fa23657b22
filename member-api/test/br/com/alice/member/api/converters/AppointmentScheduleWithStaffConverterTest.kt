package br.com.alice.member.api.converters

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.core.extensions.toUrlEncoded
import br.com.alice.common.featureaccess.features.AppointmentScheduleWithStaff
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.builders.SchedulingUrlBuilder.Companion.asQueryParam
import br.com.alice.member.api.builders.SchedulingUrlBuilder.Companion.encodeContent
import br.com.alice.member.api.models.AppointmentScheduleResponse
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class AppointmentScheduleWithStaffConverterTest {

    private val schedulingUrlBuilder: SchedulingUrlBuilder = mockk()

    private val appointmentScheduleWithStaffConverter = AppointmentScheduleWithStaffConverter(
        schedulingUrlBuilder
    )

    @Test
    fun `#convert should return expected appointment schedule with staff`() = runBlocking<Unit> {
        val staff = TestModelFactory.buildStaff()
        val personCalendly = TestModelFactory.buildPersonCalendly(PersonId())
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(staffId = staff.id)
        val appointmentScheduleWithStaff = AppointmentScheduleWithStaff(
            appointmentSchedule = appointmentSchedule,
            staff = staff
        )

        val utmTerm = gson.toJson(mapOf("person_calendly_id" to personCalendly.id)).toUrlEncoded()
         val utmParam = "&utm_term=$utmTerm"
        val eventId = appointmentSchedule.eventId ?: appointmentSchedule.eventUuid.toString()

        val params = mapOf(
            "health_plan_task_id" to appointmentSchedule.healthPlanTaskId.toString().toSha256(),
            "staff_id" to staff.id.toString(),
            "event_name" to appointmentSchedule.eventName,
            "appointment_schedule_id" to appointmentSchedule.id.toString(),
        )

        coEvery {
            schedulingUrlBuilder.buildCancellationUrl(
                eventId,
                params = params,
                personCalendly = personCalendly,
                shouldReleaseNewScheduleFlow = false
            )
        } returns "https://calendly.com/cancellations/C4L3NDLY3V3NT?text_color=000000&primary_color=e01f80&${params.encodeContent().asQueryParam().substring(1)}$utmParam"

        coEvery {
            schedulingUrlBuilder.buildRescheduleUrl(
                eventId,
                params = params,
                shouldReleaseNewScheduleFlow = false,
                personCalendly = personCalendly
            )
        } returns "https://webview.alice.com.br/schedule-first-appointment/?schedule_url=https://calendly.com/reschedulings/C4L3NDLY3V3NT?text_color=000000&primary_color=e01f80$utmParam"

        val cancelUrl = SchedulingUrlBuilder.buildCancellationUrl(
            eventId,
            params = mapOf(
                "health_plan_task_id" to appointmentSchedule.healthPlanTaskId.toString().toSha256(),
                "staff_id" to staff.id.toString(),
                "event_name" to appointmentSchedule.eventName,
                "appointment_schedule_id" to appointmentSchedule.id.toString(),
            ),
            personCalendly = personCalendly
        )
        val rescheduleUrl = SchedulingUrlBuilder.buildRescheduleUrl(eventId, personCalendly = personCalendly)

        val expected = AppointmentScheduleResponse(
            id = appointmentSchedule.id.toString(),
            healthProfessionals = listOf(staff.let(HealthProfessionalConverter::convert)),
            name = appointmentSchedule.eventName + " com " + staff.firstName,
            startTime = appointmentSchedule.startTime.toString(),
            endTime = appointmentSchedule.endTime?.toString(),
            cancelUrl = cancelUrl,
            rescheduleUrl = rescheduleUrl,
            location = appointmentSchedule.location.orEmpty(),
            locationUrl = null
        )

        val actual = appointmentScheduleWithStaffConverter.convert(appointmentScheduleWithStaff, personCalendly = personCalendly)
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `#convert should return expected appointment schedule`() {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule()

        val expected = AppointmentScheduleResponse(
            id = appointmentSchedule.id.toString(),
            name = appointmentSchedule.eventName,
            startTime = appointmentSchedule.startTime.toSaoPauloTimeZone().toString(),
            endTime = appointmentSchedule.endTime?.toSaoPauloTimeZone().toString(),
            location = appointmentSchedule.location.orEmpty(),
            locationUrl = null
        )

        val actual = AppointmentScheduleConverter.convert(appointmentSchedule)
        assertThat(actual).isEqualTo(expected)
    }
}
