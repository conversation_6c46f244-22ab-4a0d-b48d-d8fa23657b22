package br.com.alice.member.api.converters

import br.com.alice.member.api.converters.HealthScoreTypeConverter
import br.com.alice.member.api.models.ScoreTypes
import org.assertj.core.api.Assertions
import kotlin.test.Test

class HealthScoreTypeConverterTest {

    @Test
    fun `#convert converts ScoreTypes to ClinicalOutcomeRecord RecordType`() {
        val response = HealthScoreTypeConverter.convert(ScoreTypes.GENERAL)
        Assertions.assertThat(response).isInstanceOf(String::class.java)
        Assertions.assertThat(response).isEqualTo("SCORE_MAGENTA")
    }

    @Test
    fun `#convert converts FOOD to FOOD_SCORE_MAGENTA`() {
        val response = HealthScoreTypeConverter.convert(ScoreTypes.FOOD)
        Assertions.assertThat(response).isInstanceOf(String::class.java)
        Assertions.assertThat(response).isEqualTo("FOOD_SCORE_MAGENTA")
    }

    @Test
    fun `#convert converts MENTAL to MENTAL_SCORE_MAGENTA`() {
        val response = HealthScoreTypeConverter.convert(ScoreTypes.MENTAL)
        Assertions.assertThat(response).isInstanceOf(String::class.java)
        Assertions.assertThat(response).isEqualTo("MENTAL_SCORE_MAGENTA")
    }

    @Test
    fun `#convert converts SLEEP to MSQ_SCORE_MAGENTA`() {
        val response = HealthScoreTypeConverter.convert(ScoreTypes.SLEEP)
        Assertions.assertThat(response).isInstanceOf(String::class.java)
        Assertions.assertThat(response).isEqualTo("MSQ_SCORE_MAGENTA")
    }

    @Test
    fun `#convert converts HABITS to MSQ_SCORE_MAGENTA`() {
        val response = HealthScoreTypeConverter.convert(ScoreTypes.ALCOHOL_AND_SMOKE)
        Assertions.assertThat(response).isInstanceOf(String::class.java)
        Assertions.assertThat(response).isEqualTo("HABITS_SCORE_MAGENTA")
    }

    @Test
    fun `#convert converts PHYSICAL_ACTIVITY to IPAQ_SCORE_MAGENTA`() {
        val response = HealthScoreTypeConverter.convert(ScoreTypes.PHYSICAL_ACTIVITY)
        Assertions.assertThat(response).isInstanceOf(String::class.java)
        Assertions.assertThat(response).isEqualTo("IPAQ_SCORE_MAGENTA")
    }

    @Test
    fun `#convert converts EUROQOL to EUROQOL_SCORE_MAGENTA`() {
        val response = HealthScoreTypeConverter.convert(ScoreTypes.QUALITY_OF_LIFE)
        Assertions.assertThat(response).isInstanceOf(String::class.java)
        Assertions.assertThat(response).isEqualTo("EUROQOL_SCORE_MAGENTA")
    }
}
