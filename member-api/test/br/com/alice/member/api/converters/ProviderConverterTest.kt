package br.com.alice.member.api.converters

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.PhoneType.PHONE
import br.com.alice.data.layer.models.PhoneType.WHATSAPP
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.models.PhoneNumberResponse
import br.com.alice.member.api.models.ProviderResponse
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ProviderConverterTest {

    @Test
    fun `#convert should convert a PhoneNumber into a PhoneNumberResponse`() {
        val phoneNumber = PhoneNumber("Consultório 1", "11999999999", PHONE)
        val expectedPhoneNumberResponse = PhoneNumberResponse("Consultório 1", "11999999999", "PHONE", "tel:11999999999")

        val phoneNumberResponse = PhoneNumberResponseConverter.convert(phoneNumber)

        assertThat(phoneNumberResponse).isEqualTo(expectedPhoneNumberResponse)
    }

    @Test
    fun `#convert should convert a PhoneNumber into a PhoneNumberResponse, eliminating special chars`() {
        val phoneNumber = PhoneNumber("Consultório 1", "(11) 99999-9999", PHONE)
        val expectedPhoneNumberResponse = PhoneNumberResponse("Consultório 1", "(11) 99999-9999", "PHONE", "tel:11999999999")

        val phoneNumberResponse = PhoneNumberResponseConverter.convert(phoneNumber)

        assertThat(phoneNumberResponse).isEqualTo(expectedPhoneNumberResponse)
    }

    @Test
    fun `#convert should convert a whatsapp international PhoneNumber into a PhoneNumberResponse`() {
        val phoneNumber = PhoneNumber("Consultório 1", "+1 (11) 99999-9999", WHATSAPP)
        val expectedPhoneNumberResponse = PhoneNumberResponse("Consultório 1", "+1 (11) 99999-9999", "WHATSAPP", "${MobileApp.Links.WHATSAPP_API_URL}111999999999")

        val phoneNumberResponse = PhoneNumberResponseConverter.convert(phoneNumber)

        assertThat(phoneNumberResponse).isEqualTo(expectedPhoneNumberResponse)
    }

    @Test
    fun `#convert should convert a whatsapp brazilian PhoneNumber into a PhoneNumberResponse`() {
        val phoneNumber = PhoneNumber("Consultório 1", "(11) 999999999", WHATSAPP)
        val expectedPhoneNumberResponse = PhoneNumberResponse("Consultório 1", "(11) 999999999", "WHATSAPP", "${MobileApp.Links.WHATSAPP_API_URL}5511999999999")

        val phoneNumberResponse = PhoneNumberResponseConverter.convert(phoneNumber)

        assertThat(phoneNumberResponse).isEqualTo(expectedPhoneNumberResponse)
    }

    @Test
    fun `#convert should convert a whatsapp brazilian PhoneNumber from RS into a PhoneNumberResponse`() {
        val phoneNumber = PhoneNumber("Consultório 1", "(55) 999999999", WHATSAPP)
        val expectedPhoneNumberResponse = PhoneNumberResponse("Consultório 1", "(55) 999999999", "WHATSAPP", "${MobileApp.Links.WHATSAPP_API_URL}5555999999999")

        val phoneNumberResponse = PhoneNumberResponseConverter.convert(phoneNumber)

        assertThat(phoneNumberResponse).isEqualTo(expectedPhoneNumberResponse)
    }

    @Test
    fun `#convert should convert a whatsapp brazilian PhoneNumber with prefix into a PhoneNumberResponse`() {
        val phoneNumber = PhoneNumber("Consultório 1", "55 11 999999999", WHATSAPP)
        val expectedPhoneNumberResponse = PhoneNumberResponse("Consultório 1", "55 11 999999999", "WHATSAPP", "${MobileApp.Links.WHATSAPP_API_URL}5511999999999")

        val phoneNumberResponse = PhoneNumberResponseConverter.convert(phoneNumber)

        assertThat(phoneNumberResponse).isEqualTo(expectedPhoneNumberResponse)
    }

    @Test
    fun `#convert should convert a whatsapp brazilian PhoneNumber from RS with prefix into a PhoneNumberResponse`() {
        val phoneNumber = PhoneNumber("Consultório 1", "55 55 999999999", WHATSAPP)
        val expectedPhoneNumberResponse = PhoneNumberResponse("Consultório 1", "55 55 999999999", "WHATSAPP", "${MobileApp.Links.WHATSAPP_API_URL}5555999999999")

        val phoneNumberResponse = PhoneNumberResponseConverter.convert(phoneNumber)

        assertThat(phoneNumberResponse).isEqualTo(expectedPhoneNumberResponse)
    }

    @Test
    fun `#convert should convert a Provider into a ProviderResponse`() {
        val provider = TestModelFactory.buildProvider()
        val expectedProviderResponse = ProviderResponse(
            id = provider.id,
            name = provider.name,
            type = "",
            specialties = emptyList(),
            subSpecialties = emptyList(),
            site = provider.site,
            crm = null,
            cnpj = provider.cnpj,
            phones = provider.phones.map { PhoneNumberResponseConverter.convert(it) },
            workingHours = null,
            qualifications = emptyList(),
            imageUrl = provider.imageUrl,
            education = emptyList(),
            curiosity = null
        )

        val providerResponse = ProviderConverter.convert(provider)

        assertThat(providerResponse).isEqualTo(expectedProviderResponse)
    }
}
