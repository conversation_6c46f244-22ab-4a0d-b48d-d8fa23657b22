package br.com.alice.member.api.converters

import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.data.layer.models.Start
import br.com.alice.data.layer.models.StartType
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class StartTransportConverterTest {

    @Test
    fun `#friendlyStart should return a message containing the start date`() {
        val start = Start(
            type = StartType.IMMEDIATE,
            date = LocalDateTime.now()
        )
        val createdAt = LocalDateTime.now().minusDays(1L).toString()

        val friendlyStart = StartTransportConverter.friendlyStart(start, createdAt)

        assertThat(friendlyStart).isEqualTo("começar em ${start.date!!.toBrazilianDateFormat()}")
    }

    @Test
    fun `#friendlyStart should return a message containing the creation date in cases of start date is missing`() {
        val start = Start(
            type = StartType.IMMEDIATE
        )
        val createdAt = LocalDateTime.now().minusDays(1L)

        val friendlyStart = StartTransportConverter.friendlyStart(start, createdAt.toString())

        assertThat(friendlyStart).isEqualTo("começar em ${createdAt.toBrazilianDateFormat()}")
    }

    @Test
    fun `#friendlyStart should return a message containing a condition if the type is conditional`() {
        val start = Start(
            type = StartType.IF_NECESSARY
        )
        val createdAt = LocalDateTime.now().minusDays(1L)

        val friendlyStart = StartTransportConverter.friendlyStart(start, createdAt.toString())

        assertThat(friendlyStart).isEqualTo("se necessário")
    }

    @Test
    fun `#friendlyStart should return a message containing a condition if the type is conditional and createdAt is null`() {
        val start = Start(
            type = StartType.IF_NECESSARY
        )

        val friendlyStart = StartTransportConverter.friendlyStart(start, null)

        assertThat(friendlyStart).isEqualTo("se necessário")
    }

    @Test
    fun `#friendlyStart should return a empty message if the start is null`() {
        val friendlyStart = StartTransportConverter.friendlyStart(null, null)

        assertThat(friendlyStart).isNull()
    }
}
