package br.com.alice.member.api.converters

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OutcomeConf
import br.com.alice.healthlogic.models.EnrichedClinicalOutcomeRecord
import br.com.alice.member.api.converters.HealthScoreSingleResponseConverter
import br.com.alice.member.api.models.ActionNavigation
import br.com.alice.member.api.models.BannerInfo
import br.com.alice.member.api.models.BannerStyle
import br.com.alice.member.api.models.HEALTH_SCORE_CALL_TO_ACTION_STRING
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.ScoreDefaultResponse
import io.mockk.coEvery
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.Test

class HealthScoreSingleResponseConverterTest {
    private val person = TestModelFactory.buildPerson()
    private val generalOutcomeConf = TestModelFactory.buildOutcomeConf(
        id = "bec90d96-4d94-4f66-9cb3-5a8956062600".toUUID(),
        key = "SCORE_MAGENTA",
        referenceRange = listOf(
            OutcomeConf.ReferenceRange(
                description = "MODERATE",
                lowerLimit = BigDecimal(0),
                upperLimit = BigDecimal(500),
            ),
            OutcomeConf.ReferenceRange(
                description = "GOOD",
                lowerLimit = BigDecimal(500),
                upperLimit = BigDecimal(750),

                ),
            OutcomeConf.ReferenceRange(
                description = "EXCELLENT",
                lowerLimit = BigDecimal(750),
                upperLimit = BigDecimal(1000)
            )
        )
    )

    private val mockNow = LocalDateTime.now()

    private val generalClinicalOutcomeRecord = listOf(
        EnrichedClinicalOutcomeRecord(
            TestModelFactory.buildClinicalOutcomeRecord(
                personId = person.id,
                outcome = 1000.toBigDecimal(),
                outcomeConfId = generalOutcomeConf.id,
                addedAt = mockNow
            ),
            generalOutcomeConf
        ),
        EnrichedClinicalOutcomeRecord(
            TestModelFactory.buildClinicalOutcomeRecord(
                personId = person.id,
                outcome = 900.toBigDecimal(),
                outcomeConfId = generalOutcomeConf.id,
                addedAt = mockNow.minusDays(90)
            ),
            generalOutcomeConf
        )
    )

    @Test
    fun `#convert converts ClinicalOutcomeRecords to ScoreCurrentResult with false isWelcome param`() {
        val isWelcome = false
        val response = HealthScoreSingleResponseConverter.convert(generalClinicalOutcomeRecord, isWelcome, false)

        Assertions.assertThat(response).isInstanceOf(ScoreDefaultResponse::class.java)
        Assertions.assertThat(response.result.first()?.id).isNotNull
        Assertions.assertThat(response.result.first()?.date).isNotNull
        Assertions.assertThat(response.result.first()?.score?.value)
            .isEqualTo(generalClinicalOutcomeRecord.first().outcome.toInt())
        Assertions.assertThat(response.result.first()?.score?.max).isEqualTo(1000)
        Assertions.assertThat(response.result.first()?.score?.delta).isEqualTo(100)

        Assertions.assertThat(response.action).isNull()

        Assertions.assertThat(response.banner).isEqualTo(
            BannerInfo(
                style = BannerStyle.SHRINK,
                resultAction = ActionNavigation(
                    title = "Seu Score Magenta",
                    description = "Saiba como funciona",
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.SCORE_MAGENTA_RESULT
                    )
                )
            )
        )

        Assertions.assertThat(response.result.first()?.date?.originalValue).isInstanceOf(LocalDateTime::class.java)
        Assertions.assertThat(response.result.first()?.date?.chartValue).isInstanceOf(String::class.java)
        Assertions.assertThat(response.result.first()?.date?.formattedValue).isInstanceOf(String::class.java)
    }

    @Test
    fun `#convert converts ClinicalOutcomeRecords to ScoreCurrentResult with scoreActionNavigations calling to action with estimated time`() = runBlocking {
        val isWelcome = false
        val scoreAgeInDays = 91L
        val expectedCallToActionLabel = HEALTH_SCORE_CALL_TO_ACTION_STRING
        val expectedResultNavigationLabel = "Seu último score foi ${generalClinicalOutcomeRecord.first().outcome.toInt()}"
        val expectedCallToActionEstimatedTimeString = "<icon>clock</icon> menos de 12 minutos"
        val expectedResultNavigationRoute = NavigationResponse(
            mobileRoute = MobileRouting.SCORE_MAGENTA_RESULT
        )
        val expectedCallToActionNavigationRoute = NavigationResponse(
            mobileRoute = MobileRouting.SCORE_MAGENTA_INPUT
        )

        mockkStatic(LocalDateTime::class) {
            coEvery { LocalDateTime.now() } returns mockNow.plusDays(scoreAgeInDays)
            val response = HealthScoreSingleResponseConverter.convert(generalClinicalOutcomeRecord, isWelcome, true)

            Assertions.assertThat(response).isInstanceOf(ScoreDefaultResponse::class.java)
            Assertions.assertThat(response.result.first()?.id).isNotNull
            Assertions.assertThat(response.result.first()?.date).isNotNull
            Assertions.assertThat(response.result.first()?.score?.value)
                .isEqualTo(generalClinicalOutcomeRecord.first().outcome.toInt())
            Assertions.assertThat(response.result.first()?.score?.max).isEqualTo(1000)
            Assertions.assertThat(response.result.first()?.score?.delta).isEqualTo(100)
            Assertions.assertThat(response.banner!!.answerAction!!.title).isEqualTo(expectedCallToActionLabel)
            Assertions.assertThat(response.banner!!.answerAction!!.description).isEqualTo(expectedCallToActionEstimatedTimeString)
            Assertions.assertThat(response.banner!!.answerAction!!.navigation).isEqualTo(expectedCallToActionNavigationRoute)
            Assertions.assertThat(response.banner!!.style).isEqualTo(BannerStyle.EXPANDED)
            Assertions.assertThat(response.banner!!.resultAction!!.title).isEqualTo(expectedResultNavigationLabel)
            Assertions.assertThat(response.banner!!.resultAction!!.navigation).isEqualTo(expectedResultNavigationRoute)

            Assertions.assertThat(response.action).isNull()

            Assertions.assertThat(response.result.first()?.date?.originalValue)
                .isInstanceOf(LocalDateTime::class.java)
            Assertions.assertThat(response.result.first()?.date?.chartValue).isInstanceOf(String::class.java)
            Assertions.assertThat(response.result.first()?.date?.formattedValue).isInstanceOf(String::class.java)

        }
    }

    @Test
    fun `#convert converts ClinicalOutcomeRecords to ScoreCurrentResult with true isWelcome param`() {
        val isWelcome = true
        val response = HealthScoreSingleResponseConverter.convert(generalClinicalOutcomeRecord, isWelcome, false)

        Assertions.assertThat(response).isInstanceOf(ScoreDefaultResponse::class.java)
        Assertions.assertThat(response.result.first()?.id).isNotNull
        Assertions.assertThat(response.result.first()?.date).isNotNull
        Assertions.assertThat(response.result.first()?.score?.value)
            .isEqualTo(generalClinicalOutcomeRecord.first().outcome.toInt())
        Assertions.assertThat(response.result.first()?.score?.max).isEqualTo(1000)
        Assertions.assertThat(response.result.first()?.score?.delta).isEqualTo(100)

        Assertions.assertThat(response.action?.label).isNotNull
        Assertions.assertThat(response.action?.icon).isNotNull

        Assertions.assertThat(response.banner).isEqualTo(
            BannerInfo(
                style = BannerStyle.SHRINK,
                resultAction = ActionNavigation(
                    title = "Seu Score Magenta",
                    description = "Saiba como funciona",
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.SCORE_MAGENTA_RESULT
                    )
                )
            )
        )

        Assertions.assertThat(response.result.first()?.date?.originalValue).isInstanceOf(LocalDateTime::class.java)
        Assertions.assertThat(response.result.first()?.date?.chartValue).isInstanceOf(String::class.java)
        Assertions.assertThat(response.result.first()?.date?.formattedValue).isInstanceOf(String::class.java)
    }

    @Test
    fun `#convert try to convert empty ClinicalOutcomeRecords to ScoreCurrentResult`() {
        val isWelcome = false
        val response = HealthScoreSingleResponseConverter.convert(emptyList(), isWelcome, false)
        Assertions.assertThat(response.result).isEmpty()
    }
}
