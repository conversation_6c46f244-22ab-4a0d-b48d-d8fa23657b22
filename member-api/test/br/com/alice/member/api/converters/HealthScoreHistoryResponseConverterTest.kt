package br.com.alice.member.api.converters

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OutcomeConf
import br.com.alice.healthlogic.models.EnrichedClinicalOutcomeRecord
import br.com.alice.member.api.converters.HealthScoreHistoryResponseConverter
import br.com.alice.member.api.models.ScoreDefaultResponse
import br.com.alice.member.api.models.ScoreResult
import org.assertj.core.api.Assertions
import java.math.BigDecimal
import java.time.LocalDateTime
import kotlin.test.Test

class HealthScoreHistoryResponseConverterTest {
    private val person = TestModelFactory.buildPerson()
    private val outcomeConf = TestModelFactory.buildOutcomeConf(
        key = "SCORE_MAGENTA",
        referenceRange = listOf(
            OutcomeConf.ReferenceRange(
                description = "MODERATE",
                lowerLimit = BigDecimal(0),
                upperLimit = BigDecimal(500),
            ),
            OutcomeConf.ReferenceRange(
                description = "GOOD",
                lowerLimit = BigDecimal(500),
                upperLimit = BigDecimal(750),

                ),
            OutcomeConf.ReferenceRange(
                description = "EXCELLENT",
                lowerLimit = BigDecimal(750),
                upperLimit = null
            )
        )
    )

    private val generalClinicalOutcomeRecord = listOf(
        EnrichedClinicalOutcomeRecord(
            TestModelFactory.buildClinicalOutcomeRecord(
                personId = person.id,
                outcomeConfId = outcomeConf.id,
                outcome = 1000.toBigDecimal()
            ),
            outcomeConf = outcomeConf
        ),
        EnrichedClinicalOutcomeRecord(
            TestModelFactory.buildClinicalOutcomeRecord(
                personId = person.id,
                outcomeConfId = outcomeConf.id,
                outcome = 900.toBigDecimal()
            ),
            outcomeConf = outcomeConf
        )
    )

    @Test
    fun `#convert converts ClinicalOutcomeRecords to ScoreCurrentResult with delta`() {
        val response = HealthScoreHistoryResponseConverter.convert(generalClinicalOutcomeRecord, emptyList(), false)

        Assertions.assertThat(response).isInstanceOf(ScoreDefaultResponse::class.java)
        Assertions.assertThat(response.result.first()).isInstanceOf(ScoreResult::class.java)
        Assertions.assertThat(response.result.first()?.score?.value).isEqualTo(generalClinicalOutcomeRecord.first().outcome.toInt())
        Assertions.assertThat(response.result.first()?.score?.max).isEqualTo(1000)
        Assertions.assertThat(response.result.first()?.score?.delta).isEqualTo(100)

        Assertions.assertThat(response.result.first()?.date?.originalValue).isInstanceOf(LocalDateTime::class.java)
        Assertions.assertThat(response.result.first()?.date?.chartValue).isInstanceOf(String::class.java)
        Assertions.assertThat(response.result.first()?.date?.formattedValue).isInstanceOf(String::class.java)
    }

    @Test
    fun `#convert converts ClinicalOutcomeRecords to ScoreCurrentResult without delta`() {
        val response = HealthScoreHistoryResponseConverter.convert(generalClinicalOutcomeRecord, emptyList(), true)

        Assertions.assertThat(response).isInstanceOf(ScoreDefaultResponse::class.java)
        Assertions.assertThat(response.result.first()).isInstanceOf(ScoreResult::class.java)
        Assertions.assertThat(response.result.first()?.score?.value).isEqualTo(generalClinicalOutcomeRecord.first().outcome.toInt())
        Assertions.assertThat(response.result.first()?.score?.max).isEqualTo(1000)
        Assertions.assertThat(response.result.first()?.score?.delta).isNull()

        Assertions.assertThat(response.result.first()?.date?.originalValue).isInstanceOf(LocalDateTime::class.java)
        Assertions.assertThat(response.result.first()?.date?.chartValue).isInstanceOf(String::class.java)
        Assertions.assertThat(response.result.first()?.date?.formattedValue).isInstanceOf(String::class.java)
    }

    @Test
    fun `#convert try to convert empty ClinicalOutcomeRecords to ScoreCurrentResult`() {
        val response = HealthScoreHistoryResponseConverter.convert(emptyList(), emptyList(), false)
        Assertions.assertThat(response.result).isEmpty()
    }
}
