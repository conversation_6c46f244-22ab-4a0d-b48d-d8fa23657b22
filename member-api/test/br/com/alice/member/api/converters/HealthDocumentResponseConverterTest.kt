package br.com.alice.member.api.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.models.HealthDocumentResponse
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.format.DateTimeFormatter.ISO_DATE_TIME
import kotlin.test.Test

class HealthDocumentResponseConverterTest {

    @Test
    fun `#convert test result file into a health document response`() = runBlocking<Unit> {
        val testResultFile = TestModelFactory.buildTestResultFile()
            .copy(file = AliceFile(RangeUUID.generate(), "fileName", "url", "pdf", 1025))

        val healthDocumentResponse = HealthDocumentResponseConverter.convert(testResultFile)
        val expectedResponse = HealthDocumentResponse(
            id = testResultFile.id,
            documentName = testResultFile.description,
            documentUrl = testResultFile.file!!.url,
            uploadedAt = testResultFile.createdAt.format(ISO_DATE_TIME),
            performedAt = testResultFile.performedAt.format(ISO_DATE_TIME),
            documentSize = "1.0 KB"
        )

        assertThat(healthDocumentResponse)
            .usingRecursiveComparison()
            .ignoringFields("documentSize")
            .isEqualTo(expectedResponse)
    }
}
