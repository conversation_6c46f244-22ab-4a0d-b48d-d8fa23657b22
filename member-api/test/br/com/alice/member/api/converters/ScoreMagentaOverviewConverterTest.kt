package br.com.alice.member.api.converters

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.healthlogic.models.EnrichedClinicalOutcomeRecord
import br.com.alice.member.api.factory.ScoreMagentaOverviewFactory
import br.com.alice.member.api.models.ScoreMagentaOverviewResponse
import br.com.alice.member.api.models.ScoreTypes
import java.math.BigDecimal
import kotlinx.coroutines.runBlocking
import kotlin.test.Test
import kotlin.test.assertEquals

class ScoreMagentaOverviewConverterTest {

    private val records = listOf(
        buildEnrichedRecord(key = "SCORE_MAGENTA", value = BigDecimal.valueOf(750)),
        buildEnrichedRecord(key = "FOOD_SCORE_MAGENTA", value = BigDecimal.valueOf(500)),
        buildEnrichedRecord(key = "MENTAL_SCORE_MAGENTA", value = BigDecimal.valueOf(600)),
        buildEnrichedRecord(key = "MSQ_SCORE_MAGENTA", value = BigDecimal.valueOf(700)),
        buildEnrichedRecord(key = "HABITS_SCORE_MAGENTA", value = BigDecimal.valueOf(800)),
        buildEnrichedRecord(key = "IPAQ_SCORE_MAGENTA", value = BigDecimal.valueOf(900)),
        buildEnrichedRecord(key = "EUROQOL_SCORE_MAGENTA", value = BigDecimal.valueOf(1000)),
    )

    @Test
    fun `#convert should convert successfully`() = runBlocking {
        val expected = ScoreMagentaOverviewResponse(
            result = 750,
            maxResult = 1000,
            labels = ScoreMagentaOverviewFactory.buildLabels(),
            pillars = buildPillars()
        )

        val result = ScoreMagentaOverviewConverter.convert(records)

        assertEquals(expected, result)
    }

    private fun buildPillars() =
        listOf(
            ScoreMagentaOverviewFactory.buildSection(ScoreTypes.FOOD, 500),
            ScoreMagentaOverviewFactory.buildSection(ScoreTypes.MENTAL, 600),
            ScoreMagentaOverviewFactory.buildSection(ScoreTypes.ALCOHOL_AND_SMOKE, 800),
            ScoreMagentaOverviewFactory.buildSection(ScoreTypes.QUALITY_OF_LIFE, 1000),
            ScoreMagentaOverviewFactory.buildSection(ScoreTypes.SLEEP, 700),
            ScoreMagentaOverviewFactory.buildSection(ScoreTypes.PHYSICAL_ACTIVITY, 900),
        )

    private fun buildEnrichedRecord(key: String, value: BigDecimal): EnrichedClinicalOutcomeRecord {
        val conf = TestModelFactory.buildOutcomeConf(key = key)
        val record = TestModelFactory.buildClinicalOutcomeRecord(outcomeConfId = conf.id, outcome = value)

        return EnrichedClinicalOutcomeRecord(
            clinicalOutcomeRecord = record,
            outcomeConf = conf
        )
    }
}
