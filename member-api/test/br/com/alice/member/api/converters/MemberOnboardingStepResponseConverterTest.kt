package br.com.alice.member.api.converters

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberOnboardingAction
import br.com.alice.data.layer.models.MemberOnboardingOptIn
import br.com.alice.data.layer.models.MemberOnboardingProgress
import br.com.alice.data.layer.models.MemberOnboardingStep
import br.com.alice.data.layer.models.MemberOnboardingTemplate
import br.com.alice.data.layer.models.Person
import br.com.alice.member.api.models.MemberOnboardingAccessoryImageInformation
import br.com.alice.member.api.models.MemberOnboardingActionResponse
import br.com.alice.member.api.models.MemberOnboardingOptInResponse
import br.com.alice.member.api.models.MemberOnboardingStepFolder
import br.com.alice.member.api.models.MemberOnboardingStepProgressResponse
import br.com.alice.member.api.models.MemberOnboardingStepResponse
import br.com.alice.member.api.models.MemberOnboardingStepTag
import br.com.alice.member.api.models.MemberOnboardingStepVideoInformation
import br.com.alice.member.api.models.MemberOnboardingWidgetComponent
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import org.assertj.core.api.Assertions
import kotlin.test.Test

class MemberOnboardingStepResponseConverterTest {
    private val memberOnboardingAction = TestModelFactory.buildMemberOnboardingAction()
    private val memberOnboardingActionWithAppState = TestModelFactory.buildMemberOnboardingAction(
        listenAppState = MemberOnboardingAction.MemberOnboardingActionAppState.PERSON_ASSOCIATED_TO_HEALTH_LEAGUE
    )
    private val memberOnboardingStep = TestModelFactory.buildMemberOnboardingStep(
        title = "Oi meu nome é {nome}",
        optIns = listOf(
            MemberOnboardingOptIn(
                text = "teste",
                link = "testada.com",
            )
        ),
        key = "testadinha",
        actions = listOf(memberOnboardingAction.id)
    )

    private val memberOnboardingTemplateLegacy = TestModelFactory.buildMemberOnboardingTemplate(
        type = MemberOnboardingTemplate.MemberOnboardingTemplateType.B2B_NO_RISK_WITH_VIDEO
    )

    private val memberOnboardingTemplateNewPortfolio = TestModelFactory.buildMemberOnboardingTemplate(
        type = MemberOnboardingTemplate.MemberOnboardingTemplateType.ADULT_WITH_DS
    )

    private val person = TestModelFactory.buildPerson()

    private val memberOnboardingStepWithNavigationToImmersion = TestModelFactory.buildMemberOnboardingStep(
        title = "Oi meu nome é {nome}",
        optIns = listOf(
            MemberOnboardingOptIn(
                text = "teste",
                link = "testada.com",
            )
        ),
        actions = listOf(memberOnboardingAction.id),
        healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.IMMERSION_SCHEDULE_LEAGUE
    )

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse`() {
        val providers = getProviders(person = person)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStep,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.widgetComponent).isInstanceOf(MemberOnboardingWidgetComponent::class.java)
        Assertions.assertThat(response.widgetComponent).isEqualTo(MemberOnboardingWidgetComponent.SCORE_MAGENTA)
        Assertions.assertThat(response.title).isEqualTo("Oi meu nome é ${person.nickName}")
    }

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse when providers person is null`() {
        val providers = getProviders(person = null)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStep,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.widgetComponent).isInstanceOf(MemberOnboardingWidgetComponent::class.java)
        Assertions.assertThat(response.widgetComponent).isEqualTo(MemberOnboardingWidgetComponent.SCORE_MAGENTA)
        Assertions.assertThat(response.title).isEqualTo(memberOnboardingStep.title)
    }

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse when person does not have a nickName`() {
        val personWithNoNickname = TestModelFactory.buildPerson(
            nickName = null,
            socialFirstName = "Robson"
        )

        val providers = getProviders(person = personWithNoNickname)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStep,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.widgetComponent).isInstanceOf(MemberOnboardingWidgetComponent::class.java)
        Assertions.assertThat(response.widgetComponent).isEqualTo(MemberOnboardingWidgetComponent.SCORE_MAGENTA)
        Assertions.assertThat(response.title).isEqualTo("Oi meu nome é ${personWithNoNickname.socialFirstName}")
    }

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse when person does not have a socialFirstName`() {
        val personWithNoSocialFirstName = TestModelFactory.buildPerson(
            nickName = null,
            socialFirstName = null,
        )

        val providers = getProviders(person = personWithNoSocialFirstName)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStep,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.widgetComponent).isInstanceOf(MemberOnboardingWidgetComponent::class.java)
        Assertions.assertThat(response.widgetComponent).isEqualTo(MemberOnboardingWidgetComponent.SCORE_MAGENTA)
        Assertions.assertThat(response.title).isEqualTo("Oi meu nome é ${personWithNoSocialFirstName.firstName}")
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to PRE_IMMERSION`() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.PRE_IMMERSION,
        )

        val providers = getProviders(person = person, hasDroppedOnboarding = false)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.QUESTIONNAIRE,
            properties = mapOf(
                "type" to "QUEST_IMMERSION_PROFILE",
                "show_emergency_bar" to true,
            )
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to PRE_IMMERSION not showing emergencyBar`() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.PRE_IMMERSION,
        )

        val providers = getProviders(person = person, hasDroppedOnboarding = true)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.QUESTIONNAIRE,
            properties = mapOf(
                "type" to "QUEST_IMMERSION_PROFILE",
                "show_emergency_bar" to false,
            )
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to PRE_IMMERSION and hasFormResult`() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.PRE_IMMERSION,
        )

        val providers = getProviders(person = person, hasFormResult = true)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isNull()
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to HEALTH_DECLARATION`() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.HEALTH_DECLARATION,
        )

        val providers = getProviders(person = person)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.HEALTH_DECLARATION,
            properties = mapOf(
                "show_emergency_bar" to true
            )
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to HEALTH_DECLARATION not showing emergencyBar `() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.HEALTH_DECLARATION,
        )

        val providers = getProviders(person = person, hasDroppedOnboarding = true)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.HEALTH_DECLARATION,
            properties = mapOf(
                "show_emergency_bar" to false
            )
        )


        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to HEALTH_DECLARATION and hasFormResult`() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.HEALTH_DECLARATION,
        )

        val providers = getProviders(person = person, hasFormResult = true)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isNull()
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to SCORE_MAGENTA_RESULT showing emergencyBar`() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.SCORE_MAGENTA_RESULT,
        )

        val providers = getProviders(person = person, hasDroppedOnboarding = false)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.SCORE_MAGENTA_RESULT,
            properties = mapOf(
                "is_welcome" to true,
                "show_emergency_bar" to true
            )
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to SCORE_MAGENTA_RESULT not showing emergencyBar`() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.SCORE_MAGENTA_RESULT,
        )

        val providers = getProviders(person = person, hasDroppedOnboarding = true)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.SCORE_MAGENTA_RESULT,
            properties = mapOf(
                "is_welcome" to true,
                "show_emergency_bar" to false
            )
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with blank fields to MemberOnboardingStepResponse`() {
        val memberOnboardingStepWithBlankFields = TestModelFactory.buildMemberOnboardingStep(
            title = "",
            description = "",
            imageUrl = "",
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.HOME
        )

        val providers = getProviders(person = person, memberOnboardingAction = emptyList())

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithBlankFields,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.HOME
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(0)
        Assertions.assertThat(response.optIns).hasSize(0)
        Assertions.assertThat(response.title).isNull()
        Assertions.assertThat(response.text).isNull()
        Assertions.assertThat(response.imageUrl).isNull()
        Assertions.assertThat(response.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts database step to api step response when navigation is scheduling immersion`() {
        val stepWithLeagueScheduleNavigation = TestModelFactory.buildMemberOnboardingStep(
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.IMMERSION_SCHEDULE_LEAGUE,
        )

        val responseLeagueSchedule = MemberOnboardingStepResponseConverter.convert(
            stepWithLeagueScheduleNavigation,
            getProviders(person = person),
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(responseLeagueSchedule.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(responseLeagueSchedule.navigation?.mobileRoute).isEqualTo(MobileRouting.WEBVIEW)
    }


    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse with LOADING and app state`() {
        val providers = getProviders(
            person = person,
            memberOnboardingAction = listOf(memberOnboardingActionWithAppState)
        )

        val memberOnboardingStepWithLoading = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            key = "testadinha",
            actions = listOf(memberOnboardingActionWithAppState.id),
            widgetComponent = MemberOnboardingStep.MemberOnboardingWidgetComponent.LOADING
        )

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithLoading,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions!!.first().listenAppState).isEqualTo(
            MemberOnboardingAction.MemberOnboardingActionAppState.PERSON_ASSOCIATED_TO_HEALTH_LEAGUE
        )
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.widgetComponent).isInstanceOf(MemberOnboardingWidgetComponent::class.java)
        Assertions.assertThat(response.widgetComponent).isEqualTo(MemberOnboardingWidgetComponent.LOADING)
        Assertions.assertThat(response.folder).isNull()
        Assertions.assertThat(response.video).isNull()
    }

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse with format VIDEO, FOLDER, accessory image and progress`() {
        val providers = getProviders(person = person, isBegin = true)

        val memberOnboardingStepWithVideo = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            format = MemberOnboardingStep.MemberOnboardingStepFormat.VIDEO,
            videoCaptionUrl = "video_legend",
            videoUrl = "video_url",
            type = MemberOnboardingStep.MemberOnboardingStepType.INIT,
            estimatedTimeText = "teste",
            estimatedTimeIcon = "teste",
            progress = listOf(
                MemberOnboardingProgress(
                    title = "Teste",
                    description = "Teste",
                    image = "Teste"
                )
            )

        )

        val expectedResponse =
            MemberOnboardingStepResponse(
                type = memberOnboardingStepWithVideo.format.toString(),
                video = MemberOnboardingStepVideoInformation(
                    videoUrl = memberOnboardingStepWithVideo.videoUrl,
                    caption = memberOnboardingStepWithVideo.videoCaptionUrl,
                    emergencyButtonEndTime = null
                ),
                folder = MemberOnboardingStepFolder(
                    title = "Boas vindas, ${(providers.person?.nickName ?: providers.person?.socialFirstName ?: providers.person?.firstName)}!",
                    text = DEFAULT_FOLDER_DESCRIPTION,
                    imageUrl = DEFAULT_PREVIEW_VIDEO_IMAGE,
                    showEmergencyButton = true
                ),
                tag = MemberOnboardingStepTag(
                    text = memberOnboardingStepWithVideo.estimatedTimeText,
                    icon = memberOnboardingStepWithVideo.estimatedTimeIcon
                ),
                progress = listOf(
                    MemberOnboardingStepProgressResponse(
                        title = "Teste",
                        description = "Teste",
                        imageUrl = "Teste"
                    )
                )
            )

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithVideo,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response)
            .usingRecursiveComparison()
            .ignoringFields("actions", "optIns", "key", "widgetComponent", "title").isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse with accessoryImage from low risk legacy template`() {
        val providers = getProviders(person = person, isBegin = true)

        val memberOnboardingStepWithVideo = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            format = MemberOnboardingStep.MemberOnboardingStepFormat.VIDEO,
            videoCaptionUrl = "video_legend",
            videoUrl = "video_url",
            type = MemberOnboardingStep.MemberOnboardingStepType.LOW_RISK,
            estimatedTimeText = "teste",
            estimatedTimeIcon = "teste",
            progress = listOf(
                MemberOnboardingProgress(
                    title = "Teste",
                    description = "Teste",
                    image = "Teste"
                )
            )

        )

        val expectedResponse =
            MemberOnboardingStepResponse(
                title = "Oi meu nome é Zé",
                widgetComponent = MemberOnboardingWidgetComponent.SCORE_MAGENTA,
                type = memberOnboardingStepWithVideo.format.toString(),
                video = MemberOnboardingStepVideoInformation(
                    videoUrl = memberOnboardingStepWithVideo.videoUrl,
                    caption = memberOnboardingStepWithVideo.videoCaptionUrl,
                    emergencyButtonEndTime = TIME_IN_SECONDS_LOW_RISK_SHOW_ACCESSORY_IMAGE
                ),
                accessoryImage = MemberOnboardingAccessoryImageInformation(
                    imageUrl = DEFAULT_ACCESSORY_IMAGE,
                    startTime = TIME_IN_SECONDS_LOW_RISK_SHOW_ACCESSORY_IMAGE
                ),
                folder = MemberOnboardingStepFolder(
                    title = "Boas vindas, ${(providers.person?.nickName ?: providers.person?.socialFirstName ?: providers.person?.firstName)}!",
                    text = DEFAULT_FOLDER_DESCRIPTION,
                    imageUrl = DEFAULT_PREVIEW_VIDEO_IMAGE,
                    showEmergencyButton = true
                ),
                actions = listOf(MemberOnboardingActionResponse(name = "", actionUrl = "")),
                optIns = listOf(MemberOnboardingOptInResponse(text = "teste", link = "testada.com")),
                progress = listOf(
                    MemberOnboardingStepProgressResponse(
                        title = "Teste",
                        description = "Teste",
                        imageUrl = "Teste"
                    )
                )
            )

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithVideo,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response)
            .usingRecursiveComparison()
            .ignoringFields("actions", "optIns", "key", "widgetComponent", "title").isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse with accessoryImage from low risk new portfolio template`() {
        val providers = getProviders(person = person, isBegin = true)

        val memberOnboardingStepWithVideo = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            format = MemberOnboardingStep.MemberOnboardingStepFormat.VIDEO,
            videoCaptionUrl = "video_legend",
            videoUrl = "video_url",
            type = MemberOnboardingStep.MemberOnboardingStepType.LOW_RISK,
            estimatedTimeText = "teste",
            estimatedTimeIcon = "teste",
            progress = listOf(
                MemberOnboardingProgress(
                    title = "Teste",
                    description = "Teste",
                    image = "Teste"
                )
            )

        )

        val expectedResponse =
            MemberOnboardingStepResponse(
                title = "Oi meu nome é Zé",
                widgetComponent = MemberOnboardingWidgetComponent.SCORE_MAGENTA,
                type = memberOnboardingStepWithVideo.format.toString(),
                video = MemberOnboardingStepVideoInformation(
                    videoUrl = memberOnboardingStepWithVideo.videoUrl,
                    caption = memberOnboardingStepWithVideo.videoCaptionUrl,
                    emergencyButtonEndTime = TIME_IN_SECONDS_LOW_RISK_SHOW_ACCESSORY_IMAGE_NEW_PORTFOLIO
                ),
                accessoryImage = MemberOnboardingAccessoryImageInformation(
                    imageUrl = DEFAULT_ACCESSORY_IMAGE,
                    startTime = TIME_IN_SECONDS_LOW_RISK_SHOW_ACCESSORY_IMAGE_NEW_PORTFOLIO
                ),
                folder = MemberOnboardingStepFolder(
                    title = "Boas vindas, ${(providers.person?.nickName ?: providers.person?.socialFirstName ?: providers.person?.firstName)}!",
                    text = DEFAULT_FOLDER_DESCRIPTION,
                    imageUrl = DEFAULT_PREVIEW_VIDEO_IMAGE,
                    showEmergencyButton = true
                ),
                actions = listOf(MemberOnboardingActionResponse(name = "", actionUrl = "")),
                optIns = listOf(MemberOnboardingOptInResponse(text = "teste", link = "testada.com")),
                progress = listOf(
                    MemberOnboardingStepProgressResponse(
                        title = "Teste",
                        description = "Teste",
                        imageUrl = "Teste"
                    )
                )
            )

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithVideo,
            providers,
            memberOnboardingTemplateNewPortfolio
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response)
            .usingRecursiveComparison()
            .ignoringFields("actions", "optIns", "key", "widgetComponent", "title").isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse with accessoryImage from high risk legacy template`() {
        val providers = getProviders(person = person, isBegin = true)

        val memberOnboardingStepWithVideo = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            key = "SuccessfulScheduling",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            format = MemberOnboardingStep.MemberOnboardingStepFormat.VIDEO,
            videoCaptionUrl = "video_legend",
            videoUrl = "video_url",
            type = MemberOnboardingStep.MemberOnboardingStepType.HIGH_RISK,
            estimatedTimeText = "teste",
            estimatedTimeIcon = "teste",
            progress = listOf(
                MemberOnboardingProgress(
                    title = "Teste",
                    description = "Teste",
                    image = "Teste"
                )
            )

        )

        val expectedResponse =
            MemberOnboardingStepResponse(
                title = "Oi meu nome é Zé",
                widgetComponent = MemberOnboardingWidgetComponent.SCORE_MAGENTA,
                type = memberOnboardingStepWithVideo.format.toString(),
                video = MemberOnboardingStepVideoInformation(
                    videoUrl = memberOnboardingStepWithVideo.videoUrl,
                    caption = memberOnboardingStepWithVideo.videoCaptionUrl,
                    emergencyButtonEndTime = TIME_IN_SECONDS_ENDING_VIDEO_SHOW_ACCESSORY_IMAGE
                ),
                accessoryImage = MemberOnboardingAccessoryImageInformation(
                    imageUrl = DEFAULT_ACCESSORY_IMAGE,
                    startTime = TIME_IN_SECONDS_ENDING_VIDEO_SHOW_ACCESSORY_IMAGE
                ),
                folder = MemberOnboardingStepFolder(
                    title = "Boas vindas, ${(providers.person?.nickName ?: providers.person?.socialFirstName ?: providers.person?.firstName)}!",
                    text = DEFAULT_FOLDER_DESCRIPTION,
                    imageUrl = DEFAULT_PREVIEW_VIDEO_IMAGE,
                    showEmergencyButton = true
                ),
                actions = listOf(MemberOnboardingActionResponse(name = "", actionUrl = "")),
                optIns = listOf(MemberOnboardingOptInResponse(text = "teste", link = "testada.com")),
                progress = listOf(
                    MemberOnboardingStepProgressResponse(
                        title = "Teste",
                        description = "Teste",
                        imageUrl = "Teste"
                    )
                )
            )

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithVideo,
            providers,
            memberOnboardingTemplateLegacy
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response)
            .usingRecursiveComparison()
            .ignoringFields("actions", "optIns", "key", "widgetComponent", "title").isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert converts MemberOnboardingStep to MemberOnboardingStepResponse with accessoryImage from high risk new portfolio template`() {
        val providers = getProviders(person = person, isBegin = true)

        val memberOnboardingStepWithVideo = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            key = "SuccessfulScheduling",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            format = MemberOnboardingStep.MemberOnboardingStepFormat.VIDEO,
            videoCaptionUrl = "video_legend",
            videoUrl = "video_url",
            type = MemberOnboardingStep.MemberOnboardingStepType.HIGH_RISK,
            estimatedTimeText = "teste",
            estimatedTimeIcon = "teste",
            progress = listOf(
                MemberOnboardingProgress(
                    title = "Teste",
                    description = "Teste",
                    image = "Teste"
                )
            )

        )

        val expectedResponse =
            MemberOnboardingStepResponse(
                title = "Oi meu nome é Zé",
                widgetComponent = MemberOnboardingWidgetComponent.SCORE_MAGENTA,
                type = memberOnboardingStepWithVideo.format.toString(),
                video = MemberOnboardingStepVideoInformation(
                    videoUrl = memberOnboardingStepWithVideo.videoUrl,
                    caption = memberOnboardingStepWithVideo.videoCaptionUrl,
                    emergencyButtonEndTime = TIME_IN_SECONDS_ENDING_VIDEO_SHOW_ACCESSORY_IMAGE_NEW_PORTFOLIO
                ),
                accessoryImage = MemberOnboardingAccessoryImageInformation(
                    imageUrl = DEFAULT_ACCESSORY_IMAGE,
                    startTime = TIME_IN_SECONDS_ENDING_VIDEO_SHOW_ACCESSORY_IMAGE_NEW_PORTFOLIO
                ),
                folder = MemberOnboardingStepFolder(
                    title = "Boas vindas, ${(providers.person?.nickName ?: providers.person?.socialFirstName ?: providers.person?.firstName)}!",
                    text = DEFAULT_FOLDER_DESCRIPTION,
                    imageUrl = DEFAULT_PREVIEW_VIDEO_IMAGE,
                    showEmergencyButton = true
                ),
                actions = listOf(MemberOnboardingActionResponse(name = "", actionUrl = "")),
                optIns = listOf(MemberOnboardingOptInResponse(text = "teste", link = "testada.com")),
                progress = listOf(
                    MemberOnboardingStepProgressResponse(
                        title = "Teste",
                        description = "Teste",
                        imageUrl = "Teste"
                    )
                )
            )

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithVideo,
            providers,
            memberOnboardingTemplateNewPortfolio
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response)
            .usingRecursiveComparison()
            .ignoringFields("actions", "optIns", "key", "widgetComponent", "title").isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with healthFormNavigation to MemberOnboardingStepResponse with navigation to ADDRESS_FIRST_INPUT not showing emergencyBar`() {
        val memberOnboardingStepWithNavigation = TestModelFactory.buildMemberOnboardingStep(
            title = "Oi meu nome é {nome}",
            optIns = listOf(
                MemberOnboardingOptIn(
                    text = "teste",
                    link = "testada.com",
                )
            ),
            actions = listOf(memberOnboardingAction.id),
            healthFormNavigation = MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.ADDRESS
        )

        val providers = getProviders(person = person, hasDroppedOnboarding = true)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigation,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.MEMBER_ADDRESS_INPUT
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isInstanceOf(NavigationResponse::class.java)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with immersion schedule league to MemberOnboardingStepResponse with navigation to IMMERSION_SCHEDULE not showing emergencyBar`() {
        val providers = getProviders(hasDroppedOnboarding = true, person = person)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigationToImmersion,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.WEBVIEW,
            properties = mapOf(
                "link" to providers.immersionSchedulingUrl,
                "show_emergency_bar" to false,
                "app_bar_type" to "none",
                "token" to "true"
            )
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    @Test
    fun `#convert converts MemberOnboardingStep with immersion schedule league to MemberOnboardingStepResponse with navigation to IMMERSION_SCHEDULE showing emergencyBar`() {
        val providers = getProviders(hasDroppedOnboarding = false, person = person)

        val response = MemberOnboardingStepResponseConverter.convert(
            memberOnboardingStepWithNavigationToImmersion,
            providers,
            memberOnboardingTemplateLegacy
        )

        val expectedNavigation = NavigationResponse(
            mobileRoute = MobileRouting.WEBVIEW,
            properties = mapOf(
                "link" to providers.immersionSchedulingUrl,
                "show_emergency_bar" to true,
                "app_bar_type" to "emergency",
                "token" to "true"
            )
        )

        Assertions.assertThat(response).isInstanceOf(MemberOnboardingStepResponse::class.java)
        Assertions.assertThat(response.actions!!.first()).isInstanceOf(MemberOnboardingActionResponse::class.java)
        Assertions.assertThat(response.actions).hasSize(1)
        Assertions.assertThat(response.optIns!!.first()).isInstanceOf(MemberOnboardingOptInResponse::class.java)
        Assertions.assertThat(response.optIns).hasSize(1)
        Assertions.assertThat(response.navigation).isEqualTo(expectedNavigation)
    }

    private fun getProviders(
        hasDroppedOnboarding: Boolean = false,
        person: Person?,
        hasFormResult: Boolean = false,
        memberOnboardingAction: List<MemberOnboardingAction>? = null,
        isBegin: Boolean = false,
    ): MemberOnboardingStepResponseProviders {
        return MemberOnboardingStepResponseProviders(
            memberOnboardingActions = memberOnboardingAction ?: listOf(this.memberOnboardingAction),
            person = person,
            hasDroppedOnboarding = hasDroppedOnboarding,
            immersionSchedulingUrl = "immersionSchedulingUrl",
            hasFormResult = hasFormResult,
            isBegin = isBegin
        )
    }
}
