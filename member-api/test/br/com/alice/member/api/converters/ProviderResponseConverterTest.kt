package br.com.alice.member.api.converters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialtyProfile
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.ProviderUnit.Origin
import br.com.alice.member.api.models.AddressResponse
import br.com.alice.member.api.models.ProviderResponse
import br.com.alice.member.api.models.QualificationResponse
import br.com.alice.member.api.models.WorkingHoursResponse
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test


class ProviderResponseConverterTest {

    private val i18n: suspend (String) -> String = { key: String -> key }
    private val providerUnitId = RangeUUID.generate()
    private val structuredAddress = TestModelFactory.buildStructuredAddress(referencedModelId = providerUnitId)
    private val addressLabel = "Principal"

    @Test
    fun `#convert provider unit into a provider response, with formatted_address on addresses`() = runBlocking<Unit> {
        val provider = TestModelFactory.buildProvider(name = "Fleury")
        val specialty = TestModelFactory.buildMedicalSpecialty()
        val providerUnit = TestModelFactory.buildProviderUnit(
            id = providerUnitId,
            type = ProviderUnit.Type.LABORATORY,
            name = "Fleury Moema",
            providerId = provider.id,
            medicalSpecialtyProfile = listOf(MedicalSpecialtyProfile(specialty.id))
        ).copy(address = structuredAddress)
        val specialties = mapOf(specialty.id to specialty)
        val actionNeeded = false

        val providerResponse =
            ProviderResponseConverter.convert(providerUnit, provider, specialties, actionNeeded, i18n)
        val expectedProviderResponse = ProviderResponse(
            id = providerUnit.id,
            type = providerUnit.type.toString().lowercase(),
            name = providerUnit.name,
            contractOrigin = Origin.ALICE,
            specialties = listOf(specialty.name),
            subSpecialties = emptyList(),
            addresses = listOf(AddressResponse(addressLabel, providerUnit.address!!.formattedAddress())),
            simplifiedAddresses = listOf(AddressResponse(addressLabel, providerUnit.address!!.formattedAddress())),
            site = providerUnit.site,
            crm = null,
            cnpj = providerUnit.cnpj,
            phones = providerUnit.phones.map { PhoneNumberResponseConverter.convert(it) },
            workingHours = WorkingHoursResponse(
                description = "always_open",
                alwaysOpen = true,
                weekdayText = listOf(
                    "Segunda-feira: always_open_status",
                    "Terça-feira: always_open_status",
                    "Quarta-feira: always_open_status",
                    "Quinta-feira: always_open_status",
                    "Sexta-feira: always_open_status",
                    "Sábado: always_open_status",
                    "Domingo: always_open_status"
                )
            ),
            qualifications = providerUnit.qualifications.map {
                QualificationResponse(
                    it.imageUrl,
                    it.name,
                    it.description
                )
            },
            imageUrl = providerUnit.imageUrl,
            education = emptyList(),
            curiosity = null,
            providerId = providerUnit.providerId
        )

        assertThat(providerResponse).isEqualTo(expectedProviderResponse)
    }

    @Test
    fun `#convert provider unit into a provider response, without formatted_address on addresses`() =
        runBlocking<Unit> {
            val provider = TestModelFactory.buildProvider(name = "Fleury")
            val specialty = TestModelFactory.buildMedicalSpecialty()
            val providerUnitId = RangeUUID.generate()
            val structuredAddress = TestModelFactory.buildStructuredAddress(referencedModelId = providerUnitId)
            val providerUnit = TestModelFactory.buildProviderUnit(
                id = providerUnitId,
                type = ProviderUnit.Type.LABORATORY,
                name = "Fleury Moema",
                providerId = provider.id,
                medicalSpecialtyProfile = listOf(MedicalSpecialtyProfile(specialty.id))
            ).copy(address = structuredAddress)

            val specialties = mapOf(specialty.id to specialty)

            val actionNeeded = false
            val providerResponse =
                ProviderResponseConverter.convert(providerUnit, provider, specialties, actionNeeded, i18n)

            val expectedProviderResponse = ProviderResponse(
                id = providerUnit.id,
                type = providerUnit.type.toString().lowercase(),
                name = providerUnit.name,
                contractOrigin = Origin.ALICE,
                specialties = listOf(specialty.name),
                subSpecialties = emptyList(),
                addresses = listOf(AddressResponse(addressLabel, providerUnit.address!!.formattedAddress())),
                simplifiedAddresses = listOf(AddressResponse(addressLabel, providerUnit.address!!.formattedAddress())),
                site = providerUnit.site,
                crm = null,
                cnpj = providerUnit.cnpj,
                phones = providerUnit.phones.map { PhoneNumberResponseConverter.convert(it) },
                workingHours = WorkingHoursResponse(
                    description = "always_open",
                    alwaysOpen = true,
                    weekdayText = listOf(
                        "Segunda-feira: always_open_status",
                        "Terça-feira: always_open_status",
                        "Quarta-feira: always_open_status",
                        "Quinta-feira: always_open_status",
                        "Sexta-feira: always_open_status",
                        "Sábado: always_open_status",
                        "Domingo: always_open_status"
                    )
                ),
                qualifications = providerUnit.qualifications.map {
                    QualificationResponse(
                        it.imageUrl,
                        it.name,
                        it.description
                    )
                },
                imageUrl = providerUnit.imageUrl,
                education = emptyList(),
                curiosity = null,
                providerId = providerUnit.providerId
            )

            assertThat(providerResponse).isEqualTo(expectedProviderResponse)
        }
}
