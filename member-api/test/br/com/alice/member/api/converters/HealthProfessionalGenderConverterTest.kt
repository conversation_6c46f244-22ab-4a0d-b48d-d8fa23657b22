package br.com.alice.member.api.converters

import br.com.alice.common.core.Role
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.converters.HealthProfessionalGenderConverter
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthProfessionalGenderConverterTest {
    private val staff = TestModelFactory.buildStaff()

    @Test
    fun `StaffResponseConverter#convert should convert roles to channel description for mobile`() {
        assertConversionRoleToChannelString(
            Role.CHIEF_PHYSICIAN,
            Gender.MALE,
            expectedDescription = "médico"
        )

        assertConversionRoleToChannelString(
            Role.CHIEF_PHYSICIAN,
            Gender.FEMALE,
            expectedDescription = "médica"
        )

        assertConversionRoleToChannelString(
            Role.CHIEF_PHYSICIAN,
            Gender.NON_BINARY,
            expectedDescription = "medicina"
        )

        assertConversionRoleToChannelString(
            Role.NUTRITIONIST,
            Gender.MALE,
            expectedDescription = "nutricionista"
        )

        assertConversionRoleToChannelString(
            Role.NUTRITIONIST,
            Gender.FEMALE,
            expectedDescription = "nutricionista"
        )

        assertConversionRoleToChannelString(
            Role.NUTRITIONIST,
            Gender.NON_BINARY,
            expectedDescription = "nutricionista"
        )

        assertConversionRoleToChannelString(
            Role.MANAGER_PHYSICIAN,
            Gender.MALE,
            expectedDescription = "médico"
        )

        assertConversionRoleToChannelString(
            Role.MANAGER_PHYSICIAN,
            Gender.FEMALE,
            expectedDescription = "médica"
        )

        assertConversionRoleToChannelString(
            Role.MANAGER_PHYSICIAN,
            Gender.NON_BINARY,
            expectedDescription = "medicina"
        )

        assertConversionRoleToChannelString(
            Role.HEALTHCARE_TEAM_NURSE,
            Gender.MALE,
            expectedDescription = "enfermeiro"
        )

        assertConversionRoleToChannelString(
            Role.HEALTHCARE_TEAM_NURSE,
            Gender.FEMALE,
            expectedDescription = "enfermeira"
        )

        assertConversionRoleToChannelString(
            Role.HEALTHCARE_TEAM_NURSE,
            Gender.NON_BINARY,
            expectedDescription = "enfermagem"
        )

        assertConversionRoleToChannelString(
            Role.DIGITAL_CARE_NURSE,
            Gender.MALE,
            expectedDescription = "enfermeiro"
        )

        assertConversionRoleToChannelString(
            Role.DIGITAL_CARE_NURSE,
            Gender.FEMALE,
            expectedDescription = "enfermeira"
        )

        assertConversionRoleToChannelString(
            Role.DIGITAL_CARE_NURSE,
            Gender.NON_BINARY,
            expectedDescription = "enfermagem"
        )

        assertConversionRoleToChannelString(
            Role.CHIEF_DIGITAL_CARE_NURSE,
            Gender.NON_BINARY,
            expectedDescription = "enfermagem"
        )

        assertConversionRoleToChannelString(
            Role.CHIEF_DIGITAL_CARE_PHYSICIAN,
            Gender.FEMALE,
            expectedDescription = "médica"
        )

        assertConversionRoleToChannelString(
            Role.PRODUCT_TECH,
            Gender.FEMALE,
            expectedDescription = "atendimento alice"
        )
    }

    private fun assertConversionRoleToChannelString(role: Role, gender: Gender, expectedDescription: String) {
        val staffToConvert = staff.copy(role = role, gender = gender)
        val actual = HealthProfessionalGenderConverter.convert(staffToConvert)

        assertThat(expectedDescription).isEqualTo(actual.description)
    }
}
