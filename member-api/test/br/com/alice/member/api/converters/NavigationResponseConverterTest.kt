package br.com.alice.member.api.converters

import br.com.alice.common.featureaccess.FeatureNavigation
import br.com.alice.common.featureaccess.Link
import br.com.alice.common.featureaccess.NavigationRouting
import br.com.alice.member.api.converters.NavigationResponseConverter
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import kotlin.test.Test
import kotlin.test.assertEquals

class NavigationResponseConverterTest {

    @Test
    fun `#NavigationResponseConverter convert should work correctly`() {
        val properties = mapOf(
            "1" to "1",
            "2" to "2"
        )

        val properties2 = mapOf(
            "3" to "3",
            "4" to "4"
        )

        val featureNavigation = FeatureNavigation(
            link = Link(
                href = "teste",
                rel = "teste"
            ),
            routing = NavigationRouting.EMERGENCY_CARE,
            properties = properties,
            navigation = FeatureNavigation(
                link = Link(
                    href = "teste2",
                    rel = "teste2"
                ),
                routing = NavigationRouting.EMERGENCY_CARE_PRICING,
                properties = properties2,
                navigation = FeatureNavigation(
                    routing = NavigationRouting.EMERGENCY_CARE
                )
            )
        )

        val expectedResult = NavigationResponse(
            link = br.com.alice.member.api.models.Link(
                href = "teste",
                rel = "teste"
            ),
            mobileRoute = MobileRouting.EMERGENCY_CARE,
            properties = properties,
            navigation = NavigationResponse(
                link = br.com.alice.member.api.models.Link(
                    href = "teste2",
                    rel = "teste2"
                ),
                mobileRoute = MobileRouting.EMERGENCY_CARE_PRICING,
                properties = properties2,
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.EMERGENCY_CARE
                )
            )
        )

        val result = NavigationResponseConverter.convert(featureNavigation)
        assertEquals(expectedResult, result)
    }
}
