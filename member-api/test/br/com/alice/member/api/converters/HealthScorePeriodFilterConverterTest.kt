package br.com.alice.member.api.converters

import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.member.api.converters.HealthScorePeriodFilterConverter
import br.com.alice.member.api.models.ScoreTypesPeriodFilterResponse
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockkStatic
import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import java.time.temporal.TemporalAdjusters
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthScorePeriodFilterConverterTest {

    private val localDateTime2022 = LocalDateTime.now().withYear(2022)
    private val localDateTime2021 = LocalDateTime.now().withYear(2021)

    private val temporalFirstDayOfYear = TemporalAdjusters.firstDayOfYear()
    private val temporalLastDayOfYear = TemporalAdjusters.lastDayOfYear()

    @BeforeTest
    fun setup() {
        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now().withYear(2022) } returns localDateTime2022
        every { LocalDateTime.now().withYear(2021) } returns localDateTime2021
    }

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#convert converts 2022 to startDate and endDate`() {
        val year = 2022
        val response = HealthScorePeriodFilterConverter.convert(year)
        Assertions.assertThat(response).isInstanceOf(ScoreTypesPeriodFilterResponse::class.java)
        Assertions.assertThat(response.startDate).isEqualTo(LocalDateTime.now().withYear(year).with(temporalFirstDayOfYear).atBeginningOfTheDay())
        Assertions.assertThat(response.endDate).isEqualTo(LocalDateTime.now().withYear(year).with(temporalLastDayOfYear).atEndOfTheDay())
    }

    @Test
    fun `#convert converts 2021 to startDate and endDate`() {
        val year = 2021
        val response = HealthScorePeriodFilterConverter.convert(year)
        Assertions.assertThat(response).isInstanceOf(ScoreTypesPeriodFilterResponse::class.java)
        Assertions.assertThat(response.startDate).isEqualTo(LocalDateTime.now().withYear(year).with(temporalFirstDayOfYear).atBeginningOfTheDay())
        Assertions.assertThat(response.endDate).isEqualTo(LocalDateTime.now().withYear(year).with(temporalLastDayOfYear).atEndOfTheDay())
    }
}
