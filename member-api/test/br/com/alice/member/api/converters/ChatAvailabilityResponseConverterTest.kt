package br.com.alice.member.api.converters

import br.com.alice.channel.models.ChatAvailability
import br.com.alice.channel.models.ChatHeader
import br.com.alice.channel.models.ChatHub
import br.com.alice.channel.models.StaffAvailable
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.converters.ChatAvailabilityResponseConverter
import br.com.alice.member.api.models.ChatAction
import br.com.alice.member.api.models.ChatAvailabilityResponse
import br.com.alice.member.api.models.ChatHeaderResponse
import br.com.alice.member.api.models.ChatHubResponse
import br.com.alice.member.api.models.StaffAvailableResponse
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ChatAvailabilityResponseConverterTest {

    @Test
    fun `ChatAvailabilityResponseConverterTest#convert should convert chatAvailability for mobile`() {
        val toConvert =
            ChatAvailability(Chat<PERSON><PERSON>(), <PERSON><PERSON><PERSON><PERSON>(), Chat<PERSON>eader(staff = listOf(StaffAvailable())))
        val expectedResponse = ChatAvailabilityResponse(
            hub = ChatHubResponse(
                isAvailable = toConvert.hub.isAvailable,
                description = toConvert.hub.description,
                availability = toConvert.hub.availability,
                action = ChatAction(
                    action = toConvert.hub.action.action.name,
                    description = toConvert.hub.action.description

                )
            ),
            hubAdministrative = ChatHubResponse(
                isAvailable = toConvert.hubAdministrative!!.isAvailable,
                description = toConvert.hubAdministrative!!.description,
                availability = toConvert.hubAdministrative!!.availability,
                action = ChatAction(
                    action = toConvert.hubAdministrative!!.action.action.name,
                    description = toConvert.hubAdministrative!!.action.description

                )
            ),
            header = ChatHeaderResponse(
                staff = listOf(
                    StaffAvailableResponse(
                        profileImageUrl = MobileApp.Assets.Placeholders.DOCTOR,
                        isAvailable = true
                    )
                ),
                description = toConvert.header.description
            )
        )
        val actual = ChatAvailabilityResponseConverter.convert(toConvert)
        assertThat(expectedResponse).isEqualTo(actual)
    }
}
