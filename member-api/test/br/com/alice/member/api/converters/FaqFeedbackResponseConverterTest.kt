package br.com.alice.member.api.converters

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.featureaccess.FeatureNavigation
import br.com.alice.common.featureaccess.NavigationRouting
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.FaqFeedback
import br.com.alice.member.api.models.FaqFeedbackResponse
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class FaqFeedbackResponseConverterTest {

    @Test
    fun `#convert should return expected message hein feedback is useful`() {
        val feedback = FaqFeedback(
            faqContentId = RangeUUID.generate(),
            useful = true,
            personId = PersonId(),
            feedback = "Texto feedback",
        )

        val navigation = FeatureNavigation(routing = NavigationRouting.FAQ)
        val feedbackMessage = "Demais!\nQue bom que te ajudou \uD83D\uDE0D"

        val expectedResponse = FaqFeedbackResponse(
            id = feedback.id,
            faqContentId = feedback.faqContentId,
            useful = feedback.useful,
            feedback = feedback.feedback,
            feedbackMessage = feedbackMessage,
            showUserInput = false,
        )

        val response = FaqFeedbackResponseConverter.convert(feedback, navigation, feedbackMessage, Brand.ALICE)
        assertThat(response).isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert should return expected message hein feedback is not useful`() {
        val feedback = FaqFeedback(
            faqContentId = RangeUUID.generate(),
            useful = false,
            personId = PersonId(),
            feedback = "Texto feedback",
        )

        val navigation = FeatureNavigation(routing = NavigationRouting.FAQ)
        val feedbackMessage = "Demais!\nQue bom que te ajudou \uD83D\uDE0D"

        val expectedResponse = FaqFeedbackResponse(
            id = feedback.id,
            faqContentId = feedback.faqContentId,
            useful = feedback.useful,
            feedback = feedback.feedback,
            feedbackMessage = feedbackMessage,
            showUserInput = false,
        )

        val response = FaqFeedbackResponseConverter.convert(feedback, navigation, feedbackMessage, Brand.ALICE)
        assertThat(response).isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert should return showUserInput = true when NavigationRouting == EMERGENCY_CARE`() {
        val feedback = FaqFeedback(
            faqContentId = RangeUUID.generate(),
            useful = true,
            personId = PersonId(),
            feedback = "Texto feedback",
        )

        val navigation = FeatureNavigation(routing = NavigationRouting.EMERGENCY_CARE)
        val feedbackMessage = "Demais!\nQue bom que te ajudou \uD83D\uDE0D"

        val expectedResponse = FaqFeedbackResponse(
            id = feedback.id,
            faqContentId = feedback.faqContentId,
            useful = feedback.useful,
            feedback = feedback.feedback,
            feedbackMessage = feedbackMessage,
            showUserInput = true,
        )

        val response = FaqFeedbackResponseConverter.convert(feedback, navigation, feedbackMessage, Brand.ALICE)
        assertThat(response).isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert should return showUserInput = false when NavigationRouting == EMERGENCY_CARE but member brand = DUQUESA`() {
        val feedback = FaqFeedback(
            faqContentId = RangeUUID.generate(),
            useful = true,
            personId = PersonId(),
            feedback = "Texto feedback",
        )

        val navigation = FeatureNavigation(routing = NavigationRouting.EMERGENCY_CARE)
        val feedbackMessage = "Demais!\nQue bom que te ajudou \uD83D\uDE0D"

        val expectedResponse = FaqFeedbackResponse(
            id = feedback.id,
            faqContentId = feedback.faqContentId,
            useful = feedback.useful,
            feedback = feedback.feedback,
            feedbackMessage = feedbackMessage,
            showUserInput = false,
        )

        val response = FaqFeedbackResponseConverter.convert(feedback, navigation, feedbackMessage, Brand.DUQUESA)
        assertThat(response).isEqualTo(expectedResponse)
    }

    @Test
    fun `#convert should return showUserInput = false when NavigationRouting != EMERGENCY_CARE`() {
        val feedback = FaqFeedback(
            faqContentId = RangeUUID.generate(),
            useful = true,
            personId = PersonId(),
            feedback = "Texto feedback",
        )

        val navigation = FeatureNavigation(routing = NavigationRouting.FAQ)
        val feedbackMessage = "Demais!\nQue bom que te ajudou \uD83D\uDE0D"

        val expectedResponse = FaqFeedbackResponse(
            id = feedback.id,
            faqContentId = feedback.faqContentId,
            useful = feedback.useful,
            feedback = feedback.feedback,
            feedbackMessage = feedbackMessage,
            showUserInput = false,
        )

        val response = FaqFeedbackResponseConverter.convert(feedback, navigation, feedbackMessage, Brand.ALICE)
        assertThat(response).isEqualTo(expectedResponse)
    }
}
