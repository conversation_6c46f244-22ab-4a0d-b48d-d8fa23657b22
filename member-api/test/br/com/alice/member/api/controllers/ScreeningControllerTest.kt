package br.com.alice.member.api.controllers

import br.com.alice.app.content.client.screens.ScreeningScreenService
import br.com.alice.app.content.model.*
import br.com.alice.app.content.model.section.CardSection
import br.com.alice.app.content.model.section.ImageSection
import br.com.alice.app.content.model.section.TextSection
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelCreationParameters
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Person
import br.com.alice.healthlogic.client.BudNavigationService
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.converters.appContent.ScreenResponseConverter
import br.com.alice.person.client.PersonService
import br.com.alice.screening.client.ScreenDataService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class ScreeningControllerTest : RoutesTestHelper() {

    private val screeningScreenService: ScreeningScreenService = mockk()
    private val screenDataService: ScreenDataService = mockk()
    private val personService: PersonService = mockk()
    private val budNavigationService: BudNavigationService = mockk()

    private val screeningController = ScreeningController(
        screeningScreenService,
        screenDataService,
        personService,
        budNavigationService
    )

    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()
    private val screeningNavigationId = RangeUUID.generate()

    private val budTriageBaseScreen = ScreensTransport(
        id = ScreenType.SCREENING_QUESTION.value,
        layout = ScreenLayout(
            type = "single_column",
            appBar = AppBar(
                title = "Triagem Alice Agora",
                back = "",
            ),
            body = listOf(
                Section(
                    id = "id",
                    type = SectionType.CARD_SECTION,
                    data = CardSection(
                        title = "title",
                        type = CardType.TEXT_CARD,
                        onCardClick = RemoteAction(
                            navigationGroup = "navigationGroupId",
                            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                            params = mapOf(
                                "action" to mapOf(
                                    "method" to "GET",
                                    "endpoint" to "http://localhost:8080/bud",
                                )
                            )
                        )
                    ),
                    minAppVersion = "1.0.0"
                )
            )
        )
    )


    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { screeningController }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        screeningScreenService,
        screenDataService,
        personService,
        budNavigationService
    )

    @Test
    fun `#getScreeningQuestion should get screening question`() {
        val screen = ScreensTransport(
            id = ScreenType.SCREENING_QUESTION.value,
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    title = "Novo atendimento",
                    back = "",
                ),
                body = listOf(
                    Section(
                        id = "screening_section_question_31",
                        type = SectionType.TEXT_SECTION,
                        data = TextSection.Content(
                            content = TextSection.Content.Value(
                                title = "Quero falar sobre minha saúde, \nSobre o que você quer falar?",
                                alignment = Alignment.LEFT,
                                layout = SectionTextLayout.TITLE_MEDIUM_HIGHLIGHT
                            )
                        ),
                        minAppVersion = "1.0.0"
                    ),
                ),
            )
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            screeningScreenService.get(
                questionKey = "31",
                personId = person.id,
                appVersion = "1.0.0",
                question = "a",
                answer = "b",
                person = person
            )
        } returns screen.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("app_content/screen/screening?question_id=31&question=a&answer=b") { response ->
                assertThat(response).isOKWithData(ScreenResponseConverter.convert(screen))
            }
        }

        coVerifyOnce {
            screeningScreenService.get(any(), any(), any(), any(), any(), any())
            personService.get(any())
        }
    }

    @Test
    fun `#finishScreeningAndOpenChat should call screen data service to finish screening with parameter to create chat`() {
        val action = RemoteAction()

        coEvery {
            screenDataService.finishScreening(
                screeningNavigationId = screeningNavigationId,
                shouldOpenChat = true,
                channelCreationParameters = match { it.category == ChannelCategory.ADMINISTRATIVE && it.subCategory == null },
                userAgent = "Ktor client",
                appVersion = "1.0.0"
            )
        } returns action.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("screening/${screeningNavigationId}/open_chat/?content=test&category=ADMINISTRATIVE") { response ->
                assertThat(response).isOKWithData(action)
            }
        }

        coVerifyOnce { screenDataService.finishScreening(any(), any(), any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#finishScreeningAndClose should call screen data service to finish screening`() {
        val action = RemoteAction()

        coEvery {
            screenDataService.finishScreening(
                screeningNavigationId = screeningNavigationId,
                userAgent = "Ktor client",
                appVersion = "1.0.0"
            )
        } returns action.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("screening/${screeningNavigationId}/close") { response ->
                assertThat(response).isOKWithData(action)
            }
        }

        coVerifyOnce { screenDataService.finishScreening(any(), any(), any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#finishScreeningAndOpenChat returns simple action and create chat with default parameters`() {
        val action = RemoteAction()

        val channelCreationParameters = ChannelCreationParameters(
            content = "test",
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.SCREENING,
            subCategoryClassifier = null,
            tags = emptyList(),
            budNodeId = null
        )

        coEvery {
            screenDataService.finishScreening(
                screeningNavigationId = screeningNavigationId,
                shouldOpenChat = true,
                channelCreationParameters = channelCreationParameters,
                userAgent = "Ktor client",
                appVersion = "1.0.0"
            )
        } returns action.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("screening/${screeningNavigationId}/open_chat/?content=test") { response ->
                assertThat(response).isOKWithData(action)
            }
        }

        coVerifyOnce { screenDataService.finishScreening(any(), any(), any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#finishScreeningAndOpenChat returns simple action and create chat with AI parameters`() {
        val action = RemoteAction()

        val channelCreationParameters = ChannelCreationParameters(
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.SCREENING,
            subCategoryClassifier = ChannelSubCategoryClassifier.AI,
            tags = emptyList(),
            budNodeId = null
        )

        coEvery {
            screenDataService.finishScreening(
                screeningNavigationId = screeningNavigationId,
                shouldOpenChat = true,
                channelCreationParameters = channelCreationParameters,
                userAgent = "Ktor client",
                appVersion = "1.0.0"
            )
        } returns action.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("screening/${screeningNavigationId}/open_chat/?kind=CHAT&category=ASSISTANCE&sub_category=SCREENING&sub_category_classifier=AI") { response ->
                assertThat(response).isOKWithData(action)
            }
        }

        coVerifyOnce { screenDataService.finishScreening(any(), any(), any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#finishScreeningAndOpenChat returns simple action and create chat with hide member input parameter`() {
        val action = RemoteAction()

        val channelCreationParameters = ChannelCreationParameters(
            kind = ChannelKind.CHAT,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.SCREENING,
            hideMemberInput = true,
            tags = emptyList(),
            budNodeId = null
        )

        coEvery {
            screenDataService.finishScreening(
                screeningNavigationId = screeningNavigationId,
                shouldOpenChat = true,
                channelCreationParameters = channelCreationParameters,
                userAgent = "Ktor client",
                appVersion = "1.0.0"
            )
        } returns action.success()

        authenticatedAs(token, toTestPerson(person)) {
            post("screening/${screeningNavigationId}/open_chat/?kind=CHAT&category=ASSISTANCE&sub_category=SCREENING&hide_member_input=true") { response ->
                assertThat(response).isOKWithData(action)
            }
        }

        coVerifyOnce { screenDataService.finishScreening(any(), any(), any(), any(), any(), any(), any()) }
    }

    @Test
    fun `#getScreeningQuestion should get new triage screen when flag is on`() = runBlocking {
        val screen = ScreensTransport(
            id = ScreenType.SCREENING_QUESTION.value,
            properties = ScreenProperties(
                alignment = ScreenAlignment(
                    vertical = ScreenAlignmentType.END,
                    horizontal = ScreenAlignmentType.START
                ),
                closeBehavior = CloseBehavior(
                    strategy = CloseStrategy.CONFIRMATION,
                    action = RemoteAction(
                        mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                        params = mapOf(
                            "action" to mapOf(
                                "method" to "GET",
                                "endpoint" to "http://localhost:8080/bud?close_button_pressed=true"
                            )
                        ),
                        transition = RemoteActionTransition.BOTTOM_SHEET_FIXED
                    )
                )
            ),
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    title = "Triagem Alice Agora",
                    back = "",
                    rightRemoteActionItems = listOf(
                        RemoteActionItem(
                            icon = "chat",
                            action = RemoteAction(
                                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                                params = mapOf(
                                    "action" to mapOf(
                                        "method" to "GET",
                                        "endpoint" to "http://localhost:8080/bud?chat_button_pressed=true",
                                    )
                                ),
                                transition = RemoteActionTransition.BOTTOM_SHEET_FIXED
                            )
                        )
                    ),
                ),
                body = listOf(
                    Section(
                        id = "bud_image_section_id",
                        type = SectionType.IMAGE_SECTION,
                        data = ImageSection(
                            content = ImageSection.Content(
                                image = Image(
                                    url = "https://alice-member-app-assets.s3.amazonaws.com/screening/consult.svg",
                                    type = ImageType.STATIC,
                                    size = ImageSize.SMALL,
                                ),
                                verticalPadding = SectionPadding.P6,
                                alignment = Alignment.LEFT,
                            )
                        ),
                        minAppVersion = "1.0.0"
                    ),
                    Section(
                        id = "bud_title_section_",
                        type = SectionType.TEXT_SECTION,
                        data = TextSection.Content(
                            content = TextSection.Content.Value(
                                title = "test",
                                alignment = Alignment.LEFT,
                                layout = SectionTextLayout.TITLE_LARGE_HIGHLIGHT
                            )
                        ),
                        minAppVersion = "1.0.0"
                    ),
                ),
                Section(
                    id = "id",
                    type = SectionType.CARD_SECTION,
                    data = CardSection(
                        title = "title",
                        type = CardType.TEXT_CARD,
                        onCardClick = RemoteAction(
                            navigationGroup = "navigationGroupId",
                            mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                            params = mapOf(
                                "action" to mapOf(
                                    "method" to "GET",
                                    "endpoint" to "http://localhost:8080/bud",
                                )
                            )
                        )
                    ),
                    minAppVersion = "1.0.0"
                )
            )
        )

        withFeatureFlag(
            namespace = FeatureNamespace.SCREENING,
            key = "triage_protocol_id",
            value = "531405c7-c745-4d59-b07a-1e0cb538e1b5"
        ) {
            coEvery { personService.get(person.id) } returns person.copy(tags = listOf("internal")).success()
            coEvery {
                budNavigationService.startNavigation(
                    person.id,
                    "531405c7-c745-4d59-b07a-1e0cb538e1b5".toUUID(),
                    "Ktor client"
                )
            } returns screen.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("app_content/screen/screening?question_id=screening-question-21") { response ->
                    assertThat(response).isOKWithData(screen)
                }
            }

            coVerifyOnce {
                budNavigationService.startNavigation(any(), any(), any())
                personService.get(any())
            }
        }
    }

    @Test
    fun `#getScreeningQuestion with correct questionId should return screen for PrescriptionProtocolId`() =
        runBlocking {
            val expectedPrescriptionProtocolId = "2b531a26-7d1a-4dc3-b4df-905e2a605100"
            val expectedTriageProtocolId = "531405c7-c745-4d59-b07a-1e0cb538e1b5"
            val questionIdValue = "screening-question-313"

            withFeatureFlags(
                FeatureNamespace.SCREENING to mapOf(
                    "triage_protocol_id" to expectedTriageProtocolId,
                    "triage_prescription_protocol_id" to expectedPrescriptionProtocolId
                )
            ) {
                coEvery { personService.get(person.id) } returns person.copy(tags = listOf("internal")).success()
                coEvery {
                    budNavigationService.startNavigation(
                        personId = person.id,
                        nodeId = expectedPrescriptionProtocolId.toUUID(),
                        userAgent = "Ktor client"
                    )
                } returns budTriageBaseScreen.success()

                authenticatedAs(token, toTestPerson(person)) {
                    get("app_content/screen/screening?question_id=$questionIdValue") { response ->
                        assertThat(response).isOKWithData(budTriageBaseScreen)
                    }
                }

                coVerifyOnce {
                    budNavigationService.startNavigation(any(), any(), any())
                    personService.get(any())
                }
            }
        }

    @Test
    fun `#getScreeningQuestion with correct questionId should return screen for TriageProtocolId`() = runBlocking {
        val expectedPrescriptionProtocolId = "2b531a26-7d1a-4dc3-b4df-905e2a605100"
        val expectedTriageProtocolId = "531405c7-c745-4d59-b07a-1e0cb538e1b5"
        val questionIdValue = "screening-question-21"

        withFeatureFlags(
            FeatureNamespace.SCREENING to mapOf(
                "triage_protocol_id" to expectedTriageProtocolId,
                "triage_prescription_protocol_id" to expectedPrescriptionProtocolId,
            )
        ) {
            coEvery { personService.get(person.id) } returns person.copy(tags = listOf("internal")).success()
            coEvery {
                budNavigationService.startNavigation(
                    personId = person.id,
                    nodeId = expectedTriageProtocolId.toUUID(),
                    userAgent = "Ktor client"
                )
            } returns budTriageBaseScreen.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("app_content/screen/screening?question_id=$questionIdValue") { response ->
                    assertThat(response).isOKWithData(budTriageBaseScreen)
                }
            }

            coVerifyOnce {
                budNavigationService.startNavigation(any(), any(), any())
                personService.get(any())
                }
            }
        }

    @Test
    fun `#getScreeningQuestion return screening question on start`() {
        val screen = ScreensTransport(
            id = ScreenType.SCREENING_QUESTION.value,
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    title = "Novo atendimento",
                    back = "",
                ),
                body = listOf()
            )
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery {
            screeningScreenService.get(
                questionKey = "screening-question-1",
                personId = person.id,
                appVersion = "1.0.0",
                question = "",
                answer = "",
                person = person
            )
        } returns screen.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("app_content/screen/screening") { response ->
                assertThat(response).isOKWithData(ScreenResponseConverter.convert(screen))
            }
        }

        coVerifyOnce {
            screeningScreenService.get(any(), any(), any(), any(), any(), any())
            personService.get(any())
        }
    }

    @Test
    fun `#getScreeningQuestion with correct questionId should return screen for new administrative triage`() =
        runBlocking {
            val expectedTriageAdministrativeProtocolId = "e69f0e89-4602-4a51-9a8c-09c6a289d200"
            val questionIdValue = "screening-question-314"

            withFeatureFlag(
                namespace= FeatureNamespace.SCREENING,
                key = "triage_administrative_protocol_id",
                value = expectedTriageAdministrativeProtocolId
            ) {
                coEvery { personService.get(person.id) } returns person.copy(tags = listOf("internal")).success()
                coEvery {
                    budNavigationService.startNavigation(
                        personId = person.id,
                        nodeId = expectedTriageAdministrativeProtocolId.toUUID(),
                        userAgent = "Ktor client"
                    )
                } returns budTriageBaseScreen.success()

                authenticatedAs(token, toTestPerson(person)) {
                    get("app_content/screen/screening?question_id=$questionIdValue") { response ->
                        assertThat(response).isOKWithData(budTriageBaseScreen)
                    }
                }

                coVerifyOnce {
                    budNavigationService.startNavigation(any(), any(), any())
                    personService.get(any())
                }
            }
        }

    @Nested
    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    inner class AiTriageFlagsTest {
        private fun flagsConfig() = listOf(
            arrayOf(
                "Return screening question with AI when person is adult",
                "screening-question-1-AI",
                person.copy(dateOfBirth = LocalDateTime.now().minusYears(20))
            ),
            arrayOf(
                "Return screening question without AI when person is child",
                "screening-question-1",
                person.copy(dateOfBirth = LocalDateTime.now().minusYears(15))
            )
        )

        @ParameterizedTest(name = "{0}")
        @MethodSource("flagsConfig")
        fun `#getScreeningQuestion returns screening question on start`(
            testName: String,
            questionKey: String,
            person: Person
        ) = runBlocking {

            val screen = ScreensTransport(
                id = ScreenType.SCREENING_QUESTION.value,
                layout = ScreenLayout(
                    type = "single_column",
                    appBar = AppBar(
                        title = "Novo atendimento",
                        back = "",
                    ),
                    body = listOf()
                )
            )

            coEvery { personService.get(person.id) } returns person.success()

            coEvery {
                screeningScreenService.get(
                    questionKey = questionKey,
                    personId = person.id,
                    appVersion = "1.0.0",
                    question = "",
                    answer = "",
                    person = person
                )
            } returns screen.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("app_content/screen/screening") { response ->
                    assertThat(response).isOKWithData(ScreenResponseConverter.convert(screen))
                }
            }

            coVerifyOnce {
                screeningScreenService.get(any(), any(), any(), any(), any(), any())
                personService.get(any())
            }
        }
    }
}
