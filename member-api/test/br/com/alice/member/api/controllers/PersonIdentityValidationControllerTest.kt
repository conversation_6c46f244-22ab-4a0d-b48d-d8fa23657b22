package br.com.alice.member.api.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PersonIdentityValidationStatus
import br.com.alice.data.layer.models.TrackPersonAB
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.IdentityValidationDistributionPath
import br.com.alice.member.api.models.PersonIdentityValidationResponse
import br.com.alice.member.api.models.ShouldValidateIdentityResponse
import br.com.alice.person.client.PersonIdentityValidationService
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Nested
import kotlin.test.BeforeTest
import kotlin.test.Test

class PersonIdentityValidationControllerTest : RoutesTestHelper() {
    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()

    private val personIdentityValidationService: PersonIdentityValidationService = mockk()
    private val trackPersonABService: TrackPersonABService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single {
            PersonIdentityValidationController(
                personIdentityValidationService,
                trackPersonABService
            )
        }
    }

    @Nested
    inner class LastValidationResult {

        @Test
        fun `#get the last validation result from current person`() {
            val personIdentityValidation =
                TestModelFactory.buildPersonIdentityValidation(status = PersonIdentityValidationStatus.VALID)
            val expectedResponse = PersonIdentityValidationResponse(status = PersonIdentityValidationStatus.VALID)

            coEvery {
                personIdentityValidationService.getLastValidationResultByPersonId(person.id)
            } returns personIdentityValidation

            authenticatedAs(token, toTestPerson(person)) {
                get("/person/last_validation_result") { response ->
                    ResponseAssert.assertThat(response).isOKWithData(expectedResponse)

                    coVerifyOnce { personIdentityValidationService.getLastValidationResultByPersonId(person.id) }
                }
            }
        }
    }

    @Nested
    inner class ShouldValidationIdentity {
        @Test
        fun `should return should validation identity as false when the FF min_app_version_identity_validation is higher than app version`() {
            runBlocking<Unit> {
                withFeatureFlags(
                    FeatureNamespace.MEMBERSHIP to mapOf(
                        "min_app_version_identity_validation" to "4.26.0",
                    )
                ) {
                    authenticatedAs(token, toTestPerson(person)) {
                        get(
                            "/person/should_validate_identity",
                            headers = (mapOf(HttpHeaders.UserAgent to "iOS/4.25.0-19307 (iPhone14,5/15.6)"))
                        ) { response ->
                            ResponseAssert.assertThat(response)
                                .isOKWithData(ShouldValidateIdentityResponse(
                                    false,
                                    null,
                                    null
                                ))
                        }
                    }
                }
            }
        }

        @Test
        fun `should return should validation identity as false when the FF identity_validation_person_skip_list has the personId in list`() =
            runBlocking<Unit> {
                withFeatureFlags(
                    FeatureNamespace.MEMBERSHIP to mapOf(
                        "min_app_version_identity_validation" to "4.25.0",
                        "identity_validation_person_skip_list" to
                                listOf(person.id),
                    )
                ) {
                    authenticatedAs(token, toTestPerson(person)) {
                        get(
                            "/person/should_validate_identity",
                            headers = (mapOf(HttpHeaders.UserAgent to "iOS/4.25.0-19307 (iPhone14,5/15.6)"))
                        ) { response ->
                            ResponseAssert.assertThat(response)
                                .isOKWithData(ShouldValidateIdentityResponse(
                                    false,
                                    null,
                                    null
                                ))
                        }
                    }
                }
            }

        @Test
        fun `should return should validation identity as false when the person is not in the ab test`() =
            runBlocking<Unit> {
                withFeatureFlags(
                    FeatureNamespace.MEMBERSHIP to mapOf(
                        "min_app_version_identity_validation" to "4.25.0",
                        "identity_validation_person_skip_list" to
                                emptyList<PersonId>()
                    ),
                ) {

                    coEvery {
                        trackPersonABService.findOrStartAb(
                            personId = person.id,
                            namespace = FeatureNamespace.MEMBERSHIP,
                            key = "identity_validation_distribution",
                            defaultPath = IdentityValidationDistributionPath.WITHOUT_VALIDATION.name
                        )
                    } returns TrackPersonAB(
                        personId = person.id,
                        featureConfigId = RangeUUID.generate(),
                        abPath = IdentityValidationDistributionPath.WITHOUT_VALIDATION.name
                    ).success()

                    authenticatedAs(token, toTestPerson(person)) {
                        get(
                            "/person/should_validate_identity",
                            headers = (mapOf(HttpHeaders.UserAgent to "iOS/4.25.0-19307 (iPhone14,5/15.6)"))
                        ) { response ->
                            ResponseAssert.assertThat(response)
                                .isOKWithData(ShouldValidateIdentityResponse(
                                    false,
                                    null,
                                    null
                                ))
                        }
                    }
                }
            }

        @Test
        fun `should return should validation identity as true when the person is in the ab test`() =
            runBlocking<Unit> {
                withFeatureFlags(
                    FeatureNamespace.MEMBERSHIP to mapOf(
                        "min_app_version_identity_validation" to "4.25.0",
                        "identity_validation_person_skip_list" to
                                emptyList<PersonId>()
                    ),
                ) {
                    coEvery { personIdentityValidationService.createTransaction(person.id) } returns "123".success()
                    coEvery { personIdentityValidationService.getAccessToken() } returns "abc".success()

                    coEvery {
                        trackPersonABService.findOrStartAb(
                            personId = person.id,
                            namespace = FeatureNamespace.MEMBERSHIP,
                            key = "identity_validation_distribution",
                            defaultPath = IdentityValidationDistributionPath.WITHOUT_VALIDATION.name
                        )
                    } returns TrackPersonAB(
                        personId = person.id,
                        featureConfigId = RangeUUID.generate(),
                        abPath = IdentityValidationDistributionPath.WITH_VALIDATION.name
                    ).success()

                    authenticatedAs(token, toTestPerson(person)) {
                        get(
                            "/person/should_validate_identity",
                            headers = (mapOf(HttpHeaders.UserAgent to "iOS/4.25.0-19307 (iPhone14,5/15.6)"))
                        ) { response ->
                            ResponseAssert.assertThat(response)
                                .isOKWithData(ShouldValidateIdentityResponse(
                                    true,
                                    "123",
                                    "abc"
                                ))
                        }
                    }
                }
            }
    }
}
