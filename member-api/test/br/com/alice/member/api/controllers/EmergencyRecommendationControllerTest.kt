package br.com.alice.member.api.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.EmergencyRecommendation
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.dragonradar.client.EmergencyRecommendationService
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.ProviderWithRecommendationResponse
import br.com.alice.person.client.MemberService
import br.com.alice.product.client.ProductService
import br.com.alice.product.model.ProductWithBundles
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class EmergencyRecommendationControllerTest : RoutesTestHelper() {

    private val providerService: ProviderService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()
    private val memberService: MemberService = mockk()
    private val productService: ProductService = mockk()
    private val emergencyRecommendationService: EmergencyRecommendationService = mockk()

    private val token = RangeUUID.generate().toString()
    private val person = TestModelFactory.buildPerson()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            EmergencyRecommendationController(
                providerService,
                providerUnitService,
                memberService,
                productService,
                emergencyRecommendationService,
            )
        }
    }

    @AfterTest
    fun clear() {
        clearAllMocks()
    }

    @Test
    fun `#getEmergencyUnitiesWithRecommendation should return all providers and recommendation`() = runBlocking {
        val healthPlanTaskId = RangeUUID.generate()
        val provider = TestModelFactory.buildProvider()
        val providerUnit = TestModelFactory.buildProviderUnit(
            providerId = provider.id,
            type = ProviderUnit.Type.EMERGENCY_UNITY
        )
        val recommendation = EmergencyRecommendation(
            personId = person.id,
            healthPlanTaskId = healthPlanTaskId,
            recommendedProviderUnitId = providerUnit.id,
        )
        val productBundle = TestModelFactory.buildProductBundle(providerIds = listOf(provider.id))
        val product = TestModelFactory.buildProduct(bundleIds = listOf(productBundle.id))
        val member = TestModelFactory.buildMember(personId = recommendation.personId, product = product)

        coEvery {
            memberService.findActiveMembership(recommendation.personId)
        } returns member.success()
        coEvery {
            productService.getProductWithBundles(product.id, any())
        } returns ProductWithBundles(product, listOf(productBundle)).success()
        coEvery {
            providerService.getByIds(listOf(provider.id))
        } returns listOf(provider).success()
        coEvery {
            providerUnitService.getByProviderIdsWithTypes(listOf(provider.id), any())
        } returns listOf(providerUnit).success()
        coEvery {
            emergencyRecommendationService.findByHealthPlanTaskId(healthPlanTaskId)
        } returns recommendation.success()


        withFeatureFlag(FeatureNamespace.MEMBERSHIP, "show_emergency_recommendation_in_app", true) {
            authenticatedAs(token, toTestPerson(person)) {

                get("/health_plan/tasks/emergency/$healthPlanTaskId/providers") { response ->
                    val payload: ProviderWithRecommendationResponse = response.bodyAsJson()

                    assertThat(response).isSuccessfulJson()
                    assertThat(payload.recommendation!!.id).isEqualTo(providerUnit.id)
                    assertThat(payload.providers.first().id).isEqualTo(providerUnit.id)
                }
            }
        }
    }

    @Test
    fun `#getEmergencyUnitiesWithRecommendation should return only providers when feature flag is disabled`() {
        val healthPlanTaskId = RangeUUID.generate()
        val provider = TestModelFactory.buildProvider()
        val providerUnitEmergency = TestModelFactory.buildProviderUnit(
            providerId = provider.id,
            type = ProviderUnit.Type.EMERGENCY_UNITY
        )
        val providerUnitMaternity = TestModelFactory.buildProviderUnit(
            providerId = provider.id,
            type = ProviderUnit.Type.MATERNITY
        )
        val recommendation = EmergencyRecommendation(
            personId = person.id,
            healthPlanTaskId = healthPlanTaskId,
            recommendedProviderUnitId = providerUnitEmergency.id,
        )
        val productBundle = TestModelFactory.buildProductBundle(providerIds = listOf(provider.id))
        val product = TestModelFactory.buildProduct(bundleIds = listOf(productBundle.id))
        val member = TestModelFactory.buildMember(personId = recommendation.personId, product = product)

        coEvery {
            memberService.findActiveMembership(recommendation.personId)
        } returns member.success()
        coEvery {
            productService.getProductWithBundles(product.id, any())
        } returns ProductWithBundles(product, listOf(productBundle)).success()
        coEvery {
            providerService.getByIds(listOf(provider.id))
        } returns listOf(provider).success()
        coEvery {
            providerUnitService.getByProviderIdsWithTypes(listOf(provider.id), any())
        } returns listOf(providerUnitEmergency, providerUnitMaternity).success()

        authenticatedAs(token, toTestPerson(person)) {

            get("/health_plan/tasks/emergency/$healthPlanTaskId/providers") { response ->
                val payload: ProviderWithRecommendationResponse = response.bodyAsJson()

                assertThat(response).isSuccessfulJson()
                assertThat(payload.recommendation).isNull()
                assertThat(payload.providers.first().id).isEqualTo(providerUnitEmergency.id)
                assertThat(payload.providers.size).isEqualTo(2)

                coVerify { emergencyRecommendationService wasNot called }
            }
        }
    }

    @Test
    fun `#getEmergencyUnitiesWithRecommendation should return null for recommendation when provider is not found`() {
        val healthPlanTaskId = RangeUUID.generate()
        val provider = TestModelFactory.buildProvider()
        val providerUnit = TestModelFactory.buildProviderUnit(
            providerId = provider.id,
            type = ProviderUnit.Type.EMERGENCY_UNITY
        )
        val recommendation = EmergencyRecommendation(
            personId = person.id,
            healthPlanTaskId = healthPlanTaskId,
            recommendedProviderUnitId = RangeUUID.generate(),
        )
        val productBundle = TestModelFactory.buildProductBundle(providerIds = listOf(provider.id))
        val product = TestModelFactory.buildProduct(bundleIds = listOf(productBundle.id))
        val member = TestModelFactory.buildMember(personId = recommendation.personId, product = product)

        coEvery {
            memberService.findActiveMembership(recommendation.personId)
        } returns member.success()
        coEvery {
            productService.getProductWithBundles(product.id, any())
        } returns ProductWithBundles(product, listOf(productBundle)).success()
        coEvery {
            providerService.getByIds(listOf(provider.id))
        } returns listOf(provider).success()
        coEvery {
            providerUnitService.getByProviderIdsWithTypes(listOf(provider.id), any())
        } returns listOf(providerUnit).success()
        coEvery {
            emergencyRecommendationService.findByHealthPlanTaskId(healthPlanTaskId)
        } returns recommendation.success()

        authenticatedAs(token, toTestPerson(person)) {

            get("/health_plan/tasks/emergency/$healthPlanTaskId/providers") { response ->
                val payload: ProviderWithRecommendationResponse = response.bodyAsJson()

                assertThat(response).isSuccessfulJson()
                assertThat(payload.recommendation).isNull()
                assertThat(payload.providers.first().id).isEqualTo(providerUnit.id)
            }
        }
    }

    @Test
    fun `#getEmergencyUnitiesWithRecommendation should return null for recommendation when there is no recommendation`() {
        val healthPlanTaskId = RangeUUID.generate()
        val provider = TestModelFactory.buildProvider()
        val providerUnit = TestModelFactory.buildProviderUnit(
            providerId = provider.id,
            type = ProviderUnit.Type.EMERGENCY_UNITY
        )
        val recommendation = EmergencyRecommendation(
            personId = person.id,
            healthPlanTaskId = healthPlanTaskId,
        )
        val productBundle = TestModelFactory.buildProductBundle(providerIds = listOf(provider.id))
        val product = TestModelFactory.buildProduct(bundleIds = listOf(productBundle.id))
        val member = TestModelFactory.buildMember(personId = recommendation.personId, product = product)

        coEvery {
            memberService.findActiveMembership(recommendation.personId)
        } returns member.success()
        coEvery {
            productService.getProductWithBundles(product.id, any())
        } returns ProductWithBundles(product, listOf(productBundle)).success()
        coEvery {
            providerService.getByIds(listOf(provider.id))
        } returns listOf(provider).success()
        coEvery {
            providerUnitService.getByProviderIdsWithTypes(listOf(provider.id), any())
        } returns listOf(providerUnit).success()
        coEvery {
            emergencyRecommendationService.findByHealthPlanTaskId(healthPlanTaskId)
        } returns recommendation.success()

        authenticatedAs(token, toTestPerson(person)) {

            get("/health_plan/tasks/emergency/$healthPlanTaskId/providers") { response ->
                val payload: ProviderWithRecommendationResponse = response.bodyAsJson()

                assertThat(response).isSuccessfulJson()
                assertThat(payload.recommendation).isNull()
                assertThat(payload.providers.first().id).isEqualTo(providerUnit.id)
            }
        }
    }

    @Test
    fun `#getEmergencyUnitiesWithRecommendation should return success even if recommendation is not found`() {
        val healthPlanTaskId = RangeUUID.generate()
        val provider = TestModelFactory.buildProvider()
        val providerUnit = TestModelFactory.buildProviderUnit(
            providerId = provider.id,
            type = ProviderUnit.Type.EMERGENCY_UNITY
        )
        val productBundle = TestModelFactory.buildProductBundle(providerIds = listOf(provider.id))
        val product = TestModelFactory.buildProduct(bundleIds = listOf(productBundle.id))
        val member = TestModelFactory.buildMember(personId = person.id, product = product)

        coEvery {
            memberService.findActiveMembership(person.id)
        } returns member.success()
        coEvery {
            productService.getProductWithBundles(product.id, any())
        } returns ProductWithBundles(product, listOf(productBundle)).success()
        coEvery {
            providerService.getByIds(listOf(provider.id))
        } returns listOf(provider).success()
        coEvery {
            providerUnitService.getByProviderIdsWithTypes(listOf(provider.id), any())
        } returns listOf(providerUnit).success()
        coEvery {
            emergencyRecommendationService.findByHealthPlanTaskId(healthPlanTaskId)
        } returns NotFoundException("not found").failure()

        authenticatedAs(token, toTestPerson(person)) {

            get("/health_plan/tasks/emergency/$healthPlanTaskId/providers") { response ->
                val payload: ProviderWithRecommendationResponse = response.bodyAsJson()

                assertThat(response).isSuccessfulJson()
                assertThat(payload.recommendation).isNull()
                assertThat(payload.providers.first().id).isEqualTo(providerUnit.id)
            }
        }
    }

}
