package br.com.alice.member.api.controllers

import br.com.alice.app.content.client.AliceScreensService
import br.com.alice.app.content.model.PaginationLayout
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.HealthPlanTaskStatus
import br.com.alice.data.layer.models.HealthPlanTaskType.EATING
import br.com.alice.data.layer.models.HealthPlanTaskType.PRESCRIPTION
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.data.layer.models.Start
import br.com.alice.data.layer.models.StartType
import br.com.alice.data.layer.models.copy
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.models.TaskProgressInfo
import br.com.alice.healthplan.models.TaskProgressType
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.api.services.FeaturePreferences
import br.com.alice.member.api.services.FeaturePreferencesNotifier
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthPlanTaskControllerTest : RoutesTestHelper() {
    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()

    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val aliceScreensService: AliceScreensService = mockk()

    private val healthPlanTaskController = HealthPlanTaskController(
        healthPlanTaskService,
        aliceScreensService
    )

    private val now = LocalDateTime.of(2021, 6, 6, 15, 0)

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { healthPlanTaskController }
    }

    @Test
    fun `#init returns TaskProgressInfo`() = mockLocalDateTime(now) {
        val taskId = RangeUUID.generate()

        val task = TestModelFactory.buildHealthPlanTask().copy(
            initiatedByMemberAt = now,
            status = HealthPlanTaskStatus.ACTIVE,
            start = Start(type = StartType.IMMEDIATE),
            deadline = Deadline(unit = PeriodUnit.CONTINUOUS, quantity = 0),
            type = EATING,
        )
        val progressInfo = TaskProgressInfo(
            initiatedAt = now,
            endAt = null,
            progress = 0,
            progressType = TaskProgressType.CONTINUOUS
        )

        coEvery { healthPlanTaskService.init(taskId, now) } returns task.success()

        authenticatedAs(token, toTestPerson(person)) {
            put("/health_plan/tasks/$taskId/init") { response ->
                assertThat(response).isOKWithData(progressInfo)
            }
        }

        coVerify(exactly = 1) { healthPlanTaskService.init(any(), any()) }
    }

    @Test
    fun `#init returns a failure response when ProgressInfo cannot be built`() {
        val taskId = RangeUUID.generate()

        val task = TestModelFactory.buildHealthPlanTask().copy(
            initiatedByMemberAt = null,
        )

        coEvery { healthPlanTaskService.init(taskId, now) } returns task.success()

        authenticatedAs(token, toTestPerson(person)) {
            put("/health_plan/tasks/$taskId/init", body = UpdateTaskRequest()) { response ->
                assertThat(response).isInternalServerError()
            }
        }

        coVerify(exactly = 1) { healthPlanTaskService.init(any(), any()) }
    }

    @Test
    fun `#getPaginated should return paginated hpt on sections`() {
        val expectedResult = PaginationLayout(
            sections = emptyList(),
            action = null
        )

        coEvery {
            aliceScreensService.getPaginatedHealthDemandsScreen(
                person.id,
                0,
                3,
                null
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/health_plan/tasks/pagination?offset=0&limit=3") { response ->
                assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { aliceScreensService.getPaginatedHealthDemandsScreen(any(), any(), any(), any()) }
    }

    @Test
    fun `#getPaginated should return paginated hpt on sections with type filters`() {
        val expectedResult = PaginationLayout(
            sections = emptyList(),
            action = null
        )

        coEvery {
            aliceScreensService.getPaginatedHealthDemandsScreen(
                person.id,
                0,
                3,
                "EATING"
            )
        } returns expectedResult.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/health_plan/tasks/pagination?offset=0&limit=3&type=EATING") { response ->
                assertThat(response).isOKWithData(expectedResult)
            }
        }

        coVerifyOnce { aliceScreensService.getPaginatedHealthDemandsScreen(any(), any(), any(), any()) }
    }

    @Test
    fun `#complete should complete a task and update appState and featureConfig`() {
        val taskId = RangeUUID.generate()
        val relatedTaskIds = listOf(RangeUUID.generate())
        val data = mapOf(
            "popovers" to mapOf(
                "health_plan_history_from_conclusion" to true
            )
        )

        val task = TestModelFactory.buildHealthPlanTask().copy(
            id = taskId,
            status = HealthPlanTaskStatus.ACTIVE,
            type = EATING
        )

        val task2 = TestModelFactory.buildHealthPlanTask().copy(
            id = relatedTaskIds.first(),
            status = HealthPlanTaskStatus.ACTIVE,
            type = PRESCRIPTION
        )

        mockkObject(AppStateNotifier)
        mockkObject(FeaturePreferencesNotifier)

        coEvery { healthPlanTaskService.completeTaskByMember(taskId) } returns task.success()
        coEvery { healthPlanTaskService.completeTaskByMember(relatedTaskIds.first()) } returns task2.success()

        coEvery {
            FeaturePreferencesNotifier.getFeaturePreferences(
                person.id,
                FeaturePreferences.APP_BAR_ITEM_POPOVER
            )
        } returns emptyMap()

        coEvery {
            FeaturePreferencesNotifier.updateFeaturePreferences(
                task.personId,
                FeaturePreferences.APP_BAR_ITEM_POPOVER,
                data
            )
        } returns Unit

        every { AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_UNIFIED_HEALTH) } returns Unit
        every { AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_HOME) } returns Unit
        every { AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_HOME_TASK_LIST) } returns Unit
        every { AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL) } returns Unit
        every { AppStateNotifier.updateAppState(person.id, AppState.HEALTH_PLAN_DETAILS) } returns Unit

        authenticatedAs(token, toTestPerson(person)) {
            put("/health_plan/tasks/$taskId/v2/complete", body = UpdateTaskRequest(relatedTaskIds)) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 2) { healthPlanTaskService.completeTaskByMember(any()) }

        coVerifyOnce {
            FeaturePreferencesNotifier.getFeaturePreferences(person.id, FeaturePreferences.APP_BAR_ITEM_POPOVER)
            FeaturePreferencesNotifier.updateFeaturePreferences(person.id, FeaturePreferences.APP_BAR_ITEM_POPOVER, data)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_UNIFIED_HEALTH)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_HOME)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_HOME_TASK_LIST)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL)
            AppStateNotifier.updateAppState(person.id, AppState.HEALTH_PLAN_DETAILS)
        }
    }

    @Test
    fun `#delete task by member should update appState and featurePreferences`() = runBlocking {
        val taskId = RangeUUID.generate()
        val relatedTaskIds = listOf<UUID>()
        val data = mapOf(
            "popovers" to mapOf(
                "health_plan_history_from_archive" to true
            )
        )
        val task = TestModelFactory.buildHealthPlanTask().copy(
            id = taskId,
            status = HealthPlanTaskStatus.ACTIVE,
            type = EATING
        )

        mockkObject(AppStateNotifier)
        mockkObject(FeaturePreferencesNotifier)

        coEvery {
            FeaturePreferencesNotifier.getFeaturePreferences(
                person.id,
                FeaturePreferences.APP_BAR_ITEM_POPOVER
            )
        } returns emptyMap()

        coEvery {
            FeaturePreferencesNotifier.updateFeaturePreferences(
                task.personId,
                FeaturePreferences.APP_BAR_ITEM_POPOVER,
                data
            )
        } returns Unit

        every { AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_UNIFIED_HEALTH) } returns Unit
        every { AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_HOME) } returns Unit
        every { AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_HOME_TASK_LIST) } returns Unit
        every { AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL) } returns Unit
        every { AppStateNotifier.updateAppState(person.id, AppState.HEALTH_PLAN_DETAILS) } returns Unit

        coEvery { healthPlanTaskService.deleteByMember(taskId) } returns task.success()

        authenticatedAs(token, toTestPerson(person)) {
            put("/health_plan/tasks/$taskId/v2/delete", body = UpdateTaskRequest(relatedTaskIds)) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            healthPlanTaskService.deleteByMember(any())
            FeaturePreferencesNotifier.getFeaturePreferences(person.id, FeaturePreferences.APP_BAR_ITEM_POPOVER)
            FeaturePreferencesNotifier.updateFeaturePreferences(person.id, FeaturePreferences.APP_BAR_ITEM_POPOVER, data)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_UNIFIED_HEALTH)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_HOME)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_HOME_TASK_LIST)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL)
            AppStateNotifier.updateAppState(person.id, AppState.HEALTH_PLAN_DETAILS)
        }
    }

    @Test
    fun `#complete should complete a task with error on FeaturePreferences and AppStates update`() {
        val taskId = RangeUUID.generate()
        val relatedTaskIds = listOf(RangeUUID.generate())

        val featurePreferenceException = Exception("FeaturePreferences error")
        val appStateException = Exception("AppState error")

        val task = TestModelFactory.buildHealthPlanTask().copy(
            id = taskId,
            status = HealthPlanTaskStatus.ACTIVE,
            type = EATING
        )

        mockkObject(AppStateNotifier)
        mockkObject(FeaturePreferencesNotifier)

        coEvery { healthPlanTaskService.completeTaskByMember(taskId) } returns task.success()
        coEvery { healthPlanTaskService.completeTaskByMember(relatedTaskIds.first()) } returns task.success()

        coEvery {
            FeaturePreferencesNotifier.getFeaturePreferences(
                person.id,
                FeaturePreferences.APP_BAR_ITEM_POPOVER
            )
        } throws featurePreferenceException

        every {
            AppStateNotifier.updateAppState(
                person.id,
                AppState.REDESIGN_UNIFIED_HEALTH
            )
        } throws appStateException

        authenticatedAs(token, toTestPerson(person)) {
            put("/health_plan/tasks/$taskId/v2/complete", body = UpdateTaskRequest(relatedTaskIds)) { response ->
                assertThat(response).isOK()
            }
        }

        coVerify(exactly = 2) { healthPlanTaskService.completeTaskByMember(any()) }

        coVerifyOnce {
            FeaturePreferencesNotifier.getFeaturePreferences(person.id, FeaturePreferences.APP_BAR_ITEM_POPOVER)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_UNIFIED_HEALTH)
        }
    }

    @Test
    fun `#delete task by member with error on FeaturePreferences and AppStates update`() = runBlocking {
        val taskId = RangeUUID.generate()
        val relatedTaskIds = listOf<UUID>()

        val featurePreferenceException = Exception("FeaturePreferences error")
        val appStateException = Exception("AppState error")

        val task = TestModelFactory.buildHealthPlanTask().copy(
            id = taskId,
            status = HealthPlanTaskStatus.ACTIVE,
            type = EATING
        )

        mockkObject(AppStateNotifier)
        mockkObject(FeaturePreferencesNotifier)

        coEvery {
            FeaturePreferencesNotifier.getFeaturePreferences(
                person.id,
                FeaturePreferences.APP_BAR_ITEM_POPOVER
            )
        } throws featurePreferenceException

        every {
            AppStateNotifier.updateAppState(
                person.id,
                AppState.REDESIGN_UNIFIED_HEALTH
            )
        } throws appStateException

        coEvery { healthPlanTaskService.deleteByMember(taskId) } returns task.success()

        authenticatedAs(token, toTestPerson(person)) {
            put("/health_plan/tasks/$taskId/v2/delete", body = UpdateTaskRequest(relatedTaskIds)) { response ->
                assertThat(response).isOK()
            }
        }

        coVerifyOnce {
            healthPlanTaskService.deleteByMember(any())
            FeaturePreferencesNotifier.getFeaturePreferences(person.id, FeaturePreferences.APP_BAR_ITEM_POPOVER)
            AppStateNotifier.updateAppState(person.id, AppState.REDESIGN_UNIFIED_HEALTH)
        }
    }
}
