package br.com.alice.member.api.controllers

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.UserAddressResponse
import br.com.alice.member.api.models.address.AddressMapsTransport
import br.com.alice.member.api.services.AddressInternalService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test


class AddressControllerTest : RoutesTestHelper() {
    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()

    private val addressService: AddressInternalService = mockk()
    private val personService: PersonService = mockk()

    private val requestAddress = UserAddressResponse(
        street = "Avenida Rebouças",
        number = "3506",
        neighbourhood = "Pinheiros",
        postalCode = "05402-600",
        city = "São Paulo",
        state = State.SP,
        complement = "Some complement"
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            AddressController(
                addressService,
                personService
            )
        }
        coEvery { personService.get(person.id) } returns person.success()
    }

    private val lat = -23.5718116
    private val lng = -46.69273700000001
    private val addressMapsTransport = AddressMapsTransport(
        placeId = "place_id",
        nickName = "Principal",
        street = "Avenida Rebouças",
        number = "3506",
        neighbourhood = "Pinheiros",
        postalCode = "05402-600",
        city = "São Paulo",
        state = "SP",
        country = "Brasil",
        lat = lat,
        lng = lng,
    )

    @Test
    fun `#getAddress should return address by member`() {
        val expectedResponse = AddressController.AddressListResponse(
            addresses = listOf(addressMapsTransport)
        )
        coEvery { addressService.getAddress(person) } returns addressMapsTransport.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/address/details") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { addressService.getAddress(any()) }
    }

    @Test
    fun `#getAddress should return empty address when address by person is invalid`() {
        val expectedResponse = AddressController.AddressListResponse()
        coEvery { addressService.getAddress(person) } returns NotFoundException("address not found").failure()

        authenticatedAs(token, toTestPerson(person)) {
            get("/address/details") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { addressService.getAddress(any()) }
    }

    @Test
    fun `#updateAddress should return updatedAddress`() {
        val expectedResponse = AddressController.AddressListResponse(
            addresses = listOf(addressMapsTransport)
        )
        coEvery { addressService.updateAddress(requestAddress, person.id) } returns addressMapsTransport.success()

        authenticatedAs(token, toTestPerson(person)) {
            put("/address", UserAddressWrapperRequest(requestAddress)) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { addressService.updateAddress(any(), any()) }
    }

}
