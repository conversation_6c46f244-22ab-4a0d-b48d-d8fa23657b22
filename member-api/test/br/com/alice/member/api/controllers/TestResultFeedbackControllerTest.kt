package br.com.alice.member.api.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AliceTestResultBundle
import br.com.alice.data.layer.models.TestResultFeedback
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.converters.HealthPlanTasksTransportConverter
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.FeedbackResponse
import br.com.alice.member.api.models.HealthPlanItemResponse
import br.com.alice.member.api.models.HealthPlanSectionName
import br.com.alice.member.api.models.ReferralsResponse
import br.com.alice.member.api.models.StaffResponse
import br.com.alice.member.api.models.TestResultFeedbackCollectionResponse
import br.com.alice.member.api.models.TestResultFeedbackResponse
import br.com.alice.member.api.services.TestResultFeedbackHealthPlanTaskService
import br.com.alice.staff.client.StaffService
import br.com.alice.testresult.client.AliceTestResultBundleService
import br.com.alice.testresult.client.ExamResultService
import br.com.alice.testresult.models.ExamResult
import br.com.alice.testresult.models.ExamResultBundle
import br.com.alice.testresult.models.ExamResultItem
import br.com.alice.wanda.client.TestResultFeedbackService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

internal class TestResultFeedbackControllerTest : RoutesTestHelper() {

    private val testResultFeedbackService: TestResultFeedbackService = mockk()
    private val aliceTestResultBundleService: AliceTestResultBundleService = mockk()
    private val examResultService: ExamResultService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val staffService: StaffService = mockk()
    private val testResultFeedbackHealthPlanTaskService: TestResultFeedbackHealthPlanTaskService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId)
    private val token = person.id.toString()
    private val resultId = RangeUUID.generate()
    private val testResultFeedback = TestModelFactory.buildTestResultFeedback(
        personId = personId,
        aliceTestResultBundleIds = listOf(resultId)
    )
    private val aliceTestResult = TestModelFactory.buildAliceTestResult(id = resultId)
    private val aliceTestResultBundle = TestModelFactory.buildAliceTestResultBundle(
        personId = personId,
        results = listOf(aliceTestResult)
    )
    private val staff = TestModelFactory.buildStaff()
    private val healthPlanTask = TestModelFactory.buildHealthPlanTask(personId, staffId = staff.id)
    private val tasksTransport = HealthPlanTasksTransportConverter.convert(
        mapOf(healthPlanTask.type to listOf(healthPlanTask)),
        mapOf(staff.id to staff),
        emptyMap()
    )
    private val producerResult = ProducerResult(LocalDateTime.now(), "", Long.MIN_VALUE)

    private val referrals: List<HealthPlanItemResponse> = tasksTransport.referral?.map {
        ReferralsResponse(it, null)
    }.orEmpty()
    private val testResultFeedbackResponse = TestResultFeedbackResponse(
        content = testResultFeedback.content,
        addedAt = testResultFeedback.addedAt,
        staff = StaffResponse(
            staff.id,
            staff.firstName,
            staff.lastName,
            staff.profileImageUrl,
            staff.role.name,
            staff.role.description
        ),
        results = listOf(
            ExamResultItem(
                id = aliceTestResult.id.toString(),
                description = aliceTestResult.name,
                attachmentUrl = "alice.com"
            )
        ),
        tasks = emptyList()
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            TestResultFeedbackController(
                testResultFeedbackService,
                aliceTestResultBundleService,
                examResultService,
                staffService,
                testResultFeedbackHealthPlanTaskService,
                kafkaProducerService
            )
        }
        coEvery { kafkaProducerService.produce(any()) } returns producerResult
        coEvery { testResultFeedbackService.getByPersonId(personId) } returns listOf(testResultFeedback).success()
        coEvery { aliceTestResultBundleService.findByResultIds(testResultFeedback.aliceTestResultBundleIds) } returns listOf(
            aliceTestResultBundle
        ).success()
        coEvery { staffService.get(testResultFeedback.staffId) } returns staff.success()
    }

    @Test
    fun `#getByPerson - should return empty list of feedbacks when not have`() = runBlocking {
        coEvery { testResultFeedbackService.getByPersonId(personId) } returns emptyList<TestResultFeedback>().success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/test_results/feedback/") { response ->
                assertThat(response).isSuccessfulJson()

                val transport: TestResultFeedbackCollectionResponse = response.bodyAsJson()

                assertThat(transport.testResultFeedbacks).isEqualTo(emptyList<FeedbackResponse>())
            }
        }
    }

    @Test
    fun `#getByPerson - should return 1 feedback`() = runBlocking {
        val expected = TestResultFeedbackCollectionResponse(
            listOf(
                FeedbackResponse(
                    id = testResultFeedback.id,
                    addedAt = testResultFeedback.addedAt,
                    description = aliceTestResultBundle.results[0].name
                )
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/test_results/feedback/") { response ->
                assertThat(response).isSuccessfulJson()

                val transport: TestResultFeedbackCollectionResponse = response.bodyAsJson()

                assertThat(transport).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getByPerson - should return description with OUTROS EXAMES`() = runBlocking {
        val expected = TestResultFeedbackCollectionResponse(
            listOf(
                FeedbackResponse(
                    id = testResultFeedback.id,
                    addedAt = testResultFeedback.addedAt,
                    description = "Outros Exames"
                )
            )
        )

        val aliceTestResultBundle =
            aliceTestResultBundle.copy(results = listOf(aliceTestResultBundle.results[0].copy(id = RangeUUID.generate())))
        coEvery { aliceTestResultBundleService.findByResultIds(testResultFeedback.aliceTestResultBundleIds) } returns listOf(
            aliceTestResultBundle
        ).success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/test_results/feedback/") { response ->
                assertThat(response).isSuccessfulJson()

                val transport: TestResultFeedbackCollectionResponse = response.bodyAsJson()

                assertThat(transport).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getByPerson - should return description with OUTROS EXAMES when return empty list of findByResultIds`() =
        runBlocking {
            val expected = TestResultFeedbackCollectionResponse(
                listOf(
                    FeedbackResponse(
                        id = testResultFeedback.id,
                        addedAt = testResultFeedback.addedAt,
                        description = "Outros Exames"
                    )
                )
            )
            coEvery { aliceTestResultBundleService.findByResultIds(testResultFeedback.aliceTestResultBundleIds) } returns emptyList<AliceTestResultBundle>().success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/v2/test_results/feedback/") { response ->
                    assertThat(response).isSuccessfulJson()

                    val transport: TestResultFeedbackCollectionResponse = response.bodyAsJson()

                    assertThat(transport).isEqualTo(expected)
                }
            }
        }

    @Test
    fun `#getByPerson - should return 1 feedback with various results`() = runBlocking {
        val aliceTestResult2 = TestModelFactory.buildAliceTestResult(name = "resultado2")
        val testResultFeedback2 = testResultFeedback.copy(
            aliceTestResultBundleIds = listOf(aliceTestResult.id, aliceTestResult2.id)
        )
        val aliceTestResultBundle2 = aliceTestResultBundle.copy(
            results = listOf(aliceTestResult, aliceTestResult2)
        )
        val expected = TestResultFeedbackCollectionResponse(
            listOf(
                FeedbackResponse(
                    id = testResultFeedback.id,
                    addedAt = testResultFeedback.addedAt,
                    description = "${aliceTestResult.name}, ${aliceTestResult2.name}"
                )
            )
        )
        coEvery { testResultFeedbackService.getByPersonId(personId) } returns listOf(testResultFeedback2).success()
        coEvery {
            aliceTestResultBundleService.findByResultIds(
                listOf(
                    aliceTestResult.id,
                    aliceTestResult2.id
                )
            )
        } returns listOf(aliceTestResultBundle2).success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/test_results/feedback/") { response ->
                assertThat(response).isSuccessfulJson()

                val transport: TestResultFeedbackCollectionResponse = response.bodyAsJson()

                assertThat(transport).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getByPerson - should return 2 feedbacks with various results`() = runBlocking {
        val aliceTestResult2 = TestModelFactory.buildAliceTestResult(name = "resultado2")
        val testResultFeedback2 = testResultFeedback.copy(
            id = RangeUUID.generate(),
            aliceTestResultBundleIds = listOf(aliceTestResult2.id)
        )
        val aliceTestResultBundle2 = aliceTestResultBundle.copy(
            results = listOf(aliceTestResult, aliceTestResult2)
        )
        val expected = TestResultFeedbackCollectionResponse(
            listOf(
                FeedbackResponse(
                    id = testResultFeedback.id,
                    addedAt = testResultFeedback.addedAt,
                    description = aliceTestResult.name
                ),
                FeedbackResponse(
                    id = testResultFeedback2.id,
                    addedAt = testResultFeedback2.addedAt,
                    description = aliceTestResult2.name
                )
            )
        )
        coEvery { testResultFeedbackService.getByPersonId(personId) } returns listOf(
            testResultFeedback,
            testResultFeedback2
        ).success()
        coEvery {
            aliceTestResultBundleService.findByResultIds(
                listOf(
                    aliceTestResult.id,
                    aliceTestResult2.id
                )
            )
        } returns listOf(aliceTestResultBundle2).success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/test_results/feedback/") { response ->
                assertThat(response).isSuccessfulJson()

                val transport: TestResultFeedbackCollectionResponse = response.bodyAsJson()

                assertThat(transport).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getById - should return test result feedback by id without tasks`() = runBlocking {
        val exams = listOf(
            ExamResult(
                date = LocalDate.now().toString(),
                provider = null,
                results = listOf(
                    ExamResultBundle(
                        id = aliceTestResult.id.toString(),
                        description = aliceTestResult.name,
                        attachmentUrl = "alice.com",
                        items = listOf(
                            ExamResultItem(
                                id = aliceTestResult.id.toString(),
                                description = aliceTestResult.name,
                                attachmentUrl = "alice.com"
                            )
                        )
                    )
                )
            )
        )

        coEvery { testResultFeedbackService.get(testResultFeedback.id) } returns testResultFeedback.success()
        coEvery { examResultService.getByPersonId(personId.toString()) } returns exams.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/test_results/feedback/${testResultFeedback.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val transport: TestResultFeedbackResponse = response.bodyAsJson()

                assertThat(transport).isEqualTo(testResultFeedbackResponse)

                coVerify(exactly = 0) { healthPlanTaskService.get(any()) }
            }
        }

        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#getById - should return test result feedback by id with tasks`() = runBlocking {
        val feedbackWithTask = testResultFeedback.copy(healthPlanTaskIds = listOf(healthPlanTask.id))
        val exams = listOf(
            ExamResult(
                date = LocalDate.now().toString(),
                provider = null,
                results = listOf(
                    ExamResultBundle(
                        id = "78945",
                        description = aliceTestResult.name,
                        attachmentUrl = "alice.com",
                        items = listOf(
                            ExamResultItem(
                                id = aliceTestResult.id.toString(),
                                description = aliceTestResult.name,
                                attachmentUrl = "alice.com"
                            )
                        )
                    )
                )
            )
        )

        coEvery { testResultFeedbackService.get(testResultFeedback.id) } returns feedbackWithTask.success()
        coEvery { examResultService.getByPersonId(personId.toString()) } returns exams.success()
        coEvery {
            testResultFeedbackHealthPlanTaskService.getHealthPlanTask(
                feedbackWithTask.healthPlanTaskIds,
                staff,
                personId
            )
        } returns referrals.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/test_results/feedback/${testResultFeedback.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val transport: TestResultFeedbackResponse = response.bodyAsJson()

                assertThat(transport).usingRecursiveComparison().ignoringFields("tasks")
                    .isEqualTo(testResultFeedbackResponse)

                val sections = transport.tasks.map(HealthPlanItemResponse::section)

                assertThat(transport.tasks).hasSize(1)
                assertThat(sections).containsExactly(HealthPlanSectionName.REFERRALS)
            }
        }

        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#getHighlight - should return 1 feedback`() = runBlocking {
        coEvery { testResultFeedbackService.getByPersonLimited(personId) } returns testResultFeedback.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/v2/test_results/feedback/highlight") { response ->
                assertThat(response).isSuccessfulJson()
            }
        }
    }

}
