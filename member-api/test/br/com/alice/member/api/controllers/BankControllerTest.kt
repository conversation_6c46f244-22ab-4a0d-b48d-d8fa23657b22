package br.com.alice.member.api.controllers

import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.BankInfoResponse
import br.com.alice.member.api.models.BankResponse
import br.com.alice.member.api.utils.BankInstitutions
import kotlin.test.BeforeTest
import kotlin.test.Test

class BankControllerTest : RoutesTestHelper() {

    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            BankController()
        }
    }

    @Test
    fun `#getBankCodes should return bank codes list`() {
        val expectedResult = BankResponse(
            banks = BankInstitutions.values().map {
                BankInfoResponse(code = it.code, description = it.description)
            }
        )

        authenticatedAs(token, toTest<PERSON>erson(person)) {
            get("/bank_codes") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResult)
            }
        }
    }
}
