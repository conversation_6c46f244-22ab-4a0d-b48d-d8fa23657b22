package br.com.alice.member.api.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.models.procedureAuthorization.AuthorizationTransport
import br.com.alice.member.api.models.procedureAuthorization.InstructionTransport
import br.com.alice.member.api.models.procedureAuthorization.TimelineEventStatus
import br.com.alice.member.api.models.procedureAuthorization.TimelineEventTransport
import br.com.alice.member.api.services.ProcedureAuthorizationDetailResponse
import br.com.alice.member.api.services.ProcedureAuthorizationListResponse
import br.com.alice.member.api.services.ProcedureAuthorizationService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class ProcedureAuthorizationControllerTest : RoutesTestHelper() {

    private val procedureAuthorizationService: ProcedureAuthorizationService = mockk()

    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()
    private val totvsGuiaId = RangeUUID.generate()

    private val authorizationTransport = AuthorizationTransport(
        id = RangeUUID.generate().toString(),
        externalCode = "0001.2023.08.********",
        status = "AUTHORIZED",
        label = "Número da guia do exame",
        requestedBy = "Alice",
        requestedAt = LocalDateTime.now(),
        clickAction = RemoteAction(
            mobileRoute = MobileRouting.EXTERNAL_APP
        ),
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            ProcedureAuthorizationController(
                procedureAuthorizationService
            )
        }
    }

    @Test
    fun `#getList should return 200 with authorizations`() {
        val expectedResponse = ProcedureAuthorizationListResponse(
            title = "title",
            instructions = listOf(InstructionTransport(icon = "icon", label = "label")),
            procedureAuthorizations = listOf(authorizationTransport)
        )

        coEvery {
            procedureAuthorizationService.getList(person.id)
        } returns expectedResponse.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/procedure_authorizations/") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)

                coVerifyOnce { procedureAuthorizationService.getList(person.id) }
            }
        }
    }

    @Test
    fun `#getDetail should return 200 with procedures`() {
        val expectedResponse = ProcedureAuthorizationDetailResponse(
            authorization = authorizationTransport,
            events = listOf(TimelineEventTransport(title = "title", status = TimelineEventStatus.FINISHED)),
        )

        coEvery {
            procedureAuthorizationService.getDetail(totvsGuiaId)
        } returns expectedResponse.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/procedure_authorizations/$totvsGuiaId") { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)

                coVerifyOnce { procedureAuthorizationService.getDetail(totvsGuiaId) }
            }
        }
    }
}
