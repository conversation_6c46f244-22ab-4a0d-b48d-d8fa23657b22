package br.com.alice.member.api.controllers

import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FormType
import br.com.alice.data.layer.models.HealthForm
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.HealthFormAnswerSourceType.CHANNEL
import br.com.alice.data.layer.models.HealthFormQuestion
import br.com.alice.data.layer.models.HealthFormQuestionOption
import br.com.alice.data.layer.models.HealthFormQuestionType
import br.com.alice.data.layer.models.HealthFormQuestionValidationType
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.BasicNavigationResponse
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.healthForm.AnswerRequest
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.questionnaire.client.FormNavigationService
import br.com.alice.questionnaire.exceptions.InvalidQuestionIndexException
import br.com.alice.questionnaire.models.HealthFormActionsTransport
import br.com.alice.questionnaire.models.HealthFormAnswerTransport
import br.com.alice.questionnaire.models.HealthFormPreviousQuestionTransport
import br.com.alice.questionnaire.models.HealthFormQuestionOptionResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionInputResponse
import br.com.alice.questionnaire.models.QuestionnaireQuestionResponse
import br.com.alice.questionnaire.models.QuestionnaireSectionResponse
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthFormControllerTest : RoutesTestHelper() {

    private val formNavigationService: FormNavigationService = mockk()
    private val appContentScreenDetailService: AppContentScreenDetailService = mockk()
    private val healthFormController = HealthFormController(formNavigationService, appContentScreenDetailService)

    private val person = TestModelFactory.buildPerson()
    private val token = RangeUUID.generate().toString()
    private val formId = RangeUUID.generate()
    private val section = TestModelFactory.buildHealthFormSection(formId)
    private val source = HealthFormAnswerSource("xpto", CHANNEL, "sub_channel")
    private val group = TestModelFactory.buildHealthFormAnswerGroup(
        personId = person.id,
        healthFormId = formId,
        source = source
    )

    private val question = HealthFormQuestion(
        healthFormId = formId,
        healthFormSectionId = section.id,
        question = "Nunc sed velit dignissim sodales ut eu sem integer vitae",
        type = HealthFormQuestionType.MULTIPLE_OPTIONS,
        index = 1,
        defaultNext = 2,
        displayAttributes = null,
        validations = mapOf(
            HealthFormQuestionValidationType.SEX to Sex.MALE
        ),
        options = listOf(
            HealthFormQuestionOption(
                next = 2,
                value = true,
                label = "Sim"
            ),
            HealthFormQuestionOption(
                next = 3,
                value = false,
                label = "Não"
            )
        )
    )

    private val homeNavigationResponse = BasicNavigationResponse(
        navigation = NavigationResponse(mobileRoute = MobileRouting.HOME)
    )

    private val questionnaireQuestionResponse = QuestionnaireQuestionResponse(
        id = question.id,
        questionnaireId = formId,
        groupId = group.id,
        question = question.question,
        details = question.details,
        progress = 100,
        input = QuestionnaireQuestionInputResponse(
            action = "",
            displayAttributes = question.displayAttributes?.mapKeys { it.key.toString() },
            imageUrl = question.imageUrl,
            options = question.options.map {
                HealthFormQuestionOptionResponse(
                    next = it.next,
                    value = it.value,
                    label = it.label
                )
            },
            type = question.type
        ),
        section = section.convertTo(QuestionnaireSectionResponse::class)
    )

    private val screenDetail = TestModelFactory.buildAppContentScreenDetail(
        healthDemandName = "Nome da demanda",
    )

    @BeforeTest
    override fun setup() {
        module.single { healthFormController }
    }

    @Test
    fun `#startForm should return home navigation if the member has already answered all questions`() = runBlocking {
        val form = HealthForm(name = "Formulário de exemplo", key = "EXAMPLE_FORM", type = FormType.HEALTH)
        val routeFormKey = form.key
        coEvery {
            formNavigationService.startForm(person.id, form.key)
        } returns InvalidQuestionIndexException("").failure()

        mockkObject(AppStateNotifier) {
            coEvery {
                AppStateNotifier.updateAppState(person.id, AppState.REMINDERS)
            } answers { nothing }

            authenticatedAs(token, toTestPerson(person)) {
                get("/health_form/$routeFormKey/start") { response ->
                    assertThat(response).isOKWithData(homeNavigationResponse)
                }
            }
        }
    }


    @Test
    fun `#startForm should return error if find question return any exception`() = runBlocking {
        val formKey = "EXAMPLE_FORM"
        coEvery {
            formNavigationService.startForm(person.id, formKey)
        } returns Exception("").failure()

        authenticatedAs(token, toTestPerson(person)) {
            get("/health_form/${formKey}/start") { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }


    @Test
    fun `#startForm should return error if try start an invalid form`() = runBlocking {
        coEvery {
            formNavigationService.startForm(person.id, "custom_form")
        } returns NotFoundException().failure()

        authenticatedAs(token, toTestPerson(person)) {
            get("/health_form/custom_form/start") { response ->
                assertThat(response).isNotFound()
            }
        }
        coVerifyOnce { formNavigationService.startForm(person.id, "custom_form") }
    }

    @Test
    fun `#startForm returns the first question of form as new response`() = runBlocking {
        val form = HealthForm(name = "Formulário de exemplo", key = "EXAMPLE_FORM", type = FormType.HEALTH)
        coEvery {
            formNavigationService.startForm(person.id, form.key)
        } returns HealthFormActionsTransport(currentQuestion = question).success()

        coEvery {
            formNavigationService.getBaseQuestionResponse(
                person.id,
                question,
                null,
                Link(href = "http://localhost/health_form/${question.healthFormId}/question/${question.id}?"),
                shouldGenerateAnswerGroup = false
            )
        } returns questionnaireQuestionResponse.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/health_form/${form.key}/start") { response ->
                assertThat(response).isOKWithData(questionnaireQuestionResponse)
            }
        }
    }

    @Test
    fun `#startForm returns the first question of form as new response considering source params`() = runBlocking {
        val form = HealthForm(name = "Formulário de exemplo", key = "EXAMPLE_FORM", type = FormType.HEALTH)

        coEvery {
            formNavigationService.startForm(person.id, form.key, source)
        } returns HealthFormActionsTransport(currentQuestion = question).success()

        coEvery {
            appContentScreenDetailService.findOneByHealthFormAnswerSource(source)
        } returns screenDetail.success()

        coEvery {
            formNavigationService.getBaseQuestionResponse(
                person.id,
                question,
                source,
                Link(href = "http://localhost/health_form/${question.healthFormId}/question/${question.id}?source_id=${source.id}&source_type=${source.type}&source_subtype=${source.subtype}"),
                null,
                null,
                screenDetail,
                shouldGenerateAnswerGroup = false
            )
        } returns questionnaireQuestionResponse.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/health_form/${form.key}/start?source_id=${source.id}&source_type=${source.type}&source_subtype=${source.subtype}") { response ->
                assertThat(response).isOKWithData(questionnaireQuestionResponse)
            }
        }

        coVerifyOnce { appContentScreenDetailService.findOneByHealthFormAnswerSource(any()) }
    }

    @Test
    fun `#saveAnswerAndGetNextQuestion should return home navigation if the member finish to answer the questions`() =
        runBlocking {
            val answerRequest = AnswerRequest(2, "xpto")

            val answerTransport = HealthFormAnswerTransport(
                healthFormId = formId,
                personId = person.id,
                questionId = question.id,
                next = answerRequest.next,
                value = answerRequest.value
            )

            coEvery {
                formNavigationService.moveForwardOneQuestion(answerTransport)
            } returns InvalidQuestionIndexException("").failure()

            mockkObject(AppStateNotifier) {
                coEvery {
                    AppStateNotifier.updateAppState(person.id, AppState.REMINDERS)
                } answers { nothing }

                authenticatedAs(token, toTestPerson(person)) {
                    put(to = "/health_form/$formId/question/${question.id}", body = answerRequest) { response ->
                        assertThat(response).isOKWithData(homeNavigationResponse)
                    }
                }
            }
        }


    @Test
    fun `#saveAnswerAndGetNextQuestion should return error when throw any other exception not being InvalidQuestionIndexException`() =
        runBlocking {
            val answerRequest = AnswerRequest(2, "xpto")

            val answerTransport = HealthFormAnswerTransport(
                healthFormId = formId,
                personId = person.id,
                questionId = question.id,
                next = answerRequest.next,
                value = answerRequest.value
            )

            coEvery {
                formNavigationService.moveForwardOneQuestion(answerTransport)
            } returns InvalidQuestionIndexException("").failure()

            mockkObject(AppStateNotifier) {
                coEvery {
                    AppStateNotifier.updateAppState(person.id, AppState.REMINDERS)
                } answers { nothing }

                authenticatedAs(token, toTestPerson(person)) {
                    put(to = "/health_form/$formId/question/${question.id}", body = answerRequest) { response ->
                        assertThat(response).isOKWithData(homeNavigationResponse)
                    }
                }
            }
        }

    @Test
    fun `#saveAnswerAndGetNextQuestion should return question`() = runBlocking {
        val answerRequest = AnswerRequest(2, "xpto")

        val answerTransport = HealthFormAnswerTransport(
            healthFormId = formId,
            personId = person.id,
            questionId = question.id,
            next = answerRequest.next,
            value = answerRequest.value
        )

        coEvery {
            formNavigationService.moveForwardOneQuestion(answerTransport)
        } returns HealthFormActionsTransport(currentQuestion = question).success()

        coEvery {
            formNavigationService.getBaseQuestionResponse(
                person.id,
                question,
                null,
                Link(href = "http://localhost/health_form/${question.healthFormId}/question/${question.id}?"),
                shouldGenerateAnswerGroup = true
            )
        } returns questionnaireQuestionResponse.success()

        authenticatedAs(token, toTestPerson(person)) {
            put(to = "/health_form/$formId/question/${question.id}", body = answerRequest) { response ->
                assertThat(response).isOKWithData(questionnaireQuestionResponse)
            }
        }
    }


    @Test
    fun `#deleteAnswerAndGetPreviousQuestion should return previous question`() = runBlocking {
        val previousQuestionTransport = HealthFormPreviousQuestionTransport(
            healthFormId = formId,
            personId = person.id,
            questionId = question.id,
        )

        val answer = TestModelFactory.buildHealthFormQuestionAnswer(
            healthFormQuestionId = question.id,
            personId = person.id,
            healthFormId = question.healthFormId,
            healthFormAnswerGroupId = group.id,
            answer = question.options.first().value.toString()
        )

        coEvery {
            formNavigationService.moveBackOneQuestion(previousQuestionTransport)
        } returns HealthFormActionsTransport(
            currentQuestion = question,
            selectedAnswer = answer
        ).success()

        coEvery {
            formNavigationService.getBaseQuestionResponse(
                person.id,
                question,
                null,
                any(),
                null,
                answer,
                shouldGenerateAnswerGroup = true
            )
        } returns questionnaireQuestionResponse.success()

        authenticatedAs(token, toTestPerson(person)) {
            delete("/health_form/$formId/previous_question/${question.id}") { response ->
                assertThat(response).isOKWithData(questionnaireQuestionResponse)
            }
        }
    }

}
