package br.com.alice.member.api.controllers

import br.com.alice.channel.client.VideoCallService
import br.com.alice.channel.client.VirtualClinicService
import br.com.alice.channel.models.RejectVideoCallReasonTransport
import br.com.alice.channel.models.RejectVideoCallReasonTransportItem
import br.com.alice.channel.models.VideoCallMeeting
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.RejectVideoCallReason
import br.com.alice.data.layer.models.VideoCallStatus.ENDED
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.HealthProfessionalResponse
import br.com.alice.member.api.models.videoCall.AmazonChimeConfig
import br.com.alice.member.api.models.videoCall.Attendee
import br.com.alice.member.api.models.videoCall.AttendeeResponse
import br.com.alice.member.api.models.videoCall.MediaPlacement
import br.com.alice.member.api.models.videoCall.Meeting
import br.com.alice.member.api.models.videoCall.MeetingResponse
import br.com.alice.member.api.models.videoCall.VideoCallMeetingResponse
import com.amazonaws.regions.Regions
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import software.amazon.awssdk.services.chimesdkmeetings.model.Attendee as AwsAttendee
import software.amazon.awssdk.services.chimesdkmeetings.model.MediaPlacement as AwsMediaPlacement
import software.amazon.awssdk.services.chimesdkmeetings.model.Meeting as AwsMeeting

class VideoCallControllerTest : RoutesTestHelper() {

    private val videoCallService: VideoCallService = mockk()
    private val virtualClinicService: VirtualClinicService = mockk()
    private val controller = VideoCallController(
        videoCallService,
        virtualClinicService
    )

    private val token = RangeUUID.generate().toString()
    private val person = TestModelFactory.buildPerson()
    private val channelId = "channel_id"

    private val staff = TestModelFactory.buildStaff()
    private val videoCall = TestModelFactory.buildVideoCall(personId = person.id, staffId = staff.id)
    private val meeting = AwsMeeting.builder()
        .meetingId(RangeUUID.generate().toString())
        .mediaRegion(Regions.US_EAST_1.name)
        .mediaPlacement(
            AwsMediaPlacement
                .builder()
                .audioHostUrl("")
                .audioFallbackUrl("")
                .screenDataUrl("")
                .screenSharingUrl("")
                .screenViewingUrl("")
                .signalingUrl("")
                .turnControlUrl("")
                .build()
        ).build()
    private val attendee = AwsAttendee
        .builder()
        .attendeeId(RangeUUID.generate().toString())
        .externalUserId(person.id.toString())
        .joinToken("xxxxxxxx")
        .build()

    private val videoCallMeeting = VideoCallMeeting(
        videoCall = videoCall,
        meeting = meeting,
        attendee = attendee
    )

    private val videoCallMeetingResponse = VideoCallMeetingResponse(
        id = videoCall.id,
        name = videoCall.type.description,
        type = videoCall.type,
        status = videoCall.status,
        amazonChimeConfig = AmazonChimeConfig(
            trackEvents = true,
            meetingResponse = MeetingResponse(
                meeting = Meeting(
                    meetingId = meeting.meetingId().toUUID(),
                    externalMeetingId = videoCall.id,
                    mediaRegion = meeting.mediaRegion(),
                    mediaPlacement = MediaPlacement(
                        audioHostUrl = "",
                        audioFallbackUrl = "",
                        screenDataUrl = "",
                        screenSharingUrl = "",
                        screenViewingUrl = "",
                        signalingUrl = "",
                        turnControlUrl = "",
                    )
                )
            ),
            attendeeResponse = AttendeeResponse(
                attendee = Attendee(
                    attendeeId = attendee.attendeeId().toUUID(),
                    externalUserId = attendee.externalUserId().toUUID(),
                    joinToken = attendee.joinToken()
                )
            )
        ),
        healthProfessional = HealthProfessionalResponse(
            id = staff.id.toString(),
            firstName = staff.firstName,
            lastName = staff.lastName,
            profileImageUrl = staff.profileImageUrl,
            description = "médico"
        )
    )

    private val rejectReasonsTransport = RejectVideoCallReasonTransport(
        reasons = listOf(
            RejectVideoCallReasonTransportItem(
                key = RejectVideoCallReason.DEFAULT.name,
                description = RejectVideoCallReason.DEFAULT.description
            ),
            RejectVideoCallReasonTransportItem(
                key = RejectVideoCallReason.UNKOWN.name,
                description = RejectVideoCallReason.UNKOWN.description
            )
        )
    )

    private val rejectReasonsExpected = RejectVideoCallReasonTransport(
        reasons = listOf(
            RejectVideoCallReasonTransportItem(
                key = RejectVideoCallReason.DEFAULT.name,
                description = RejectVideoCallReason.DEFAULT.description
            )
        )
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(videoCallService)

    @Test
    fun `#join add the member in video call meeting`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.ALICE_APP,
            key = "member_video_call_events_tracking_enabled",
            value = true
        ) {
            coEvery {
                videoCallService.getStaffFromMeeting(videoCall.id)
            } returns staff.success()
            coEvery {
                videoCallService.addPersonInMeeting(videoCall.id, person.id.id)
            } returns videoCallMeeting.success()

            authenticatedAs(token, toTestPerson(person)) {
                post("/video_call/meetings/${videoCall.id}/join") { response ->
                    assertThat(response).isOKWithData(videoCallMeetingResponse)
                }
            }

            coVerifyOnce { videoCallService.getStaffFromMeeting(any()) }
            coVerifyOnce { videoCallService.addPersonInMeeting(any(), any()) }
        }

    }

    @Test
    fun `#join don't load info if meeting already was finished`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.ALICE_APP,
            key = "member_video_call_events_tracking_enabled",
            value = true
        ) {
            val videoCallMeeting = VideoCallMeeting(videoCall = videoCall.copy(status = ENDED))
            val expected = VideoCallMeetingResponse(
                id = videoCallMeeting.videoCall.id,
                name = videoCallMeeting.videoCall.type.description,
                type = videoCallMeeting.videoCall.type,
                status = videoCallMeeting.videoCall.status
            )

            coEvery {
                videoCallService.getStaffFromMeeting(videoCall.id)
            } returns NotFoundException().failure()
            coEvery {
                videoCallService.addPersonInMeeting(videoCall.id, person.id.id)
            } returns videoCallMeeting.success()

            authenticatedAs(token, toTestPerson(person)) {
                post("/video_call/meetings/${videoCall.id}/join") { response ->
                    assertThat(response).isOKWithData(expected)
                }
            }

            coVerifyOnce { videoCallService.getStaffFromMeeting(any()) }
            coVerifyOnce { videoCallService.addPersonInMeeting(any(), any()) }
        }
    }

    @Test
    fun `#quit remove the member from video call meeting`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.ALICE_APP,
            key = "member_video_call_events_tracking_enabled",
            value = true
        ) {
            coEvery {
                videoCallService.removeAttendeeFromMeeting(videoCall.id, person.id.id, RejectVideoCallReason.UNKOWN)
            } returns videoCallMeeting.success()

            authenticatedAs(token, toTestPerson(person)) {
                post("/video_call/meetings/${videoCall.id}/quit") { response ->
                    assertThat(response).isOKWithData(videoCallMeetingResponse.copy(healthProfessional = null))
                }
            }

            coVerifyOnce { videoCallService.removeAttendeeFromMeeting(any(), any(), any()) }
        }
    }

    @Test
    fun `#getRejectReasons returns reject reasons`() {
        coEvery {
            videoCallService.getRejectVideoCallReasons()
        } returns rejectReasonsTransport.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/video_call/reject_reasons") { response ->
                assertThat(response).isOKWithData(rejectReasonsExpected)
            }
        }

        coVerifyOnce { videoCallService.getRejectVideoCallReasons() }
    }

    @Test
    fun `#getRejectReasons throws error when service throws error`() {
        coEvery {
            videoCallService.getRejectVideoCallReasons()
        } returns Exception("ex").failure()

        authenticatedAs(token, toTestPerson(person)) {
            get("/video_call/reject_reasons") { response ->
                assertThat(response).isInternalServerError()
            }
        }

        coVerifyOnce { videoCallService.getRejectVideoCallReasons() }
    }

    @Test
    fun `#dropVideoCall returns success after drop the virtual clinic before video call is started`() {
        val rejectReason = RejectVideoCallReason.WAITING_TIME_TOO_LONGER
        coEvery {
            virtualClinicService.cancelVirtualClinic(channelId, rejectReason)
        } returns channelId.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(
                "/virtual_clinic/${channelId}/drop",
                QuitVideoCallRequest(rejectReason, QuitVirtualClinicType.OFF_QUEUE)
            ) { response ->
                assertThat(response).isOKWithData(channelId)
            }
        }

        coVerifyOnce { virtualClinicService.cancelVirtualClinic(any(), any()) }
        coVerifyNone { virtualClinicService.denyVirtualClinicVideoCall(any(), any()) }
    }

    @Test
    fun `#dropVideoCall returns success after drop the virtual clinic declining video call invite`() {
        val rejectReason = RejectVideoCallReason.WAITING_TIME_TOO_LONGER
        coEvery {
            virtualClinicService.denyVirtualClinicVideoCall(channelId, rejectReason)
        } returns channelId.success()

        authenticatedAs(token, toTestPerson(person)) {
            post(
                "/virtual_clinic/${channelId}/drop",
                QuitVideoCallRequest(rejectReason, QuitVirtualClinicType.DECLINE_VIDEO_CALL)
            ) { response ->
                assertThat(response).isOKWithData(channelId)
            }
        }

        coVerifyOnce { virtualClinicService.denyVirtualClinicVideoCall(any(), any()) }
        coVerifyNone { virtualClinicService.cancelVirtualClinic(any(), any()) }
    }

}
