package br.com.alice.member.api.controllers

import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.googlemaps.services.AutocompleteTransport
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.models.address.AddressMapsTransport
import br.com.alice.member.api.models.address.AutocompleteResponse
import br.com.alice.member.api.services.AddressInternalService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class MapsAddressControllerTest : RoutesTestHelper() {

    private val addressService: AddressInternalService = mockk()
    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()
    private val session = "session".toSafeUUID()
    private val placeId = "place_id"
    private val response = AutocompleteTransport(
        placeId = placeId,
        description = "Av. Rebouças, 3506 - Pinheiros, São Paulo - SP, 05402-600, Brasil",
        mainText = "Av. Rebouças, 3506",
        secondaryText = "Pinheiros, São Paulo - SP, 05402-600, Brasil"
    )
    private val lat = -23.5718116
    private val lng = -46.69273700000001
    private val addressMapsTransport = AddressMapsTransport(
        placeId = "place_id",
        nickName = "Principal",
        street = "Avenida Rebouças",
        number = "3506",
        neighbourhood = "Pinheiros",
        postalCode = "05402-600",
        city = "São Paulo",
        state = "SP",
        country = "place_id",
        lat = lat,
        lng = lng,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            MapsAddressController(
                addressService
            )
        }
    }

    @Test
    fun `autocompleteAddress should return autocomplete response`() {
        val expectedResponse = AutocompleteResponse(listOf(response))
        val headers = mapOf(
            "X-Session-Id" to session.toString(),
        )
        coEvery { addressService.autocompletedAddress("av briga", session) } returns listOf(response).success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/maps/address/search?q=av%20briga&session=$session", headers = headers) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { addressService.autocompletedAddress(any(), any()) }
    }

    @Test
    fun `autocompleteAddress should return bad request when missing parameters`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("/maps/address/search") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
        coVerifyNone { addressService.autocompletedAddress(any(), any()) }
    }

    @Test
    fun `getByPlaceId should return address by place id`() {
        coEvery { addressService.getByPlaceId(placeId) } returns addressMapsTransport.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/maps/address/$placeId") { response ->
                ResponseAssert.assertThat(response).isOKWithData(addressMapsTransport)
            }
        }

        coVerifyOnce { addressService.getByPlaceId(any()) }
    }

    @Test
    fun `getByGeolocation should return address by lat and lng`() {
        coEvery { addressService.getAddress(lat, lng) } returns addressMapsTransport.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/maps/address/geo_location?lat=$lat&lng=$lng") { response ->
                ResponseAssert.assertThat(response).isOKWithData(addressMapsTransport)
            }
        }

        coVerifyOnce { addressService.getAddress(lat = any(), lng = any()) }
    }

    @Test
    fun `getByGeolocation should return bad request when missing lat or lng`() {
        authenticatedAs(token, toTestPerson(person)) {
            get("/maps/address/geo_location") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }
        coVerifyNone { addressService.getAddress(lat = any(), lng = any()) }
    }
    
}
