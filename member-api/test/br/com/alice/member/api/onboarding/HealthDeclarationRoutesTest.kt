package br.com.alice.member.api.onboarding

import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.returns
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.HealthDeclarationQuestionType
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.controllers.onboarding.v1.HealthDeclarationController
import br.com.alice.member.api.models.onboarding.ConditionAnswerRequest
import br.com.alice.member.api.models.onboarding.ConditionPayload
import br.com.alice.member.api.models.onboarding.ConfirmationFormQuestion
import br.com.alice.member.api.models.onboarding.ImcAnswerRequest
import br.com.alice.member.api.models.onboarding.ImcFormQuestion
import br.com.alice.member.api.models.onboarding.SurgeryAnswerRequest
import br.com.alice.member.api.models.onboarding.SurgeryPayload
import br.com.alice.membership.client.onboarding.HealthDeclarationForm
import br.com.alice.membership.client.onboarding.PersonRegistrationService
import br.com.alice.membership.model.onboarding.HealthDeclarationAnswerOption
import br.com.alice.membership.model.onboarding.HealthDeclarationNotStartedException
import br.com.alice.membership.model.onboarding.HealthDeclarationQuestion
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthDeclarationRoutesTest : RoutesTestHelper() {

    private val healthDeclarationForm: HealthDeclarationForm = mockk()
    private val personService: PersonService = mockk()
    private val registrationService: PersonRegistrationService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService = mockk()

    private val person = TestModelFactory.buildPerson()
    private val dependent = TestModelFactory.buildPerson()
    private val randomPerson = TestModelFactory.buildPerson()

    private val registration = TestModelFactory.buildPersonRegistration(person.id)

    private val beneficiary = TestModelFactory.buildBeneficiary()
    private val dependentBeneficiary = TestModelFactory.buildBeneficiary(parentBeneficiary = beneficiary.id)
    private val randomBeneficiary = TestModelFactory.buildBeneficiary()

    private val healthDeclaration = TestModelFactory.buildHealthDeclaration(personId = dependent.id)
    private val healthDeclarationQuestion = HealthDeclarationQuestion(
        type = HealthDeclarationQuestionType.CONFIRMATION,
        question = "Question?",
        progress = 100,
    )

    private val token = RangeUUID.generate().toString()

    private val newHealthDeclarationMinimumAppVersion = SemanticVersion("3.63.0")
    private val userAgent = mapOf(
        HttpHeaders.UserAgent to "iOS${newHealthDeclarationMinimumAppVersion.version}"
    )


    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            HealthDeclarationController(
                personService,
                registrationService,
                healthDeclarationForm,
                beneficiaryService,
                beneficiaryOnboardingService
            )
        }


    }

    @Test
    fun `#getFirstFormQuestion should return 200 OK with IMC question config`() {
        val imcQuestion: HealthDeclarationQuestion = mockk()
        coEvery { imcQuestion.type } returns HealthDeclarationQuestionType.CONFIRMATION
        coEvery { imcQuestion.question } returns "Question?"
        coEvery { imcQuestion.progress } returns 5

        coEvery {
            healthDeclarationForm.start(
                person
            )
        } returns imcQuestion.success()
        coEvery { personService.get(person.id) } returns Result.success(person)


        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/health_declaration", headers = userAgent) { response ->
                assertThat(response).isSuccessfulJson()

                val expected = ImcFormQuestion(imcQuestion, personId = person.id.toString())
                val body: ImcFormQuestion = response.bodyAsJson()

                assertThat(body.id).isEqualTo(expected.id)
            }
        }
    }

    @Test
    fun `#answerImcQuestion should return 400 Bad Request when a health declaration hasn't started`() {
        val imcAnswerRequest = ImcAnswerRequest(172.0, 72.0)
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { registrationService.findByPerson(person.id) } returns registration.success()

        coEvery {
            healthDeclarationForm.fillHeightAndWeight(
                person,
                any(),
            )
        } returns Result.failure(
            HealthDeclarationNotStartedException()
        )

        authenticatedAs(token, toTestPerson(person)) {
            put(
                "/onboarding/health_declaration/answers/imc",
                body = imcAnswerRequest,
                headers = userAgent
            ) { response ->
                assertThat(response).isBadRequestWithErrorCode("health_declaration_not_started")
            }
        }
    }

    @Test
    fun `#answerCondition with subquestion should return it`() {
        val conditionPayload = ConditionPayload(
            answers = listOf(
                ConditionAnswerRequest("Dor no Joelho")
            )
        )
        val hematologicalQuestion = HealthDeclarationQuestion(
            type = HealthDeclarationQuestionType.HEMATOLOGICAL,
            question = "Você tem ou já teve alguma condição hematológica (sanguínea)?",
            answerOptions = listOf(
                HealthDeclarationAnswerOption(
                    label = "Anemia", value = "D64", subQuestion = HealthDeclarationQuestion(
                        type = HealthDeclarationQuestionType.ANEMIA,
                        question = "Qual tipo de anemia você tem?",
                        answerOptions = listOf(
                            HealthDeclarationAnswerOption(label = "Anemia falciforme", value = "D57"),
                            HealthDeclarationAnswerOption(label = "Anemia hemolítica", value = "D58"),
                            HealthDeclarationAnswerOption(label = "Anemia aplástica", value = "D60"),
                            HealthDeclarationAnswerOption(label = "Deficiência de nutrientes", value = "D53")
                        )
                    )
                ),
            )
        )
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { registrationService.findByPerson(person.id) } returns registration.success()

        coEvery {
            healthDeclarationForm.answer(
                person,
                any(),
            )
        } returns hematologicalQuestion

        authenticatedAs(token, toTestPerson(person)) {
            put(
                "/onboarding/health_declaration/answers/hematological", body = conditionPayload,
                headers = userAgent
            ) { response ->
                assertThat(response.content.toString()).contains("sub_question")
            }
        }
    }

    @Test
    fun `#answerSurgeries should return 200 OK with a non required confirmation question`() {
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { registrationService.findByPerson(person.id) } returns registration.success()
        val condition = "Dor no Joelho"
        val payload = SurgeryPayload(
            answers = listOf(
                SurgeryAnswerRequest(condition, value = "true")
            )
        )
        val confirmationQuestion = HealthDeclarationQuestion(
            type = HealthDeclarationQuestionType.CONFIRMATION,
            question = "Você tem mais alguma coisa?"
        )

        coEvery {
            healthDeclarationForm.answerSurgeries(
                person,
                listOf(condition)
            )
        } returns confirmationQuestion.success()

        authenticatedAs(token, toTestPerson(person)) {
            put("/onboarding/health_declaration/answers/surgeries", payload) { response ->
                assertThat(response).isOK()

                val body: ConfirmationFormQuestion = response.bodyAsJson()
                assertThat(body.required).isFalse()
            }
        }
    }

    @Test
    fun `#confirmForPerson should return 200 OK is person is dependent`() {
        val condition = "Dor no Joelho"
        val payload = SurgeryPayload(
            answers = listOf(
                SurgeryAnswerRequest(condition, value = "true")
            )
        )

        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()
        coEvery { beneficiaryService.findByPersonId(dependent.id) } returns dependentBeneficiary.success()
        coEvery { registrationService.findByPerson(person.id) } returns registration.success()
        coEvery { healthDeclarationForm.findByPerson(dependent.id) } returns healthDeclaration.success()
        coEvery { healthDeclarationForm.confirm(any(), person, any()) } returns Result.success(healthDeclaration)
        coEvery {
            beneficiaryService.findByPersonId(
                dependent.id,
                BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns beneficiary.success()
        authenticatedAs(token, toTestPerson(person)) {
            put("/onboarding/${dependent.id}/health_declaration/confirm", payload) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#confirmForPerson should return 403 FORBIDDEN if person is not dependent`() {
        val condition = "Dor no Joelho"
        val payload = SurgeryPayload(
            answers = listOf(
                SurgeryAnswerRequest(condition, value = "true")
            )
        )

        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()
        coEvery { beneficiaryService.findByPersonId(randomPerson.id) } returns randomBeneficiary.success()
        coEvery { registrationService.findByPerson(person.id) } returns registration.success()
        coEvery { healthDeclarationForm.findByPerson(randomPerson.id) } returns healthDeclaration.success()
        coEvery { healthDeclarationForm.confirm(any(), person, any()) } returns Result.success(healthDeclaration)

        authenticatedAs(token, toTestPerson(person)) {
            put("/onboarding/${randomPerson.id}/health_declaration/confirm", payload) { response ->
                assertThat(response).isForbidden()
            }
        }
    }

    @Test
    fun `#confirmForPerson should return 200 OK is person is dependent and move dependent to next phase`() {
        val condition = "Dor no Joelho"
        val payload = SurgeryPayload(
            answers = listOf(
                SurgeryAnswerRequest(condition, value = "true")
            )
        )
        val currentPhase = TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
        )
        val onboarding = TestModelFactory.buildBeneficiaryOnboarding(phases = listOf(currentPhase))

        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()
        coEvery { beneficiaryService.findByPersonId(dependent.id) } returns dependentBeneficiary.success()
        coEvery { registrationService.findByPerson(person.id) } returns registration.success()
        coEvery { healthDeclarationForm.findByPerson(dependent.id) } returns healthDeclaration.success()
        coEvery { healthDeclarationForm.confirm(any(), person, any()) } returns Result.success(healthDeclaration)
        coEvery {
            beneficiaryService.findByPersonId(
                dependent.id,
                BeneficiaryService.FindOptions(withOnboarding = true)
            )
        } returns beneficiary.copy(onboarding = onboarding).success()
        coEvery {
            beneficiaryOnboardingService.moveToPhase(
                beneficiary.id,
                phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT,
                any(),
                maxSteps = 5,
            )
        } returns TestModelFactory.buildBeneficiaryOnboardingPhase(
            phase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT
        ).success()

        authenticatedAs(token, toTestPerson(person)) {
            put("/onboarding/${dependent.id}/health_declaration/confirm", payload) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#answerConditionForPerson should return 200 OK is person is dependent`() {
        val payload = SurgeryPayload(
            answers = listOf(
                SurgeryAnswerRequest("Dor no Joelho", value = "true")
            )
        )

        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()
        coEvery { beneficiaryService.findByPersonId(dependent.id) } returns dependentBeneficiary.success()
        coEvery { personService.get(dependent.id) } returns Result.success(dependent)
        coEvery {
            healthDeclarationForm.answer(
                any(),
                any(),
            )
        } returns Result.success(healthDeclarationQuestion)

        authenticatedAs(token, toTestPerson(person)) {
            put(
                "/onboarding/${dependent.id}/health_declaration/answers/${HealthDeclarationQuestionType.SURGERY}",
                body = payload,
                headers = userAgent
            ) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#answerConditionForPerson should return 200 OK even when the surgery answer is not grouped`() {
        val payload = SurgeryPayload(
            answers = listOf(
                SurgeryAnswerRequest("Doença de Von Willebrand", value = "D68")
            )
        )

        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()
        coEvery { beneficiaryService.findByPersonId(dependent.id) } returns dependentBeneficiary.success()
        coEvery { personService.get(dependent.id) } returns Result.success(dependent)
        coEvery {
            healthDeclarationForm.answer(
                any(),
                any(),
            )
        } returns Result.success(
            HealthDeclarationQuestion(
                type = HealthDeclarationQuestionType.SURGERY,
                question = "Question?",
                answerOptions = listOf(
                    HealthDeclarationAnswerOption(
                        label = "Sim",
                        value = "true",
                        exclusive = true
                    ),
                    HealthDeclarationAnswerOption(
                        label = "Não",
                        value = "false",
                        exclusive = true
                    )
                ),
                progress = 100,
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            put(
                "/onboarding/${dependent.id}/health_declaration/answers/${HealthDeclarationQuestionType.HEMATOLOGICAL}",
                body = payload,
                headers = userAgent
            ) { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#answerConditionForPerson should return 403 FORBIDDEN if person is not dependent`() {
        val payload = SurgeryPayload(
            answers = listOf(
                SurgeryAnswerRequest("Dor no Joelho", value = "true")
            )
        )

        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { beneficiaryService.findByPersonId(person.id) } returns beneficiary.success()
        coEvery { beneficiaryService.findByPersonId(randomPerson.id) } returns randomBeneficiary.success()
        coEvery {
            healthDeclarationForm.answer(
                any(),
                any(),
            )
        } returns Result.success(healthDeclarationQuestion)

        authenticatedAs(token, toTestPerson(person)) {
            put(
                "/onboarding/${randomPerson.id}/health_declaration/answers/${HealthDeclarationQuestionType.SURGERY}",
                payload
            ) { response ->
                assertThat(response).isForbidden()
            }
        }
    }

}
