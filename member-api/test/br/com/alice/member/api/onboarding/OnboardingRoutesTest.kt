package br.com.alice.member.api.onboarding

import br.com.alice.bottini.client.OpportunityService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.orZero
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.CoPaymentType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareModelType
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.OnboardingPhase.WAITING_FOR_REVIEW
import br.com.alice.data.layer.models.Opportunity
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.data.layer.models.PrimaryAttentionType
import br.com.alice.data.layer.models.ProductInfo
import br.com.alice.data.layer.models.ProductType
import br.com.alice.data.layer.models.RefundType
import br.com.alice.data.layer.models.TierType
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.controllers.onboarding.v1.OnboardingController
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.onboarding.FirstPaymentInvoiceItemResponse
import br.com.alice.member.api.models.onboarding.FirstPaymentInvoiceResponse
import br.com.alice.member.api.models.onboarding.FirstPaymentResponse
import br.com.alice.member.api.services.OnboardingMobileRouter
import br.com.alice.membership.client.PersonPreferencesService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.ShoppingService
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.moneyin.client.InvoicesService
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test

class OnboardingRoutesTest : RoutesTestHelper() {

    private val onboardingService: OnboardingService = mockk()
    private val portabilityService: InsurancePortabilityService = mockk()
    private val personService: PersonService = mockk()
    private val shoppingService: ShoppingService = mockk()
    private val personPreferencesService: PersonPreferencesService = mockk()
    private val invoicesService: InvoicesService = mockk()
    private val billingAccountablePartyService: BillingAccountablePartyService = mockk()
    private val opportunityService: OpportunityService = mockk()

    private val onboardingController = OnboardingController(
        onboardingService,
        portabilityService,
        personService,
        shoppingService,
        invoicesService,
        billingAccountablePartyService,
        personPreferencesService,
        opportunityService
    )

    private val person = TestModelFactory.buildPerson()

    private val token: String = UUID.randomUUID().toString()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { onboardingController }
    }

    @Test
    fun `#getStatus should return 401 Unauthorized when auth header is not present`() {
        internalAuthentication {
            get("/onboarding/status") { assertThat(it).isUnauthorized() }
        }
    }

    @Test
    fun `#getStatus should return 200 OK and create onboarding via shoppingService when onboarding process does not exists`() =
        runBlocking {
            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException("Onboarding not found for Person Id '${person.id}'").failure()
            coEvery { shoppingService.startShopping(person) } returns TestModelFactory.buildPersonOnboarding(
                currentPhase = WAITING_FOR_REVIEW
            ).success()
            coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
                .success()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns TestModelFactory.buildPersonPreferences(
                PaymentMethod.PIX
            ).success()

            val expected =
                OnboardingMobileRouter.getNavigation(person, WAITING_FOR_REVIEW, portabilityRequested = false)

            authenticatedAs(token, toTestPerson(person)) {
                get("/onboarding/status") { response ->
                    assertThat(response).isSuccessfulJson()

                    val actualResponse: NavigationResponse = response.bodyAsJson()
                    assertThat(actualResponse).isEqualTo(expected)
                }
            }
        }

    @Test
    fun `#getStatus should return 200 OK and create onboarding via shoppingService when onboarding process does not exists and person is B2B`() =
        runBlocking {
            val person = person.copy(
                productInfo = ProductInfo(
                    brand = Brand.ALICE,
                    primaryAttention = PrimaryAttentionType.ALICE,
                    tier = TierType.TIER_1,
                    coPayment = CoPaymentType.NONE,
                    healthcareModelType = HealthcareModelType.V3,
                    refund = RefundType.FULL,
                    productType = ProductType.B2B
                )
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException("Onboarding not found for Person Id '${person.id}'").failure()
            coEvery { shoppingService.startShopping(person) } returns TestModelFactory.buildPersonOnboarding(
                currentPhase = WAITING_FOR_REVIEW
            ).success()
            coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
                .success()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns TestModelFactory.buildPersonPreferences(
                PaymentMethod.PIX
            ).success()

            val expected =
                OnboardingMobileRouter.getNavigation(person, WAITING_FOR_REVIEW, portabilityRequested = false)

            authenticatedAs(token, toTestPerson(person)) {
                get("/onboarding/status") { response ->
                    assertThat(response).isSuccessfulJson()

                    val actualResponse: NavigationResponse = response.bodyAsJson()
                    assertThat(actualResponse).isEqualTo(expected)
                }
            }
        }

    @Test
    fun `#getStatus should return 200 OK and create onboarding via shoppingService when onboarding process does not exists and person is B2C with nationalId on ff`() =
        runBlocking {
            val person = person.copy(
                productInfo = ProductInfo(
                    brand = Brand.ALICE,
                    primaryAttention = PrimaryAttentionType.ALICE,
                    tier = TierType.TIER_1,
                    coPayment = CoPaymentType.NONE,
                    healthcareModelType = HealthcareModelType.V3,
                    refund = RefundType.FULL,
                    productType = ProductType.B2C
                ),
                opportunityId = RangeUUID.generate()
            )

            val opportunity = Opportunity(
                id = person.opportunityId!!,
                simulationId = RangeUUID.generate(),
                productId = RangeUUID.generate(),
                prices = emptyList(),
                productPriceListingId = RangeUUID.generate(),
                expiresAt = LocalDate.now().atEndOfTheDay().plusDays(1),
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException("Onboarding not found for Person Id '${person.id}'").failure()
            coEvery { shoppingService.startShopping(person) } returns TestModelFactory.buildPersonOnboarding(
                currentPhase = WAITING_FOR_REVIEW
            ).success()
            coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
                .success()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns TestModelFactory.buildPersonPreferences(
                PaymentMethod.PIX
            ).success()
            coEvery { opportunityService.get(person.opportunityId!!) } returns opportunity.success()

            val expected =
                OnboardingMobileRouter.getNavigation(person, WAITING_FOR_REVIEW, portabilityRequested = false)

            withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", listOf(person.nationalId)) {
                authenticatedAs(token, toTestPerson(person)) {
                    get("/onboarding/status") { response ->
                        assertThat(response).isSuccessfulJson()

                        val actualResponse: NavigationResponse = response.bodyAsJson()
                        assertThat(actualResponse).isEqualTo(expected)
                    }
                }
            }
        }

    @Test
    fun `#getStatus should return 200 OK and create onboarding via shoppingService when onboarding process exists and person is B2C with nationalId not on ff and not expired opportunity`() =
        runBlocking {
            val person = person.copy(
                productInfo = ProductInfo(
                    brand = Brand.ALICE,
                    primaryAttention = PrimaryAttentionType.ALICE,
                    tier = TierType.TIER_1,
                    coPayment = CoPaymentType.NONE,
                    healthcareModelType = HealthcareModelType.V3,
                    refund = RefundType.FULL,
                    productType = ProductType.B2C
                ),
                opportunityId = RangeUUID.generate()
            )

            val opportunity = Opportunity(
                id = person.opportunityId!!,
                simulationId = RangeUUID.generate(),
                productId = RangeUUID.generate(),
                prices = emptyList(),
                productPriceListingId = RangeUUID.generate(),
                expiresAt = LocalDate.now().atEndOfTheDay().plusDays(1),
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns TestModelFactory.buildPersonOnboarding(
                currentPhase = WAITING_FOR_REVIEW
            ).success()
            coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
                .success()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns TestModelFactory.buildPersonPreferences(
                PaymentMethod.PIX
            ).success()
            coEvery { opportunityService.get(person.opportunityId!!) } returns opportunity.success()

            val expected =
                OnboardingMobileRouter.getNavigation(person, WAITING_FOR_REVIEW, portabilityRequested = false)

            withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", emptyList<String>()) {
                authenticatedAs(token, toTestPerson(person)) {
                    get("/onboarding/status") { response ->
                        assertThat(response).isSuccessfulJson()

                        val actualResponse: NavigationResponse = response.bodyAsJson()
                        assertThat(actualResponse).isEqualTo(expected)
                    }
                }
            }
        }

    @Test
    fun `#getStatus should return 200 OK and create onboarding via shoppingService when onboarding process does not exists, person is B2C not on ff and expired opportunity`() =
        runBlocking {
            val person = person.copy(
                productInfo = ProductInfo(
                    brand = Brand.ALICE,
                    primaryAttention = PrimaryAttentionType.ALICE,
                    tier = TierType.TIER_1,
                    coPayment = CoPaymentType.NONE,
                    healthcareModelType = HealthcareModelType.V3,
                    refund = RefundType.FULL,
                    productType = ProductType.B2C
                ),
                opportunityId = RangeUUID.generate()
            )

            val opportunity = Opportunity(
                id = person.opportunityId!!,
                simulationId = RangeUUID.generate(),
                productId = RangeUUID.generate(),
                prices = emptyList(),
                productPriceListingId = RangeUUID.generate(),
                expiresAt = LocalDate.now().atEndOfTheDay().minusDays(31)
            )

            coEvery { personService.get(person.id) } returns person.success()
            coEvery { onboardingService.findByPerson(person.id) } returns NotFoundException("Onboarding not found for Person Id '${person.id}'").failure()
            coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
                .success()
            coEvery { personPreferencesService.findByPersonId(person.id) } returns TestModelFactory.buildPersonPreferences(
                PaymentMethod.PIX
            ).success()
            coEvery { opportunityService.get(person.opportunityId!!) } returns opportunity.success()

            withFeatureFlag(FeatureNamespace.ALICE_APP, "gas_b2c_allowed_members", emptyList<String>()) {
                authenticatedAs(token, toTestPerson(person)) {
                    get("/onboarding/status") { response ->
                        assertThat(response).isUnauthorized()
                    }
                }
            }

            coVerify { shoppingService wasNot called }
        }

    @Test
    fun `#getStatus should return 200 OK with WAITING_FOR_REVIEW mobile route`() = runBlocking {
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns TestModelFactory.buildPersonOnboarding(
            currentPhase = WAITING_FOR_REVIEW
        ).success()
        coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
            .success()
        coEvery { personPreferencesService.findByPersonId(person.id) } returns TestModelFactory.buildPersonPreferences(
            PaymentMethod.PIX
        ).success()

        val expected = OnboardingMobileRouter.getNavigation(person, WAITING_FOR_REVIEW, portabilityRequested = false)

        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/status") { response ->
                assertThat(response).isSuccessfulJson()

                val actualResponse: NavigationResponse = response.bodyAsJson()
                assertThat(actualResponse).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getStatus should return 200 OK with WAITING_FOR_REVIEW mobile route when person is B2B`() = runBlocking {
        val person = person.copy(
            productInfo = ProductInfo(
                brand = Brand.ALICE,
                primaryAttention = PrimaryAttentionType.ALICE,
                tier = TierType.TIER_1,
                coPayment = CoPaymentType.NONE,
                healthcareModelType = HealthcareModelType.V3,
                refund = RefundType.FULL,
                productType = ProductType.B2B
            )
        )

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns TestModelFactory.buildPersonOnboarding(
            currentPhase = WAITING_FOR_REVIEW
        ).success()
        coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
            .success()
        coEvery { personPreferencesService.findByPersonId(person.id) } returns TestModelFactory.buildPersonPreferences(
            PaymentMethod.PIX
        ).success()

        val expected = OnboardingMobileRouter.getNavigation(person, WAITING_FOR_REVIEW, portabilityRequested = false)

        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/status") { response ->
                assertThat(response).isSuccessfulJson()

                val actualResponse: NavigationResponse = response.bodyAsJson()
                assertThat(actualResponse).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getStatus should return 401 Unauthorized when person is B2C`() = runBlocking {
        val person = person.copy(
            productInfo = ProductInfo(
                brand = Brand.ALICE,
                primaryAttention = PrimaryAttentionType.ALICE,
                tier = TierType.TIER_1,
                coPayment = CoPaymentType.NONE,
                healthcareModelType = HealthcareModelType.V3,
                refund = RefundType.FULL,
                productType = ProductType.B2C
            )
        )
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.findByPerson(person.id) } returns TestModelFactory.buildPersonOnboarding(
            currentPhase = WAITING_FOR_REVIEW
        ).success()
        coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest()
            .success()
        coEvery { personPreferencesService.findByPersonId(person.id) } returns TestModelFactory.buildPersonPreferences(
            PaymentMethod.PIX
        ).success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/status") { response ->
                assertThat(response).isUnauthorized()
            }
        }
    }

    @Test
    fun `#fetchFirstPayment should return 200 and the first payment when the payment method is PIX`() = runBlocking {
        val payment = TestModelFactory.buildInvoicePayment(method = PaymentMethod.PIX)
        val paymentPix = TestModelFactory.buildPixPaymentDetail(paymentId = payment.id)
        val paymentWithDetails = payment.withPaymentDetail(paymentPix)
        val memberInvoice = TestModelFactory.buildMemberInvoice(
            copay = BigDecimal.ZERO,
        )
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val firstPayment = FirstPaymentResponse(
            id = memberInvoice.id,
            amount = memberInvoice.totalAmount,
            dueDate = memberInvoice.dueDate,
            status = paymentWithDetails.status,
            reason = PaymentReason.FIRST_PAYMENT,
            method = paymentWithDetails.method,
            paymentUrl = paymentPix.paymentUrl,
            paymentCode = paymentPix.paymentCode,
            nationalId = person.nationalId,
            invoice = FirstPaymentInvoiceResponse(
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                amount = paymentWithDetails.amount,
                referenceDate = memberInvoice.referenceDate,
                nationalId = person.nationalId,
                invoiceItems = listOf(
                    FirstPaymentInvoiceItemResponse(
                        title = "Proporcional dias não inclusos",
                        amount = memberInvoice.invoiceBreakdown?.proRation.orZero(),
                    ),
                    FirstPaymentInvoiceItemResponse(
                        title = "Seu plano",
                        amount = memberInvoice.invoiceBreakdown?.productPrice.orZero(),
                    ),
                )
            )

        )

        coEvery {
            invoicesService.findInvoiceAndPaymentsByPersonId(
                person.id,
                billingAccountableParty.id
            )
        } returns (listOf(paymentWithDetails) to memberInvoice).success()
        coEvery { personService.getNationalId(person.id) } returns person.nationalId.success()
        coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/first_payment") { response ->
                assertThat(response).isSuccessfulJson()

                val actualResponse: FirstPaymentResponse = response.bodyAsJson()
                assertThat(actualResponse).isEqualTo(firstPayment)
            }
        }
    }

    @Test
    fun `#fetchFirstPayment should return 200 and the first payment when the payment method is BOLETO`() = runBlocking {
        val payment = TestModelFactory.buildInvoicePayment(method = PaymentMethod.BOLETO)
        val paymentBoleto = TestModelFactory.buildBoletoPaymentDetail(paymentId = payment.id)
        val paymentWithDetails = payment.withPaymentDetail(paymentBoleto)
        val memberInvoice = TestModelFactory.buildMemberInvoice()
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val firstPayment = FirstPaymentResponse(
            id = memberInvoice.id,
            amount = memberInvoice.totalAmount,
            dueDate = memberInvoice.dueDate,
            status = paymentWithDetails.status,
            reason = PaymentReason.FIRST_PAYMENT,
            method = paymentWithDetails.method,
            paymentUrl = paymentBoleto.paymentUrl,
            paymentCode = paymentBoleto.barcode,
            nationalId = person.nationalId,
            invoice = FirstPaymentInvoiceResponse(
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                amount = paymentWithDetails.amount,
                referenceDate = memberInvoice.referenceDate,
                nationalId = person.nationalId,
                invoiceItems = listOf(
                    FirstPaymentInvoiceItemResponse(
                        title = "Coparticipação",
                        amount = memberInvoice.invoiceBreakdown?.copay.orZero(),
                    ),
                    FirstPaymentInvoiceItemResponse(
                        title = "Proporcional dias não inclusos",
                        amount = memberInvoice.invoiceBreakdown?.proRation.orZero(),
                    ),
                    FirstPaymentInvoiceItemResponse(
                        title = "Seu plano",
                        amount = memberInvoice.invoiceBreakdown?.productPrice.orZero(),
                    ),
                )
            )

        )

        coEvery {
            invoicesService.findInvoiceAndPaymentsByPersonId(
                person.id,
                billingAccountableParty.id
            )
        } returns (listOf(paymentWithDetails) to memberInvoice).success()
        coEvery { personService.getNationalId(person.id) } returns person.nationalId.success()
        coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/first_payment") { response ->
                assertThat(response).isSuccessfulJson()

                val actualResponse: FirstPaymentResponse = response.bodyAsJson()
                assertThat(actualResponse).isEqualTo(firstPayment)
            }
        }
    }

    @Test
    fun `#fetchFirstPayment should return 200 and the first payment when the payment method is SIMPLE_CREDIT_CARD`() =
        runBlocking {
            val payment = TestModelFactory.buildInvoicePayment(method = PaymentMethod.SIMPLE_CREDIT_CARD)
            val paymentSimpleCreditCard = TestModelFactory.buildBoletoPaymentDetail(paymentId = payment.id)
            val paymentWithDetails = payment.withPaymentDetail(paymentSimpleCreditCard)
            val memberInvoice = TestModelFactory.buildMemberInvoice()
            val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
            val firstPayment = FirstPaymentResponse(
                id = memberInvoice.id,
                amount = memberInvoice.totalAmount,
                dueDate = memberInvoice.dueDate,
                status = paymentWithDetails.status,
                reason = PaymentReason.FIRST_PAYMENT,
                method = paymentWithDetails.method,
                paymentUrl = paymentSimpleCreditCard.paymentUrl,
                paymentCode = null,
                nationalId = person.nationalId,
                invoice = FirstPaymentInvoiceResponse(
                    firstName = billingAccountableParty.firstName,
                    lastName = billingAccountableParty.lastName,
                    amount = paymentWithDetails.amount,
                    referenceDate = memberInvoice.referenceDate,
                    nationalId = person.nationalId,
                    invoiceItems = listOf(
                        FirstPaymentInvoiceItemResponse(
                            title = "Coparticipação",
                            amount = memberInvoice.invoiceBreakdown?.copay.orZero(),
                        ),
                        FirstPaymentInvoiceItemResponse(
                            title = "Proporcional dias não inclusos",
                            amount = memberInvoice.invoiceBreakdown?.proRation.orZero(),
                        ),
                        FirstPaymentInvoiceItemResponse(
                            title = "Seu plano",
                            amount = memberInvoice.invoiceBreakdown?.productPrice.orZero(),
                        ),
                    )
                )

            )

            coEvery {
                invoicesService.findInvoiceAndPaymentsByPersonId(
                    person.id,
                    billingAccountableParty.id
                )
            } returns (listOf(paymentWithDetails) to memberInvoice).success()
            coEvery { personService.getNationalId(person.id) } returns person.nationalId.success()
            coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty.success()

            authenticatedAs(token, toTestPerson(person)) {
                get("/onboarding/first_payment") { response ->
                    assertThat(response).isSuccessfulJson()

                    val actualResponse: FirstPaymentResponse = response.bodyAsJson()
                    assertThat(actualResponse).isEqualTo(firstPayment)
                }
            }
        }

    @Test
    fun `#fetchFirstPayment should return status as CANCELED when due date is before today`() = runBlocking {
        val payment = TestModelFactory.buildInvoicePayment(method = PaymentMethod.SIMPLE_CREDIT_CARD)
        val paymentSimpleCreditCard = TestModelFactory.buildBoletoPaymentDetail(paymentId = payment.id)
        val paymentWithDetails = payment.withPaymentDetail(paymentSimpleCreditCard)
        val memberInvoice = TestModelFactory.buildMemberInvoice(dueDate = LocalDateTime.now().minusDays(1))
        val billingAccountableParty = TestModelFactory.buildBillingAccountableParty()
        val firstPayment = FirstPaymentResponse(
            id = memberInvoice.id,
            amount = memberInvoice.totalAmount,
            dueDate = memberInvoice.dueDate,
            status = InvoicePaymentStatus.CANCELED,
            reason = PaymentReason.FIRST_PAYMENT,
            method = paymentWithDetails.method,
            paymentUrl = paymentSimpleCreditCard.paymentUrl,
            paymentCode = null,
            nationalId = person.nationalId,
            invoice = FirstPaymentInvoiceResponse(
                firstName = billingAccountableParty.firstName,
                lastName = billingAccountableParty.lastName,
                amount = paymentWithDetails.amount,
                referenceDate = memberInvoice.referenceDate,
                nationalId = person.nationalId,
                invoiceItems = listOf(
                    FirstPaymentInvoiceItemResponse(
                        title = "Coparticipação",
                        amount = memberInvoice.invoiceBreakdown?.copay.orZero(),
                    ),
                    FirstPaymentInvoiceItemResponse(
                        title = "Proporcional dias não inclusos",
                        amount = memberInvoice.invoiceBreakdown?.proRation.orZero(),
                    ),
                    FirstPaymentInvoiceItemResponse(
                        title = "Seu plano",
                        amount = memberInvoice.invoiceBreakdown?.productPrice.orZero(),
                    ),
                )
            )

        )

        coEvery {
            invoicesService.findInvoiceAndPaymentsByPersonId(
                person.id,
                billingAccountableParty.id
            )
        } returns (listOf(paymentWithDetails) to memberInvoice).success()
        coEvery { personService.getNationalId(person.id) } returns person.nationalId.success()
        coEvery { billingAccountablePartyService.getCurrent(person.id) } returns billingAccountableParty.success()

        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/first_payment") { response ->
                assertThat(response).isSuccessfulJson()

                val actualResponse: FirstPaymentResponse = response.bodyAsJson()
                assertThat(actualResponse).isEqualTo(firstPayment)
            }
        }
    }
}
