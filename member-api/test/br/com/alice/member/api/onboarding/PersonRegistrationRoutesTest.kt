package br.com.alice.member.api.onboarding

import M
import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.RunningMode
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.PersonRegistration
import br.com.alice.data.layer.models.PersonRegistrationAnswer
import br.com.alice.data.layer.models.PersonRegistrationStep
import br.com.alice.data.layer.models.PersonRegistrationStep.DOCUMENT_PHOTO
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.controllers.onboarding.v1.AnswerRequest
import br.com.alice.member.api.controllers.onboarding.v1.PersonRegistrationController
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.onboarding.ChatResponse
import br.com.alice.member.api.services.MemberDocuments
import br.com.alice.membership.client.RegistrationNotFinishedException
import br.com.alice.membership.client.onboarding.InvalidAnswerException
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.PersonRegistrationService
import br.com.alice.membership.client.onboarding.RegistrationAlreadyFinishedException
import br.com.alice.membership.client.onboarding.RegistrationNotStartedException
import br.com.alice.membership.model.onboarding.NameConfirmationStateAnswers
import br.com.alice.membership.model.onboarding.StepResponse
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpMethod
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class PersonRegistrationRoutesTest : RoutesTestHelper() {

    companion object {
        const val REGISTRATION_ROUTE = "/onboarding/registration"
    }

    private val personRegistrationService: PersonRegistrationService = mockk()
    private val personService: PersonService = mockk()
    private val memberDocuments: MemberDocuments = mockk()
    private val onboardingService: OnboardingService = mockk()
    private val portabilityService: InsurancePortabilityService = mockk()

    private val personRegistrationController = PersonRegistrationController(
        personRegistrationService,
        personService,
        memberDocuments,
        onboardingService,
        portabilityService
    )

    private val token = RangeUUID.generate().toString()
    private val person = TestModelFactory.buildPerson()

    private val openRegistrationProcess = PersonRegistration(
        currentStep = PersonRegistrationStep.NAME_CONFIRMATION,
        personId = person.id,
        answers = emptyList()
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { personRegistrationController }
    }

    @Test
    fun `#findByCurrentPerson should return 401 Unauthorized when the person is not present`() {
        internalAuthentication {
            get(REGISTRATION_ROUTE) { response ->
                assertThat(response).isUnauthorized()
            }
        }
    }

    @Test
    fun `#findByCurrentPerson should return 200 OK when the person is on onboarding process (not finished)`() {
        coEvery { personRegistrationService.findByPerson(person.id) } returns openRegistrationProcess.success()
        coEvery { personService.get(person.id) } returns Result.success(person)

        authenticatedAs(token, toTestPerson(person)) {
            get(REGISTRATION_ROUTE) { response ->
                assertThat(response).isOK()

                val expectedActionUrl = answerUrl(openRegistrationProcess)

                val stepResponse = gson.fromJson(response.bodyAsText(), ChatResponse::class.java)
                assertThat(stepResponse.id).isEqualTo(openRegistrationProcess.currentStep.toString())
                assertThat(stepResponse.input.action).matches { it.href.contains(expectedActionUrl) }
                assertThat(stepResponse.input.config).isNotNull
                assertThat(stepResponse.messages).hasSize(3)
            }
        }
    }

    @Test
    fun `#findByCurrentPerson should return 400 BadRequest when the person does not have any onboarding process`() {
        coEvery { personRegistrationService.findByPerson(person.id) } returns NotFoundException("Registration not found for Person '${person.id}'").failure()

        authenticatedAs(token, toTestPerson(person)) {
            get(REGISTRATION_ROUTE) { response ->
                assertThat(response).isBadRequest()

                val errorResponse = gson.fromJson(response.bodyAsText(), ErrorResponse::class.java)
                assertThat(errorResponse.code).isEqualTo("resource_not_found")
            }
        }
    }

    @Test
    fun `#findByCurrentPerson should return 400 BadRequest when the person has an finished onboarding process`() {
        val registration = openRegistrationProcess.copy(
            finishedAt = LocalDateTime.now()
        )

        coEvery { personRegistrationService.findByPerson(person.id) } returns registration.success()

        authenticatedAs(token, toTestPerson(person)) {
            get(REGISTRATION_ROUTE) { response ->
                assertThat(response).isBadRequest()

                val errorResponse = gson.fromJson(response.bodyAsText(), ErrorResponse::class.java)
                assertThat(errorResponse.code).isEqualTo("resource_not_found")
            }
        }
    }

    @Test
    fun `#answer should return 200 OK with next step chat messages if the question was answered correctly`() {
        val personRegistration = PersonRegistration(
            currentStep = PersonRegistrationStep.NAME_CONFIRMATION,
            personId = person.id,
            answers = emptyList()
        )

        val answerStepAnswer = PersonRegistrationAnswer(personRegistration.currentStep, NameConfirmationStateAnswers.CONTINUE)

        val nextRegistrationState = PersonRegistration(
            currentStep = PersonRegistrationStep.NICK_NAME_CHANGE,
            personId = person.id,
            answers = listOf(answerStepAnswer)
        )

        val answerRequest = AnswerRequest(NameConfirmationStateAnswers.CONTINUE)

        coEvery { personRegistrationService.findByPerson(person.id) } returns personRegistration.success()
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { personRegistrationService.answer(person, answerStepAnswer) } returns Result.success(
            StepResponse(
                onboardingPhase = OnboardingPhase.REGISTRATION,
                registration = nextRegistrationState
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            put(answerUrl(personRegistration), body = answerRequest) { response ->
                assertThat(response).isOK()

                val nextStepResponse = gson.fromJson(response.bodyAsText(), ChatResponse::class.java)
                assertThat(nextStepResponse.id).isEqualTo(nextRegistrationState.currentStep.toString())
                assertThat(nextStepResponse.input).isNotNull
                assertThat(nextStepResponse.input.action.href).isEqualTo("http://localhost/onboarding/registration/answers/nick_name_change")
            }
        }
    }

    @Test
    fun `#answer should return 400 BadRequest when the onboarding is already finished`() {
        val answerRequest = AnswerRequest("Some Answer")

        coEvery { personRegistrationService.findByPerson(person.id) } returns openRegistrationProcess.success()
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { personRegistrationService.answer(person, any()) } returns Result.failure(RegistrationAlreadyFinishedException(person.id))

        authenticatedAs(token, toTestPerson(person)) {
            put(answerUrl(openRegistrationProcess), body = answerRequest) { response ->
                assertThat(response).isBadRequest()

                val nextStepResponse = gson.fromJson(response.bodyAsText(), ErrorResponse::class.java)
                assertThat(nextStepResponse.code).isEqualTo(M.REGISTRATION_ALREADY_FINISHED)
            }
        }
    }

    @Test
    fun `#answer should return 400 BadRequest when the onboarding hasnt started`() {
        val answerRequest = AnswerRequest("Some Answer")

        coEvery { personRegistrationService.findByPerson(person.id) } returns openRegistrationProcess.success()
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { personRegistrationService.answer(person, any()) } returns Result.failure(RegistrationNotStartedException(person.id))

        authenticatedAs(token, toTestPerson(person)) {
            put(answerUrl(openRegistrationProcess), body = answerRequest) { response ->
                assertThat(response).isBadRequest()

                val nextStepResponse = gson.fromJson(response.bodyAsText(), ErrorResponse::class.java)
                assertThat(nextStepResponse.code).isEqualTo(M.REGISTRATION_NOT_STARTED)
            }
        }
    }

    @Test
    fun `#answer should return 200 OK when the user answered the last onboarding question`() {
        val newNickName = "Bene"

        val finishedOnboarding = openRegistrationProcess.copy(
            finishedAt = LocalDateTime.now(),
            currentStep = PersonRegistrationStep.TERMS_ACCEPTANCE,
            answers = listOf(
                PersonRegistrationAnswer(PersonRegistrationStep.NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                PersonRegistrationAnswer(PersonRegistrationStep.NICK_NAME_CHANGE, newNickName)
            )
        )

        val answerRequest = AnswerRequest("true")

        coEvery { personRegistrationService.findByPerson(person.id) } returns finishedOnboarding.success()
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { personRegistrationService.answer(person, any()) } returns Result.success(
            StepResponse(
                onboardingPhase = OnboardingPhase.WAITING_FOR_REVIEW,
                registration = finishedOnboarding
            )
        )

        coEvery { personService.updatePerson(finishedOnboarding) } returns Result.success(
            person.copy(
                nickName = newNickName
            )
        )

        coEvery { onboardingService.findByPerson(person.id) } returns TestModelFactory.buildPersonOnboarding().success()
        coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest().success()

        authenticatedAs(token, toTestPerson(person)) {
            put(answerUrl(openRegistrationProcess), body = answerRequest) { response ->
                assertThat(response).isOK()

                val finishedResponse: PersonRegistrationFinishedResponseTest = response.bodyAsJson()
                assertThat(finishedResponse.navigation.mobileRoute).isEqualTo(MobileRouting.HEALTH_DECLARATION_APPOINTMENT)
            }
        }
    }

    @Test
    fun `#answer should return 200 OK and creates a new ticket when the last question was answered`() {
        val newNickName = "Bene"

        val finishedOnboarding = openRegistrationProcess.copy(
            finishedAt = LocalDateTime.now(),
            currentStep = PersonRegistrationStep.TERMS_ACCEPTANCE,
            answers = listOf(
                PersonRegistrationAnswer(PersonRegistrationStep.NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                PersonRegistrationAnswer(PersonRegistrationStep.NICK_NAME_CHANGE, newNickName)
            )
        )

        val answerRequest = AnswerRequest("true")

        coEvery { personRegistrationService.findByPerson(person.id) } returns finishedOnboarding.success()
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { personRegistrationService.answer(person, any()) } returns Result.success(
            StepResponse(
                onboardingPhase = OnboardingPhase.WAITING_FOR_REVIEW,
                registration = finishedOnboarding
            )
        )

        coEvery { onboardingService.findByPerson(person.id) } returns TestModelFactory.buildPersonOnboarding().success()
        coEvery { portabilityService.findByPerson(person.id) } returns TestModelFactory.buildInsurancePortabilityRequest().success()

        authenticatedAs(token, toTestPerson(person)) {
            put(answerUrl(openRegistrationProcess), body = answerRequest) { response ->
                assertThat(response).isOK()

                val finishedResponse: PersonRegistrationFinishedResponseTest = response.bodyAsJson()
                assertThat(finishedResponse.navigation.mobileRoute).isEqualTo(MobileRouting.HEALTH_DECLARATION_APPOINTMENT)
            }
        }
    }

    @Suppress("UNCHECKED_CAST")
    @Test
    fun `#answer should return 400 BadRequest with an ErrorResponse and the chat messages to show to the member`() {
        val registration = openRegistrationProcess.copy(
            answers = listOf(
                PersonRegistrationAnswer(PersonRegistrationStep.NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE)
            )
        )

        val answerRequest = AnswerRequest("")

        coEvery { personRegistrationService.findByPerson(person.id) } returns registration.success()
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { personRegistrationService.answer(person, any()) } returns InvalidAnswerException("This answer is not valid").failure()

        authenticatedAs(token, toTestPerson(person)) {
            put(answerUrl(registration), body = answerRequest) { response ->
                assertThat(response).isBadRequest()

                val errorResponse = gson.fromJson(response.bodyAsText(), ErrorResponse::class.java)
                assertThat(errorResponse.code).isEqualTo("validation_error")

                val chatResponse = errorResponse.data as Map<String, Any>
                assertThat(chatResponse["id"]).isEqualTo(registration.currentStep.toString())
            }
        }

        coVerifyOnce { personRegistrationService.findByPerson(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { personRegistrationService.answer(any(), any()) }
        confirmVerified(
            personRegistrationService,
            personService,
            memberDocuments,
            onboardingService,
            portabilityService,
        )
    }

    @Test
    fun `#answer should return 400 Bad Request with an ErrorResponse when something wrong happened on person update`() {
        val newNickName = "Bene"

        val finishedOnboarding = openRegistrationProcess.copy(
            finishedAt = LocalDateTime.now(),
            currentStep = PersonRegistrationStep.TERMS_ACCEPTANCE,
            answers = listOf(
                PersonRegistrationAnswer(PersonRegistrationStep.NAME_CONFIRMATION, NameConfirmationStateAnswers.CHANGE),
                PersonRegistrationAnswer(PersonRegistrationStep.NICK_NAME_CHANGE, newNickName)
            )
        )

        val answerRequest = AnswerRequest("true")

        coEvery { personRegistrationService.findByPerson(person.id) } returns finishedOnboarding.success()
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { personRegistrationService.answer(person, any()) } returns Result.failure(RegistrationNotFinishedException("Registration has not been finished for PersonId '${person.id}'"))

        authenticatedAs(token, toTestPerson(person)) {
            put(answerUrl(finishedOnboarding), body = answerRequest) { response ->
                assertThat(response).isBadRequest()

                val errorResponse = gson.fromJson(response.bodyAsText(), ErrorResponse::class.java)
                assertThat(errorResponse.code).isEqualTo("registration_not_finished")
            }
        }
    }

    @Test
    fun `#uploadPhoto should return 400 Bad Request if request doesnt have a multipart header`() {
        authenticatedAs(token, toTestPerson(person)) {
            put("$REGISTRATION_ROUTE/photos") { response ->
                assertThat(response).isBadRequest()

                val errorResponse = gson.fromJson(response.bodyAsText(), ErrorResponse::class.java)
                assertThat(errorResponse.code).isEqualTo("multipart_required")
            }
        }
    }

    @Test
    fun `#uploadPhoto should return 400 Bad Request if file is not an image`() {
        authenticatedAs(token, toTestPerson(person)) {
            multipart(HttpMethod.Put, "$REGISTRATION_ROUTE/photos", fileName = "invalid_image.txt") { response ->
                assertThat(response).isBadRequest()

                val errorResponse = gson.fromJson(response.bodyAsText(), ErrorResponse::class.java)
                assertThat(errorResponse.code).isEqualTo("image_required")
            }
        }
    }

    @Test
    fun `#uploadPhoto should return 200 OK if the upload was done correctly`() {
        val bucketName = "SomeBucket"

        mockkObject(ServiceConfig)
        every { ServiceConfig.environment() } returns RunningMode.TEST
        every { ServiceConfig.bucket("onboardingPhotoBucket") } returns bucketName

        coEvery { memberDocuments.uploadSelfiePhoto(person.id, any()) } returns "someUrl"

        coEvery { personRegistrationService.findByPerson(person.id) } returns openRegistrationProcess.success()
        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { personRegistrationService.answer(person, any()) } returns Result.success(
            StepResponse(
                onboardingPhase = OnboardingPhase.REGISTRATION,
                registration = openRegistrationProcess
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            multipart(HttpMethod.Put, "$REGISTRATION_ROUTE/photos", fileName = "apple.png") { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#uploadIdentityDocument should return 200 OK if the upload was successfully done`() {
        val fileVaultItemId = RangeUUID.generate().toString()
        val answer = PersonRegistrationAnswer(DOCUMENT_PHOTO, fileVaultItemId)
        val registrationWithNewAnswer = openRegistrationProcess.copy(
            answers = listOf(answer)
        )

        coEvery { personService.get(person.id) } returns Result.success(person)
        coEvery { memberDocuments.uploadIdentityDocument(person.id, any()) } returns fileVaultItemId
        coEvery { personRegistrationService.answer(person, answer) } returns Result.success(
            StepResponse(
                onboardingPhase = OnboardingPhase.REGISTRATION,
                registration = registrationWithNewAnswer
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            multipart(HttpMethod.Put, "$REGISTRATION_ROUTE/documents", fileName = "apple.png") { response ->
                assertThat(response).isOK()
            }
        }
    }

    private fun answerUrl(registration: PersonRegistration) =
        "$REGISTRATION_ROUTE/answers/${registration.currentStep.toString().lowercase()}"
}

data class PersonRegistrationFinishedResponseTest(val navigation: NavigationResponse)
