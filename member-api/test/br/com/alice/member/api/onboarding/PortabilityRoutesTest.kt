package br.com.alice.member.api.onboarding

import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InsurancePortabilityRequestAnswer
import br.com.alice.data.layer.models.InsurancePortabilityRequestQuestionType
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.builders.PortabilityQuestionResponseBuilder
import br.com.alice.member.api.builders.PortabilityStatusResponseBuilder
import br.com.alice.member.api.controllers.onboarding.v1.PortabilityController
import br.com.alice.member.api.models.BasicNavigationResponse
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.onboarding.AnswerQuestionRequest
import br.com.alice.member.api.models.onboarding.PortabilityQuestionResponse
import br.com.alice.member.api.models.onboarding.PortabilityStatusResponse
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.membership.client.onboarding.ProductOrderService
import br.com.alice.membership.model.OrderWithProductWithProviders
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.person.client.PersonService
import br.com.alice.product.model.ProductWithProviders
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlin.test.BeforeTest
import kotlin.test.Test

class PortabilityRoutesTest : RoutesTestHelper() {

    private val person = TestModelFactory.buildPerson()
    private val token = person.id.toString()

    private val product = TestModelFactory.buildProduct()
    private val providers = TestModelFactory.buildProviders()

    private val productWithProviders = ProductWithProviders(product, providers)

    private val personService: PersonService = mockk()
    private val productOrderService: ProductOrderService = mockk()
    private val portabilityService: InsurancePortabilityService = mockk()
    private val onboardingService: OnboardingService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            PortabilityController(
                personService,
                productOrderService,
                portabilityService,
                onboardingService,
            )
        }
    }

    @Test
    fun `#get questions should return response as expected`() {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product)
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { productOrderService.findOrderWithProductAndProvidersByPersonId(person.id) } returns orderWithProduct.success()

        val expectedResponse: PortabilityQuestionResponse = mockk()

        mockkObject(PortabilityQuestionResponseBuilder) {
            coEvery { PortabilityQuestionResponseBuilder.buildPortabilityOptinQuestionResponse(person, orderWithProduct) } returns expectedResponse

            authenticatedAs(token, toTestPerson(person)) {
                get("/onboarding/portability/questions") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }
    }

    @Test
    fun `#put questions with questionType = eligibility and value = skip should return response as expected`() {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product)
        val onboarding = TestModelFactory.buildPersonOnboarding(personId = person.id, currentPhase = OnboardingPhase.REGISTRATION)
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { productOrderService.findOrderWithProductAndProvidersByPersonId(person.id) } returns orderWithProduct.success()
        coEvery { portabilityService.skip(person.id) } returns onboarding.success()

        val request = AnswerQuestionRequest(value = "skip")

        val expectedResponse = BasicNavigationResponse(
                navigation =  NavigationResponse(
                mobileRoute = MobileRouting.REGISTRATION,
                link = Links.REGISTRATION
            )
        )

        authenticatedAs(token, toTestPerson(person)) {
            put("/onboarding/portability/questions/eligibility", body = request) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#put questions with questionType = eligibility and value = confirm should return response as expected`() {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product)
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { productOrderService.findOrderWithProductAndProvidersByPersonId(person.id) } returns orderWithProduct.success()

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest()
        coEvery { portabilityService.request(person.id) } returns portabilityRequest.success()

        mockkObject(PortabilityQuestionResponseBuilder) {
            val expectedResponse: PortabilityQuestionResponse = mockk()
            coEvery { PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest) } returns expectedResponse.success()

            val request = AnswerQuestionRequest(value = "confirm")

            authenticatedAs(token, toTestPerson(person)) {
                put("/onboarding/portability/questions/eligibility", body = request) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }

            coVerify(exactly = 1) { PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest) }
        }

        coVerify(exactly = 1) { portabilityService.request(person.id) }
    }

    @Test
    fun `#put questions with questionType = eligibility and invalid value should return error as expected`() {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product)
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { productOrderService.findOrderWithProductAndProvidersByPersonId(person.id) } returns orderWithProduct.success()

        val request = AnswerQuestionRequest(value = "whatever")

        authenticatedAs(token, toTestPerson(person)) {
            put("/onboarding/portability/questions/eligibility", body = request) { response ->
                assertThat(response).isBadRequestWithErrorCode("unexpected_answer")
            }
        }
    }

    @Test
    fun `#put questions with questionType = min_grace_period should return response as expected`() {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product)
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { productOrderService.findOrderWithProductAndProvidersByPersonId(person.id) } returns orderWithProduct.success()

        val answer = InsurancePortabilityRequestAnswer(answer = "true", questionType = InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD)
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest()

        coEvery { portabilityService.answer(person.id, answer) } returns portabilityRequest.success()

        mockkObject(PortabilityQuestionResponseBuilder) {
            val expectedResponse: PortabilityQuestionResponse = mockk()
            coEvery { PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest) } returns expectedResponse.success()

            val request = AnswerQuestionRequest(value = "true")

            authenticatedAs(token, toTestPerson(person)) {
                put("/onboarding/portability/questions/min_grace_period", body = request) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }
    }

    @Test
    fun `#put questions with questionType = current_health_insurance should return response as expected`() {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product = product)
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { productOrderService.findOrderWithProductAndProvidersByPersonId(person.id) } returns orderWithProduct.success()

        val answer = InsurancePortabilityRequestAnswer(answer = "Sulamerica", questionType = InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE)
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest()

        coEvery { portabilityService.answer(person.id, answer) } returns portabilityRequest.success()

        mockkObject(PortabilityQuestionResponseBuilder) {
            val expectedResponse: PortabilityQuestionResponse = mockk()
            coEvery { PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest) } returns expectedResponse.success()

            val request = AnswerQuestionRequest(value = "Sulamerica")

            authenticatedAs(token, toTestPerson(person)) {
                put("/onboarding/portability/questions/current_health_insurance", body = request) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }
    }

    @Test
    fun `#put questions with questionType = current_health_insurance_hospitals should return response as expected`() {
        val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product)
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        coEvery { personService.get(person.id) } returns person.success()
        coEvery { productOrderService.findOrderWithProductAndProvidersByPersonId(person.id) } returns orderWithProduct.success()

        val answer = InsurancePortabilityRequestAnswer(answer = "haoc", questionType = InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE_HOSPITALS)
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest()

        coEvery { portabilityService.answer(person.id, answer) } returns portabilityRequest.success()

        mockkObject(PortabilityQuestionResponseBuilder) {
            val expectedResponse: PortabilityQuestionResponse = mockk()
            coEvery { PortabilityQuestionResponseBuilder.buildQuestionResponse(person, orderWithProduct, portabilityRequest) } returns expectedResponse.success()

            val request = AnswerQuestionRequest(value = "haoc")

            authenticatedAs(token, toTestPerson(person)) {
                put("/onboarding/portability/questions/current_health_insurance_hospitals", body = request) { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }
    }

    @Test
    fun `#get status should return response as expected`() {
        coEvery { personService.get(person.id) } returns person.success()

        val productOrder = TestModelFactory.buildProductOrder(personId = person.id, product = product)
        val orderWithProduct = OrderWithProductWithProviders(productOrder, productWithProviders)

        coEvery { productOrderService.findOrderWithProductAndProvidersByPersonId(person.id) } returns orderWithProduct.success()

        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest()
        coEvery { portabilityService.findByPerson(person.id) } returns portabilityRequest.success()

        val personOnboarding = TestModelFactory.buildPersonOnboarding(personId = person.id)
        coEvery { onboardingService.findByPerson(person.id) } returns personOnboarding.success()

        mockkObject(PortabilityStatusResponseBuilder) {
            val expectedResponse: PortabilityStatusResponse = mockk()
            coEvery { PortabilityStatusResponseBuilder.buildStatusResponse(person, orderWithProduct, portabilityRequest, personOnboarding) } returns expectedResponse

            authenticatedAs(token, toTestPerson(person)) {
                get("/onboarding/portability/status") { response ->
                    assertThat(response).isOKWithData(expectedResponse)
                }
            }
        }
    }
}
