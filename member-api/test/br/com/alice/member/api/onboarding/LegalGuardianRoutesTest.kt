package br.com.alice.member.api.onboarding

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.controllers.onboarding.v3.LegalGuardianController
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.onboarding.LegalGuardianInfoTempRequest
import br.com.alice.member.api.services.OnboardingMobileRouter
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.onboarding.client.LegalGuardianInfoTempService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.BeforeTest
import kotlin.test.Test

class LegalGuardianRoutesTest : RoutesTestHelper() {

    private val legalGuardianInfoTempService: LegalGuardianInfoTempService = mockk()
    private val personService: PersonService = mockk()
    private val onboardingService: OnboardingService = mockk()

    private val legalGuardianController = LegalGuardianController(
        legalGuardianInfoTempService,
        personService,
        onboardingService
    )

    private val token: String = RangeUUID.generate().toString()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { legalGuardianController }
    }

    @Test
    fun `#getLegalGuardian should return 401 Unauthorized when auth header is not present`() {
        internalAuthentication {
            post("/onboarding/legal_guardian") { ResponseAssert.assertThat(it).isUnauthorized() }
        }
    }

    @Test
    fun `#create should return 200 OK when it is a new person`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        val personOnboarding = TestModelFactory.buildPersonOnboarding(personId = person.id, currentPhase = OnboardingPhase.LEGAL_GUARDIAN_REGISTER.nextPhase())
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.changePhaseTo(person.id, OnboardingPhase.LEGAL_GUARDIAN_REGISTER.nextPhase()) } returns personOnboarding.success()


        authenticatedAs(token, toTestPerson(person)) {
            val legalGuardian = TestModelFactory.buildLegalGuardianInfoTemp(person.id)
            val request = LegalGuardianInfoTempRequest(
                firstName = legalGuardian.firstName,
                lastName = legalGuardian.lastName,
                nationalId = legalGuardian.nationalId,
                email = legalGuardian.email,
                address = legalGuardian.address,
                degreeOfKinship = legalGuardian.degreeOfKinship,
                identityDocumentIssuingBody = legalGuardian.identityDocumentIssuingBody,
                identityDocument = legalGuardian.identityDocument,
            )

            val navigation = NavigationResponse(
                mobileRoute = MobileRouting.PORTABILITY_QUESTIONS,
                link = Links.PORTABILITY_QUESTIONS_V2
            )

            coEvery {
                legalGuardianInfoTempService.create(match {
                    it.personId == legalGuardian.personId &&
                    it.firstName == legalGuardian.firstName &&
                    it.lastName == legalGuardian.lastName &&
                    it.nationalId == legalGuardian.nationalId &&
                    it.email == legalGuardian.email &&
                    it.address == legalGuardian.address &&
                    it.identityDocument == legalGuardian.identityDocument &&
                    it.identityDocumentIssuingBody == legalGuardian.identityDocumentIssuingBody &&
                    it.degreeOfKinship == legalGuardian.degreeOfKinship
                })
            } returns legalGuardian.success()

            post("/onboarding/legal_guardian", request) { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()

                val actualResponse: NavigationResponse = response.bodyAsJson()
                Assertions.assertThat(actualResponse).isEqualTo(navigation)
                coVerify(exactly = 2) { personService.get(person.id) }
                coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.LEGAL_GUARDIAN_REGISTER.nextPhase()) }

                coVerify(exactly = 1) {
                    legalGuardianInfoTempService.create(match {
                        it.personId == legalGuardian.personId &&
                        it.firstName == legalGuardian.firstName &&
                        it.lastName == legalGuardian.lastName &&
                        it.nationalId == legalGuardian.nationalId &&
                        it.email == legalGuardian.email &&
                        it.address == legalGuardian.address &&
                        it.identityDocument == legalGuardian.identityDocument &&
                        it.identityDocumentIssuingBody == legalGuardian.identityDocumentIssuingBody &&
                        it.degreeOfKinship == legalGuardian.degreeOfKinship
                    })
                }
            }
        }
    }

    @Test
    fun `#create should throw ChildLegalGuardianSameEmailException when the email is equal`() = runBlocking {
        val person = TestModelFactory.buildPerson()
        coEvery { personService.get(any()) } returns person.success()

        authenticatedAs(token, toTestPerson(person)) {
            val legalGuardian = TestModelFactory.buildLegalGuardianInfoTemp(person.id)
            val request = LegalGuardianInfoTempRequest(
                firstName = legalGuardian.firstName,
                lastName = legalGuardian.lastName,
                nationalId = legalGuardian.nationalId,
                email = person.email,
                address = legalGuardian.address,
                degreeOfKinship = legalGuardian.degreeOfKinship,
                identityDocumentIssuingBody = legalGuardian.identityDocumentIssuingBody,
                identityDocument = legalGuardian.identityDocument,
            )

            post("/onboarding/legal_guardian", request) { response ->
                ResponseAssert.assertThat(response).isBadRequestWithErrorCode("child_legal_guardian_invalid")
            }
        }
    }

    @Test
    fun `#nextOnboardingPhase should return new navigation when person exists`(){
        val person = TestModelFactory.buildPerson()
        val personOnboarding = TestModelFactory.buildPersonOnboarding(personId = person.id, currentPhase = OnboardingPhase.CHILD_VIDEOS_REQUEST.nextPhase())
        coEvery { personService.get(person.id) } returns person.success()
        coEvery { onboardingService.changePhaseTo(person.id, OnboardingPhase.CHILD_VIDEOS_REQUEST.nextPhase()) } returns personOnboarding.success()

        mockkObject(OnboardingMobileRouter) {
            val navigation = NavigationResponse(
                mobileRoute = MobileRouting.HEALTH_DECLARATION_APPOINTMENT
            )

            coEvery {
                OnboardingMobileRouter.getNavigation(
                    person,
                    OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT,
                    false
                )
            } returns navigation

            authenticatedAs(token, toTestPerson(person)) {
                get("/onboarding/legal_guardian/next_phase") { response ->

                    val actualResponse: NavigationResponse = response.bodyAsJson()
                    Assertions.assertThat(actualResponse).isEqualTo(navigation)
                    coVerify(exactly = 1) { personService.get(person.id) }
                    coVerify(exactly = 1) { onboardingService.changePhaseTo(person.id, OnboardingPhase.CHILD_VIDEOS_REQUEST.nextPhase()) }
                }
            }
        }
    }
}
