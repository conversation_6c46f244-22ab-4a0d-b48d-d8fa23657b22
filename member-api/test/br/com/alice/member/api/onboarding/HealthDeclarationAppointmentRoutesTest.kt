package br.com.alice.member.api.onboarding

import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.builders.HealthDeclarationAppointmentBuilder
import br.com.alice.member.api.controllers.onboarding.v1.HealthDeclarationAppointmentController
import br.com.alice.member.api.models.onboarding.BeneficiaryHealthDeclarationAppointmentResponse
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentCard
import br.com.alice.member.api.models.onboarding.HealthDeclarationAppointmentResponse
import br.com.alice.member.api.usecases.GetBeneficiaryUseCase
import br.com.alice.membership.client.onboarding.HealthDeclarationAppointmentScheduler
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.onboarding.client.LegalGuardianAssociationService
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.client.PersonCalendlyService
import br.com.alice.schedule.model.AppointmentScheduleWithStaff
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthDeclarationAppointmentRoutesTest : RoutesTestHelper() {

    private val person = TestModelFactory.buildPerson()
    private val personCalendly = TestModelFactory.buildPersonCalendly(person.id)
    private val token = person.id.toString()

    private val healthDeclarationAppointmentScheduler: HealthDeclarationAppointmentScheduler = mockk()
    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val onboardingService: OnboardingService = mockk()
    private val personService: PersonService = mockk()
    private val legalGuardianAssociationService: LegalGuardianAssociationService = mockk()
    private val personCalendlyService: PersonCalendlyService = mockk()
    private val getBeneficiaryUseCase: GetBeneficiaryUseCase = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { HealthDeclarationAppointmentController(
            healthDeclarationAppointmentScheduler,
            appointmentScheduleService,
            onboardingService,
            personService,
            personCalendlyService,
            legalGuardianAssociationService,
            getBeneficiaryUseCase,
        ) }
    }

    @Test
    fun `#getHealthDeclarationAppointments should return as expected given HEALTH_DECLARATION AppointmentSchedules`() = mockLocalDateTime { date ->
        val scheduleUrl = "https://calendar.alice.com.br/time-alice/fale-com-alice"

        coEvery { healthDeclarationAppointmentScheduler.getScheduleUrl(person.id) } returns scheduleUrl.success()

        val appointmentSchedules = listOf(
            AppointmentScheduleWithStaff(
                appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.HEALTH_DECLARATION),
                staff = TestModelFactory.buildStaff()
            ),
            AppointmentScheduleWithStaff(
                appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.HEALTH_DECLARATION),
                staff = TestModelFactory.buildStaff()
            ),
            AppointmentScheduleWithStaff(
                appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.IMMERSION),
                staff = TestModelFactory.buildStaff()
            )
        )

        val healthDeclarationAppointmentSchedules = appointmentSchedules.filter { it.appointmentSchedule.type == AppointmentScheduleType.HEALTH_DECLARATION }
        val currentPhase = OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT

        coEvery { onboardingService.getCurrentPhase(person.id) } returns currentPhase.success()
        coEvery {
            appointmentScheduleService.findWithStaffBy(
                AppointmentScheduleFilter(
                    personId = person.id,
                    status = listOf(AppointmentScheduleStatus.SCHEDULED),
                    endTimeGreater = date,
                    sortOrder = SortOrder.Ascending
                )
            )
        } returns appointmentSchedules.success()

        val response = HealthDeclarationAppointmentResponse(
            card = HealthDeclarationAppointmentCard(
                title = "Response"
            ),
            nextPhases = emptyList()
        )

        mockkObject(HealthDeclarationAppointmentBuilder)
        coEvery { HealthDeclarationAppointmentBuilder.buildHealthDeclarationAppointmentResponse(healthDeclarationAppointmentSchedules,
            currentPhase.nextPhases().toList(), scheduleUrl, personCalendly) } returns response

        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/health_declaration_appointment") { response ->
                assertThat(response).isEqualTo(response)
            }
        }
    }

    @Test
    fun `#getHealthDeclarationAppointments should return as expected given none HEALTH_DECLARATION AppointmentSchedules`()= mockLocalDateTime { date ->
        val scheduleUrl = "https://calendar.alice.com.br/time-alice/fale-com-alice"

        coEvery { healthDeclarationAppointmentScheduler.getScheduleUrl(person.id) } returns scheduleUrl.success()

        val appointmentSchedules = listOf(
            AppointmentScheduleWithStaff(
                appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.OTHER),
                staff = TestModelFactory.buildStaff()
            ),
            AppointmentScheduleWithStaff(
                appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.IMMERSION),
                staff = TestModelFactory.buildStaff()
            )
        )

        val currentPhase = OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT

        coEvery { onboardingService.getCurrentPhase(person.id) } returns currentPhase.success()
        coEvery {
            appointmentScheduleService.findWithStaffBy(
                AppointmentScheduleFilter(
                    personId = person.id,
                    status = listOf(AppointmentScheduleStatus.SCHEDULED),
                    endTimeGreater = date,
                    sortOrder = SortOrder.Ascending
                )
            )
        } returns appointmentSchedules.success()

        val response = HealthDeclarationAppointmentResponse(
            card = HealthDeclarationAppointmentCard(
                title = "Response"
            ),
            nextPhases = emptyList()
        )

        mockkObject(HealthDeclarationAppointmentBuilder)
        coEvery { HealthDeclarationAppointmentBuilder.buildHealthDeclarationAppointmentResponse(emptyList(),
            currentPhase.nextPhases().toList(), scheduleUrl, personCalendly) } returns response

        authenticatedAs(token, toTestPerson(person)) {
            get("/onboarding/health_declaration_appointment") { response ->
                assertThat(response).isEqualTo(response)
            }
        }
    }

    @Test
    fun `#getBeneficiaryHealthDeclarationAppointments should return as expected given HEALTH_DECLARATION AppointmentSchedules`() = runBlocking {
        val scheduleUrl = "https://calendar.alice.com.br/time-alice/fale-com-alice"

        val appointmentSchedules = listOf(
            AppointmentScheduleWithStaff(
                appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.HEALTH_DECLARATION),
                staff = TestModelFactory.buildStaff()
            ),
            AppointmentScheduleWithStaff(
                appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.HEALTH_DECLARATION),
                staff = TestModelFactory.buildStaff()
            ),
            AppointmentScheduleWithStaff(
                appointmentSchedule = TestModelFactory.buildAppointmentSchedule(type = AppointmentScheduleType.IMMERSION),
                staff = TestModelFactory.buildStaff()
            )
        )

        val healthDeclarationAppointmentSchedules = appointmentSchedules.filter { it.appointmentSchedule.type == AppointmentScheduleType.HEALTH_DECLARATION }
        mockkObject(HealthDeclarationAppointmentBuilder)
        val expectedResponse = BeneficiaryHealthDeclarationAppointmentResponse(scheduleUrl = scheduleUrl)

        coEvery { healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(any(), any()) } returns scheduleUrl.success()
        coEvery { personCalendlyService.getOrCreate(person.id) } returns personCalendly.success()
        coEvery {
            appointmentScheduleService.findWithStaffBy(
                AppointmentScheduleFilter(
                    personId = person.id,
                    status = listOf(AppointmentScheduleStatus.SCHEDULED),
                    endTimeGreater = LocalDateTime.MIN,
                    sortOrder = SortOrder.Ascending
                )
            )
        } returns appointmentSchedules.success()
        coEvery { HealthDeclarationAppointmentBuilder.buildBeneficiaryHealthDeclarationAppointmentResponse(any(), any(), any()) } returns expectedResponse
        coEvery { getBeneficiaryUseCase.getBeneficiaryWithDependentsByPersonId(person.id) } returns GetBeneficiaryUseCase.BeneficiaryInfo(
            person,
            TestModelFactory.buildBeneficiary(),
            TestModelFactory.buildCompany(),
            emptyList(),
            TestModelFactory.buildMember(),
            null,
            0,
            isMinor = false,
            isCurrentRespondent = true,
            isHighRisk = false,
        )


        authenticatedAs(token, toTestPerson(person)) {
            get("/beneficiaries/video_call") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerify(exactly = 1) { healthDeclarationAppointmentScheduler.getBeneficiaryScheduleUrl(person.id, false) }
        coVerify(exactly = 1) { appointmentScheduleService.findWithStaffBy(any()) }
        coVerify(exactly = 1) {
            HealthDeclarationAppointmentBuilder.buildBeneficiaryHealthDeclarationAppointmentResponse(
                healthDeclarationAppointmentSchedules,
                scheduleUrl,
                personCalendly
            )
        }
    }
}
