package br.com.alice.member.api.extensions

import br.com.alice.data.layer.models.Attachment
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_NAVIGATION_ICON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_DESCRIPTION
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_ICON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_IMAGE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.PRESCRIPTION_NAVIGATION_NAME
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.TEST_REQUEST_NAVIGATION_DESCRIPTION
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.TEST_REQUEST_NAVIGATION_ICON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.TEST_REQUEST_NAVIGATION_IMAGE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.TEST_REQUEST_NAVIGATION_NAME
import br.com.alice.member.api.models.HealthPlanItemAttachment
import br.com.alice.member.api.models.HealthPlanItemDrawer

fun List<Attachment>.toItemAttachment() = this.map { attachment -> attachment.toHealthPlanItemAttachment() }

fun List<Attachment>.toHealthPlanDrawer() = this.map { attachment -> attachment.toHealthPlanDrawer() }

fun Attachment.toHealthPlanItemAttachment() =
    ServiceConfig.url("/health_plan/attachments/${this.id}").let { link ->
        if (this.isPrescription()) {
            HealthPlanItemAttachment(
                name = PRESCRIPTION_NAVIGATION_NAME,
                description = PRESCRIPTION_NAVIGATION_DESCRIPTION,
                imageUrl = PRESCRIPTION_NAVIGATION_IMAGE,
                icon = PRESCRIPTION_NAVIGATION_ICON,
                link = link
            )
        } else if (this.isTestRequest()) {
            HealthPlanItemAttachment(
                name = TEST_REQUEST_NAVIGATION_NAME,
                description = TEST_REQUEST_NAVIGATION_DESCRIPTION,
                imageUrl = TEST_REQUEST_NAVIGATION_IMAGE,
                icon = TEST_REQUEST_NAVIGATION_ICON,
                link = link
            )
        } else {
            HealthPlanItemAttachment(
                name = this.fileName,
                link = link,
                icon = AUTHORIZATION_NAVIGATION_ICON
            )
        }
    }

fun Attachment.toHealthPlanDrawer() =
    if (this.isTestRequest()) {
        HealthPlanItemDrawer.testRequestDrawer(this)
    } else {
        HealthPlanItemDrawer.attachmentDrawer(this)
    }

private fun Attachment.isPrescription() = this.fileName.contains("Prescrição")

private fun Attachment.isTestRequest() =
    this.fileName.contains("Pedido_de_exame") || this.fileName.contains("Pedido de Exame")
