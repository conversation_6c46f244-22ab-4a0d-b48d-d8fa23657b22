package br.com.alice.member.api.extensions

import br.com.alice.data.layer.models.GlossAuthorizationInfoData
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaStatus

fun MvAuthorizedProcedure.cleanedStatus(totvsGuia: TotvsGuia): MvAuthorizedProcedureStatus =
    when (totvsGuia.status) {
        TotvsGuiaStatus.PENDING -> MvAuthorizedProcedureStatus.PENDING
        TotvsGuiaStatus.CANCELLED -> MvAuthorizedProcedureStatus.CANCELLED
        else -> when(this.status) {
            MvAuthorizedProcedureStatus.ACTIVE,
            MvAuthorizedProcedureStatus.PENDING -> MvAuthorizedProcedureStatus.PENDING

            MvAuthorizedProcedureStatus.AUTHORIZED,
            MvAuthorizedProcedureStatus.PRE_EXECUTED -> MvAuthorizedProcedureStatus.AUTHORIZED

            else -> this.status
        }
    }

fun MvAuthorizedProcedure.cleanedGloss(totvsGuia: TotvsGuia): GlossAuthorizationInfoData? =
    if ((totvsGuia.status == TotvsGuiaStatus.UNAUTHORIZED || totvsGuia.status == TotvsGuiaStatus.PARTIALLY_AUTHORIZED)) {
        this.gloss?.firstOrNull()
    } else null

