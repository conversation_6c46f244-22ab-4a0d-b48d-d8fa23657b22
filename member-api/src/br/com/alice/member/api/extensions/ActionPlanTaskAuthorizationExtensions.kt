package br.com.alice.member.api.extensions

import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_CONFIRMATION_MODAL_CANCEL_BUTTON_LABEL
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_CONFIRMATION_MODAL_CONFIRM_BUTTON_LABEL
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_CONFIRMATION_MODAL_DESCRIPTION
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_CONFIRMATION_MODAL_IMAGE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_CONFIRMATION_MODAL_TITLE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_NAVIGATION_DESCRIPTION
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_NAVIGATION_ICON
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_NAVIGATION_IMAGE
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationConstants.AUTHORIZATION_NAVIGATION_NAME
import br.com.alice.member.api.models.HealthPlanItemNavigation
import br.com.alice.member.api.models.HealthPlanItemNavigationTag
import br.com.alice.member.api.models.HealthPlanItemNavigationTagColor
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.procedureAuthorization.ActionPlanTaskAuthorization
import br.com.alice.member.api.models.v2.ActionNavigationConfirmationModal

fun ActionPlanTaskAuthorization.toHealthPlanItemNavigation() = HealthPlanItemNavigation(
    name = AUTHORIZATION_NAVIGATION_NAME,
    description = AUTHORIZATION_NAVIGATION_DESCRIPTION,
    imageUrl = AUTHORIZATION_NAVIGATION_IMAGE,
    icon = AUTHORIZATION_NAVIGATION_ICON,
    tag = HealthPlanItemNavigationTag(
        icon = this.totvsGuiaStatus.tagIcon(),
        text = this.totvsGuiaStatus.tagText(),
        color = this.totvsGuiaStatus.tagColor(),
    ),
    navigation = NavigationResponse(
        mobileRoute = if (this.totvsGuiaId != null) MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL else MobileRouting.PROCEDURE_AUTHORIZATION_LIST,
        properties = if (this.totvsGuiaId != null) mapOf("id" to this.totvsGuiaId) else null,
    ),
)

fun ActionPlanTaskAuthorization.toActionNavigationConfirmationModal() =
    if (this.totvsGuiaStatus.isPending()) ActionNavigationConfirmationModal(
        title = AUTHORIZATION_CONFIRMATION_MODAL_TITLE,
        imageUrl = AUTHORIZATION_CONFIRMATION_MODAL_IMAGE,
        description = AUTHORIZATION_CONFIRMATION_MODAL_DESCRIPTION,
        confirmButtonLabel = AUTHORIZATION_CONFIRMATION_MODAL_CONFIRM_BUTTON_LABEL,
        cancelButtonLabel = AUTHORIZATION_CONFIRMATION_MODAL_CANCEL_BUTTON_LABEL
    ) else null

private fun TotvsGuiaStatus.isPending() = this == TotvsGuiaStatus.PENDING

private fun TotvsGuiaStatus.tagIcon() = when (this) {
    TotvsGuiaStatus.AUTHORIZED -> "check_outlined"
    TotvsGuiaStatus.CANCELLED -> "alert_circle_outlined"
    TotvsGuiaStatus.PARTIALLY_AUTHORIZED -> "chat_sent"
    TotvsGuiaStatus.PENDING -> "clock"
    TotvsGuiaStatus.UNAUTHORIZED -> "block"
    TotvsGuiaStatus.UNKNOWN -> "unknown"
}

private fun TotvsGuiaStatus.tagText() = when (this) {
    TotvsGuiaStatus.AUTHORIZED -> "Autorizada"
    TotvsGuiaStatus.CANCELLED -> "Cancelada"
    TotvsGuiaStatus.PARTIALLY_AUTHORIZED -> "Parcialmente autorizada"
    TotvsGuiaStatus.PENDING -> "Análise em andamento"
    TotvsGuiaStatus.UNAUTHORIZED -> "Não autorizada"
    TotvsGuiaStatus.UNKNOWN -> "Desconhecido"
}

private fun TotvsGuiaStatus.tagColor() = when(this){
    TotvsGuiaStatus.AUTHORIZED -> HealthPlanItemNavigationTagColor.GREEN
    TotvsGuiaStatus.CANCELLED -> HealthPlanItemNavigationTagColor.GRAY
    TotvsGuiaStatus.PARTIALLY_AUTHORIZED -> HealthPlanItemNavigationTagColor.BLUE
    TotvsGuiaStatus.PENDING -> HealthPlanItemNavigationTagColor.YELLOW
    TotvsGuiaStatus.UNAUTHORIZED -> HealthPlanItemNavigationTagColor.RED
    TotvsGuiaStatus.UNKNOWN -> HealthPlanItemNavigationTagColor.YELLOW
}


