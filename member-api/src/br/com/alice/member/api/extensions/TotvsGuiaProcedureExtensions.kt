package br.com.alice.member.api.extensions

import br.com.alice.common.MvUtil
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaStatus

fun TotvsGuia.procedureLabel() = when (this.type) {
    MvUtil.TISS.PS -> "Número da guia do Pronto-Socorro"
    MvUtil.TISS.HOSPITALIZATION -> "Número da guia da Internação"
    MvUtil.TISS.EXTENSION -> "Prorrogação da Internação"
    MvUtil.TISS.EXAM -> "Número da guia do Exame"
    MvUtil.TISS.THERAPY -> "Número da guia da Terapia"
    MvUtil.TISS.APPOINTMENT -> "Número da guia da Consulta"
    MvUtil.TISS.RADIOTHERAPY -> "Número da guia da Radioterapia"
    MvUtil.TISS.CHEMOTHERAPY -> "Número da guia da Quimioterapia"
    MvUtil.TISS.UNKNOWN -> "Número da guia"
    else -> "Número da guia"
}

fun List<TotvsGuiaStatus>.mergedStatus(): TotvsGuiaStatus {
    if (this.isEmpty()) throw InvalidArgumentException(code = "invalid_list_of_status", message = "list must not be empty")

    val hasUnauthorized = this.contains(TotvsGuiaStatus.UNAUTHORIZED)

    val mergedStatus = this.fold(this.first()) { acc, current ->
        if (current.index() < acc.index()) current else acc
    }

    return if (hasUnauthorized && mergedStatus.index() == 2) TotvsGuiaStatus.PARTIALLY_AUTHORIZED else mergedStatus
}

private fun TotvsGuiaStatus.index() = when (this) {
    TotvsGuiaStatus.PENDING -> 0
    TotvsGuiaStatus.PARTIALLY_AUTHORIZED -> 1
    TotvsGuiaStatus.AUTHORIZED -> 2
    TotvsGuiaStatus.UNAUTHORIZED -> 3
    TotvsGuiaStatus.CANCELLED -> 4
    TotvsGuiaStatus.UNKNOWN -> 5
}
