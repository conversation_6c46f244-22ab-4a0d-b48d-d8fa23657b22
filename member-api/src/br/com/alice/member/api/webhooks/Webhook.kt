package br.com.alice.member.api.webhooks

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.withRootServicePolicy
import br.com.alice.data.layer.MEMBERSHIP_WEBHOOKS_ROOT_SERVICE_NAME

abstract class Webhook : Controller() {
    suspend fun unauthenticated(func: suspend () -> Response): Response =
        withRootServicePolicy(MEMBERSHIP_WEBHOOKS_ROOT_SERVICE_NAME) {
            func.invoke()
        }
}
