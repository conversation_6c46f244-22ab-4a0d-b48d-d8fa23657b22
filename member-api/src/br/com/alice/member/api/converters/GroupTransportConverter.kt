package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.healthplan.models.HealthPlanTaskGroupTransport
import br.com.alice.member.api.models.GroupTransport

object GroupTransportConverter : Converter<HealthPlanTaskGroupTransport, GroupTransport>(
    HealthPlanTaskGroupTransport::class, GroupTransport::class
) {
    fun convert(source: HealthPlanTaskGroupTransport): GroupTransport =
        super.convert(
            source,
            map(GroupTransport::id) from source.id.toString()
        )
}
