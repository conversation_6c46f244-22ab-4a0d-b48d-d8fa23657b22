package br.com.alice.member.api.converters

import br.com.alice.healthlogic.models.EnrichedClinicalOutcomeRecord
import br.com.alice.member.api.factory.ScoreMagentaOverviewFactory
import br.com.alice.member.api.models.ScoreMagentaOverviewResponse
import br.com.alice.member.api.models.ScoreTypes

object ScoreMagentaOverviewConverter {

    private const val DEFAULT_MAX_RESULT = 1000
    private val pillarSections = listOf(
        ScoreTypes.FOOD,
        ScoreTypes.MENTAL,
        ScoreTypes.ALCOHOL_AND_SMOKE,
        ScoreTypes.QUALITY_OF_LIFE,
        ScoreTypes.SLEEP,
        ScoreTypes.PHYSICAL_ACTIVITY,
    )

    fun convert(outcomes: List<EnrichedClinicalOutcomeRecord>) = ScoreMagentaOverviewResponse(
        result = outcomes.getScoreValue(ScoreTypes.GENERAL),
        maxResult = DEFAULT_MAX_RESULT,
        labels = ScoreMagentaOverviewFactory.buildLabels(),
        pillars = buildPillars(outcomes)
    )

    private fun buildPillars(outcomes: List<EnrichedClinicalOutcomeRecord>) =
        pillarSections.map { type ->
            ScoreMagentaOverviewFactory.buildSection(
                type = type,
                value = outcomes.getScoreValue(type)
            )
        }

    private fun List<EnrichedClinicalOutcomeRecord>.findByType(type: ScoreTypes) =
        HealthScoreTypeConverter.convert(type).let { typeKey ->
            this.firstOrNull { it.outcomeConf.key == typeKey }
        }

    private fun List<EnrichedClinicalOutcomeRecord>.getScoreValue(type: ScoreTypes) =
        this.findByType(type)?.outcome?.toInt() ?: 0
}
