package br.com.alice.member.api.converters

import br.com.alice.data.layer.models.CoPaymentChargeType
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.member.api.models.InfoValue
import br.com.alice.member.api.models.InfoWrapper
import br.com.alice.member.api.models.MemberOnboardingProductDetailsResponse
import br.com.alice.member.api.models.MemberProductDetails
import br.com.alice.member.api.models.ProductDetailsPerk
import br.com.alice.product.client.PersonCoPaymentCostInfo
import java.math.BigDecimal
import java.text.NumberFormat
import java.util.Locale

object MemberOnboardingProductConverter {

    fun convert(person: Person, details: MemberProductDetails) =
        MemberOnboardingProductDetailsResponse(
            title = buildTitle(person, details),
            perks = buildPerks(details),
            copay = buildCopay(details),
            refund = if (details.hasRefundCost) buildRefundCost(details) else buildRefund(details),
        )

    private fun buildTitle(
        person: Person,
        details: MemberProductDetails
    ) = "${person.firstName}, seu plano atual é ${details.product.displayName}"

    private fun buildPerks(details: MemberProductDetails) = listOf(
        ProductDetailsPerk(
            icon = "bed",
            title = "Acomodação",
            description = details.product.accommodation?.title.orEmpty(),
        ),
        ProductDetailsPerk(
            icon = "airplane",
            title = "Cobertura",
            description = details.product.takeIf { it.hasNationalCoverage }?.let { "Nacional" }.orEmpty(),
        ),
        ProductDetailsPerk(
            icon = "hospital",
            title = "Hospitais",
            description = details.providers
                .filter { ProviderType.hospitalTypes().contains(it.type) }
                .minByOrNull { it.flagship }
                ?.let { "${it.name} e outros" }
                .orEmpty(),
        ),
        ProductDetailsPerk(
            icon = "lab",
            title = "Laboratórios",
            description = details.providers
                .filter { it.type == ProviderType.LABORATORY }
                .minByOrNull { it.flagship }
                ?.let { "${it.name} e outros" }
                .orEmpty(),
        ),
        ProductDetailsPerk(
            icon = "doctor",
            title = "Médico de Família",
            description = "Isento de coparticipação",
        ),
        ProductDetailsPerk(
            icon = "heart_outlined",
            title = "Alice Agora - Saúde 24h",
            description = "Isento de coparticipação",
        ),
    )

    private fun buildRefund(details: MemberProductDetails) = InfoWrapper(
        icon = "refund_money",
        title = "Reembolso",
        subtitle = if (details.hasRefund) "Confira os valores na tabela" else "Seu plano não possui",
        description = details.takeIf { it.hasRefund }?.let {
            "Seu plano tem reembolso para consultas, terapias e honorários médicos (cirurgias, procedimentos ambulatoriais e visita hospitalar)."
        },
        values = details.refund.map {
            InfoValue(
                title = it.event,
                value = it.price?.value?.toMoneyString() ?: "sob análise*"
            )
        }
    )

    private fun buildRefundCost(details: MemberProductDetails) = InfoWrapper(
        icon = "refund_money",
        title = "Reembolso",
        subtitle = if (details.hasRefundCost) "Confira os valores na tabela" else "Seu plano não possui",
        description = details.takeIf { it.hasRefundCost }?.let {
            "Seu plano tem reembolso para consultas, terapias e honorários médicos (cirurgias, procedimentos ambulatoriais e visita hospitalar)."
        },
        values = details.refundCost?.prices?.map {
            InfoValue(
                title = it.event,
                value = it.value?.toMoneyString() ?: "sob análise*"
            )
        } ?: emptyList()
    )

    private fun buildCopay(details: MemberProductDetails) = InfoWrapper(
        icon = "money",
        title = "Coparticipação",
        subtitle = if (details.hasCopay) "Confira os valores na tabela" else "Seu plano não possui",
        description = details.takeIf { it.hasCopay }?.let {
            "Seu plano possui coparticipação, com **valor fixo para consultas e internação e de 30% do custo dos demais serviços.**"
        },
        values = details.coPayment.map {
            InfoValue(
                title = it.toEventTitle(),
                value = it.price.value.toMoneyString()
            )
        }
    )

    private fun PersonCoPaymentCostInfo.toEventTitle(): String {
        val chargeType = when (this.chargeType) {
            CoPaymentChargeType.FIXED_VALUE -> "valor fixo"
            CoPaymentChargeType.BILLING_CEILING -> "teto"
        }

        return "${this.event} (${chargeType})"
    }

    private fun BigDecimal.toMoneyString(): String {
        val formatter = NumberFormat.getCurrencyInstance(Locale.forLanguageTag("pt-BR"))
        formatter.maximumFractionDigits = 0

        return formatter.format(this.toDouble())
    }
}
