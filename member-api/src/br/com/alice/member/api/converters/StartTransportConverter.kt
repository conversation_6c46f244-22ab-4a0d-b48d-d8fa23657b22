package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.map
import br.com.alice.data.layer.models.Start
import br.com.alice.data.layer.models.StartType
import br.com.alice.member.api.models.StartTransport
import br.com.alice.member.api.models.StartTypeTransport

object StartTransportConverter : Converter<Start, StartTransport>(
    Start::class, StartTransport::class
) {
    fun convert(source: Start, createdAt: String?): StartTransport {
        return super.convert(
            source,
            map(StartTransport::description) from friendlyStart(source, createdAt),
            map(StartTransport::type) from if (source.date != null) StartTypeTransport.TIMESTAMP else StartTypeTransport.CONDITIONAL,
            map(StartTransport::date) from source.date?.toLocalDate()?.toString()
        )
    }

    fun friendlyStart(
        start: Start?,
        createdAt: String?
    ): String? {
        if (start == null) return null

        return when {
            (start.date != null && start.type == StartType.IMMEDIATE) -> "começar em ${start.date!!.toBrazilianDateFormat()}"
            (createdAt != null && start.type == StartType.IMMEDIATE) -> "começar em ${createdAt.toLocalDateTime().toBrazilianDateFormat()}"
            else -> start.type?.description
        }
    }
}
