package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.googlemaps.services.Address
import br.com.alice.common.map
import br.com.alice.member.api.models.address.AddressMapsTransport
import br.com.alice.data.layer.models.Address as PersonAddress

object AddressTransportConverter : Converter<Address, AddressMapsTransport>(
    Address::class, AddressMapsTransport::class
) {
    fun convert(
        source: Address,
        nickName: String,
        personAddress: PersonAddress? = null
    ): AddressMapsTransport {
        return super.convert(
            source,
            map(AddressMapsTransport::nickName) from nickName,
            map(AddressMapsTransport::placeId) from source.id,
            map(AddressMapsTransport::complement) from (source.complement ?: personAddress?.complement),
            map(AddressMapsTransport::postalCode) from (source.postalCode.ifEmpty { personAddress?.postalCode.orEmpty() }),
            map(AddressMapsTransport::street) from (source.street.ifEmpty { personAddress?.street.orEmpty() }),
            map(AddressMapsTransport::number) from (source.number.ifEmpty { personAddress?.number.orEmpty() })
        )
    }
}
