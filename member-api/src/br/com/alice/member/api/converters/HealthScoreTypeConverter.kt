package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.member.api.models.ScoreTypes

object HealthScoreTypeConverter : Converter<ScoreTypes, String>(
    ScoreTypes::class, String::class
) {
    fun convert(source: ScoreTypes): String {
        return when (source) {
            ScoreTypes.GENERAL -> "SCORE_MAGENTA"
            ScoreTypes.QUALITY_OF_LIFE -> "EUROQOL_SCORE_MAGENTA"
            ScoreTypes.FOOD -> "FOOD_SCORE_MAGENTA"
            ScoreTypes.ALCOHOL_AND_SMOKE -> "HABITS_SCORE_MAGENTA"
            ScoreTypes.PHYSICAL_ACTIVITY -> "IPAQ_SCORE_MAGENTA"
            ScoreTypes.MENTAL -> "MENTAL_SCORE_MAGENTA"
            ScoreTypes.SLEEP -> "MSQ_SCORE_MAGENTA"
        }
    }
}
