package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.convertTo
import br.com.alice.common.map
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.member.api.models.onboarding.v2.AccommodationResponse
import br.com.alice.member.api.models.onboarding.v2.ContentOptionsBundle
import br.com.alice.member.api.models.onboarding.v2.ProviderResponse
import br.com.alice.membership.model.onboarding.ProductOptionBundle

object ContentOptionsBundleConverter : Converter<ProductOptionBundle, ContentOptionsBundle>(
    ProductOptionBundle::class, ContentOptionsBundle::class
) {
    fun convert(source: ProductOptionBundle, types: List<ProviderType>): ContentOptionsBundle {
        return super.convert(
            source,
            map(ContentOptionsBundle::ids) from source.bundleIds.map { it.toString() },
            map(ContentOptionsBundle::providers) from source.providers.filter { types.contains(it.type) }
                .sortedBy { types.indexOf(it.type) }.map { it.convertTo(ProviderResponse::class) },
            map(ContentOptionsBundle::accommodation) from source.accommodation?.let {
                AccommodationResponse(
                    it,
                    it.title
                )
            }
        )
    }
}
