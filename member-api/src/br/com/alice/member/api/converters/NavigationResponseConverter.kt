package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.convertTo
import br.com.alice.common.featureaccess.FeatureNavigation
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse

object NavigationResponseConverter : Converter<FeatureNavigation, NavigationResponse>(
    FeatureNavigation::class, NavigationResponse::class
) {
    fun convert(source: FeatureNavigation): NavigationResponse {
        return NavigationResponse(
            mobileRoute = MobileRouting.values().first { it.toString() == source.routing.toString() },
            link = source.link?.convertTo(Link::class),
            properties = source.properties,
            navigation = if (source.navigation != null) convert(source.navigation!!) else null,
        )
    }
}

