package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.normalizeWhiteSpaces
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.featureaccess.features.AppointmentScheduleWithStaff
import br.com.alice.common.logging.logger
import br.com.alice.common.map
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleCheckInStatus
import br.com.alice.data.layer.models.PersonCalendly
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.models.AppointmentScheduleResponse
import br.com.alice.member.api.models.HealthProfessionalResponse
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class AppointmentScheduleWithStaffConverter(
    private val schedulingUrlBuilder: SchedulingUrlBuilder,
) :
    Converter<AppointmentScheduleWithStaff, AppointmentScheduleResponse>(
        AppointmentScheduleWithStaff::class, AppointmentScheduleResponse::class
    ) {

    suspend fun convert(
        source: AppointmentScheduleWithStaff,
        isPoolScheduling: Boolean = false,
        personCalendly: PersonCalendly?,
        checkInStatus: AppointmentScheduleCheckInStatus? = null,
        isAdvancedAccess: Boolean = false,
    ): AppointmentScheduleResponse = coroutineScope {
        val listOfHealthProfessionals = source.staff
            ?.let(HealthProfessionalConverter::convert)
            ?.let { listOf(it) } ?: emptyList()

        val appointmentSchedule = source.appointmentSchedule
        val eventId = appointmentSchedule.eventId ?: appointmentSchedule.eventUuid.toString()

        val params = mapOf(
            "health_plan_task_id" to appointmentSchedule.healthPlanTaskId.toString().toSha256(),
            "staff_id" to appointmentSchedule.staffId.toString(),
            "event_name" to appointmentSchedule.eventName,
            "appointment_schedule_id" to appointmentSchedule.id.toString(),
        ).let {
            if (appointmentSchedule.appointmentScheduleEventTypeId != null) {
                it.plus(
                    "appointment_schedule_event_type_id" to appointmentSchedule.appointmentScheduleEventTypeId.toString()
                )
            } else it
        }.let {
            if (appointmentSchedule.providerUnitId != null) {
                it.plus(
                    "provider_unit_id" to appointmentSchedule.providerUnitId.toString()
                )
            } else it
        }

        logger.info("AppointmentSchedulesConverter::convert", "params" to params.entries)

        val cancelUrlDef = async {
            schedulingUrlBuilder.buildCancellationUrl(
                eventId = eventId,
                params = params,
                shouldReleaseNewScheduleFlow = appointmentSchedule.scheduledByInternalScheduler,
                personCalendly,
            )
        }
        val rescheduleUrlDef = async {
            schedulingUrlBuilder.buildRescheduleUrl(
                eventId = eventId,
                params = params,
                shouldReleaseNewScheduleFlow = appointmentSchedule.scheduledByInternalScheduler,
                isPoolScheduling = isPoolScheduling,
                personCalendly,
                isAdvancedAccess,
            )
        }

        val locationUrlDef = async { SchedulingUrlBuilder.buildLocationUrl(appointmentSchedule.location) }

        return@coroutineScope super.convert(
            source,
            map(AppointmentScheduleResponse::id) from appointmentSchedule.id.toString(),
            map(AppointmentScheduleResponse::name) from buildEventName(appointmentSchedule, listOfHealthProfessionals),
            map(AppointmentScheduleResponse::healthProfessionals) from listOfHealthProfessionals,
            map(AppointmentScheduleResponse::startTime) from transformStartTime(appointmentSchedule),
            map(AppointmentScheduleResponse::endTime) from appointmentSchedule.endTime?.toString(),
            map(AppointmentScheduleResponse::cancelUrl) from cancelUrlDef.await(),
            map(AppointmentScheduleResponse::rescheduleUrl) from rescheduleUrlDef.await(),
            map(AppointmentScheduleResponse::location) from (appointmentSchedule.location ?: ""),
            map(AppointmentScheduleResponse::locationUrl) from locationUrlDef.await(),
            map(AppointmentScheduleResponse::checkInStatus) from checkInStatus
        )
    }

    private fun buildEventName(
        appointmentSchedule: AppointmentSchedule,
        healthProfessionals: List<HealthProfessionalResponse>
    ) =
        (appointmentSchedule.eventName + healthProfessionals.firstOrNull()?.let { " com ${it.firstName}" }
            .orEmpty()).normalizeWhiteSpaces()

    private fun transformStartTime(appointmentSchedule: AppointmentSchedule) = appointmentSchedule
        .startTime
        .toString()

}

object AppointmentScheduleConverter : Converter<AppointmentSchedule, AppointmentScheduleResponse>(
    AppointmentSchedule::class, AppointmentScheduleResponse::class
) {
    fun convert(source: AppointmentSchedule): AppointmentScheduleResponse {
        val locationUrl = SchedulingUrlBuilder.buildLocationUrl(source.location)

        return super.convert(
            source,
            map(AppointmentScheduleResponse::id) from source.id.toString(),
            map(AppointmentScheduleResponse::name) from source.eventName,
            map(AppointmentScheduleResponse::startTime) from source.startTime.toSaoPauloTimeZone().toString(),
            map(AppointmentScheduleResponse::endTime) from source.endTime?.toSaoPauloTimeZone().toString(),
            map(AppointmentScheduleResponse::location) from (source.location ?: ""),
            map(AppointmentScheduleResponse::locationUrl) from locationUrl
        )
    }
}
