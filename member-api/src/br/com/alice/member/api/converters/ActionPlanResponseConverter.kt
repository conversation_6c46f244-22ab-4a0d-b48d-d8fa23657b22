package br.com.alice.member.api.converters

import br.com.alice.action.plan.model.ActionPlanTaskTransport
import br.com.alice.action.plan.model.ActionPlanTasksTransport
import br.com.alice.action.plan.model.PrescriptionTransport
import br.com.alice.action.plan.model.ReferralTransport
import br.com.alice.action.plan.model.TestRequestTransport
import br.com.alice.app.content.constants.ACCREDITED_NETWORK_LABORATORIES_PARAMS
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.document.html.removeHtmlAndPreserveNewLines
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberOnboardingCheckpoint
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonCalendly
import br.com.alice.data.layer.models.SpecialistType
import br.com.alice.data.layer.models.TestPreparation
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.models.HealthPlanTransport
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationBuilder
import br.com.alice.member.api.models.HealthHabitResponse
import br.com.alice.member.api.models.HealthPlanItemCalendar
import br.com.alice.member.api.models.HealthPlanItemResponse
import br.com.alice.member.api.models.HealthPlanItemResponseStatus
import br.com.alice.member.api.models.HealthPlanResponse
import br.com.alice.member.api.models.HealthPlanSectionName
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.PrescriptionResponse
import br.com.alice.member.api.models.PresentationCardResponse
import br.com.alice.member.api.models.ReferralsResponse
import br.com.alice.member.api.models.TestCode
import br.com.alice.member.api.models.TestPreparationTransport
import br.com.alice.member.api.models.TestRequestGroupResponse
import br.com.alice.member.api.models.TestRequestResponse
import br.com.alice.member.onboarding.client.MemberOnboardingCheckpointService
import java.util.UUID

class ActionPlanResponseConverter(
    private val schedulingUrlBuilder: SchedulingUrlBuilder,
    private val memberOnboardingCheckpointService: MemberOnboardingCheckpointService,
    private val actionPlanPresentationBuilder: ActionPlanPresentationBuilder
) {

    suspend fun convert(
        source: HealthPlanTransport,
        tasks: ActionPlanTasksTransport,
        person: Person,
        scheduleOptions: List<AppointmentScheduleOption>,
        testCodes: List<TestCode>,
        preparations: Map<String, TestPreparation> = emptyMap(),
        personCalendly: PersonCalendly,
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType> = emptyList(),
        shouldCheckOnboardingCheckpoint: Boolean = false,
        semanticVersion: SemanticVersion? = null
    ): HealthPlanResponse {
        val healthHabits = convertInHealthHabitResponses(tasks)

        val prescriptions = convertInPrescriptionResponses(tasks.prescription)

        val testRequests = convertInTestRequestResponse(
            tasks.testRequest,
            scheduleOptions,
            person,
            personCalendly,
            testCodes,
            preparations,
            semanticVersion
        )

        val referrals = convertInReferralsResponses(
            tasks.referral,
            scheduleOptions,
            person,
            personCalendly,
            appointmentScheduleEventTypes,
            semanticVersion
        )

        val allItems = getAllItemsFromHealthHabits(
            healthHabits,
            prescriptions,
            testRequests,
            referrals,
        )

        val cardInfo = buildCardInfo(
            allItems.size,
            person,
            shouldCheckOnboardingCheckpoint
        )

        return HealthPlanResponse(
            title = source.healthGoal ?: "Seu plano de ação",
            description = source.description.removeHtmlAndPreserveNewLines(),
            items = allItems,
            healthcareTeam = null,
            createdAt = source.createdAt,
            cardInfo = cardInfo
        )
    }

    private suspend fun convertInReferralsResponses(
        tasks: List<ReferralTransport>?,
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        personCalendly: PersonCalendly,
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType> = emptyList(),
        appVersion: SemanticVersion?
    ) = tasks?.map {
        val calendar = buildCalendar(
            scheduleOptions,
            person,
            it,
            personCalendly,
            appointmentScheduleEventTypes,
        )

        val presentationFields = actionPlanPresentationBuilder.getPresentationFields(it, calendar, appVersion)

        logger.info(
            "ActionPlanResponseConverter: referral calendar",
            "calendar" to calendar?.calendarUrl?.split("&token=")?.first(),
            "referral_id" to it.id,
            "presentation_fields" to presentationFields
        )

        ReferralsResponse(it, calendar, presentationFields)
    }.orEmpty()

    private suspend fun convertInTestRequestResponse(
        tasks: List<TestRequestTransport>?,
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        personCalendly: PersonCalendly,
        testCodes: List<TestCode>,
        preparations: Map<String, TestPreparation>,
        appVersion: SemanticVersion?
    ): List<TestRequestGroupResponse> {
        val testRequestsByGroup = getTestRequestsByGroup(
            tasks,
            scheduleOptions,
            person,
            personCalendly,
            testCodes,
            appVersion
        )

        return testRequestsByGroup.flatMap { testRequestGroup ->
            val testRequestsByCalendar = testRequestGroup.value.groupBy { it.calendar?.examTitle }

            testRequestsByCalendar.keys.mapIndexed { index, it ->
                val testRequests = testRequestsByCalendar[it].orEmpty()
                val group = testRequestGroup.key!!
                val examIndex = getExamIndex(testRequestsByCalendar, index)
                val filteredTestCodes = getTestCodes(tasks, testRequests)
                val testPreparations = buildTestPreparationTransportFromPreparations(
                    preparations,
                    filteredTestCodes
                )

                TestRequestGroupResponse(
                    group,
                    examIndex,
                    testRequests,
                    testPreparations,
                )
            }
        }
    }

    private fun convertInPrescriptionResponses(tasks: List<PrescriptionTransport>?) =
        tasks?.map {
            val presentationFields = actionPlanPresentationBuilder.getPresentationFields(it)

            PrescriptionResponse(it, presentationFields)
        }.orEmpty()

    private fun convertInHealthHabitResponses(tasks: ActionPlanTasksTransport) =
        HealthPlanSectionName.healthHabits()
            .flatMap { section ->
                val items = section.get(tasks) ?: emptyList()
                items.map {
                    val presentationFields = actionPlanPresentationBuilder.getPresentationFields(it)
                    HealthHabitResponse(section, it, presentationFields)
                }
            }


    private fun getAllItemsFromHealthHabits(
        healthHabits: List<HealthHabitResponse>,
        prescriptions: List<PrescriptionResponse>,
        testRequests: List<HealthPlanItemResponse>,
        referrals: List<ReferralsResponse>,
    ) = healthHabits
        .asSequence()
        .plus(prescriptions)
        .plus(testRequests)
        .plus(referrals)
        .filter { item -> filterStatus(item) }
        .sortedByDescending(HealthPlanItemResponse::createdAt)
        .toList()

    private fun filterStatus(
        item: HealthPlanItemResponse
    ) =
        item.status == HealthPlanItemResponseStatus.TO_DO ||
                item.status == HealthPlanItemResponseStatus.SCHEDULED ||
                item.status == HealthPlanItemResponseStatus.OVERDUE ||
                item.status == HealthPlanItemResponseStatus.EXPIRED ||
                item.status == HealthPlanItemResponseStatus.DONE

    private fun getExamIndex(
        testRequestsByCalendar: Map<String?, List<TestRequestResponse>>,
        index: Int
    ) = if (testRequestsByCalendar.size > 1) index.inc().toString() else null

    private fun buildTestPreparationTransportFromPreparations(
        preparations: Map<String, TestPreparation>,
        testCodes: List<String>
    ) = preparations
        .filter { it.key in testCodes }
        .map {
            TestPreparationTransport(
                it.value.title,
                it.value.instructions,
            )
        }

    private fun getTestCodes(
        tasks: List<TestRequestTransport>?,
        testRequests: List<TestRequestResponse>
    ) = tasks?.filter { testRequest ->
        testRequest.id in testRequests.map { it.healthPlanTaskId }
    }?.mapNotNull { it.code } ?: emptyList()

    private suspend fun getTestRequestsByGroup(
        tasks: List<TestRequestTransport>?,
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        personCalendly: PersonCalendly,
        testCodes: List<TestCode>,
        appVersion: SemanticVersion?
    ) = tasks?.map {
        val calendar = buildCalendar(
            scheduleOptions,
            person,
            it,
            personCalendly,
            testCodes,
        )

        val presentationFields = actionPlanPresentationBuilder.getPresentationFields(it, calendar, appVersion)

        TestRequestResponse(it, calendar, presentationFields)
    }
        .orEmpty()
        .filter { it.status != null }
        .groupBy { it.group }
        .filter { it.key != null && it.value.isNotEmpty() }

    private suspend fun buildCardInfo(
        itemsCount: Int,
        person: Person,
        shouldCheckOnboardingCheckpoint: Boolean
    ): PresentationCardResponse? {
        if (shouldCheckOnboardingCheckpoint && itemsCount == 0) {
            val checkpoint =
                memberOnboardingCheckpointService.findByPersonId(person.id).getOrNullIfNotFound() ?: return null

            if (checkpoint.hasEndedOnboarding()) return when (checkpoint.riskDuringStratification) {
                MemberOnboardingCheckpoint.MemberOnboardingCheckpointStratificationRisk.LOW ->
                    buildCardLowRisk()

                MemberOnboardingCheckpoint.MemberOnboardingCheckpointStratificationRisk.HIGH ->
                    buildCardHighRisk()

                else -> null
            }
        }
        return null
    }

    private fun buildCardLowRisk() = PresentationCardResponse(
        title = "Seu primeiro plano de ação",
        description = "Nossa equipe está analisando a fundo suas respostas do formulário de imersão e montando seu Plano de Ação inicial."
    )

    private fun buildCardHighRisk() = PresentationCardResponse(
        title = "Seu primeiro plano de ação",
        description = "Seu primeiro Plano de Ação será construído após você concluir a imersão já agendada."
    )

    private suspend fun buildCalendar(
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        request: TestRequestTransport,
        personCalendly: PersonCalendly,
        testCodes: List<TestCode>,
    ): HealthPlanItemCalendar {
        logger.info(
            "ActionPlanResponseConverter - Request Code",
            "request_code" to request.code,
            "appointment_schedule_options_titles" to scheduleOptions.map { it.title },
            "request" to request.id
        )
        request.code?.let { code ->
            testCodes
                .filter { it.codes.binarySearch(code) >= 0 }
                .firstNotNullOfOrNull { scheduleOptions.find { option -> option.title.startsWith(it.title) } }
                ?.let {
                    return getCalendarAppointmentScheduleOption(
                        person,
                        it,
                        it.title,
                        personCalendly,
                        buildExtrasFromTaskId(request),
                    )
                }
        }

        return HealthPlanItemCalendar(
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.ACCREDITED_NETWORK,
                properties = ACCREDITED_NETWORK_LABORATORIES_PARAMS
            )
        )
    }

    private suspend fun buildCalendar(
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        referralTransport: ReferralTransport,
        personCalendly: PersonCalendly,
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType> = emptyList(),
    ): HealthPlanItemCalendar? =
        referralTransport.isDone().let { isDone ->
            if (isDone) return null
            else {
                val appointmentScheduleEventType = getAppointmentScheduleEventTypeForTask(
                    appointmentScheduleEventTypes,
                    referralTransport.specialty?.id,
                    referralTransport.subSpecialty?.id
                )

                val hasAppointmentScheduleEventType = appointmentScheduleEventType != null

                val extras = buildExtrasFromTaskId(referralTransport)

                logger.info(
                    "ActionPlanResponseConverter::buildCalendar",
                    "person_id" to referralTransport.personId,
                    "has_appointment_schedule_event_type" to hasAppointmentScheduleEventType,
                    "appointment_schedule_event_type_id" to appointmentScheduleEventType?.id
                )

                when {
                    hasAppointmentScheduleEventType &&
                            isEligibleForProfessionalPool(referralTransport.suggestedSpecialist?.id) -> {
                        getCalendarForPoolScheduling(
                            person,
                            extras,
                            appointmentScheduleEventType!!,
                            scheduleOptions.find { it.appointmentScheduleEventTypeId == appointmentScheduleEventType.id },
                            personCalendly
                        )
                    }

                    hasSuggestedSpecialistAndItIsStaff(referralTransport) -> getCalendarByStaff(
                        scheduleOptions,
                        person,
                        referralTransport.suggestedSpecialist!!.id,
                        personCalendly,
                        extras,
                        referralTransport.subSpecialty?.id,
                    )

                    else -> null
                }
            }
        }


    private fun buildExtrasFromTaskId(taskTransport: ActionPlanTaskTransport) =
        mapOf("health_plan_task_id" to taskTransport.id.toString().toSha256())

    private fun hasSuggestedSpecialistAndItIsStaff(referralTransport: ReferralTransport) =
        referralTransport.suggestedSpecialist?.type == SpecialistType.STAFF && referralTransport.suggestedSpecialist?.id != null

    private suspend fun getCalendarByStaff(
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        id: UUID,
        personCalendly: PersonCalendly,
        extras: Map<String, String>,
        subSpecialtyId: UUID? = null,
    ): HealthPlanItemCalendar? {
        val scheduleOptionsByStaff = scheduleOptions.filter { id == it.staffId }
        logger.info(
            "ActionPlanResponseConverter::getCalendarByStaff",
            "schedule_options_by_staff" to scheduleOptionsByStaff.size
        )

        return if (scheduleOptionsByStaff.isEmpty()) null
        else {
            val option = if (subSpecialtyId != null) {
                scheduleOptionsByStaff.find { scheduleOption ->
                    scheduleOption.subSpecialtyIds?.contains(subSpecialtyId) == true
                }
            } else scheduleOptionsByStaff.firstOrNull()

            logger.info(
                "ActionPlanResponseConverter::getCalendarByStaff",
                "option_id" to option?.id,
                "option_specialist_id" to option?.specialistId,
                "option_calendar_url" to option?.calendarUrl,
            )

            option?.let {
                getCalendarAppointmentScheduleOption(
                    person,
                    option,
                    option.title,
                    personCalendly,
                    extras,
                )
            }
        }
    }

    private suspend fun getCalendarAppointmentScheduleOption(
        person: Person,
        appointmentScheduleOption: AppointmentScheduleOption,
        examTitle: String,
        personCalendly: PersonCalendly,
        extras: Map<String, String> = mapOf(),
    ) =
        HealthPlanItemCalendar(
            calendarUrl = schedulingUrlBuilder.buildScheduleUrlWithAppointmentScheduleOption(
                person,
                appointmentScheduleOption,
                personCalendly,
                extras = extras,
            ),
            examTitle = examTitle,
            scheduleOptions = schedulingUrlBuilder.buildScheduleOptions(
                appointmentScheduleOption,
                person,
                personCalendly,
                extras
            )
        )

    private fun getAppointmentScheduleEventTypeForTask(
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>,
        specialtyId: UUID?,
        subSpecialtyId: UUID?,
    ) = appointmentScheduleEventTypes.find {
        it.specialtyId == specialtyId && it.subSpecialtyIds?.contains(subSpecialtyId) == true
    }

    private suspend fun getCalendarForPoolScheduling(
        person: Person,
        extras: Map<String, String> = mapOf(),
        appointmentScheduleEventType: AppointmentScheduleEventType,
        appointmentScheduleOption: AppointmentScheduleOption?,
        personCalendly: PersonCalendly,
    ) =
        HealthPlanItemCalendar(
            calendarUrl = schedulingUrlBuilder.buildScheduleUrlForPoolScheduling(
                person,
                appointmentScheduleEventType,
                extras,
            ),
            examTitle = appointmentScheduleEventType.title,
            scheduleOptions = schedulingUrlBuilder.buildScheduleOptions(
                appointmentScheduleOption,
                person,
                personCalendly,
                extras
            )
        )

    // Esse código é uma gambiarra. Hoje o encaminhamento para um pool de profissionais é criado escolhendo
    // especialidade, subespecialidade e um profissional. Ao invés de mudar o fluxo para permitir que não seja escolhido
    // um profissional, decidimos manter o fluxo antigo que usa profissionais "gambiarra" para concluir o encaminhamento
    // Ex: "Preparador físico data mais próxima", "Nutricionista data mais próxima"
    // No futuro essa validação não será mais necessária (espero), quando permitiremos um encaminhamento feito não mais a um
    // profissional específico
    private fun isEligibleForProfessionalPool(staffId: UUID?) =
        staffId == null || FeatureService
            .get(FeatureNamespace.SCHEDULE, "professional_pool_staff_ids", emptyList<String>())
            .contains(staffId.toString())
}
