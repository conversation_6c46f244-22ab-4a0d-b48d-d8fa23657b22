package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.healthlogic.models.EnrichedClinicalOutcomeRecord
import br.com.alice.member.api.models.ScoreDates
import br.com.alice.member.api.models.ScoreDefaultResponse
import br.com.alice.member.api.models.ScoreDetails
import br.com.alice.member.api.models.ScoreResult
import br.com.alice.member.api.models.ScoreValues
import br.com.alice.member.api.models.healthScoreList
import java.time.format.DateTimeFormatter
import java.util.Locale

object HealthScoreHistoryTypesResponseConverter : Converter<EnrichedClinicalOutcomeRecord, ScoreDefaultResponse>(
    EnrichedClinicalOutcomeRecord::class, ScoreDefaultResponse::class
) {

    fun convert(source: List<EnrichedClinicalOutcomeRecord>, pastSource: List<EnrichedClinicalOutcomeRecord?>): ScoreDefaultResponse {
        return buildResultsResponse(source, pastSource)
    }

    private fun buildResultsResponse(source: List<EnrichedClinicalOutcomeRecord>, pastSource: List<EnrichedClinicalOutcomeRecord?>): ScoreDefaultResponse {
        val result = source.map {item ->
            val pastResult = pastSource.find{
                if (it != null) {
                    it.outcomeConf.id == item.outcomeConf.id
                } else {
                    false
                }
            }
            val delta = pastResult?.let { (item.outcome.toInt() - it.outcome.toInt()) }

            val healthScoreType = healthScoreList.first {it.first == item.outcomeConf.key}.second

            ScoreResult(
                dimension = healthScoreType.name,
                pillar = healthScoreType.pillar,
                detailImageUrl = healthScoreType.detailImageUrl,
                headerImageUrl = healthScoreType.headerImageUrl,
                listImageUrl = healthScoreType.listImageUrl,
                score = ScoreValues(value = item.outcome.toInt(), delta = delta, max = 1000),
                about = ScoreDetails(
                    description = healthScoreType.descriptionText,
                    ranges = HealthScoreHistoryResponseConverter.buildRangeListResponse(item.outcomeConf)
                ),
                date = ScoreDates(
                    chartValue = item.addedAt.format(DateTimeFormatter.ofPattern("MMM").withLocale(Locale.forLanguageTag("pt-BR"))),
                    formattedValue = item.addedAt.format(DateTimeFormatter.ofPattern("dd/MM/yy").withLocale(Locale.forLanguageTag("pt-BR"))),
                    originalValue = item.addedAt,
                )
            )
        }
        return ScoreDefaultResponse(result = result)
    }

}
