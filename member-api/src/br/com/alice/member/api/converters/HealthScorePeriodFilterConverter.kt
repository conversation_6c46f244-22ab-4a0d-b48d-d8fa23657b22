package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.member.api.models.ScoreTypesPeriodFilterResponse
import java.time.LocalDateTime
import java.time.temporal.TemporalAdjusters

object HealthScorePeriodFilterConverter : Converter<Int, ScoreTypesPeriodFilterResponse>(
    Int::class, ScoreTypesPeriodFilterResponse::class
) {
    fun convert(source: Int): ScoreTypesPeriodFilterResponse {
        val firstDayOfYear = TemporalAdjusters.firstDayOfYear()
        val lastDayOfYear = TemporalAdjusters.lastDayOfYear()
        return ScoreTypesPeriodFilterResponse(
            startDate = LocalDateTime.now().withYear(source).with(firstDayOfYear).atBeginningOfTheDay(),
            endDate = LocalDateTime.now().withYear(source).with(lastDayOfYear).atEndOfTheDay()
        )
    }
}
