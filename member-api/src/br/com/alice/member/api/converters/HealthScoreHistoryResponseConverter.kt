package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.data.layer.models.OutcomeConf
import br.com.alice.healthlogic.models.EnrichedClinicalOutcomeRecord
import br.com.alice.member.api.models.ScoreDates
import br.com.alice.member.api.models.ScoreDefaultResponse
import br.com.alice.member.api.models.ScoreDetails
import br.com.alice.member.api.models.ScoreFilter
import br.com.alice.member.api.models.ScoreRange
import br.com.alice.member.api.models.ScoreResult
import br.com.alice.member.api.models.ScoreValues
import br.com.alice.member.api.models.healthScoreList
import br.com.alice.member.api.models.rangeList
import java.time.format.DateTimeFormatter
import java.util.Locale

object HealthScoreHistoryResponseConverter : Converter<EnrichedClinicalOutcomeRecord, ScoreDefaultResponse>(
    EnrichedClinicalOutcomeRecord::class, ScoreDefaultResponse::class
) {

    fun convert(source: List<EnrichedClinicalOutcomeRecord>, availablePeriods: List<String>, hideDelta: Boolean): ScoreDefaultResponse {
        return buildResultsResponse(source, availablePeriods, hideDelta)
    }

    private fun buildResultsResponse(source: List<EnrichedClinicalOutcomeRecord>, availablePeriods: List<String>, hideDelta: Boolean): ScoreDefaultResponse {
        val result = source.mapIndexed { idx, item ->
            val pastResult = source.getOrNull((idx+1))

            val referenceDeltaId = pastResult?.let { pastResult.id }

            var delta: Int? = null
            if (!hideDelta)
                delta = pastResult?.let { (item.outcome.toInt() - it.outcome.toInt()) }

            val healthScoreType = healthScoreList.first { it.first == item.outcomeConf.key }.second

            ScoreResult(
                id = item.id,
                dimension = healthScoreType.name,
                pillar = healthScoreType.pillar,
                detailImageUrl = healthScoreType.detailImageUrl,
                headerImageUrl = healthScoreType.headerImageUrl,
                listImageUrl = healthScoreType.listImageUrl,
                score = ScoreValues(
                    value = item.outcome.toInt(),
                    delta = delta,
                    reference_delta_id = referenceDeltaId,
                    max = 1000
                ),
                about = ScoreDetails(
                    description = healthScoreType.descriptionText,
                    ranges = buildRangeListResponse(item.outcomeConf)
                ),
                date = ScoreDates(
                    chartValue = item.addedAt.format(DateTimeFormatter.ofPattern("MMM").withLocale(Locale.forLanguageTag("pt-BR"))),
                    formattedValue = item.addedAt.format(DateTimeFormatter.ofPattern("dd/MM/yy").withLocale(Locale.forLanguageTag("pt-BR"))),
                    originalValue = item.addedAt,
                )
            )
        }
        return ScoreDefaultResponse(
            result = result,
            filter = listOf(
                ScoreFilter(
                    field = "period",
                    values = availablePeriods.associateBy { it }
                ),
            ),
        )
    }

    fun buildRangeListResponse(type: OutcomeConf): List<ScoreRange> {
        return type.referenceRange.map {
            val rangeList = rangeList.first { item -> item.first == it.description }.second
            ScoreRange(
                title = rangeList.name,
                imageUrl = rangeList.imageUrl,
                interval = "${it.lowerLimit.toString()} a ${it.upperLimit.toString()}",
            )
        }
    }

}
