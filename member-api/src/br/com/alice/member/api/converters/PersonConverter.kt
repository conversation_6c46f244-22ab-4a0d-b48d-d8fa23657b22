package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Person
import br.com.alice.member.api.models.onboarding.CreatePersonRequest
import java.time.LocalDateTime

object PersonConverter:
    Converter<CreatePersonRequest, Person>(CreatePersonRequest::class, Person::class) {

    fun convert(source: CreatePersonRequest): Person {
        return convert(source,
            map(Person::addresses) from getAddress(source),
        map(Person::dateOfBirth) from getBirthDate(source))
    }

    private fun getBirthDate(source: CreatePersonRequest) =
        if (source.dateOfBirth != null)
            LocalDateTime.parse(source.dateOfBirth)
        else
            null

    private fun getAddress(source: CreatePersonRequest) =
        if (source.postalCode != null )
            listOf(Address.buildDefaultWithPostalCode(source.postalCode))
        else emptyList()
}
