package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.Frequency
import br.com.alice.data.layer.models.SentenceReference.SentenceBuilder
import br.com.alice.member.api.models.FrequencyTransport

object FrequencyTransportConverter : Converter<Frequency, FrequencyTransport>(
    Frequency::class, FrequencyTransport::class
) {
    fun convert(source: Frequency): FrequencyTransport {
        return super.convert(
            source,
            map(FrequencyTransport::description) from SentenceBuilder.friendlyFrequency(source)
        )
    }
}
