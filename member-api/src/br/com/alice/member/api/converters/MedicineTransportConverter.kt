package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.PrescriptionMedicine
import br.com.alice.member.api.models.MedicineTransport

object MedicineTransportConverter : Converter<PrescriptionMedicine, MedicineTransport>(
    PrescriptionMedicine::class, MedicineTransport::class
) {
    fun convert(source: PrescriptionMedicine): MedicineTransport {
        return super.convert(
            source,
            map(MedicineTransport::unit) from source.getUnit(),
            map(MedicineTransport::type) from source.type.toString()
        )
    }
}
