package br.com.alice.member.api.converters

import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonCalendly
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.SpecialistType.STAFF
import br.com.alice.data.layer.models.TestPreparation
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.models.HealthPlanTaskTransport
import br.com.alice.healthplan.models.HealthPlanTasksTransport
import br.com.alice.healthplan.models.PrescriptionTransport
import br.com.alice.healthplan.models.ReferralTransport
import br.com.alice.healthplan.models.TestRequestTransport
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.models.HealthHabitResponse
import br.com.alice.member.api.models.HealthPlanItemCalendar
import br.com.alice.member.api.models.HealthPlanItemResponse
import br.com.alice.member.api.models.HealthPlanItemResponseStatus
import br.com.alice.member.api.models.HealthPlanSectionName
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.PrescriptionResponse
import br.com.alice.member.api.models.ReferralsResponse
import br.com.alice.member.api.models.TestCode
import br.com.alice.member.api.models.TestPreparationTransport
import br.com.alice.member.api.models.TestRequestGroupResponse
import br.com.alice.member.api.models.TestRequestResponse
import java.util.UUID

class HealthPlanResponseConverter(
    private val schedulingUrlBuilder: SchedulingUrlBuilder,
) {
    suspend fun convertInReferralsResponses(
        tasks: List<ReferralTransport>?,
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        personCalendly: PersonCalendly,
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType> = emptyList()
    ) = tasks?.map {
        val calendar = buildCalendar(
            scheduleOptions,
            person,
            it,
            personCalendly,
            appointmentScheduleEventTypes,
        )

        logger.info(
            "HealthPlanResponseConverter: referral calendar",
            "calendar" to calendar?.calendarUrl?.split("&token=")?.first(),
            "referral_id" to it.id
        )

        ReferralsResponse(it, calendar)
    }.orEmpty()

    suspend fun convertInTestRequestResponse(
        tasks: List<TestRequestTransport>?,
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        personCalendly: PersonCalendly,
        testCodes: List<TestCode>,
        preparations: Map<String, TestPreparation>,
        isGroupTestRequests: Boolean = true
    ) = if (isGroupTestRequests) {
            val testRequestsByGroup = getTestRequestsByGroup(
                tasks,
                scheduleOptions,
                person,
                personCalendly,
                testCodes,
            )

            testRequestsByGroup.flatMap { testRequestGroup ->
                val testRequestsByCalendar = testRequestGroup.value.groupBy { it.calendar?.examTitle }

                testRequestsByCalendar.keys.mapIndexed { index, it ->
                    val testRequests = testRequestsByCalendar[it].orEmpty()
                    val group = testRequestGroup.key!!
                    val examIndex = getExamIndex(testRequestsByCalendar, index)
                    val filteredTestCodes = getTestCodes(tasks, testRequests)
                    val testPreparations = buildTestPreparationTransportFromPreparations(
                        preparations,
                        filteredTestCodes
                    )

                    TestRequestGroupResponse(
                        group,
                        examIndex,
                        testRequests,
                        testPreparations,
                    )
                }
            }
        } else {
            tasks?.map {
                val calendar = buildCalendar(
                    scheduleOptions,
                    person,
                    it,
                    personCalendly,
                    testCodes,
                )

                TestRequestResponse(it, calendar)
            }.orEmpty()
        }

    fun convertInPrescriptionResponses(tasks: List<PrescriptionTransport>?) =
        tasks?.map(::PrescriptionResponse).orEmpty()

    fun convertInHealthHabitResponses(tasks: HealthPlanTasksTransport) =
        HealthPlanSectionName.healthHabits()
            .flatMap { section ->
                val items = section.get(tasks) ?: emptyList()
                items.map { HealthHabitResponse(section, it) }
            }


    private fun getAllItemsFromHealthHabits(
        healthHabits: List<HealthHabitResponse>,
        prescriptions: List<PrescriptionResponse>,
        testRequests: List<HealthPlanItemResponse>,
        referrals: List<ReferralsResponse>,
        showDeletedTasks: Boolean,
    ) = healthHabits
        .asSequence()
        .plus(prescriptions)
        .plus(testRequests)
        .plus(referrals)
        .filter { item -> filterStatus(item, showDeletedTasks) }
        .sortedByDescending(HealthPlanItemResponse::createdAt)
        .toList()

    private fun filterStatus(
        item: HealthPlanItemResponse,
        showDeletedTasks: Boolean
    ) =
        item.status == HealthPlanItemResponseStatus.TO_DO ||
                item.status == HealthPlanItemResponseStatus.DONE ||
                (showDeletedTasks && item.status == HealthPlanItemResponseStatus.DELETED_BY_MEMBER)

    private fun getExamIndex(
        testRequestsByCalendar: Map<String?, List<TestRequestResponse>>,
        index: Int
    ) = if (testRequestsByCalendar.size > 1) index.inc().toString() else null

    private fun buildTestPreparationTransportFromPreparations(
        preparations: Map<String, TestPreparation>,
        testCodes: List<String>
    ) = preparations
        .filter { it.key in testCodes }
        .map {
            TestPreparationTransport(
                it.value.title,
                it.value.instructions,
            )
        }

    private fun getTestCodes(
        tasks: List<TestRequestTransport>?,
        testRequests: List<TestRequestResponse>
    ) = tasks?.filter { testRequest ->
        testRequest.id in testRequests.map { it.healthPlanTaskId }
    }?.mapNotNull { it.code } ?: emptyList()

    private suspend fun getTestRequestsByGroup(
        tasks: List<TestRequestTransport>?,
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        personCalendly: PersonCalendly,
        testCodes: List<TestCode>,
    ) = tasks?.map {
        val calendar = buildCalendar(
            scheduleOptions,
            person,
            it,
            personCalendly,
            testCodes,
        )

        TestRequestResponse(it, calendar)
    }
        .orEmpty()
        .filter { it.status != null }
        .groupBy { it.group }
        .filter { it.key != null && it.value.isNotEmpty() }

    private suspend fun buildCalendar(
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        request: TestRequestTransport,
        personCalendly: PersonCalendly,
        testCodes: List<TestCode>,
    ): HealthPlanItemCalendar {
        logger.info(
            "HealthPlanResponseConverter - Request Code",
            "request_code" to request.code,
            "appointment_schedule_options_titles" to scheduleOptions.map { it.title },
            "request" to request.id
        )
        request.code?.let { code ->
            testCodes
                .filter { it.codes.binarySearch(code) >= 0 }
                .firstNotNullOfOrNull { scheduleOptions.find { option -> option.title.startsWith(it.title) } }
                ?.let {
                    return getCalendarAppointmentScheduleOption(
                        person,
                        it,
                        it.title,
                        personCalendly,
                        buildExtrasFromTaskId(request),
                    )
                }
        }

        return HealthPlanItemCalendar(
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.HEALTH_COMMUNITY,
                properties = mapOf("category" to ProviderUnit.Type.LABORATORY.toString()),
            )
        )
    }

    private suspend fun buildCalendar(
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        referralTransport: ReferralTransport,
        personCalendly: PersonCalendly,
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType> = emptyList(),
    ): HealthPlanItemCalendar? =
        referralTransport.isDone().let { isDone ->
            if (isDone) return null
            else {
                val appointmentScheduleEventType = getAppointmentScheduleEventTypeForTask(
                    appointmentScheduleEventTypes,
                    referralTransport.specialty?.id,
                    referralTransport.subSpecialty?.id
                )

                val hasAppointmentScheduleEventType = appointmentScheduleEventType != null

                val extras = buildExtrasFromTaskId(referralTransport)

                logger.info(
                    "HealthPlanResponseConverter::buildCalendar",
                    "person_id" to referralTransport.personId,
                    "has_appointment_schedule_event_type" to hasAppointmentScheduleEventType,
                    "appointment_schedule_event_type_id" to appointmentScheduleEventType?.id
                )

                when {
                    hasAppointmentScheduleEventType &&
                            isEligibleForProfessionalPool(referralTransport.suggestedSpecialist?.id) -> {
                        getCalendarForPoolScheduling(
                            person,
                            extras,
                            appointmentScheduleEventType!!,
                            scheduleOptions.find { it.appointmentScheduleEventTypeId == appointmentScheduleEventType.id },
                            personCalendly
                        )
                    }
                    hasSuggestedSpecialistAndItIsStaff(referralTransport) -> getCalendarByStaff(
                        scheduleOptions,
                        person,
                        referralTransport.suggestedSpecialist!!.id,
                        personCalendly,
                        extras,
                        referralTransport.subSpecialty?.id,
                    )
                    else -> null
                }
            }
        }


    private fun buildExtrasFromTaskId(taskTransport: HealthPlanTaskTransport) =
        mapOf("health_plan_task_id" to taskTransport.id.toString().toSha256())

    private fun hasSuggestedSpecialistAndItIsStaff(referralTransport: ReferralTransport) =
        referralTransport.suggestedSpecialist?.type == STAFF && referralTransport.suggestedSpecialist?.id != null

    private suspend fun getCalendarByStaff(
        scheduleOptions: List<AppointmentScheduleOption>,
        person: Person,
        id: UUID,
        personCalendly: PersonCalendly,
        extras: Map<String, String>,
        subSpecialtyId: UUID? = null,
    ): HealthPlanItemCalendar? {
        val scheduleOptionsByStaff = scheduleOptions.filter { id == it.staffId }
        logger.info(
            "HealthPlanResponseConverter::getCalendarByStaff",
            "schedule_options_by_staff" to scheduleOptionsByStaff.size
        )

        return if (scheduleOptionsByStaff.isEmpty()) null
        else {
            val option = if (subSpecialtyId != null) {
                scheduleOptionsByStaff.find { scheduleOption ->
                    scheduleOption.subSpecialtyIds?.contains(subSpecialtyId) == true
                }
            } else scheduleOptionsByStaff.firstOrNull()

            logger.info(
                "HealthPlanResponseConverter::getCalendarByStaff",
                "option_id" to option?.id,
                "option_specialist_id" to option?.specialistId,
                "option_calendar_url" to option?.calendarUrl,
            )

            option?.let {
                getCalendarAppointmentScheduleOption(
                    person,
                    option,
                    option.title,
                    personCalendly,
                    extras,
                )
            }
        }
    }

    private suspend fun getCalendarAppointmentScheduleOption(
        person: Person,
        appointmentScheduleOption: AppointmentScheduleOption,
        examTitle: String,
        personCalendly: PersonCalendly,
        extras: Map<String, String> = mapOf(),
    ) =
        HealthPlanItemCalendar(
            calendarUrl = schedulingUrlBuilder.buildScheduleUrlWithAppointmentScheduleOption(
                person,
                appointmentScheduleOption,
                personCalendly,
                extras = extras,
            ),
            examTitle = examTitle,
            scheduleOptions = schedulingUrlBuilder.buildScheduleOptions(
                appointmentScheduleOption,
                person,
                personCalendly,
                extras
            )
        )

    private fun getAppointmentScheduleEventTypeForTask(
        appointmentScheduleEventTypes: List<AppointmentScheduleEventType>,
        specialtyId: UUID?,
        subSpecialtyId: UUID?,
    ) = appointmentScheduleEventTypes.find {
        it.specialtyId == specialtyId && it.subSpecialtyIds?.contains(subSpecialtyId) == true
    }

    private suspend fun getCalendarForPoolScheduling(
        person: Person,
        extras: Map<String, String> = mapOf(),
        appointmentScheduleEventType: AppointmentScheduleEventType,
        appointmentScheduleOption: AppointmentScheduleOption?,
        personCalendly: PersonCalendly,
    ) =
        HealthPlanItemCalendar(
            calendarUrl = schedulingUrlBuilder.buildScheduleUrlForPoolScheduling(
                person,
                appointmentScheduleEventType,
                extras,
            ),
            examTitle = appointmentScheduleEventType.title,
            scheduleOptions = schedulingUrlBuilder.buildScheduleOptions(
                appointmentScheduleOption,
                person,
                personCalendly,
                extras
            )
        )

    // Esse código é uma gambiarra. Hoje o encaminhamento para um pool de profissionais é criado escolhendo
    // especialidade, subespecialidade e um profissional. Ao invés de mudar o fluxo para permitir que não seja escolhido
    // um profissional, decidimos manter o fluxo antigo que usa profissionais "gambiarra" para concluir o encaminhamento
    // Ex: "Preparador físico data mais próxima", "Nutricionista data mais próxima"
    // No futuro essa validação não será mais necessária (espero), quando permitiremos um encaminhamento feito não mais a um
    // profissional específico
    private fun isEligibleForProfessionalPool(staffId: UUID?) =
        staffId == null || FeatureService
            .get(FeatureNamespace.SCHEDULE, "professional_pool_staff_ids", emptyList<String>())
            .contains(staffId.toString())
}
