package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.map
import br.com.alice.data.layer.models.Deadline
import br.com.alice.data.layer.models.PeriodUnit
import br.com.alice.member.api.models.DeadlineTransport
import br.com.alice.member.api.models.DeadlineTypeTransport

object DeadlineTransportConverter : Converter<Deadline, DeadlineTransport>(
    Deadline::class, DeadlineTransport::class
) {
    fun convert(source: Deadline, createdAt: String?): DeadlineTransport {
        return super.convert(
            source,
            map(DeadlineTransport::description) from friendlyDeadline(source, createdAt),
            map(DeadlineTransport::type) from when {
                source.unit == PeriodUnit.CONTINUOUS -> DeadlineTypeTransport.CONTINUOUS
                source.date != null -> DeadlineTypeTransport.TIMESTAMP
                else -> DeadlineTypeTransport.PERIODIC
            },
            map(DeadlineTransport::date) from source.date?.toLocalDate()?.toString()
        )
    }

    fun friendlyDeadline(deadline: Deadline?, createdAt: String?): String? {
        if (deadline == null) return null

        val filledDeadline = deadline.fillDate(createdAt?.toLocalDateTime())

        return when {
            deadline.unit == PeriodUnit.CONTINUOUS -> "contínuo"
            deadline.unit == PeriodUnit.UNDEFINED -> null
            deadline.date != null -> "até ${deadline.date!!.toBrazilianDateFormat()}"
            filledDeadline.date != null -> "até ${filledDeadline.date!!.toBrazilianDateFormat()}"
            else -> null
        }
    }
}
