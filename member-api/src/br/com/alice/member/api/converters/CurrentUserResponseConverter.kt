package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.convertTo
import br.com.alice.common.map
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonAdditionalInfo
import br.com.alice.member.api.models.CurrentUserResponse
import br.com.alice.member.api.models.UserAdditionalInfoResponse
import br.com.alice.member.api.models.UserAddressResponse

object CurrentUserResponseConverter : Converter<Person, CurrentUserResponse>(
    Person::class, CurrentUserResponse::class
) {
    fun convert(person: Person, personAdditionalInfo: PersonAdditionalInfo? = null): CurrentUserResponse {
        return super.convert(
            person,
            map(CurrentUserResponse::sex) from person.sex?.toString(),
            map(CurrentUserResponse::gender) from person.gender?.toString(),
            map(CurrentUserResponse::pronoun) from person.pronoun?.toString(),
            map(CurrentUserResponse::address) from person.mainAddress?.convertTo(UserAddressResponse::class),
            map(CurrentUserResponse::friendlyAddress) from person.mainAddress?.toString(),
            map(CurrentUserResponse::profilePictureUrl) from person.profilePicture?.url,
            map(CurrentUserResponse::additionalInfo) from personAdditionalInfo?.convertTo(UserAdditionalInfoResponse::class)
        )
    }
}
