package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.Dose
import br.com.alice.member.api.models.DoseTransport

object DoseTransportConverter : Converter<Dose, DoseTransport>(
    Dose::class, DoseTransport::class
) {
    fun convert(source: Dose): DoseTransport {
        return super.convert(
            source,
            map(DoseTransport::unit) from if (source.quantity >= 1) source.unit.plural else source.unit.singular
        )
    }
}
