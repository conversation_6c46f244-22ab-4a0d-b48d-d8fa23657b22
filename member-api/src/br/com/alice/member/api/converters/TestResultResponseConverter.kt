package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.capitalizeEachWord
import br.com.alice.common.map
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.TestResultResponse
import br.com.alice.testresult.models.AliceCommonTestResultFile
import java.time.format.DateTimeFormatter.ISO_DATE_TIME

object AliceTestResultResponseConverter :
    Converter<AliceCommonTestResultFile, TestResultResponse>(AliceCommonTestResultFile::class, TestResultResponse::class) {

    fun convert(source: AliceCommonTestResultFile) = super.convert(
        source,
        map(TestResultResponse::partner) from source.integrationSource?.toString()?.capitalizeEachWord(),
        map(TestResultResponse::performedAt) from source.performedAt.format(ISO_DATE_TIME),
        map(TestResultResponse::resultDocumentUrl) from buildDocumentUrl(source)
    )

    private fun buildDocumentUrl(source: AliceCommonTestResultFile) = source.integrationSource?.let {
        ServiceConfig.url("/test_results/${it.toString().lowercase()}/${source.id}/file")
    } ?: ServiceConfig.url("/test_results/${source.id}/file")

}
