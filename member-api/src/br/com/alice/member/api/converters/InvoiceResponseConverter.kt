package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.isZero
import br.com.alice.common.logging.logger
import br.com.alice.common.map
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.BolepixPaymentDetail
import br.com.alice.data.layer.models.BoletoPaymentDetail
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.InvoiceLiquidation
import br.com.alice.data.layer.models.InvoiceLiquidationStatus
import br.com.alice.data.layer.models.InvoicePayment
import br.com.alice.data.layer.models.InvoicePaymentStatus
import br.com.alice.data.layer.models.InvoiceStatus
import br.com.alice.data.layer.models.MemberInvoice
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.PixPaymentDetail
import br.com.alice.data.layer.models.SimpleCreditCardPaymentDetail
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.models.CTA
import br.com.alice.member.api.models.InvoiceBreakdownItemResponse
import br.com.alice.member.api.models.InvoiceResponse
import br.com.alice.member.api.models.InvoiceStatusResponse
import br.com.alice.member.api.models.InvoiceStatusResponse.CANCELED
import br.com.alice.member.api.models.InvoiceStatusResponse.FAILED
import br.com.alice.member.api.models.InvoiceStatusResponse.OPEN
import br.com.alice.member.api.models.InvoiceStatusResponse.OVERDUE
import br.com.alice.member.api.models.InvoiceStatusResponse.PAID
import br.com.alice.member.api.models.InvoiceStatusResponse.CANCELED_BY_LIQUIDATION
import br.com.alice.member.api.models.PaymentInfoResponse
import br.com.alice.member.api.models.PaymentOptionResponse
import br.com.alice.moneyin.converters.InvoiceBreakdownConverter
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.format.DateTimeFormatter

object InvoiceResponseConverter :
    Converter<MemberInvoice, InvoiceResponse>(MemberInvoice::class, InvoiceResponse::class) {

    private val BASE_URL = ServiceConfig.baseUrl

    fun convert(
        source: InvoiceLiquidation,
        billingAccountableParty: BillingAccountableParty? = null,
        payment: InvoicePayment? = null,
        appVersion: SemanticVersion? = null,
    ): InvoiceResponse {
        return InvoiceResponse(
            id = source.id,
            status = getInvoiceStatusResponseByLiquidation(source),
            boletoUrl = null,
            barcode = null,
            referenceDate = buildDate(source.dueDate),
            dueDate = source.dueDate.format(DateTimeFormatter.ISO_DATE),
            totalAmount = source.amount,
            breakdown = null,
            paymentInfo = if (!hasMinimumAppVersion(appVersion)) getPaymentInfo(
                payment,
                billingAccountableParty
            ) else null,
            paymentOptions = if (hasMinimumAppVersion(appVersion)) getPaymentOptions(
                payment,
                billingAccountableParty
            ) else null,
            canGenerateSecondCopy = false,
        )
    }

    private fun getStatus(invoiceStatus: InvoiceStatusResponse, dueDate: LocalDateTime) = when (invoiceStatus) {
        CANCELED_BY_LIQUIDATION -> CANCELED
        OVERDUE -> if (!hasMinimumDaysForSecondCopyGeneration(dueDate)) OPEN else invoiceStatus
        else -> invoiceStatus
    }

    fun convert(
        source: MemberInvoice,
        payment: InvoicePayment? = null,
        billingAccountableParty: BillingAccountableParty? = null,
        appVersion: SemanticVersion? = null,
        canGenerateSecondCopy: Boolean = true,
    ): InvoiceResponse {
        val dueDate = getInvoiceBoletoPaymentDetail(source.invoicePayments)?.dueDate ?: source.dueDate
        val invoiceStatus = getInvoiceStatusResponseByInvoice(source)
        val status = getStatus(invoiceStatus, dueDate)

        return convert(
            source,
            map(InvoiceResponse::id) from source.id,
            map(InvoiceResponse::canGenerateSecondCopy) from (canGenerateSecondCopy && getSecondCopyGenerationInfo(
                source,
                dueDate,
                payment
            )),
            map(InvoiceResponse::barcode) from getInvoiceBoletoPaymentDetail(source.invoicePayments)?.barcode,
            map(InvoiceResponse::boletoUrl) from getInvoiceBoletoPaymentDetail(source.invoicePayments)?.paymentUrl,
            map(InvoiceResponse::referenceDate) from buildDate(source.referenceDate),
            map(InvoiceResponse::dueDate) from dueDate.format(DateTimeFormatter.ISO_DATE),
            map(InvoiceResponse::status) from status,
            map(InvoiceResponse::breakdown) from buildBreakdown(source),
            if (hasMinimumAppVersion(appVersion))
                map(InvoiceResponse::paymentOptions) from getPaymentOptions(payment, billingAccountableParty)
            else
                map(InvoiceResponse::paymentInfo) from getPaymentInfo(payment, billingAccountableParty)
        )
    }

    private fun buildDate(date: LocalDate): String {
        val referenceDate = "${YearMonth.from(date)}"
        val referenceMonth = when (referenceDate.substring(5, 7)) {
            "01" -> "Janeiro"
            "02" -> "Fevereiro"
            "03" -> "Março"
            "04" -> "Abril"
            "05" -> "Maio"
            "06" -> "Junho"
            "07" -> "Julho"
            "08" -> "Agosto"
            "09" -> "Setembro"
            "10" -> "Outubro"
            "11" -> "Novembro"
            "12" -> "Dezembro"
            else -> null
        }
        return if (referenceMonth != null) referenceMonth + ", " + referenceDate.substring(0, 4) else referenceDate
    }

    private fun getSecondCopyGenerationInfo(
        invoice: MemberInvoice,
        dueDate: LocalDateTime,
        payment: InvoicePayment?
    ): Boolean {
        return payment?.id != null && invoice.status == InvoiceStatus.OPEN && hasMinimumDaysForSecondCopyGeneration(
            dueDate
        )
    }

    private fun hasMinimumDaysForSecondCopyGeneration(dueDate: LocalDateTime): Boolean {
        val daysFromDueDate = Duration.between(dueDate, LocalDateTime.now()).toDays()
        return daysFromDueDate >= minimumDaysForSecondCopyGeneration()
    }

    private fun getPaymentInfo(
        payment: InvoicePayment? = null,
        billingAccountableParty: BillingAccountableParty? = null
    ): PaymentInfoResponse? {
        val shouldHidePaymentDetailInfo = billingAccountableParty?.let { it.isLegalPerson } ?: false

        if (shouldHidePaymentDetailInfo)
            return null

        return payment?.let { buildPaymentDetail(it) }
    }

    private fun getPaymentOptions(
        payment: InvoicePayment? = null,
        billingAccountableParty: BillingAccountableParty? = null
    ): List<PaymentOptionResponse>? {
        val shouldHidePaymentDetailInfo = billingAccountableParty?.let { it.isLegalPerson } ?: false

        if (shouldHidePaymentDetailInfo)
            return null

        return payment?.let { buildPaymentDetailOptions(it) }
    }

    private fun buildPaymentDetail(invoicePayment: InvoicePayment): PaymentInfoResponse? {
        logger.info(
            "build-payment-detail",
            "member_invoice_id" to invoicePayment.memberInvoiceId,
            "is_new_format" to false,
            "invoice_payment_id" to invoicePayment.id,
            "detail" to invoicePayment.paymentDetail
        )

        val paymentDetail = invoicePayment.paymentDetail ?: return null

        return when (paymentDetail) {
            is BoletoPaymentDetail -> {
                if (invoicePayment.isApproved) {
                    PaymentInfoResponse(id = invoicePayment.id.toString(), title = "Pagamento feito via Boleto")
                } else {
                    PaymentInfoResponse(
                        id = invoicePayment.id.toString(),
                        title = "Código de barra",
                        code = paymentDetail.digitableLine,
                        documentUrl = "$BASE_URL/invoices/bank_slip/${invoicePayment.id}"
                    )
                }
            }

            is SimpleCreditCardPaymentDetail -> PaymentInfoResponse(
                id = invoicePayment.id.toString(),
                title = if (invoicePayment.isApproved) "Pagamento feito via Cartão de Crédito" else "Cartão de Crédito",
                paymentUrl = paymentDetail.paymentUrl,
            )

            is PixPaymentDetail -> PaymentInfoResponse(
                id = invoicePayment.id.toString(),
                title = if (invoicePayment.isApproved) "Pagamento feito via PIX" else "Pagamento via PIX",
                code = paymentDetail.copyAndPaste,
                documentUrl = "$BASE_URL/invoices/bank_slip/${invoicePayment.id}"
            )

            is BolepixPaymentDetail -> PaymentInfoResponse(
                id = invoicePayment.id.toString(),
                title = if (invoicePayment.isApproved) "Pagamento feito via PIX" else "Pagamento via PIX",
                code = paymentDetail.pixCopyAndPaste,
                documentUrl = "$BASE_URL/invoices/bank_slip/${invoicePayment.id}"
            )

            else -> null
        }
    }

    private fun buildPaymentDetailOptions(invoicePayment: InvoicePayment): List<PaymentOptionResponse>? {
        logger.info(
            "build-payment-detail-options",
            "member_invoice_id" to invoicePayment.memberInvoiceId,
            "is_new_format" to true,
            "invoice_payment_id" to invoicePayment.id,
            "detail" to invoicePayment.paymentDetail
        )
        val paymentDetail = invoicePayment.paymentDetail ?: return null

        return when (paymentDetail) {
            is BoletoPaymentDetail -> {
                listOfNotNull(buildBoletoResponse(invoicePayment, paymentDetail.barcode, paymentDetail.paymentUrl))
            }

            is SimpleCreditCardPaymentDetail -> listOfNotNull(
                buildSimpleCardCreditResponse(
                    invoicePayment,
                    paymentDetail.paymentUrl
                )
            )

            is PixPaymentDetail -> listOfNotNull(
                buildPixResponse(invoicePayment, paymentDetail.paymentCode)
            )

            is BolepixPaymentDetail ->
                listOfNotNull(
                    buildPixResponse(invoicePayment, paymentDetail.paymentCodePix),
                    buildBoletoResponse(invoicePayment, paymentDetail.barcodeBoleto, paymentDetail.paymentUrl),
                )

            else -> null
        }
    }

    private fun buildBreakdown(invoice: MemberInvoice): List<InvoiceBreakdownItemResponse> {
        val breakdown = invoice.invoiceBreakdown?.let { breakdown ->
            InvoiceBreakdownConverter.convert(breakdown).map {
                InvoiceBreakdownItemResponse(
                    amount = it.amount,
                    title = it.title
                )
            }
        } ?: emptyList()

        return breakdown.filterNot { it.amount.isZero() }
    }

    @Deprecated("This soon will be removed. We've created a more detailed payment info in PaymentInfo")
    private fun getInvoiceBoletoPaymentDetail(invoicePayments: List<InvoicePayment>?) =
        invoicePayments?.filter { it.method == PaymentMethod.BOLETO }?.let { boletoPayments ->
            (boletoPayments.firstOrNull { it.status == InvoicePaymentStatus.APPROVED }
                ?: boletoPayments.firstOrNull { it.status == InvoicePaymentStatus.PENDING }
                ?: boletoPayments.firstOrNull { it.status == InvoicePaymentStatus.DECLINED }
                ?: boletoPayments.firstOrNull { it.status == InvoicePaymentStatus.CANCELED }
                ?: boletoPayments.firstOrNull { it.status == InvoicePaymentStatus.FAILED }
                    )?.paymentDetail as BoletoPaymentDetail?
        }

    private fun getInvoiceStatusResponseByInvoice(memberInvoice: MemberInvoice) = when (memberInvoice.status) {
        InvoiceStatus.PAID -> PAID
        InvoiceStatus.OPEN -> if (memberInvoice.isOverdue) OVERDUE else OPEN
        InvoiceStatus.CANCELED -> CANCELED
        InvoiceStatus.FAILED -> FAILED
        InvoiceStatus.CANCELED_BY_LIQUIDATION -> CANCELED_BY_LIQUIDATION
    }

    private fun getInvoiceStatusResponseByLiquidation(invoiceLiquidation: InvoiceLiquidation) =
        when (invoiceLiquidation.status) {
            InvoiceLiquidationStatus.PAID, InvoiceLiquidationStatus.PARTIALLY_PAID -> PAID
            InvoiceLiquidationStatus.PROCESSED, InvoiceLiquidationStatus.PROCESSING, InvoiceLiquidationStatus.WAITING_PAYMENT -> OPEN
            InvoiceLiquidationStatus.CANCELED -> CANCELED
        }


    private fun buildBoletoResponse(
        invoicePayment: InvoicePayment,
        code: String?,
        documentUrl: String?
    ): PaymentOptionResponse? {
        if (invoicePayment.isApproved && invoicePayment.method != PaymentMethod.BOLETO) return null

        val actionCta = CTA(label = "Pagar com boleto", icon = "barcode")
        val copyCta = CTA(label = "Copiar código do boleto", icon = "copy")

        return if (invoicePayment.isApproved) {
            PaymentOptionResponse(id = invoicePayment.id.toString(), header = "Pagamento feito via Boleto")
        } else {
            PaymentOptionResponse(
                id = invoicePayment.id.toString(),
                header = "Código de barra",
                actionCta = actionCta,
                copyCta = copyCta,
                code = code,
                documentUrl = if (documentUrl != null) "$BASE_URL/invoices/bank_slip/${invoicePayment.id}" else null
            )
        }
    }

    private fun buildPixResponse(
        invoicePayment: InvoicePayment,
        code: String?,
    ): PaymentOptionResponse? {
        if (invoicePayment.isApproved && invoicePayment.method != PaymentMethod.PIX) return null

        val actionCta = CTA(label = "Pagar com PIX", icon = "pix")
        val copyCta = CTA(label = "Copiar código PIX", icon = "copy")

        return if (invoicePayment.isApproved) {
            PaymentOptionResponse(id = invoicePayment.id.toString(), header = "Pagamento feito via PIX")
        } else {
            PaymentOptionResponse(
                id = invoicePayment.id.toString(),
                header = "Pagamento via PIX",
                code = code,
                actionCta = actionCta,
                copyCta = copyCta,
            )
        }
    }

    private fun buildSimpleCardCreditResponse(
        invoicePayment: InvoicePayment,
        paymentUrl: String
    ): PaymentOptionResponse? {
        if (invoicePayment.isApproved && invoicePayment.method != PaymentMethod.SIMPLE_CREDIT_CARD) return null

        val actionCta = CTA(label = "Pagar com Cartão de Crédito", icon = "credit_card")

        return if (invoicePayment.isApproved) {
            PaymentOptionResponse(id = invoicePayment.id.toString(), header = "Pagamento feito via Cartão de Crédito")
        } else {
            PaymentOptionResponse(
                id = invoicePayment.id.toString(),
                header = "Cartão de Crédito",
                documentUrl = paymentUrl,
                actionCta = actionCta,
            )
        }
    }

    private fun hasMinimumAppVersion(appVersion: SemanticVersion?): Boolean = FeatureService.get(
        namespace = FeatureNamespace.MEMBERSHIP,
        key = "minimum_app_version_for_use_bolepix",
        defaultValue = "10.0.0"
    ).let { appVersion != null && appVersion >= SemanticVersion(it) }

    private fun minimumDaysForSecondCopyGeneration(): Int = FeatureService.get(
        namespace = FeatureNamespace.MEMBERSHIP,
        key = "minimum_days_for_second_copy_generation",
        defaultValue = 30
    )
}

