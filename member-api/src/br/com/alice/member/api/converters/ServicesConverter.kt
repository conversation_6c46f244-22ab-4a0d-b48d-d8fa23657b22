package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.featureaccess.features.ServicesResource
import br.com.alice.member.api.models.ServiceResponse
import br.com.alice.member.api.models.ServicesResponse

object ServicesConverter
    : Converter<ServicesResource, ServicesResponse>(ServicesResource::class, ServicesResponse::class) {

    fun convert(source: ServicesResource?): ServicesResponse {
        return ServicesResponse(
                title = source?.title,
                services = source?.services?.map {
                    ServiceResponse(
                        navigation = NavigationResponseConverter.convert(it.navigation),
                        card = it.card
                    )
                }
            )
    }
}
