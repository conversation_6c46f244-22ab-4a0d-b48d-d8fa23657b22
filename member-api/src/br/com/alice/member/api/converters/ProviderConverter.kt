package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.Provider
import br.com.alice.member.api.models.ProviderResponse

object ProviderConverter
    : Converter<Provider, ProviderResponse>(Provider::class, ProviderResponse::class) {
    fun convert(source: Provider): ProviderResponse {
        return convert(source,
            map(ProviderResponse::phones) from source.phones.map { PhoneNumberResponseConverter.convert(it) },
            map(ProviderResponse::simplifiedAddresses) from emptyList(),
            map(ProviderResponse::addresses) from emptyList(),
            map(ProviderResponse::crm) from null,
            map(ProviderResponse::curiosity) from null,
            map(ProviderResponse::specialties) from emptyList(),
            map(ProviderResponse::subSpecialties) from emptyList(),
            map(ProviderResponse::education) from emptyList(),
            map(ProviderResponse::type) from ""
        )
    }
}
