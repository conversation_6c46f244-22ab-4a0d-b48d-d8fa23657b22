package br.com.alice.member.api.converters

import br.com.alice.common.Converter
import br.com.alice.common.core.extensions.bytesToHumanReadableString
import br.com.alice.common.map
import br.com.alice.data.layer.models.TestResultFile
import br.com.alice.member.api.models.HealthDocumentResponse
import java.time.format.DateTimeFormatter.ISO_DATE_TIME

object HealthDocumentResponseConverter :
    Converter<TestResultFile, HealthDocumentResponse>(TestResultFile::class, HealthDocumentResponse::class) {

    fun convert(source: TestResultFile) = super.convert(
        source,
        map(HealthDocumentResponse::documentName) from source.description,
        map(HealthDocumentResponse::documentUrl) from source.file!!.url,
        map(HealthDocumentResponse::thumbnailUrl) from source.thumbnail?.url,
        map(HealthDocumentResponse::uploadedAt) from source.createdAt.format(ISO_DATE_TIME),
        map(HealthDocumentResponse::performedAt) from source.performedAt.format(ISO_DATE_TIME),
        map(HealthDocumentResponse::documentSize) from source.file?.fileSize?.bytesToHumanReadableString()
    )
}
