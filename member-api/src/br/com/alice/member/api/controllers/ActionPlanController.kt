package br.com.alice.member.api.controllers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.fromSaoPauloToUTCTimeZone
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.logging.logger
import br.com.alice.member.api.models.ActionPlanScheduledAtRequest
import br.com.alice.member.api.models.ActionPlanScheduledAtResponse
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier

class ActionPlanController(
    private val actionPlanTaskService: ActionPlanTaskService,
) : Controller() {

    suspend fun setTaskAsScheduled(request: ActionPlanScheduledAtRequest) =
        // TODO use timezone header to convert the date to UTC properly
        actionPlanTaskService.setAsScheduled(
            request.actionPlanTaskId,
            request.scheduledAt.fromSaoPauloToUTCTimeZone()
        ).coFoldResponse(
            {
                updateAppState(currentUid().toPersonId())
                ActionPlanScheduledAtResponse(
                    actionPlanTaskId = it.id,
                    scheduledAt = it.scheduledAt!!,
                    status = it.status,
                    updatedAt = it.updatedAt,
                )
            }
        )

    private fun updateAppState(personId: PersonId) {
        runCatching {
            AppStateNotifier.updateAppState(personId = personId, AppState.HEALTH_PLAN_DETAILS)
        }.onFailure {
            logger.error("updateAppState: Failed to update app state", it)
        }
    }
}
