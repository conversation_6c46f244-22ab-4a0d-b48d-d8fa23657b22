package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.foldResponse
import br.com.alice.member.api.models.BankInfoResponse
import br.com.alice.member.api.models.BankResponse
import br.com.alice.member.api.utils.BankInstitutions

class BankController: Controller() {

    suspend fun getBankCodes(): Response =
        coResultOf<BankResponse, Throwable> {
            BankResponse(
                banks = BankInstitutions.values().map {
                    BankInfoResponse(code = it.code, description = it.description)
                }
            )
        }.foldResponse()

}
