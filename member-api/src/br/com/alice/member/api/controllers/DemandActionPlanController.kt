package br.com.alice.member.api.controllers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.client.DemandActionPlanService
import br.com.alice.action.plan.model.ActionPlanTaskFilters
import br.com.alice.action.plan.model.ActionPlanTaskFilters.Companion.INACTIVE_STATUSES
import br.com.alice.action.plan.model.ActionPlanTaskFilters.Companion.TO_DO_STATUSES
import br.com.alice.app.content.client.screens.DemandScreenService
import br.com.alice.authentication.currentUserId
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.ActionPlanTaskStatus
import br.com.alice.data.layer.models.ActionPlanTaskType
import br.com.alice.data.layer.models.DemandActionPlan
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.ActionPlanCardListConverter
import br.com.alice.member.api.converters.appContent.ScreenResponseConverter
import br.com.alice.member.api.converters.appContent.SectionResponseConverter
import br.com.alice.member.api.models.appContent.Section
import com.github.kittinunf.result.map
import io.ktor.http.Parameters
import java.util.UUID

class DemandActionPlanController(
    private val actionPlanTaskService: ActionPlanTaskService,
    private val demandActionPlanService: DemandActionPlanService,
    private val cardListConverter: ActionPlanCardListConverter,
    private val demandScreenService: DemandScreenService
) : Controller(), Spannable {

    suspend fun getTasksByDemand(demandId: String, queryParams: Parameters): Response =
        span("getTasksByDemand") { span ->
            val queryStatus = queryParams["status"] ?: "active"
            val queryFilterByType = queryParams["type"]
            val status = DemandTaskFilters.valueOf(queryStatus.uppercase())
            val personId = currentUserId().toPersonId()

            span.setAttribute("demand_id", demandId)
            span.setAttribute("person_id", personId)
            span.setAttribute("query_param_status", status)
            span.setAttribute("query_param_filter_by_type", queryFilterByType ?: "")

            demandActionPlanService.get(demandId.toUUID()).coFoldResponse(
                { demandActionPlan ->
                    span.setAttribute("demand_tasks_size", demandActionPlan.relatedTaskIds.size)

                    val cardSections = getTasksByFilter(
                        demandActionPlan,
                        status,
                        personId,
                        queryFilterByType
                    )
                    Cards(
                        content = cardSections
                    )
                }
            )
        }

    suspend fun getMoreRecent(): Response =
        span("getMoreRecent") { span ->
            val personId = currentUserId().toPersonId()
            span.setAttribute("person_id", personId)

            demandScreenService.getMoreRecent(personId)
                .map { recent -> Cards(recent.content.map { SectionResponseConverter.convert(it) }) }
                .foldResponse()
        }

    suspend fun getAll(): Response = span("getAll") { span ->
        val personId = currentUserId().toPersonId()
        span.setAttribute("person_id", personId)

        demandScreenService.getAll(personId)
            .map { ScreenResponseConverter.convert(it) }
            .foldResponse()
    }

    private suspend fun getTasksByFilter(
        demandActionPlan: DemandActionPlan,
        status: DemandTaskFilters,
        personId: PersonId,
        filterByType: String?
    ) = when (status) {
        DemandTaskFilters.INACTIVE -> getTasks(demandActionPlan, personId, INACTIVE_STATUSES)
        DemandTaskFilters.ACTIVE -> getTasks(demandActionPlan, personId, TO_DO_STATUSES, filterByType)
    }

    private suspend fun getTasks(
        demandActionPlan: DemandActionPlan,
        personId: PersonId,
        statuses: List<ActionPlanTaskStatus>,
        filterByType: String? = null
    ): List<Section> {
        val count = countTasks(demandActionPlan.relatedTaskIds, personId, statuses)
        return actionPlanTaskService.getTasksByFilters(
            ActionPlanTaskFilters(
                offset = 0,
                limit = count,
                personId = personId,
                taskIds = demandActionPlan.relatedTaskIds,
                favorite = getFavoriteFilter(filterByType),
                types = getTypeFilter(filterByType),
                statuses = statuses
            )
        ).map {
            cardListConverter.convert(it, personId)
        }.get()
    }

    private suspend fun countTasks(
        relatedTaskIds: List<UUID>,
        personId: PersonId,
        statuses: List<ActionPlanTaskStatus>
    ) = actionPlanTaskService.countTasksByFilters(
        ActionPlanTaskFilters(
            personId = personId,
            taskIds = relatedTaskIds,
            statuses = statuses
        )
    ).get()

    private fun getTypeFilter(filterByType: String?) =
        if (filterByType != null && filterByType != "favorites") listOf(ActionPlanTaskType.valueOf(filterByType))
        else null

    private fun getFavoriteFilter(filterByType: String?) =
        if (filterByType != null && filterByType == "favorites") true
        else null
}

enum class DemandTaskFilters(val value: String) {
    INACTIVE("inactive"),
    ACTIVE("active")
}

data class Cards(
    private val content: List<Section>
)
