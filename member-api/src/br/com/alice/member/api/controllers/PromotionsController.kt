package br.com.alice.member.api.controllers

import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.member.api.builders.PromoCodeResponseBuilder
import br.com.alice.membership.client.PromoCodeService
import com.github.kittinunf.result.map
import io.ktor.http.Parameters

class PromotionsController(
    private val promoCodeService: PromoCodeService
) : Controller() {

    suspend fun getMGMPromotion(parameters: Parameters) =
        promoCodeService.findMGMPromoCodeByPerson(currentUid().toPersonId())
        .map {promoCode ->
            val isFullScreen = parameters["is_full_screen"]?.toBoolean() ?: false
            PromoCodeResponseBuilder.build(promoCode, isFullScreen)
        }.foldResponse()

}
