package br.com.alice.member.api.controllers

import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.exceptions.MissingParamsException
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.foldResponse
import br.com.alice.member.api.models.address.AddressMapsTransport
import br.com.alice.member.api.models.address.AutocompleteResponse
import br.com.alice.member.api.models.currentAppSessionId
import br.com.alice.member.api.services.AddressInternalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.map
import io.ktor.http.Parameters

class MapsAddressController(
    private val addressService: AddressInternalService,
) : Controller() {
    suspend fun autocompleteAddress(queryParams: Parameters): Response {
        val query = queryParams["q"] ?: throw MissingParamsException("q")
        val session = currentAppSessionId()?.toSafeUUID() ?: RangeUUID.generate()

        return addressService.autocompletedAddress(query, session).map {
            AutocompleteResponse(addresses = it)
        }.foldResponse()
    }

    suspend fun getByGeolocation(queryParams: Parameters): Response {
        val lat = queryParams["lat"]?.toDoubleOrNull()
        val lng = queryParams["lng"]?.toDoubleOrNull()

        return getByLatAndLng(lat, lng).foldResponse()
    }

    private suspend fun getByLatAndLng(lat: Double?, lng: Double?): Result<AddressMapsTransport, Throwable> =
        if (lat == null || lng == null) MissingParamsException("lat or lng").failure()
        else addressService.getAddress(lat, lng)

    suspend fun getByPlaceId(placeId: String) =
        addressService.getByPlaceId(placeId).foldResponse()
}
