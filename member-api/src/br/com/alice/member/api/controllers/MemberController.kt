package br.com.alice.member.api.controllers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.common.Response
import br.com.alice.common.ValidationErrorResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProductType
import br.com.alice.ehr.client.MemberCptsService
import br.com.alice.ehr.model.removeOthers
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.member.api.builders.CPTWithGracesResponseBuilder
import br.com.alice.member.api.builders.MembershipResponseBuilder
import br.com.alice.member.api.converters.MemberABPathResponseConverter
import br.com.alice.member.api.models.MemberAlreadySignedContractResponse
import br.com.alice.member.api.models.currentAppVersion
import br.com.alice.member.api.services.MemberDocuments
import br.com.alice.member.api.usecases.ValidatePreActivatedMemberUseCase
import br.com.alice.membership.client.ContractRegistry
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.server.util.getOrFail

class MemberController(
    private val personService: PersonService,
    private val memberService: MemberService,
    private val memberDocuments: MemberDocuments,
    private val contractRegistry: ContractRegistry,
    private val memberCptsService: MemberCptsService,
    private val trackPersonABService: TrackPersonABService,
    private val beneficiaryService: BeneficiaryService,
    private val validatePreActivatedMemberUseCase: ValidatePreActivatedMemberUseCase
) : Controller() {

    suspend fun getMemberCards() = memberService.findActiveMembershipWithProduct(
        currentUid().toPersonId(),
        findOptions = MemberService.FindOptions(withCassiMember = true)
    )
        .flatMapPair { personService.get(it.member.personId) }
        .map { (person, memberWithProductAndCassi) ->
            MembershipResponseBuilder.buildMembershipResponse(
                person,
                memberWithProductAndCassi
            )
        }
        .foldResponse()

    suspend fun getContractTemporaryLink() = contractRegistry.findContract(currentUid().toPersonId())
        .map { it.certificateUrl ?: it.documentUrl }
        .map { memberDocuments.getExternalUrl(it) }
        .foldResponse(HttpStatusCode.TemporaryRedirect)

    suspend fun checkContractAlreadySigned(): Response {
        val personId = currentUid().toPersonId()
        return memberService.getCurrent(personId).map { member ->
            when (member.productType) {
                ProductType.B2C -> {
                    contractRegistry.findContract(member.personId).getOrNullIfNotFound()?.alreadySigned ?: false
                }

                else -> {
                    beneficiaryService.checkContractAlreadySigned(member.id).get()
                }
            }
        }.map { alreadySigned ->
            MemberAlreadySignedContractResponse(alreadySigned)
        }.foldResponse()
    }

    suspend fun getCptsAndGraces(): Response {
        val personId = currentUid().toPersonId()
        val appVersion = currentAppVersion()

        return memberService.findByPersonIds(listOf(personId))
            .map { members ->
                if (members.size != 1) ValidationErrorResponse("There should be only one membership")
                else {
                    val member = members.first()
                    val isPreActivatedMember = validatePreActivatedMemberUseCase.isPreActivatedMember(member, appVersion)
                    val memberCpt = memberCptsService.buildPersonCptsByPersonId(personId).get()
                    val memberCptsWithoutOthers = memberCpt.removeOthers()
                    val cptsAndGraces =
                        CPTWithGracesResponseBuilder
                            .buildCPTWithGracesResponse(member, memberCptsWithoutOthers, isPreActivatedMember)
                            .toResponse()
                    logger.info(
                        "Cpts and Graces calculated for member",
                        "person_id" to personId,
                        "member_id" to member.id,
                        "member_cpt" to memberCptsWithoutOthers,
                        "cpt_and_graces_calculated" to cptsAndGraces,
                    )
                    cptsAndGraces
                }
            }.get()
    }

    suspend fun getMemberABPath(queryParams: Parameters): Response {
        val fallbackPath = "default"
        val key = queryParams.getOrFail("key")
        val namespace = queryParams.getOrFail("namespace")
        val featureNamespace = FeatureNamespace.valueOf(namespace.uppercase())

        val personId = currentUid().toPersonId()

        return trackPersonABService.findOrStartAb(
            personId = personId,
            namespace = featureNamespace,
            key = key,
            defaultPath = fallbackPath
        ).fold(
            success = { MemberABPathResponseConverter.convert(it, namespace, key) },
            failure = { ex ->
                logger.error("Error getting AB path", ex)
                MemberABPathResponseConverter.fallbackResponse(personId, namespace, key, fallbackPath)
            }
        ).toResponse()
    }
}
