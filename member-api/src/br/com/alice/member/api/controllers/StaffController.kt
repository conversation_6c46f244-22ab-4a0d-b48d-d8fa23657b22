package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Staff
import br.com.alice.member.api.converters.HealthProfessionalGenderConverter
import br.com.alice.member.api.models.HealthProfessionalResponse
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.getOrElse
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class StaffController(
    private val staffService: StaffService,
    private val healthProfessionalService: HealthProfessionalService
) : Controller() {

    suspend fun getStaff(params: Parameters): Response = span("getStaff") { span ->
        coroutineScope {
            logger.info(
                "StaffController::getStaff",
                "person_id" to currentUid(),
                "params" to params.entries()
            )

            val staffIdParams = params["ids"]

            if (staffIdParams.isNullOrEmpty()) {
                logger.info("None id was provided. Returning empty Staff response")
                return@coroutineScope Response(HttpStatusCode.OK, emptyList<HealthProfessionalResponse>())
            }

            val staffIds = staffIdParams.split(",").filter(String::isNotEmpty).map(UUID::fromString)
            val healthProfessionalDeferred =
                async { healthProfessionalService.getByStaffIds(staffIds).getOrElse { emptyList() } }
            val result = staffService.findByList(staffIds)

            result.coFoldResponse(
                { staffList ->
                    val healthProfessionals = healthProfessionalDeferred.await()
                    staffList.toHealthProfessionalResponse(healthProfessionals)
                }
            )
        }
    }

    private suspend fun List<Staff>.toHealthProfessionalResponse(
        healthProfessionals: List<HealthProfessional>
    ): List<HealthProfessionalResponse> {
        val healthProfessionalMap = healthProfessionals.associateBy { it.staffId }
        val saoPauloLocalDate = LocalDateTime.now().toSaoPauloTimeZone().toLocalDate()
        return this.map { staff ->
            val healthProfessionalOnVacation = healthProfessionalMap[staff.id]?.isOnVacation(saoPauloLocalDate) ?: false
            HealthProfessionalGenderConverter.convert(staff, healthProfessionalOnVacation)
        }
    }

    private suspend fun HealthProfessional.isOnVacation(saoPauloLocalDate: LocalDate): Boolean =
        span("isOnVacation") { span ->
            this.onVacationUntil?.let {
                val onVacationUntilToSaoPauloLocalDate = it.toSaoPauloTimeZone().toLocalDate()
                span.setAttribute("on_vacation_until", onVacationUntilToSaoPauloLocalDate)
                span.setAttribute("sao_paulo_local_date_now", saoPauloLocalDate)
                onVacationUntilToSaoPauloLocalDate >= saoPauloLocalDate
            } ?: false
        }

}
