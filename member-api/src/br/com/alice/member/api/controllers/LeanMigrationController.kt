package br.com.alice.member.api.controllers

import br.com.alice.authentication.currentUserId
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.toResponse
import br.com.alice.member.api.converters.LeanMigration

class LeanMigrationController : Controller() {

    suspend fun playlist(): Response =
        LeanMigration.build(currentUserId().toPersonId()).toResponse()
}

data class LeanMigrationResponse(
    val person: PersonMigration,
    val pageTitle: String,
    val playlist: List<PlaylistItem>
)
