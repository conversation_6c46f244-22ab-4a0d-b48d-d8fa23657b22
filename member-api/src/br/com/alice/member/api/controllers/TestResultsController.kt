package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.isUUID
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.data.layer.models.UploadedBy
import br.com.alice.member.api.converters.AliceTestResultResponseConverter
import br.com.alice.member.api.models.TestResultListResponse
import br.com.alice.testresult.client.TestResultFileService
import br.com.alice.testresult.models.AliceCommonTestResultFile
import br.com.alice.testresult.models.FileTransportType
import com.github.kittinunf.result.map
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.HttpStatusCode.Companion.TemporaryRedirect
import io.ktor.http.content.ByteArrayContent
import br.com.alice.ehr.client.TestResultFileService as OldTestResultFileService


class TestResultsController(
    private val testResultFileService: OldTestResultFileService,
    private val commonTestResultFileService: TestResultFileService,
) : Controller() {

    suspend fun getTestResultDocument(id: String): Response {
        val personId = currentUid()
        logger.info(
            "Retrieving test result file for the person",
            "personId" to personId,
            "testResultFileId" to id
        )

        val testResultDocument = testResultFileService.getTestResultDocumentLink(id).get()
        return Response(TemporaryRedirect, testResultDocument)
    }

    suspend fun getResultDocumentByPartner(partnerAsString: String, id: String): Response {
        val provider = ProviderIntegration.valueOf(partnerAsString.uppercase())

        if (provider == ProviderIntegration.FLEURY && id.isUUID()) {
            val link = commonTestResultFileService.getTestResultDocumentLinkByProvider(id.toUUID(), provider).get()
            return Response(TemporaryRedirect, link)
        }

        val response = commonTestResultFileService.getTestResultDocumentByProvider(id, provider).get()
        return when (response.type) {
            FileTransportType.LINK -> Response(TemporaryRedirect, response.url!!)
            FileTransportType.BYTE_ARRAY -> Response(
                OK,
                ByteArrayContent(response.byteArray!!, ContentType.Application.Pdf, OK)
            )
        }
    }

    suspend fun list(): Response {
        val personId = currentUid()
        return commonTestResultFileService.getTestResultsByPersonId(personId.toPersonId())
            .map { it + filesResults(personId) }
            .mapEach(AliceTestResultResponseConverter::convert)
            .map { it.sortedByDescending { testResult -> testResult.performedAt } }
            .map(::TestResultListResponse)
            .foldResponse()
    }

    private suspend fun filesResults(personId: String): List<AliceCommonTestResultFile> =
        testResultFileService.findByPersonId(personId)
            .map { fileResults -> fileResults.filter { it.uploadedBy == UploadedBy.STAFF } }
            .mapEach {
                AliceCommonTestResultFile(
                    description = it.description,
                    performedAt = it.performedAt,
                    id = it.id.toString(),
                )
            }.fold(
                { it }, { emptyList() }
            )
}
