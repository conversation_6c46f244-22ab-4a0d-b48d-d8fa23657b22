package br.com.alice.member.api.controllers

import br.com.alice.common.NotFoundResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.logging.logger
import br.com.alice.member.api.services.MemberDocuments
import io.ktor.http.HttpStatusCode

class HealthPlanController(
    private val memberDocuments: MemberDocuments,
) : Controller() {

    suspend fun getAttachment(attachmentId: String): Response {
        logger.info("HealthPlanController.getAttachment", "attachment_id" to attachmentId, "person_id" to currentUid())
        val file = memberDocuments.getFileFromVault(attachmentId) ?: return NotFoundResponse()
        return Response(HttpStatusCode.TemporaryRedirect, file.url)
    }

}
