package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.EmergencyRecommendation
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.Provider
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.dragonradar.client.EmergencyRecommendationService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.converters.ProviderResponseConverter
import br.com.alice.member.api.models.ProviderResponse
import br.com.alice.member.api.models.ProviderWithRecommendationResponse
import br.com.alice.person.client.MemberService
import br.com.alice.product.client.ProductService
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class EmergencyRecommendationController(
    private val providerService: ProviderService,
    private val providerUnitService: ProviderUnitService,
    private val memberService: MemberService,
    private val productService: ProductService,
    private val emergencyRecommendationService: EmergencyRecommendationService,
) : Controller() {

    companion object {
        private val VALID_PROVIDER_UNIT_ID_TYPES = listOf(
            ProviderUnit.Type.EMERGENCY_UNITY,
            ProviderUnit.Type.MATERNITY
        )

        private val IGNORED_PRODUCT_BUNDLE_TYPES = listOf(
            ProductBundleType.SPECIALITY_TIERS,
            ProductBundleType.SPECIALIST
        )
    }

    suspend fun getEmergencyUnitiesWithRecommendation(emergencyHealthPlanTaskId: UUID): Response =
        coroutineScope {
            val unitiesDef = async { findAllEmergencyUnites().get() }
            val emergencyRecommendationDef = async { getEmergencyRecommendation(emergencyHealthPlanTaskId) }

            val unities = unitiesDef.await()
            val emergencyRecommendation = emergencyRecommendationDef.await()

            val recommendedUnit = emergencyRecommendation?.let {
                unities.firstOrNull { unit -> unit.id == it.recommendedProviderUnitId }
            }

            ProviderWithRecommendationResponse(
                recommendation = recommendedUnit,
                providers = unities
            ).toResponse()
        }


    private suspend fun getEmergencyRecommendation(emergencyHealthPlanTaskId: UUID): EmergencyRecommendation? =
        if (isEmergencyRecommendationEnabled()) {
            emergencyRecommendationService.findByHealthPlanTaskId(emergencyHealthPlanTaskId).getOrNull()
        } else {
            null
        }

    private suspend fun findAllEmergencyUnites(): Result<List<ProviderResponse>, Throwable> {
        val personId = currentUid().toPersonId()
        return memberService.findActiveMembership(personId)
            .map {
                val productId = it.productId
                val i18n = this::i18n

                val providers = getProviders(productId)
                val providerUnits = getProviderUnits(providers)

                val providersMap = providers.associateBy { p -> p.id }
                providerUnits.map { providerUnit ->
                    ProviderResponseConverter.convert(
                        providerUnit,
                        providersMap[providerUnit.providerId]!!,
                        emptyMap(),
                        false,
                        i18n
                    )
                }
            }
    }

    private suspend fun getProviders(productId: UUID) =
        productService.getProductWithBundles(
            productId,
            ProductService.FindOptions(
                withPriceListing = false
            )
        )
            .flatMap { product ->
                val providerIds = product.bundles
                    .filterNot {
                        IGNORED_PRODUCT_BUNDLE_TYPES.contains(it.type)
                    }.map { it.providerIds }
                    .flatten()
                providerService.getByIds(providerIds)
            }.get()

    private suspend fun getProviderUnits(providers: List<Provider>): List<ProviderUnit> {
        return providerUnitService.getByProviderIdsWithTypes(
            providers.map { it.id },
            VALID_PROVIDER_UNIT_ID_TYPES,
        ).get()
    }


    private fun isEmergencyRecommendationEnabled() =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "show_emergency_recommendation_in_app",
            defaultValue = false
        )
}
