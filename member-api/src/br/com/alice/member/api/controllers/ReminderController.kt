package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.builders.ReminderResponseBuilder
import br.com.alice.member.api.models.reminder.ReminderResponse
import br.com.alice.member.api.models.reminder.RemindersResponse
import br.com.alice.member.api.services.ReminderService
import io.ktor.http.HttpStatusCode

class ReminderController(
    private val reminderService: ReminderService
) : Controller() {

    suspend fun listReminders(): Response {
        val personId = currentUid()
        val reminders = listOfNotNull(
            buildHealthDocumentsReminder(personId),
        )

        if (reminders.isEmpty())
            return Response(HttpStatusCode.NotFound)

        return RemindersResponse(
            reminders = reminders
        ).toResponse()
    }

    private suspend fun buildHealthDocumentsReminder(personId: String): ReminderResponse? {
        val shouldSendReminder = FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "health_documents_reminder",
            defaultValue = false
        )

        return if (
            !shouldSendReminder ||
            reminderService.personHasHealthDocuments(personId)
        )
            null
        else
            ReminderResponseBuilder.buildHealthDocumentsReminder(this::i18n)
    }

}
