package br.com.alice.member.api.controllers

import br.com.alice.channel.client.VideoCallService
import br.com.alice.channel.client.VirtualClinicService
import br.com.alice.channel.models.VideoCallMeeting
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.RejectVideoCallReason
import br.com.alice.data.layer.models.Staff
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.converters.HealthProfessionalGenderConverter
import br.com.alice.member.api.models.videoCall.AmazonChimeConfig
import br.com.alice.member.api.models.videoCall.Attendee
import br.com.alice.member.api.models.videoCall.AttendeeResponse
import br.com.alice.member.api.models.videoCall.MediaPlacement
import br.com.alice.member.api.models.videoCall.Meeting
import br.com.alice.member.api.models.videoCall.MeetingResponse
import br.com.alice.member.api.models.videoCall.VideoCallMeetingResponse
import com.github.kittinunf.result.map
import java.util.UUID
import software.amazon.awssdk.services.chimesdkmeetings.model.Attendee as AwsAttendee
import software.amazon.awssdk.services.chimesdkmeetings.model.Meeting as AwsMeeting

class VideoCallController(
    private val videoCallService: VideoCallService,
    private val virtualClinicService: VirtualClinicService
) : Controller(), Spannable {

    suspend fun join(videoCallId: UUID): Response {
        val personId = currentUid().toUUID()
        logger.info(
            "VideoCallController.join: member joining a meeting",
            "video_call_id" to videoCallId,
            "person_id" to personId
        )

        return videoCallService.addPersonInMeeting(
            videoCallId = videoCallId,
            personId = personId
        ).then {
            logger.info(
                "VideoCallController.join: member joined a meeting",
                "video_call" to it.videoCall,
            )
        }.thenError {
            logger.info(
                "VideoCallController.join: error to join a meeting",
                "video_call_id" to videoCallId,
                "person_id" to personId,
                it
            )
        }.map { videoCallMeeting ->
            val staff = videoCallService.getStaffFromMeeting(videoCallId).getOrNullIfNotFound()
            VideoCallConverter.convert(videoCallMeeting, staff, isMemberVideoCallTrackingEnabled())
        }.foldResponse()
    }

    private fun isMemberVideoCallTrackingEnabled() : Boolean = FeatureService.get(
        namespace = FeatureNamespace.ALICE_APP,
        key = "member_video_call_events_tracking_enabled",
        defaultValue = false
    )

    suspend fun quit(videoCallId: UUID) = span("quit") {
        quitVideoCall(videoCallId)
    }

    suspend fun getRejectReasons() = span("getRejectReasons") { span ->
        currentUid().toPersonId().let { personId ->
            span.setAttribute("person_id", personId)

            videoCallService.getRejectVideoCallReasons()
                .map { it.copy(reasons = it.reasons.filter { reason -> reason.key != RejectVideoCallReason.UNKOWN.name }) }
                .recordResult(span)
                .foldResponse()
        }
    }

    suspend fun dropVirtualClinic(
        channelId: String,
        request: QuitVideoCallRequest
    ) = span("dropVirtualClinic") { span ->
        currentUid().toUUID().let { personId ->
            span.setAttribute("person_id", personId)
            span.setAttribute("channel_id", channelId)
            span.setAttribute("reject_reason", request.rejectVideoCallReason)

            when (request.type) {
                QuitVirtualClinicType.OFF_QUEUE ->
                    virtualClinicService.cancelVirtualClinic(channelId, request.rejectVideoCallReason)
                QuitVirtualClinicType.DECLINE_VIDEO_CALL ->
                    virtualClinicService.denyVirtualClinicVideoCall(channelId, request.rejectVideoCallReason)
            }
                .recordResult(span)
                .foldResponse()
        }
    }

    private suspend fun quitVideoCall(
        videoCallId: UUID,
        rejectVideoCallReason: RejectVideoCallReason = RejectVideoCallReason.UNKOWN
    ) = span("quitVideoCall") { span ->
        currentUid().toUUID().let { personId ->
            span.setAttribute("person_id", personId)
            span.setAttribute("video_call_id", videoCallId)

            videoCallService.removeAttendeeFromMeeting(
                videoCallId = videoCallId,
                userId = personId,
                rejectVideoCallReason = rejectVideoCallReason
            )
                .map { VideoCallConverter.convert(it, trackEvents = isMemberVideoCallTrackingEnabled()) }
                .recordResult(span)
                .foldResponse()
        }
    }

}

object VideoCallConverter {
    fun convert(source: VideoCallMeeting, staff: Staff? = null, trackEvents: Boolean) =
        VideoCallMeetingResponse(
            id = source.videoCall.id,
            name = source.videoCall.type.description,
            type = source.videoCall.type,
            status = source.videoCall.status,
            amazonChimeConfig = source.meeting?.let { meeting ->
                AmazonChimeConfig(
                    meetingResponse = MeetingResponse(MeetingConverter.convert(meeting, source.videoCall.id)),
                    attendeeResponse = source.attendee?.let { AttendeeResponse(AttendeeConverter.convert(it)) },
                    trackEvents = trackEvents
                )
            },
            healthProfessional = staff?.let {
                HealthProfessionalGenderConverter.convert(it)
            }
        )
}

object MeetingConverter {
    fun convert(source: AwsMeeting, videoCallId: UUID) =
        Meeting(
            meetingId = source.meetingId().toUUID(),
            externalMeetingId = videoCallId,
            mediaRegion = source.mediaRegion(),
            mediaPlacement = MediaPlacement(
                audioHostUrl = source.mediaPlacement().audioHostUrl(),
                audioFallbackUrl = source.mediaPlacement().audioFallbackUrl(),
                screenDataUrl = source.mediaPlacement().screenDataUrl(),
                screenSharingUrl = source.mediaPlacement().screenSharingUrl(),
                screenViewingUrl = source.mediaPlacement().screenViewingUrl(),
                signalingUrl = source.mediaPlacement().signalingUrl(),
                turnControlUrl = source.mediaPlacement().turnControlUrl()
            )
        )
}

object AttendeeConverter {
    fun convert(source: AwsAttendee) =
        Attendee(
            attendeeId = source.attendeeId().toUUID(),
            externalUserId = source.externalUserId().toUUID(),
            joinToken = source.joinToken()
        )
}

data class QuitVideoCallRequest(
    val rejectVideoCallReason: RejectVideoCallReason,
    val type: QuitVirtualClinicType = QuitVirtualClinicType.DECLINE_VIDEO_CALL
)

enum class QuitVirtualClinicType {
    OFF_QUEUE,
    DECLINE_VIDEO_CALL
}
