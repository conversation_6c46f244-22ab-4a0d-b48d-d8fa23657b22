package br.com.alice.member.api.controllers

import br.com.alice.authentication.currentUserId
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.toResponse
import br.com.alice.member.api.converters.LeagueMigration
import java.util.UUID

class LeagueMigrationController : Controller() {
    suspend fun playlist(): Response =
        LeagueMigration.build(currentUserId().toPersonId()).toResponse()
}

data class LeagueMigrationResponse(
    val person: PersonMigration,
    val pageTitle: String,
    val playlist: List<PlaylistItem>
)

data class PersonMigration(
    val id: UUID
)

data class PlaylistItem(
    val id: UUID,
    val order: Int,
    val video: Video,
    val page: Page
)

data class Video(
    val title: String,
    val duration: Long,
    val thumbnail: String,
    val youtubeId: String,
)

data class Page(
    val title: String,
    val subtitle: String? = null,
    val contents: List<PageContent>
)

data class PageContent(
    val title: String? = null,
    val content: String,
)
