package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.models.AuthResponse
import br.com.alice.member.api.models.SignInRequest
import br.com.alice.member.api.models.SignInResponseWithRefreshAndIdTokens
import br.com.alice.member.api.services.AuthService
import br.com.alice.member.api.services.FirebaseClient
import br.com.alice.membership.client.LoginNotFoundException
import br.com.alice.membership.client.LoginNotFoundForGASException
import com.github.kittinunf.result.map

class AuthControllerV2(
    private val authService: AuthService,
    private val firebaseClient: FirebaseClient,
) : Controller() {
    suspend fun signInAllowsMultipleLogin(credentials: SignInRequest): Response {
        val response = validateSignIn(credentials)

        if (response != null) {
            val firebaseResponse = getSignInFirebase(response.customToken).get()

            return SignInResponseWithRefreshAndIdTokens(
                customToken = response.customToken,
                idToken = firebaseResponse.idToken,
                refreshToken = firebaseResponse.refreshToken,
                personId = response.person.id.id,
                name = response.person.socialFirstName ?: response.person.socialName ?: response.person.nickName
                ?: response.person.firstName,
                profileImageUrl = if (response.person.profilePicture != null)
                    response.person.profilePicture?.url
                else null
            ).toResponse()
        }
        if (gasB2CAllowedMembers(credentials.nationalId)) {
            logger.error(
                "AuthControllerV2::signInAllowsMultipleLogin failure gas b2c is not allowed",
                "id" to credentials.nationalId
            )
            throw LoginNotFoundForGASException("Invalid credentials")
        } else {
            logger.error("AuthControllerV2::signInAllowsMultipleLogin failure login not found", "id" to credentials.nationalId)
            throw LoginNotFoundException("Invalid credentials")
        }
    }

    private suspend fun getSignInFirebase(accessToken: String) =
        firebaseClient.getSignInTokens(accessToken)
            .thenError {
                logger.error("AuthControllerV2::getSignInFirebase failure", it)
            }

    private suspend fun validateSignIn(credentials: SignInRequest): AuthResponse =
        authService.authenticateByNationalIdAndGetPerson(
            nationalId = credentials.nationalId,
            oneTimePassword = credentials.otp
        )
            .thenError {
                logger.error("AuthControllerV2::validateSignIn failure", "id" to credentials.nationalId, it)
            }
            .map { AuthResponse(it.customToken, it.person) }.get()

    private fun gasB2CAllowedMembers(nationalId: String) =
        FeatureService.inList(
            namespace = FeatureNamespace.ALICE_APP,
            key = "gas_b2c_allowed_members",
            testValue = nationalId,
            defaultReturn = false
        )
}
