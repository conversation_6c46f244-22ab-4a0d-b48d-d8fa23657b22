package br.com.alice.member.api.controllers

import M
import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.app.content.client.BottomTabsService
import br.com.alice.app.content.model.BottomTabsTransport
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenType
import br.com.alice.authentication.currentUserId
import br.com.alice.bottini.client.OpportunityService
import br.com.alice.business.client.BeneficiaryOnboardingService
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toLocalDateFromBrazilianFormat
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapOfNotNull
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.MemberAppVersioning
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.redis.Result
import br.com.alice.data.layer.models.BeneficiaryOnboarding
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Member
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonOnboarding
import br.com.alice.data.layer.models.TermType
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.builders.PersonMissingDataNavigationBuilder
import br.com.alice.member.api.converters.appContent.BottomTabsResponseConverter
import br.com.alice.member.api.metrics.Metrics
import br.com.alice.member.api.models.AppMessage
import br.com.alice.member.api.models.Link
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.Navigation
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.SessionResponse
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.services.FeaturePreferences
import br.com.alice.member.api.services.FeaturePreferencesNotifier
import br.com.alice.member.api.services.OnboardingMobileRouter
import br.com.alice.member.api.services.SessionNavigationService
import br.com.alice.member.api.usecases.ValidatePreActivatedMemberUseCase
import br.com.alice.membership.client.DeviceService
import br.com.alice.membership.client.MemberContractTermService
import br.com.alice.membership.client.PersonPreferencesService
import br.com.alice.membership.client.onboarding.OnboardingService
import br.com.alice.onboarding.client.InsurancePortabilityService
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.HttpStatusCode.Companion.Unauthorized
import io.opentelemetry.api.trace.Span
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime

class SessionsController(
    private val deviceService: DeviceService,
    private val onboardingService: OnboardingService,
    private val sessionNavigationService: SessionNavigationService,
    private val portabilityService: InsurancePortabilityService,
    private val personService: PersonService,
    private val memberService: MemberService,
    private val memberContractTermService: MemberContractTermService,
    private val personPreferencesService: PersonPreferencesService,
    private val bottomTabsService: BottomTabsService,
    private val actionPlanTaskService: ActionPlanTaskService,
    private val cache: GenericCache,
    private val appContentScreenDetailService: AppContentScreenDetailService,
    private val opportunityService: OpportunityService,
    private val validatePreActivatedMemberUseCase: ValidatePreActivatedMemberUseCase,
    private val beneficiaryOnboardingService: BeneficiaryOnboardingService,
) : Controller() {

    private val pendingB2BMembershipNavigation = NavigationResponse(
        mobileRoute = MobileRouting.CHESHIRE_SCREEN,
        properties = mapOf(
            "action" to RemoteAction(
                method = RemoteActionMethod.GET,
                endpoint = ServiceConfig.url("/beneficiaries/screen/activation_home"),
                params = mapOf("screen_id" to ScreenType.ACTIVATION_HOME)
            )
        )
    )

    private val preActivatedB2BNavigation = NavigationResponse(
        mobileRoute = MobileRouting.HOME,
    )

    private val finishMembershipActivationNavigation = NavigationResponse(
        mobileRoute = MobileRouting.B2B_ACTIVATION,
        properties = mapOf("navigate_to_route_on_complete" to true)
    )

    private fun getPhoneNumber(brand: Brand) = when (brand) {
        Brand.DUQUESA -> Link(href = getAdministrativePhone(), rel = "phone_number")
        else -> Link(href = MobileApp.Links.PHONE_NUMBER, rel = "phone_number")
    }

    private suspend fun staticMessages() = listOf(
        AppMessage(M.TITLE_OUVIDORIA, i18n(M.TITLE_OUVIDORIA)),
        AppMessage(M.CONTENT_OUVIDORIA, i18n(M.CONTENT_OUVIDORIA))
    )

    suspend fun start(): Response = span("start") { span ->
        coroutineScope {
            val personId = PersonId.fromString(currentUid())
            val (person, personOnboarding, activeOrPendingMembership) = fetchUserData(personId)

            if (shouldReturnUnauthorized(activeOrPendingMembership, person, span))
                return@coroutineScope Response(Unauthorized)

            val session = initializeSession(person, personOnboarding, activeOrPendingMembership, span)

            Response(
                OK,
                session
            )
        }
    }

    suspend fun beforeSession(): Response {
        val newSessionId = RangeUUID.generate().toString()

        logger.info(
            "unauthenticated_session_started",
            "user_agent" to userAgent(),
            "session_id" to newSessionId
        )

        return Response(
            OK, SessionResponse(
                id = newSessionId,
                messages = staticMessages(),
                links = staticLinks()
            )
        )
    }

    private suspend fun initializeSession(
        person: Person,
        personOnboarding: PersonOnboarding?,
        activeOrPendingMembership: Member?,
        span: Span,
    ): SessionResponse = coroutineScope {

        // Retrieving additional information
        val personId = PersonId.fromString(currentUid())
        val portabilityDeferred = async { portabilityService.findByPerson(personId).getOrNullIfNotFound() }
        val crmKeyDeferred =
            async { sessionNavigationService.getCrmKey(person.nationalId).getOrNullIfNotFound() }

        //Session properties
        val newSessionId = RangeUUID.generate().toString()
        val deviceId = fcmToken()
        val voipToken = voipToken()
        val userAgent = userAgent()
        val appVersion = parseAppVersion(userAgent, span)
        val activeMembership = activeOrPendingMembership?.takeIf { it.active }
        val brand = activeMembership?.brand ?: Brand.ALICE
        val portability = portabilityDeferred.await()
        val portabilityRequested = portability != null
        val crmKey = crmKeyDeferred.await()
        val bottomTabs = fetchBottomTabs(person, activeOrPendingMembership, appVersion)

        logSessionAttributes(span, newSessionId, personId, deviceId, voipToken, userAgent, appVersion)
        updateDeviceInfo(personId, deviceId, appVersion, voipToken, span)

        val navigation = determineNavigation(
            person,
            activeOrPendingMembership,
            personOnboarding,
            portabilityRequested,
            appVersion
        )

        logger.info(
            "SessionsController::start - navigation",
            "navigation" to navigation.mobileRoute.toString(),
            "membership_id" to activeMembership?.id.toString(),
            "membership_active" to activeMembership?.active.toString(),
            "person_id" to personId
        )

        // check overdue and expired member tasks
        tryUpdateExpiredAndOverdueTasks(personId)
        // check overdue member health forms
        tryUpdateOverdueHomeForms(personId)

        Metrics.incrementStartSession(brand)

        span.setAttribute("navigation", navigation.mobileRoute.toString())
        span.setAttribute("brand", brand.toString())

        buildSessionResponse(
            newSessionId,
            brand,
            navigation,
            crmKey,
            bottomTabs,
            shouldForceNavigation(navigation)
        )
    }

    private suspend fun fetchBottomTabs(
        person: Person,
        activeMembership: Member?,
        appVersion: SemanticVersion?
    ): List<BottomTabsTransport> {
        val bottomTabs = bottomTabsService.get(person, activeMembership, appVersion).get()
        return BottomTabsResponseConverter.convert(bottomTabs)
    }

    private suspend fun updateDeviceInfo(
        personId: PersonId,
        deviceId: String?,
        appVersion: SemanticVersion?,
        voipToken: String?,
        span: Span
    ) {
        deviceId?.let {
            try {
                deviceService.upsertDevice(personId, it, appVersion?.toString(), voipToken)
            } catch (ex: Exception) {
                span.recordException(ex)
                logger.error("Error upserting device", "deviceId" to deviceId)
            }
        }
    }

    private fun parseAppVersion(userAgent: String, span: Span): SemanticVersion? {
        return runCatching { MemberAppVersioning(userAgent).version }
            .onFailure { ex ->
                span.recordException(ex)
                logger.error("Error parsing app version", "userAgent" to userAgent)
            }.getOrNull()
    }

    private fun logSessionAttributes(
        span: Span,
        sessionId: String,
        personId: PersonId,
        deviceId: String?,
        voipToken: String?,
        userAgent: String,
        appVersion: SemanticVersion?,
    ) {
        span.setAttribute("session_id", sessionId)
        span.setAttribute("person_id", personId.toString())
        span.setAttribute("device_id", deviceId.toString())
        span.setAttribute("voip_token", voipToken.toString())
        span.setAttribute("user_agent", userAgent)
        span.setAttribute("app_version", appVersion.toString())
    }

    private suspend fun fetchUserData(personId: PersonId): Triple<Person, PersonOnboarding?, Member?> = coroutineScope {
        val personDeferred = async { personService.get(personId).get() }
        val activeOrPendingMembershipDeferred = async {
            memberService.findActiveOrPendingMembership(personId).getOrNullIfNotFound()
        }
        val personOnboardingDeferred = async {
            onboardingService.findByPerson(personId).getOrNullIfNotFound()
        }

        Triple(
            personDeferred.await(),
            personOnboardingDeferred.await(),
            activeOrPendingMembershipDeferred.await()
        )
    }

    private suspend fun shouldReturnUnauthorized(
        activeOrPendingMembership: Member?,
        person: Person,
        span: Span
    ): Boolean {
        logger.info(
            "SessionController:shouldReturnUnauthorized",
            "active_or_pending_membership_id" to activeOrPendingMembership?.id,
            "person_product_type" to person.productInfo?.productType,
        )
        val userAgent = userAgent()
        val appVersion = parseAppVersion(userAgent, span)
        val brokenAppVersion = "4.41.0"

        val shouldValidateBrokenAppVersion = FeatureService.get(
            FeatureNamespace.ALICE_APP,
            "should_validate_broken_app_version",
            true
        )

        //TODO: REMOVE THIS AFTER FIX ON CHESIRE
        if(shouldValidateBrokenAppVersion && activeOrPendingMembership?.isPending == true && appVersion?.version == brokenAppVersion)
            return true

        if (activeOrPendingMembership?.active == true)
            return false

        if (activeOrPendingMembership?.isB2BOrAdesao == true) return false

        val personProductInfo = person.productInfo ?: return false

        if (personProductInfo.isB2B() && activeOrPendingMembership == null) return true //TODO: Review when B2C is selling again due to rejoin b2b -> b2c

        return !checkElligibleOpportunity(person) && !gasB2CAllowedMembers(
            person.nationalId
        )
    }

    private fun gasB2CAllowedMembers(nationalId: String) =
        FeatureService.inList(
            namespace = FeatureNamespace.ALICE_APP,
            key = "gas_b2c_allowed_members",
            testValue = nationalId,
            defaultReturn = false
        )

    private suspend fun checkElligibleOpportunity(person: Person) =
        person.opportunityId?.let { opportunityId ->
            opportunityService.get(opportunityId).map { opportunity ->
                !opportunity.expired
            }.get()
        } ?: false

    private suspend fun buildSessionResponse(
        newSessionId: String,
        brand: Brand,
        navigation: Navigation,
        crmKey: String?,
        bottomTabs: List<BottomTabsTransport>,
        forceNavigation: Boolean = false,
    ) = SessionResponse(
        id = newSessionId,
        messages = staticMessages(),
        links = staticLinks(brand),
        navigation = navigation,
        crmKey = crmKey,
        bottomTabs = bottomTabs,
        personBrand = brand,
        forceNavigation = forceNavigation,
        initialTab = setInitialTab(bottomTabs)
    )

    suspend fun determineNavigation(
        person: Person,
        activeOrPendingMembership: Member?,
        personOnboarding: PersonOnboarding?,
        portabilityRequested: Boolean,
        appVersion: SemanticVersion?,
    ): Navigation {

        val onboarding = getOnboarding(activeOrPendingMembership)

        val notFinishedNoRiskFlowActivation = onboarding?.flowType == BeneficiaryOnboardingFlowType.NO_RISK_FLOW
                && onboarding.currentPhase?.phase != BeneficiaryOnboardingPhaseType.FINISHED

        // Return navigation for active memberships with activation processes completed - B2B and B2C
        if (activeOrPendingMembership?.active == true && !notFinishedNoRiskFlowActivation)
            return postMemberActivationNavigation(activeOrPendingMembership, person, appVersion)

        val b2bActivationNavigation = getB2BActivationNavigationIfNeeded(
            activeOrPendingMembership,
            appVersion,
            notFinishedNoRiskFlowActivation,
            onboarding
        )

        // Return B2B activation navigation for pending memberships
        if (b2bActivationNavigation != null) {
            return b2bActivationNavigation
        }

        // Return navigation for pending B2C membership
        return if (personOnboarding == null) {
            logger.info(
                "SessionsController::getNavigation - getMissingDataNavigation",
                "person" to person.id
            )
            getMissingDataNavigation(person)
        } else {
            sessionNavigationService
                .getOnboardingPhase(person, personOnboarding)
                .let {
                    val personPreferences = personPreferencesService.findByPersonId(person.id).getOrNullIfNotFound()
                    OnboardingMobileRouter.getNavigation(
                        person = person,
                        phase = it,
                        portabilityRequested = portabilityRequested,
                        preferences = personPreferences,
                    )
                }
        }
    }

    private suspend fun getOnboarding(member: Member?) =
        if (member?.isB2BOrAdesao == true && member.beneficiaryId != null) {
            beneficiaryOnboardingService.findByBeneficiaryId(member.beneficiaryId!!)
                .getOrNullIfNotFound()
        } else null

    private suspend fun getB2BActivationNavigationIfNeeded(
        activeOrPendingMembership: Member?,
        appVersion: SemanticVersion?,
        isNotFinishedNoRiskB2BMembership: Boolean,
        onboarding: BeneficiaryOnboarding?,
    ): Navigation? {

        if (activeOrPendingMembership == null) {
            return null
        }

        val minimumAppVersionActivationHome = FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "minimum_app_version_activation_home",
            "5.0.0"
        )

        logger.info(
            "SessionsController::shouldRedirectToB2BActivation",
            "minimumAppVersionActivationHome" to minimumAppVersionActivationHome,
            "appVersion" to appVersion.toString(),
            "membershipIsPending" to activeOrPendingMembership.isPending,
            "membershipIsB2B" to activeOrPendingMembership.isB2B,
        )

        if (appVersion == null || (appVersion < SemanticVersion(minimumAppVersionActivationHome))) {
            return null
        }

        val isFullRiskFlow = onboarding?.flowType == BeneficiaryOnboardingFlowType.FULL_RISK_FLOW
        val isPendingB2BMembership = isPendingB2BMembership(activeOrPendingMembership)

        val shouldGoToPreActivationHome = isPendingB2BMembership && shouldGoToPreActivationHome(activeOrPendingMembership, appVersion)

        return when {
            isPendingB2BMembership && isFullRiskFlow && !shouldGoToPreActivationHome ->
                pendingB2BMembershipNavigation
            isPendingB2BMembership && shouldGoToPreActivationHome ->
                preActivatedB2BNavigation
            isNotFinishedNoRiskB2BMembership ->
                finishMembershipActivationNavigation
            else -> null
        }
    }

    private suspend fun shouldGoToPreActivationHome(activeOrPendingMembership: Member?, appVersion: SemanticVersion) =
        validatePreActivatedMemberUseCase.isPreActivatedMemberAndOnWaitingForReviewPhase(
            activeOrPendingMembership,
            appVersion
        )

    private fun isPendingB2BMembership(activeOrPendingMembership: Member?): Boolean =
        activeOrPendingMembership?.isPending == true && activeOrPendingMembership.isB2BOrAdesao


    // Return if navigation should be forced to display on app
    private fun shouldForceNavigation(navigation: Navigation) =
        navigation == pendingB2BMembershipNavigation ||
        navigation == finishMembershipActivationNavigation ||
        navigation == preActivatedB2BNavigation ||
        navigation.mobileRoute == MobileRouting.WELCOME

    private suspend fun postMemberActivationNavigation(
        activeMembership: Member,
        person: Person,
        appVersion: SemanticVersion?
    ) = when {
        shouldDirectToB2BTerms(activeMembership) -> NavigationResponse(mobileRoute = MobileRouting.B2B_TERMS)
        shouldRedirectToWelcomeScreen(activeMembership, appVersion) -> buildNavigateWelcomeScreen(person, activeMembership)
        else -> NavigationResponse(mobileRoute = MobileRouting.HOME)
    }

    private fun buildNavigateWelcomeScreen(
        person: Person,
        activeMembership: Member
    ): NavigationResponse = NavigationResponse(
        mobileRoute = MobileRouting.WELCOME,
        params = mapOfNotNull(
            "first_name" to person.firstName,
            "last_name" to person.lastName,
            person.profilePicture?.url?.let { "image_url" to it },
            activeMembership.selectedProduct.priceListing?.title?.let { "product" to it }
        )
    )

    private fun shouldRedirectToWelcomeScreen(member: Member, appVersion: SemanticVersion?) : Boolean {
        if (appVersion == null || appVersion < minimumAppVersionForWelcomeScreen()) return false

        val activationDate = member.activationDate?.toLocalDate() ?: return false
        if (activationDate.isBefore(minimumActivateDateForWelcomeScreen())) return false

        val preferences = FeaturePreferencesNotifier.getFeaturePreferences(member.personId, FeaturePreferences.WELCOME_SCREEN)
        return if (preferences == null) {
            FeaturePreferencesNotifier.updateFeaturePreferences(member.personId, FeaturePreferences.WELCOME_SCREEN, mapOf("show" to true))
            true
        } else (preferences["show"] as? Boolean) == true

    }

    private fun minimumAppVersionForWelcomeScreen() =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "minimum_app_version_for_show_welcome_screen",
            defaultValue = "99.0.0"
        ).let { SemanticVersion(it) }

    private fun minimumActivateDateForWelcomeScreen(): LocalDate =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "minimum_activation_date_for_show_welcome_screen",
            defaultValue = "31/12/2040"
        ).toLocalDateFromBrazilianFormat()


    private fun getMissingDataNavigation(person: Person) =
        PersonMissingDataNavigationBuilder.buildNavigation(MobileRouting.GAS_INIT, person)

    private suspend fun shouldDirectToB2BTerms(activeMembership: Member?): Boolean {
        return activeMembership?.let { checkIfMemberHasPendingTermsToSign(activeMembership) } ?: false
    }

    private suspend fun checkIfMemberHasPendingTermsToSign(member: Member): Boolean =
        if (member.isB2B) {
            memberContractTermService.findByMemberIdAndTermType(member.id, TermType.DATA_PROCESSING_TERMS)
                .map { it.isEmpty() }
                .get()
        } else {
            false
        }

    private suspend fun tryUpdateExpiredAndOverdueTasks(personId: PersonId) {
        val key = "member:${personId.id}:check-tasks"
        val cacheTTL = hoursInSecondsUntilMidnight()
        cache.get(
            key = key,
            type = Boolean::class,
            expirationTime = cacheTTL
        ) {
            updateExpiredAndOverdueTasks()
        }
    }

    private suspend fun tryUpdateOverdueHomeForms(personId: PersonId) {
        val key = "member:${personId.id}:check-health-forms"
        val cacheTTL = hoursInSecondsUntilMidnight()
        cache.get(
            key = key,
            type = Boolean::class,
            expirationTime = cacheTTL,
            callbackWithResult = this::updateOverdueHomeForms,
        ) {
            true
        }
    }


    private fun hoursInSecondsUntilMidnight(): Long {
        val currentHour = LocalDateTime.now().toSaoPauloTimeZone().hour
        val differenceUntilMidnight = (24 - currentHour)
        logger.info(
            "Start session - getting task check Redis TTL",
            "current_hour" to currentHour,
            "midnight_difference" to differenceUntilMidnight
        )
        return differenceUntilMidnight * 60 * 60L
    }

    private suspend fun updateExpiredAndOverdueTasks() =
        currentUserId().toPersonId().let { personId ->
            if (enableUpdateExpiredAndOverdueTasks()) {
                expireTasks(personId)
                overdueTasks(personId)
                true
            } else {
                false
            }
        }

    private suspend fun overdueTasks(personId: PersonId) =
        if (shouldUseNewCalculationDeadlineFlow())
            emptyList()
        else
            actionPlanTaskService.markTasksAsOverdue(personId).getOrElse {
                logger.error(
                    "Start session - error trying to overdue tasks",
                    "person_id" to personId,
                    it
                )
                emptyList()
            }

    private suspend fun expireTasks(personId: PersonId) =
        actionPlanTaskService.markTasksAsExpired(personId).getOrElse {
            logger.error(
                "Start session - error trying to expire tasks",
                "person_id" to personId,
                it
            )
            emptyList()
        }

    private suspend fun updateOverdueHomeForms(result: Result) =
        when (result) {
            Result.SUCCESS -> {
                val personId = currentUserId().toPersonId()
                logger.info(
                    "Start session - check for overdue home forms already done",
                    "person_id" to personId
                )
                true
            }

            Result.FAILURE -> {
                val personId = currentUserId().toPersonId()
                logger.info(
                    "Start session - doing check for overdue home forms",
                    "person_id" to personId
                )
                if (enableUpdateExpiredAndOverdueTasks()) {
                    val homeFormsOverdue = appContentScreenDetailService.overdueAllHomeFormsFromPersonId(personId).get()
                    logger.info(
                        "Start session - check for overdue home forms done",
                        "person_id" to personId,
                        "home_forms_overdue" to homeFormsOverdue.size,
                    )
                }
                true
            }
        }

    private fun enableUpdateExpiredAndOverdueTasks() =
        FeatureService.get(
            FeatureNamespace.ALICE_APP,
            "enable_update_expired_and_overdue_tasks",
            false
        )
    private fun staticLinks(brand: Brand = Brand.ALICE): List<Link> {
        val gasHelpWhatsappNumber =
            FeatureService.get(FeatureNamespace.ALICE_APP, "gas_help_whatsapp_number", "5511940646175")
        return listOf(
            Link(href = MobileApp.Links.TERMS, rel = "terms_of_use"),
            Link(href = ServiceConfig.signUpUrl, rel = "wait_list"), // TODO: Remove after app move to `signup`
            Link(href = ServiceConfig.signUpUrl, rel = "signup"),
            Link(href = MobileApp.Links.FAQ, rel = "faq"),
            Link(href = MobileApp.Links.LANDING_PAGE, rel = "landing_page"),
            Link(href = MobileApp.Links.NATIONAL_COVERAGE, rel = "national_coverage"),
            Link(href = "${MobileApp.Links.GAS_HELP}$gasHelpWhatsappNumber", rel = "gas_help"),
            getPhoneNumber(brand),
            Link(href = getAdministrativePhone(), rel = "duquesa_phone_number"),
            Link(href = MobileApp.Links.PHONE_NUMBER, rel = "alice_phone_number")
        )
    }

    private fun getAdministrativePhone(): String {
        return FeatureService.get(FeatureNamespace.ALICE_APP, "duquesa_alice_cx_phone", "")
    }

    private fun setInitialTab(bottomTabs: List<BottomTabsTransport>): String? =
        bottomTabs.firstOrNull { it.selected == true }?.id

    private fun shouldUseNewCalculationDeadlineFlow() =
        FeatureService.get(
            namespace = FeatureNamespace.HEALTH_PLAN,
            key = "should_use_new_health_plan_task_deadline_calculation_flow",
            defaultValue = false
        )
}
