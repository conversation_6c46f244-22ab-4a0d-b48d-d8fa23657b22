package br.com.alice.member.api.controllers

import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.MessageType
import br.com.alice.channel.models.Origin
import br.com.alice.channel.models.SendMessageRequest
import br.com.alice.common.Brand
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.featureaccess.Feature
import br.com.alice.common.featureaccess.features.FaqFeedbackFeatureResource
import br.com.alice.common.foldResponse
import br.com.alice.data.layer.models.FaqFeedback
import br.com.alice.data.layer.models.FaqGroupType
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.ProductInfo
import br.com.alice.data.layer.models.RefundType
import br.com.alice.data.layer.models.TierType
import br.com.alice.member.api.FeatureAccess
import br.com.alice.member.api.builders.FaqGroupsResponseBuilder
import br.com.alice.member.api.converters.FaqFeedbackResponseConverter
import br.com.alice.member.api.models.FaqFeedbackRequest
import br.com.alice.member.api.models.FaqFeedbackTextRequest
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.currentMemberAppVersion
import br.com.alice.person.client.PersonService
import br.com.alice.questionnaire.client.FaqContentService
import br.com.alice.questionnaire.client.FaqFeedbackService
import br.com.alice.questionnaire.client.FaqGroupService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map

class FaqController(
    private val faqGroupService: FaqGroupService,
    private val faqContentService: FaqContentService,
    private val faqFeedbackService: FaqFeedbackService,
    private val channelService: ChannelService,
    private val personService: PersonService,
) : Controller() {

    suspend fun listFaqGroups(): Response = getPerson().flatMap { person ->
        faqGroupService.findByTypes(getGroupType(person.productInfo)).flatMapPair { groups ->
            faqContentService.findByGroupIds(groups.map { it.id })
        }.map { (faqContents, faqGroups) ->
            FaqGroupsResponseBuilder.buildListFaqGroups(faqGroups, faqContents)
        }
    }.foldResponse()

    private fun getGroupType(productInfo: ProductInfo?): List<FaqGroupType> =
        buildList {
            if (productInfo?.refund == RefundType.FULL) add(FaqGroupType.REFUND_FULL)

            if (productInfo?.tier == TierType.TIER_1) add(FaqGroupType.TIER_1)

            when (productInfo?.brand) {
                Brand.ALICE -> add(FaqGroupType.ALICE)
                Brand.DUQUESA -> add(FaqGroupType.DUQUESA)
                Brand.ALICE_DUQUESA -> add(FaqGroupType.ALICE_DUQUESA)
                else -> add(FaqGroupType.ALICE)
            }
        }

    suspend fun saveFaqFeedback(request: FaqFeedbackRequest): Response =
        getPerson().map { person ->
            val brand = person.productInfo?.brand ?: Brand.ALICE

            val sendFeedbackFeatureAccessResult = getSaveFeedbackFeatureAccessResult(request.useful)

            val faqFeedback = faqFeedbackService.add(
                FaqFeedback(
                    faqContentId = request.faqContentId,
                    useful = request.useful,
                    personId = if (request.anonymous) null else currentUid().toPersonId()
                )
            ).get()

            FaqFeedbackResponseConverter.convert(
                faqFeedback = faqFeedback,
                featureNavigation = sendFeedbackFeatureAccessResult.featureNavigation!!,
                feedbackMessage = sendFeedbackFeatureAccessResult.response?.feedbackMessage!!,
                brand = brand,
            )
        }.foldResponse()

    private suspend fun getPerson(): Result<Person, Throwable> =
        personService.get(currentUid().toPersonId())

    private suspend fun getSaveFeedbackFeatureAccessResult(useful: Boolean) =
        FeatureAccess.filter(
            feature = Feature.FAQ_FEEDBACK,
            resource = FaqFeedbackFeatureResource(useful = useful)
        )

    suspend fun saveFaqFeedbackText(id: String, request: FaqFeedbackTextRequest): Response {
        val personId = currentUid().toPersonId()
        var faqMessageRequest = SendMessageRequest(
            type = MessageType.TEXT,
            content = request.feedback,
            origin = Origin.HELP_CENTER,
            appVersion = currentMemberAppVersion().toString()
        )

        return faqContentService.get(id.toUUID())
            .map {
                faqMessageRequest = faqMessageRequest.copy(chatName = it.title)
                FaqFeedback(faqContentId = it.id, useful = false, feedback = request.feedback, personId = personId)
            }
            .flatMap { faqFeedbackService.add(it) }
            .flatMap { channelService.sendMessage(personId, faqMessageRequest) }
            .map {
                NavigationResponse(
                    mobileRoute = MobileRouting.CHANNEL,
                    properties = mapOf("channel_id" to it)
                )
            }.foldResponse()
    }
}
