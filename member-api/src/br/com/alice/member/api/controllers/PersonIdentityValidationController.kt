package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.MemberAppVersioning
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.converters.PersonIdentityValidationConverter
import br.com.alice.member.api.models.IdentityValidationDistributionPath
import br.com.alice.member.api.models.ShouldValidateIdentityResponse
import br.com.alice.member.api.models.IdentityValidationRequest
import br.com.alice.person.client.PersonIdentityValidationService
import com.github.kittinunf.result.map

class PersonIdentityValidationController(
    private val personIdentityValidationService: PersonIdentityValidationService,
    private val trackPersonABService: TrackPersonABService
) : Controller() {

    suspend fun getLastValidationResult(): Response {
        val currentPersonId = currentUid()

        val personId = currentPersonId.toPersonId()

        return personIdentityValidationService.getLastValidationResultByPersonId(personId)
            .map { PersonIdentityValidationConverter.convert(it) }
            .foldResponse()
    }

    suspend fun shouldValidateIdentity(): Response {
        val personId = currentUid().toPersonId()
        val appVersion = MemberAppVersioning(userAgent()).version

        val isIdentityValidationEnabled = FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "min_app_version_identity_validation",
            "5.0.0"
        ).let { appVersion >= SemanticVersion(it) }

        val skipIdentityValidationForPerson = FeatureService.inList(
            FeatureNamespace.MEMBERSHIP,
            "identity_validation_person_skip_list",
            personId.toString(),
            false
        )

        val shouldLogIdentityValidationAccessToken = FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "should_log_identity_validation_access_token",
            false
        )

        val shouldValidateIdentity = isIdentityValidationEnabled && !skipIdentityValidationForPerson && isPersonInABTest(personId)

        var transactionId: String? = null
        var accessToken: String? = null

        if (shouldValidateIdentity) {
            transactionId = personIdentityValidationService.createTransaction(personId).get()
            accessToken = personIdentityValidationService.getAccessToken().get()
        }

        logger.info(
            "PersonIdentityValidationController::shouldValidateIdentity",
            "app_version" to appVersion.version,
            "is_identity_validation_enabled" to isIdentityValidationEnabled,
            "skip_identity_validation_for_person" to skipIdentityValidationForPerson,
            "transaction_id" to transactionId,
            "access_token" to accessToken.takeIf { shouldLogIdentityValidationAccessToken },
        )

        return ShouldValidateIdentityResponse(shouldValidateIdentity, transactionId, accessToken).toResponse()
    }

    private suspend fun isPersonInABTest(personId: PersonId): Boolean {
        val abTest = trackPersonABService.findOrStartAb(
            personId = personId,
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "identity_validation_distribution",
            defaultPath = IdentityValidationDistributionPath.WITHOUT_VALIDATION.name
        ).fold(
            { IdentityValidationDistributionPath.fromString(it.abPath) },
            { IdentityValidationDistributionPath.WITHOUT_VALIDATION }
        )

        return abTest == IdentityValidationDistributionPath.WITH_VALIDATION
    }

    suspend fun requestValidation(request: IdentityValidationRequest): Response {
        val personId = currentUid().toPersonId()

        return personIdentityValidationService.requestValidation(personId, request.transactionId, request.selfie).foldResponse()
    }
}
