package br.com.alice.member.api.controllers

import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.member.api.models.NewAccessCodeRequest
import br.com.alice.member.api.models.PersonLoginResponse
import br.com.alice.member.api.models.SignInRequest
import br.com.alice.member.api.models.SignInResponse
import br.com.alice.member.api.services.AuthService
import br.com.alice.membership.client.LoginNotFoundException
import br.com.alice.membership.client.PersonDuquesaLoginException
import com.github.kittinunf.result.map
import kotlin.reflect.KClass

class AuthController(private val authService: AuthService) : Controller() {

    suspend fun generateNewAccessCode(newAccessCodeRequest: NewAccessCodeRequest): Response =
        authService.getPersonAndSendNewAccessCodeByNationalId(newAccessCodeRequest.nationalId)
        .map { PersonLoginResponse(it.contactName, it.obfuscatedEmail, it.contactName, it.obfuscatedPhoneNumber) }
        .foldResponse(
            { it },
            handleNewAccessCodeError()
        )

    //temporary code to deal with some issues on mobile
    suspend fun signIn(credentials: SignInRequest): Response =
        authService.authenticateByNationalId(
            nationalId = credentials.nationalId,
            oneTimePassword = credentials.otp
        ).map { SignInResponse(it) }.foldResponse()

    suspend fun generateToken() = authService.generateToken(currentUid().toPersonId())
        .map { SignInResponse(it) }
        .foldResponse()


    private fun handleNewAccessCodeError(): Pair<KClass<LoginNotFoundException>, (Throwable) -> ErrorResponse> =
        LoginNotFoundException::class to {
            val error = it as LoginNotFoundException
            ErrorResponse(error.code, error.message.orEmpty())
        }
}


