package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.foldNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CampaignPartners
import br.com.alice.data.layer.models.PersonBenefitOptInStatus
import br.com.alice.member.api.models.PartnerCampaignRequest
import br.com.alice.member.api.models.PartnerCampaignResponse
import br.com.alice.member.api.models.PartnerCampaignResponseConverter
import br.com.alice.membership.client.PersonBenefitService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success

class PartnerCampaignController(
    private val personBenefitService: PersonBenefitService
) : Controller() {

    suspend fun optIn(partnerCampaignRequest: PartnerCampaignRequest): Response {
        val personId = currentUid()
        logger.info(
            "PartnerCampaignController::optIn",
            "person_id" to personId,
            "request" to partnerCampaignRequest
        )
        return personBenefitService.optIn(
            partner = partnerCampaignRequest.partner,
            personId = personId.toPersonId(),
            campaignId = partnerCampaignRequest.campaignId
        )
            .map {
                PartnerCampaignResponseConverter.convert(it)
            }
            .then { partnerCampaignResponse ->
                logger.info(
                    "PartnerCampaignController::optIn",
                    "partner_campaign_response" to partnerCampaignResponse
                )
            }
            .foldResponse()
    }

    suspend fun optOut(partnerCampaignRequest: PartnerCampaignRequest): Response {
        val personId = currentUid()
        logger.info(
            "PartnerCampaignController::optOut",
            "person_id" to personId,
            "request" to partnerCampaignRequest
        )
        return personBenefitService.optOut(
            partner = partnerCampaignRequest.partner,
            personId = personId.toPersonId()
        )
            .map {
                PartnerCampaignResponseConverter.convert(it)
            }
            .then { partnerCampaignResponse ->
                logger.info(
                    "PartnerCampaignController::optOut",
                    "partner_campaign_response" to partnerCampaignResponse
                )
            }
            .foldResponse()
    }

    suspend fun getOptInStatus(partner: String): Response {
        val personId = currentUid()
        logger.info("PartnerCampaignController::getOptInStatus", "partner" to partner, "person_id" to personId)
        return personBenefitService.getForPerson(personId.toPersonId(), partner)
            .map {
                PartnerCampaignResponseConverter.convert(it)
            }
            .foldNotFound {
                PartnerCampaignResponse(
                    partner = CampaignPartners.valueOf(partner),
                    status = PersonBenefitOptInStatus.UNKNOWN
                ).success()
            }
            .then { partnerCampaignResponse ->
                logger.info(
                    "PartnerCampaignController::getOptInStatus",
                    "partner_campaign_response" to partnerCampaignResponse
                )
            }
            .foldResponse()
    }
}
