package br.com.alice.member.api.controllers

import br.com.alice.app.content.client.screens.ScreeningScreenService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelCreationParameters
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategory.SCREENING
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Person
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.client.BudNavigationService
import br.com.alice.member.api.converters.appContent.ScreenResponseConverter
import br.com.alice.member.api.models.currentMemberAppVersion
import br.com.alice.member.api.models.requestUserAgent
import br.com.alice.person.client.PersonService
import br.com.alice.screening.client.ScreenDataService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.Parameters
import java.util.UUID

class ScreeningController(
    private val screeningScreenService: ScreeningScreenService,
    private val screenDataService: ScreenDataService,
    private val personService: PersonService,
    private val budNavigationService: BudNavigationService,
) : Controller() {

    companion object {
        private const val HEALTH_SCREENING_START_QUESTION_FOR_PREVIOUS_VERSION = "screening-question-21"
        private const val HEALTH_SCREENING_START_QUESTION = "screening-question-21-b"
        private const val PRESCRIPTION_TRIAGE_START_QUESTION = "screening-question-313"
        private const val ADMINISTRATIVE_TRIAGE_START_QUESTION = "screening-question-314"

        private val initialQuestions = arrayOf(
            HEALTH_SCREENING_START_QUESTION,
            HEALTH_SCREENING_START_QUESTION_FOR_PREVIOUS_VERSION,
            PRESCRIPTION_TRIAGE_START_QUESTION,
            ADMINISTRATIVE_TRIAGE_START_QUESTION
        )
    }

    suspend fun getScreeningQuestion(queryParams: Parameters): Response {
        val questionId = queryParams["question_id"]
        val question = queryParams["question"]
        val answer = queryParams["answer"]
        val screeningNavigationId = queryParams["screening_navigation_id"]
        val channelCreationType = queryParams["channel_creation_type"]
        val personId = currentUid()

        logger.info(
            "ScreeningController::getScreeningQuestion",
            "question_id" to questionId,
            "person_id" to personId,
            "screening_navigation_id" to screeningNavigationId,
            "channel_creation_type" to channelCreationType,
        )

        val appVersion = currentMemberAppVersion().version

        return personService.get(personId.toPersonId()).flatMap { person ->
            val questionIdValue = validateAndGetQuestionId(questionId, person)

            if (initialQuestions.contains(questionIdValue)) {
                val protocolId = getProtocolId(questionIdValue)

                logger.info(
                    "ScreeningController::getScreeningQuestion - Use new triage flow",
                    "question_id" to questionIdValue,
                    "endpoint" to protocolId,
                    "personId" to personId
                )

                return budNavigationService.startNavigation(
                    personId = personId.toPersonId(),
                    nodeId = protocolId.toUUID(),
                    userAgent = requestUserAgent()
                ).foldResponse()
            }

            logger.info(
                "ScreeningController::getScreeningQuestion - Use old triage flow",
                "question_id" to questionIdValue,
            )

            getScreeningScreenFromServer(
                questionIdValue = questionIdValue,
                appVersion = appVersion.version,
                question = question ?: "",
                answer = answer ?: "",
                person = person,
                channelCreationType = channelCreationType,
            ).map { result -> ScreenResponseConverter.convert(result) }
        }.foldResponse()
    }

    suspend fun finishScreeningAndOpenChat(screeningNavigationId: UUID, queryParams: Parameters): Response {
        logger.info(
            "ScreeningController::finishScreenAndOpenChat",
            "screening_navigation_id" to screeningNavigationId,
        )

        val content = queryParams["content"]
        val kind = queryParams["kind"]?.let { ChannelKind.valueOf(it) } ?: ChannelKind.CHAT
        val category = queryParams["category"]?.let { ChannelCategory.valueOf(it) } ?: ChannelCategory.ASSISTANCE
        val subCategory = queryParams["sub_category"]?.let { ChannelSubCategory.valueOf(it) }
            ?: if (category == ChannelCategory.ADMINISTRATIVE) null else SCREENING
        val subCategoryClassifier = queryParams["sub_category_classifier"]
            ?.let { ChannelSubCategoryClassifier.valueOf(it) }
        val hideMemberInput = queryParams["hide_member_input"]?.toBoolean() ?: false

        val tags = queryParams["tags"]?.split(",")
        val question = queryParams["question"]
        val answer = queryParams["answer"]

        val channelCreationParameters = ChannelCreationParameters(
            content = content,
            kind = kind,
            category = category,
            subCategory = subCategory,
            subCategoryClassifier = subCategoryClassifier,
            hideMemberInput = hideMemberInput,
            tags = tags ?: emptyList()
        )

        return screenDataService.finishScreening(
            screeningNavigationId = screeningNavigationId,
            shouldOpenChat = true,
            channelCreationParameters = channelCreationParameters,
            userAgent = requestUserAgent(),
            appVersion = currentMemberAppVersion().version.version,
            question = question,
            answer = answer,
        ).foldResponse()
    }

    suspend fun finishScreeningAndClose(screeningNavigationId: UUID): Response {
        logger.info(
            "ScreeningController::finishScreeningAndClose",
            "screening_navigation_id" to screeningNavigationId,
        )

        return screenDataService.finishScreening(
            screeningNavigationId = screeningNavigationId,
            userAgent = requestUserAgent(),
            appVersion = currentMemberAppVersion().version.version,
        ).foldResponse()
    }

    private suspend fun getScreeningScreenFromServer(
        questionIdValue: String,
        appVersion: String,
        question: String,
        answer: String,
        person: Person,
        channelCreationType: String? = null,
    ) =
        screeningScreenService.get(
            questionKey = questionIdValue,
            personId = currentUid().toPersonId(),
            appVersion = appVersion,
            question = question,
            answer = answer,
            person = person,
            channelCreationTypeString = channelCreationType
        )

    private fun getEntryQuestionId(person: Person) =
        if (canStartAiTriage(person)) "screening-question-1-AI" else "screening-question-1"

    private fun validateAndGetQuestionId(questionId: String?, person: Person) =
        if (questionId == null || questionId == "1") {
            getEntryQuestionId(person)
        } else {
            questionId
        }

    private fun getProtocolId(questionIdValue: String) =
        when (questionIdValue) {
            PRESCRIPTION_TRIAGE_START_QUESTION -> FeatureService.get(
                namespace = FeatureNamespace.SCREENING,
                key = "triage_prescription_protocol_id",
                defaultValue = "2b531a26-7d1a-4dc3-b4df-905e2a605100"
            )
            ADMINISTRATIVE_TRIAGE_START_QUESTION -> FeatureService.get(
                namespace = FeatureNamespace.SCREENING,
                key = "triage_administrative_protocol_id",
                defaultValue = "e69f0e89-4602-4a51-9a8c-09c6a289d200"
            )

            else -> getTriageProtocolId()
        }

    private fun getTriageProtocolId() =
        FeatureService.get(
            namespace = FeatureNamespace.SCREENING,
            key = "triage_protocol_id",
            defaultValue = "0a19f0ad-255b-4b5a-a9e3-322f71730400"
        )

    private fun canStartAiTriage(person: Person) = person.isAdult
}
