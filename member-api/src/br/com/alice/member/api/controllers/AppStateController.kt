package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.membership.model.events.UpdateAppStateRequestedEvent
import br.com.alice.membership.model.events.UpdateAppStateRequestedPayload
import io.ktor.http.HttpStatusCode

class AppStateController(
    private val kafkaProducerService: KafkaProducerService
) : Controller() {

    suspend fun clearAppStateByPersonId(request: ClearAppStateRequest) =
        try {
            val personId = request.personId
            val appState = request.appState
            logger.info(
                "AppStateController::clearAppStateByPersonId error",
                "person_id" to personId,
                "app_state" to appState
            )

            val updateAppStateRequestedEvent = UpdateAppStateRequestedEvent(
                updateAppStateRequestedPayload = UpdateAppStateRequestedPayload(
                    personId = personId.toPersonId(),
                    appState = request.appState
                )
            )

            kafkaProducerService.produce(updateAppStateRequestedEvent)
            Response(status = HttpStatusCode.OK)
        } catch (throwable: Throwable) {
            logger.error("AppStateController::clearAppStateByPersonId error", "error_message" to throwable.message)
            Response(status = HttpStatusCode.InternalServerError)
        }

}

data class ClearAppStateRequest(
    val personId: String,
    val appState: String
)
