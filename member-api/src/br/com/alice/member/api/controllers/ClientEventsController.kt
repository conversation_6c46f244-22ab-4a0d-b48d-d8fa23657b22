package br.com.alice.member.api.controllers

import br.com.alice.common.controllers.Controller
import br.com.alice.common.observability.controllers.ClientEvent
import br.com.alice.common.observability.controllers.ClientEventsControllerBase

class ClientEventsController : Controller(), ClientEventsControllerBase {

    override suspend fun enrich(events: List<ClientEvent>): List<ClientEvent> =
        events.map { event ->
            event.copy(data = event.data.orEmpty() + mapOf("person_id" to currentUid()))
        }
}
