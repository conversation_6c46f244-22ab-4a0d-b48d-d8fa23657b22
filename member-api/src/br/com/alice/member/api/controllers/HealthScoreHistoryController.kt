package br.com.alice.member.api.controllers

import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.client.HealthScoreResultService
import br.com.alice.member.api.converters.HealthScoreHistoryResponseConverter
import br.com.alice.member.api.converters.HealthScoreHistoryTypesResponseConverter
import br.com.alice.member.api.converters.HealthScorePeriodFilterConverter
import br.com.alice.member.api.converters.HealthScoreTypeConverter
import br.com.alice.member.api.metrics.Metrics
import br.com.alice.member.api.models.ScoreTypes
import br.com.alice.member.api.models.ScoreTypesPeriodFilterResponse
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import io.ktor.http.Parameters
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

class HealthScoreHistoryController(
    private val healthScoreResultService : HealthScoreResultService,
): Controller() {

    suspend fun getHealthScoreHistory(typeName: String, queryParams: Parameters): Response {
        val personId = currentUid().toPersonId()
        val type = HealthScoreTypeConverter.convert(ScoreTypes.valueOf(typeName.uppercase()))
        val period = HealthScorePeriodFilterConverter.convert(
            Integer.parseInt(queryParams["period"] ?: LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy")))
        )

        logger.info(
            "getHealthScoreHistory() result was requested.",
            "personId" to personId.toString(),
            "type" to type,
            "period" to period.toString()
        )
        val validPeriod = validatePeriod(personId, period, type) //Fix for new year's bug

        return healthScoreResultService.getHealthScoreHistoryResultsByTypeAndDate(personId, validPeriod.startDate, validPeriod.endDate, type)
            .flatMap {
                if (it.isEmpty()) {
                    Metrics.incrementScoreMagentaHistoryResultFailure(type)
                    Result.failure(NotFoundException("List of entities is empty"))
                } else {
                    Metrics.incrementScoreMagentaHistoryResultSuccess(type)
                    Result.success(it)
                }
            }.coFoldResponse({ results ->
                val availablePeriods = healthScoreResultService.getAvailableYearsOfHealthScoreHistory(personId, type).get()
                HealthScoreHistoryResponseConverter.convert(results, availablePeriods, hideDelta()) },
            NotFoundException::class to { ErrorResponse("not_found", "Health Score History Results were not found for person with id: $personId") }
        )
    }

    private suspend fun validatePeriod(
        personId: PersonId,
        period: ScoreTypesPeriodFilterResponse,
        type: String
    ): ScoreTypesPeriodFilterResponse {
        val hasScoreMagentaInPeriod =
            healthScoreResultService.hasHealthScoreResultBetweenDates(personId, period.startDate, period.endDate, type)
                .get()

        return if (hasScoreMagentaInPeriod) {
            period
        } else {
            period.copy(
                startDate = period.startDate.minusYears(1),
                endDate = period.endDate.minusYears(1),
            )
        }
    }

    suspend fun getHealthScoreHistoryTypesById(generalResultId: UUID, queryParams: Parameters): Response {
        val personId = currentUid().toPersonId()
        val referenceDeltaId = queryParams["reference_delta_id"]?.toUUID()

        logger.info(
            "getHealthScoreHistoryTypesById() result was requested.",
            "personId" to personId.toString(),
            "generalResultId" to generalResultId.toString(),
            "referenceDeltaId" to referenceDeltaId.toString(),
        )

        return healthScoreResultService.getHealthScorePartials(generalResultId)
            .thenError { Metrics.incrementScoreMagentaPillarResultFailure() }
            .coFoldResponse({ results ->
                val pastOutcomeRecords = if (showDelta(referenceDeltaId)) {
                    healthScoreResultService
                        .getHealthScorePartials(referenceDeltaId!!)
                        .getOrElse { emptyList() }
                } else {
                    emptyList()
                }

                Metrics.incrementScoreMagentaPillarResultSuccess(results.size, pastOutcomeRecords.size)
                HealthScoreHistoryTypesResponseConverter.convert(results, pastOutcomeRecords)
            },
            NotFoundException::class to { ErrorResponse("not_found", "Health Score History Results were not found for person with id: $personId") }
        )
    }

    private fun hideDelta() = FeatureService.get(FeatureNamespace.ALICE_APP, "score_magenta_hide_delta", false)
    
    private fun showDelta(referenceDeltaId: UUID?) = (referenceDeltaId != null && !hideDelta())
}
