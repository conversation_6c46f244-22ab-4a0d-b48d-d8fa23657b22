package br.com.alice.member.api.controllers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.model.ActionPlanTasksTransport
import br.com.alice.authentication.currentUserType
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.featureaccess.Feature
import br.com.alice.common.featureaccess.features.AppointmentScheduleOptionResource
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.data.layer.models.AppointmentSchedule
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.Person
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.client.HealthPlanService
import br.com.alice.healthplan.models.HealthPlanTransport
import br.com.alice.member.api.FeatureAccess
import br.com.alice.member.api.builders.AppointmentScheduleOptionGroupsResponseBuilder
import br.com.alice.member.api.models.TestCode
import br.com.alice.member.api.services.TestCodesService
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.util.UUID

class AppointmentScheduleOptionController(
    private val personService: PersonService,
    private val healthcareTeamService: HealthcareTeamService,
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService,
    private val healthPlanService: HealthPlanService,
    private val actionPlanTaskService: ActionPlanTaskService,
    private val testCodesService: TestCodesService,
    private val appointmentScheduleService: AppointmentScheduleService,
    private val appointmentScheduleOptionGroupsResponseBuilder: AppointmentScheduleOptionGroupsResponseBuilder,
    private val trackPersonABService: TrackPersonABService,
) : Controller() {

    companion object {
        const val SCHEDULE_TYPE_LIMIT = 7
    }

    suspend fun getAppointmentScheduleOptions(): Response = span("getAppointmentScheduleOptions") { span ->
        coroutineScope {
            span.setAttribute("person_id", currentUid().toPersonId())
            buildAppointmentScheduleOptionResources()
                .map {
                    val resources = withoutCommunityScheduleOption(it)
                    span.setAttribute("healthcare_team_id", resources.healthcareTeam?.id)
                    span.setAttribute(
                        "appointment_schedule_options_count",
                        resources.appointmentScheduleOptions.size
                    )
                    span.setAttribute("next_appointments_count", resources.nextAppointments.size)
                    appointmentScheduleOptionGroupsResponseBuilder.buildAppointmentScheduleOptionGroupsResponse(
                        person = resources.person,
                        healthPlanTransport = resources.healthPlan,
                        tasks = resources.tasks,
                        options = resources.appointmentScheduleOptions,
                        testCodes = resources.exams,
                        userType = currentUserType()
                    )
                }
                .foldResponse()
        }
    }

    suspend fun getAppointmentScheduleOptionsByType(type: String): Response =
        span("getAppointmentScheduleOptionsByType") { span ->
            coroutineScope {
                buildAppointmentScheduleOptionResources(type)
                    .map { resources ->
                        val isHealthcareTeamScreening = isHealthCareTeamScreening(type, resources.healthcareTeam?.id)
                        val showHealthcareTeamScreening = if (isHealthcareTeamScreening) {
                            pathOnABHealthCareTeamScreeningTest(resources.person.id)
                        } else false

                        span.setAttribute("person_id", resources.person.id)
                        span.setAttribute("appointment_schedule_option_type", type)
                        span.setAttribute("healthcare_team_id", resources.healthcareTeam?.id)
                        span.setAttribute("is_healthcare_team_screening", isHealthcareTeamScreening)
                        span.setAttribute("show_healthcare_team_screening", showHealthcareTeamScreening)

                        if (showHealthcareTeamScreening) {
                            appointmentScheduleOptionGroupsResponseBuilder.buildHealthCareTeamScreeningResponse(
                                person = resources.person,
                                nurseSchedule = resources.appointmentScheduleOptions.find { it.staffId == resources.healthcareTeam?.nurseStaffId },
                                physicianSchedule = resources.appointmentScheduleOptions.find { it.staffId == resources.healthcareTeam?.physicianStaffId },
                            )
                        } else {
                            appointmentScheduleOptionGroupsResponseBuilder.buildAppointmentScheduleOptionGroupsResponse(
                                resources.person,
                                resources.healthPlan,
                                resources.tasks,
                                resources.appointmentScheduleOptions,
                                resources.exams,
                                currentUserType(),
                            )
                        }
                    }
                    .foldResponse()
            }
        }

    private fun isHealthCareTeamScreening(type: String, healthcareTeamId: UUID? = null): Boolean {
        if (healthcareTeamId == null) return false

        if (type != AppointmentScheduleType.HEALTHCARE_TEAM.toString()) return false

        val healthcareTeamIdsNotOnScreening =
            FeatureService.getList<UUID>(FeatureNamespace.SCHEDULE, "mini_triagem_healthcare_team_ids")

        return healthcareTeamIdsNotOnScreening.isNotEmpty() && !healthcareTeamIdsNotOnScreening.contains(
            healthcareTeamId
        )
    }

    private suspend fun pathOnABHealthCareTeamScreeningTest(personId: PersonId): Boolean =
        trackPersonABService.findOrStartAb(
            personId = personId,
            namespace = FeatureNamespace.SCHEDULE,
            key = "mini_triagem_distribution",
            defaultPath = "false"
        ).then {
            logger.info(
                "AppointmentScheduleOptionController: getAppointmentScheduleOptionsByType - AB path",
                "abPath" to it.abPath
            )
        }.fold({ it.abPath.toBoolean() }, { false })

    private fun withoutCommunityScheduleOption(
        resources: AppointmentScheduleOptionResources,
    ): AppointmentScheduleOptionResources {
        val scheduleOptions = resources
            .appointmentScheduleOptions
            .filter { it.type != AppointmentScheduleType.COMMUNITY }

        return resources.copy(
            appointmentScheduleOptions = scheduleOptions
        )
    }

    private suspend fun listByPerson(
        appointmentScheduleOptionType: String? = null,
        personId: PersonId
    ) = span("listByPerson") { span ->
        span.setAttribute("person_id", personId.toString())
        span.setAttribute("appointment_schedule_option_type", appointmentScheduleOptionType.orEmpty())
        if (appointmentScheduleOptionType.isNullOrBlank()) {
            appointmentScheduleOptionService.listByPerson(
                personId = personId,
                fromReferral = false
            )
        } else {
            appointmentScheduleOptionService.listByPersonAndType(
                personId = personId,
                type = AppointmentScheduleType.valueOf(appointmentScheduleOptionType.uppercase()),
                fromReferral = false
            )
        }
    }

    private suspend fun buildAppointmentScheduleOptionResources(
        appointmentScheduleOptionType: String? = null
    ): Result<AppointmentScheduleOptionResources, Throwable> = coroutineScope {
        val personId = currentUid().toPersonId()

        val personDeferred = async { personService.get(personId).get() }
        val healthPlanDeferred = async { healthPlanService.getByPerson(personId).getOrNullIfNotFound() }
        val tasksDeferred = async { actionPlanTaskService.getByPerson(personId).get() }
        val healthcareTeamDeferred =
            async { healthcareTeamService.getHealthcareTeamByPerson(personId).getOrNullIfNotFound() }
        val immersionDeferred = async {
            appointmentScheduleService.findBy(
                AppointmentScheduleFilter(
                    personId = personId,
                    types = listOf(AppointmentScheduleType.IMMERSION)
                )
            ).get()
        }
        val nextAppointmentDeferred = async {
            appointmentScheduleService.findBy(
                AppointmentScheduleFilter(
                    personId = personId,
                    status = listOf(AppointmentScheduleStatus.SCHEDULED),
                    startDate = LocalDate.now(),
                    sortOrder = SortOrder.Ascending
                )
            ).get()
        }

        val person = personDeferred.await()
        val nextAppointments = nextAppointmentDeferred.await()
        val healthcareTeam = healthcareTeamDeferred.await()

        listByPerson(appointmentScheduleOptionType, personId)
            .map { appointmentScheduleOptions ->
                val filteredSchedules = filterResources(
                    appointmentScheduleOptions,
                    nextAppointments.groupBy { it.type }.filter {
                        it.value.size >= SCHEDULE_TYPE_LIMIT
                    }.map { it.key }
                )

                AppointmentScheduleOptionResources(
                    appointmentScheduleOptions = filteredSchedules.scheduleOptions,
                    exams = testCodesService.getTestCodes(),
                    hasImmersion = immersionDeferred.await().isNotEmpty(),
                    healthPlan = healthPlanDeferred.await(),
                    healthcareTeam = healthcareTeam,
                    nextAppointments = nextAppointments,
                    person = person,
                    tasks = tasksDeferred.await(),
                )
            }
    }

    private suspend fun filterResources(
        scheduleOptions: List<AppointmentScheduleOption>,
        scheduleReachedLimitTypes: List<AppointmentScheduleType>
    ) = FeatureAccess.filter(
        feature = Feature.APPOINTMENT_SCHEDULE_OPTION,
        resource = AppointmentScheduleOptionResource(
            scheduleOptions = scheduleOptions.filterNot { scheduleReachedLimitTypes.contains(it.type) }
        )
    ).response!!
}

data class AppointmentScheduleOptionResources(
    val appointmentScheduleOptions: List<AppointmentScheduleOption>,
    val exams: List<TestCode>,
    val hasImmersion: Boolean,
    val healthPlan: HealthPlanTransport?,
    val healthcareTeam: HealthcareTeam?,
    val nextAppointments: List<AppointmentSchedule>,
    val person: Person,
    val tasks: ActionPlanTasksTransport
)
