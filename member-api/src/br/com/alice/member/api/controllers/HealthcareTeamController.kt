package br.com.alice.member.api.controllers

import br.com.alice.common.NotFoundResponse
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.member.api.models.HealthcareTeamWelcomeTransport
import br.com.alice.staff.client.HealthcareTeamService
import io.ktor.http.HttpStatusCode

class HealthcareTeamController(
    private val healthcareTeamService: HealthcareTeamService,
) : Controller() {

    suspend fun getWelcomeCard(): Response {
        val personId = currentUid().toPersonId()
        logger.info(
            "HealthcareTeamController::welcome card",
            "person_id" to personId,
        )

        return if (checkIfPersonHasHealthcareTeam(personId)) {
            HealthcareTeamWelcomeTransport
                .buildCard()
                .toResponse()
        } else {
            NotFoundResponse(httpStatusCode = HttpStatusCode.NotFound)
        }
    }
    suspend fun getWelcome(): Response {
        val personId = currentUid()
        logger.info(
            "HealthcareTeamController::welcome",
            "person_id" to personId,
        )

        return HealthcareTeamWelcomeTransport
            .build()
            .toResponse()
    }

    private suspend fun checkIfPersonHasHealthcareTeam(personId: PersonId) =
        healthcareTeamService.getHealthcareTeamByPerson(personId).fold(
            { true },
            { false }
        )

}
