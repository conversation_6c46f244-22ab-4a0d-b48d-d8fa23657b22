package br.com.alice.member.api.controllers

import br.com.alice.app.content.client.AliceScreensService
import br.com.alice.app.content.model.PaginationLayout
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.toResponse
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.healthplan.converters.taskProgress.TaskProgressInfoBuilderImpl
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.converters.appContent.SectionResponseConverter
import br.com.alice.member.api.metrics.Metrics
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.api.services.FeaturePreferences
import br.com.alice.member.api.services.FeaturePreferencesNotifier
import br.com.alice.member.api.models.appContent.PaginationLayout as MemberApiPaginationLayout
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.time.LocalDateTime
import java.util.UUID

data class UpdateTaskRequest(
    val relatedTaskIds: List<UUID>? = null
)

class HealthPlanTaskController(
    private val healthPlanTaskService: HealthPlanTaskService,
    private val aliceScreensService: AliceScreensService
) : Controller() {

    suspend fun acknowledge(healthPlanTaskId: UUID, request: UpdateTaskRequest): Response {
        val personId = currentUid().toPersonId()
        val ids = request.relatedTaskIds?.plus(healthPlanTaskId) ?: listOf(healthPlanTaskId)
        logger.info(
            "HealthPlanTaskController::acknowledge",
            "health_plan_task_id" to healthPlanTaskId,
            "person_id" to personId.toString()
        )
        ids.map { taskId ->
            healthPlanTaskService.acknowledge(taskId, LocalDateTime.now())
        }
        return Response(HttpStatusCode.OK)
    }

    suspend fun init(healthPlanTaskId: UUID, request: UpdateTaskRequest): Response {
        val personId = currentUid().toPersonId()
        val ids = request.relatedTaskIds?.plus(healthPlanTaskId) ?: listOf(healthPlanTaskId)

        logger.info(
            "HealthPlanTaskController::init",
            "health_plan_task_id" to healthPlanTaskId,
            "person_id" to personId.toString(),
            "relatedIds" to request.relatedTaskIds?.toString(),
        )

        return ids.map { taskId ->
            healthPlanTaskService
                .init(taskId, LocalDateTime.now())
                .then { Metrics.incrementInitHealthPlanTaskSuccess() }
                .thenError { Metrics.incrementInitHealthPlanTaskFailure() }
                .map { TaskProgressInfoBuilderImpl().build(it)!! }.get()
        }.toResponse()
    }

    suspend fun delete(healthPlanTaskId: UUID, request: UpdateTaskRequest): Response {
        val personId = currentUid().toPersonId()
        val ids = request.relatedTaskIds?.plus(healthPlanTaskId) ?: listOf(healthPlanTaskId)

        logger.info(
            "HealthPlanTaskController::delete",
            "health_plan_task_id" to healthPlanTaskId,
            "person_id" to personId.toString(),
            "relatedIds" to request.relatedTaskIds?.toString(),
        )

        return ids.forEach { taskId ->
            healthPlanTaskService
                .deleteByMember(taskId)
                .then { Metrics.incrementMemberDeleteTaskSuccess() }
                .thenError { Metrics.incrementMemberDeleteTaskFailure() }
        }.apply{
            updateHealthPlanPopoversFeaturePreferences(personId, popoverKey = "health_plan_history_from_archive")
            updateTasksStatusInApp(personId)
        }.toResponse()
    }

    suspend fun favorite(healthPlanTaskId: UUID, request: UpdateTaskRequest): Response {
        val personId = currentUid().toPersonId()
        val ids = request.relatedTaskIds?.plus(healthPlanTaskId) ?: listOf(healthPlanTaskId)

        logger.info(
            "HealthPlanTaskController::favorite",
            "health_plan_task_id" to healthPlanTaskId,
            "person_id" to personId.toString(),
            "relatedIds" to request.relatedTaskIds?.toString(),
        )

        return ids.forEach { taskId ->
            healthPlanTaskService
                .favorite(taskId)
                .then { Metrics.incrementMemberFavoriteTaskSuccess() }
                .thenError { Metrics.incrementMemberFavoriteTaskFailure() }
        }.toResponse()
    }

    suspend fun complete(healthPlanTaskId: UUID, request: UpdateTaskRequest): Response {
        val personId = currentUid().toPersonId()
        val ids = request.relatedTaskIds?.plus(healthPlanTaskId) ?: listOf(healthPlanTaskId)

        logger.info(
            "HealthPlanTaskController::complete",
            "health_plan_task_id" to healthPlanTaskId,
            "person_id" to personId.toString(),
            "relatedIds" to request.relatedTaskIds?.toString(),
        )

        return ids.forEach { taskId ->
            healthPlanTaskService
                .completeTaskByMember(taskId)
                .then { Metrics.incrementMemberDeleteTaskSuccess() }
                .thenError { Metrics.incrementMemberDeleteTaskFailure() }
        }.apply {
            updateHealthPlanPopoversFeaturePreferences(personId, popoverKey = "health_plan_history_from_conclusion")
            updateTasksStatusInApp(personId)
        }.toResponse()
    }

    suspend fun initOld(healthPlanTaskId: UUID): Response {
        val personId = currentUid().toPersonId()
        logger.info(
            "HealthPlanTaskController::init",
            "health_plan_task_id" to healthPlanTaskId,
            "person_id" to personId.toString()
        )
        return healthPlanTaskService
            .init(healthPlanTaskId, LocalDateTime.now())
            .then { Metrics.incrementInitHealthPlanTaskSuccess() }
            .thenError { Metrics.incrementInitHealthPlanTaskFailure() }
            .map { TaskProgressInfoBuilderImpl().build(it)!! }
            .foldResponse()
    }

    suspend fun getPaginated(queryParams: Parameters): Response {
        val personId = currentUid().toPersonId()
        val offset = queryParams["offset"]?.toInt() ?: throw InvalidArgumentException("Offset parameter is required")
        val limit = queryParams["limit"]?.toInt() ?: throw InvalidArgumentException("Limit parameter is required")
        val filterByTaskType = queryParams["type"]

        val paginationLayout = aliceScreensService.getPaginatedHealthDemandsScreen(
            personId,
            offset,
            limit,
            filterByTaskType
        ).get()

        return formatPaginationLayoutUrls(paginationLayout).foldResponse()
    }

    private fun formatPaginationLayoutUrls(paginationLayout: PaginationLayout) =
        MemberApiPaginationLayout(
            sections = paginationLayout.sections.map { SectionResponseConverter.convert(it) },
            action = paginationLayout.action?.let {
                br.com.alice.member.api.models.appContent.RemoteAction(
                    method = paginationLayout.action?.method,
                    endpoint = paginationLayout.action?.endpoint?.let {
                        if (it.startsWith("http")) it else ServiceConfig.url(it)
                    }
                )
            }
        ).success()

    private fun updateTasksStatusInApp(personId: PersonId) {
        runCatching {
            AppStateNotifier.updateAppState(personId, AppState.REDESIGN_UNIFIED_HEALTH)
            AppStateNotifier.updateAppState(personId, AppState.REDESIGN_HEALTH_PLAN_HOME)
            AppStateNotifier.updateAppState(personId, AppState.REDESIGN_HEALTH_PLAN_HOME_TASK_LIST)
            AppStateNotifier.updateAppState(personId, AppState.REDESIGN_HEALTH_PLAN_DEMAND_DETAIL)
            AppStateNotifier.updateAppState(personId, AppState.HEALTH_PLAN_DETAILS)
        }.onFailure {
            logger.error(
                "Error updating HealthPlan tasks status in app",
                "person_id" to personId.id,
                it
            )
        }
    }


    @Suppress("UNCHECKED_CAST")
    private fun updateHealthPlanPopoversFeaturePreferences(personId: PersonId, popoverKey: String): Boolean {
        runCatching {
            val preferences = FeaturePreferencesNotifier.getFeaturePreferences(
                personId,
                FeaturePreferences.APP_BAR_ITEM_POPOVER,
            ) ?: emptyMap()

            val popoversPreferences = (preferences["popovers"] as? Map<String, Boolean>).orEmpty()

            if (popoverKey !in popoversPreferences) {
                val updatedPreferences = preferences.plus("popovers" to popoversPreferences + (popoverKey to true))
                FeaturePreferencesNotifier.updateFeaturePreferences(
                    personId,
                    FeaturePreferences.APP_BAR_ITEM_POPOVER,
                    updatedPreferences
                )

                logger.info(
                    "HealthPlanTaskController::updateHealthPlanPopoversFeaturePreferences",
                    "person_id" to personId.id,
                    "popover_key" to popoverKey,
                    "preferences" to updatedPreferences
                )
            }
        }.onFailure {
            logger.error(
                "Error updating HealthPlan popovers feature preferences",
                "person_id" to personId.id,
                "popover_key" to popoverKey,
                it
            )
        }

        return true
    }
}
