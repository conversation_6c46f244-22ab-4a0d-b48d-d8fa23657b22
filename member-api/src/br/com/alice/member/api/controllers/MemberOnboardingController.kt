package br.com.alice.member.api.controllers

import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.idToken
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberOnboardingCheckpoint
import br.com.alice.data.layer.models.MemberOnboardingStep
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.HEALTH_DECLARATION
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.IMMERSION_SCHEDULE_LEAGUE
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepHealthFormEnum.PRE_IMMERSION
import br.com.alice.data.layer.models.MemberOnboardingStep.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingTemplate
import br.com.alice.data.layer.models.Person
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.client.HealthScoreResultService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.member.api.converters.MemberOnboardingStepResponseConverter
import br.com.alice.member.api.converters.MemberOnboardingStepResponseProviders
import br.com.alice.member.api.metrics.Metrics
import br.com.alice.member.api.models.MemberOnboardingStepResponse
import br.com.alice.member.onboarding.client.MemberOnboardingProgressService
import br.com.alice.member.onboarding.client.MemberOnboardingStratificationService
import br.com.alice.member.onboarding.client.MemberOnboardingTemplateService
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.map
import io.ktor.http.HttpStatusCode
import java.time.LocalDateTime
import java.util.UUID

class MemberOnboardingController(
    private val memberOnboardingProgressService: MemberOnboardingProgressService,
    private val memberOnboardingStratificationService: MemberOnboardingStratificationService,
    private val memberOnboardingTemplateService: MemberOnboardingTemplateService,
    private val personService: PersonService,
    private val riskService: RiskService,
    private val healthScoreResultService: HealthScoreResultService,
    private val scheduleService: AppointmentScheduleService,
    private val healthcareTeamService: HealthcareTeamService,
    private val healthPlanTaskService: HealthPlanTaskService,
) : Controller() {

    suspend fun startOnboarding(): Response {
        val personId = currentUid().toPersonId()

        return memberOnboardingProgressService.findStepFromPersonId(personId).coFoldResponse(
            { (checkpoint, currentStep) ->
                val hasFormResult =
                    if (currentStep.isMidStep()) hasFormResult(checkpoint, currentStep, personId) else false
                val hasDroppedOnboarding = checkpoint.hasDroppedOnboarding()

                logger.info(
                    "MemberOnboardingController.startOnboarding: step loaded",
                    "personId" to personId,
                    "stepKey" to currentStep.key,
                    "hasFormResult" to hasFormResult,
                    "returnedFromDrop" to hasDroppedOnboarding,
                    "producedEvent" to currentStep.event
                )

                val staffId = checkpoint.referralTaskId?.let {
                    healthPlanTaskService.get(it).get().releasedByStaffId
                }

                val template = memberOnboardingTemplateService.find(checkpoint.memberOnboardingTemplateId).get()

                returnMemberOnboardingStepResponse(
                    currentStep,
                    personId,
                    hasFormResult,
                    hasDroppedOnboarding,
                    referralTaskId = checkpoint.referralTaskId,
                    staffId = staffId,
                    isBegin = true,
                    template,
                )
            },
            NotFoundException::class to { returnNotFoundStepErrorResponse(personId) },
        )
    }

    suspend fun saveOnboardingAndGetNextStep(stepId: String): Response {
        val personId = currentUid().toPersonId()
        val stepIdUUID = stepId.toUUID()

        return memberOnboardingProgressService.findStepFromStepId(personId, stepIdUUID).coFoldResponse(
            { (checkpoint, nextStep) ->

                val hasDroppedOnboarding = checkpoint.hasDroppedOnboarding()

                logger.info(
                    "MemberOnboardingController.saveOnboardingAndGetNextStep: step loaded",
                    "personId" to personId,
                    "stepKey" to nextStep.key,
                    "returnedFromDrop" to hasDroppedOnboarding,
                    "producedEvent" to nextStep.event
                )

                val staffId = checkpoint.referralTaskId?.let {
                    healthPlanTaskService.get(it).get().releasedByStaffId
                }

                val template = memberOnboardingTemplateService.find(checkpoint.memberOnboardingTemplateId).get()

                returnMemberOnboardingStepResponse(
                    nextStep,
                    personId,
                    hasFormResult = false,
                    hasDroppedOnboarding = hasDroppedOnboarding,
                    referralTaskId = checkpoint.referralTaskId,
                    staffId = staffId,
                    template = template,
                )
            },
            NotFoundException::class to { returnNotFoundStepErrorResponse(personId) },
        )
    }

    suspend fun dropOnboarding(): Response {
        val personId = currentUid().toPersonId()
        return memberOnboardingProgressService.dropCheckpoint(personId).coFoldResponse(
            { checkpoint ->
                logger.info(
                    "MemberOnboardingController.dropOnboarding: onboarding dropped",
                    "personId" to personId,
                    "checkpointCurrentStepId" to checkpoint.currentStepId
                )
                HttpStatusCode.Created
            },
            NotFoundException::class to { returnNotFoundCheckpointResponse(personId) },
        )
    }

    suspend fun findLastStep(): Response {
        val personId = currentUid().toPersonId()
        logger.info(
            "Trying to find the last onboarding step",
            "personId" to personId
        )
        return memberOnboardingStratificationService.getByPersonId(personId).coFoldResponse(
            { (checkpoint, lastStep) ->
                logger.info(
                    "MemberOnboardingController.findLastStep: last step loaded",
                    "personId" to personId,
                    "lastStepKey" to lastStep.key
                )
                val hasDroppedOnboarding = checkpoint.hasDroppedOnboarding()

                val staffId = checkpoint.referralTaskId?.let {
                    healthPlanTaskService.get(it).get().releasedByStaffId
                }

                val template = memberOnboardingTemplateService.find(checkpoint.memberOnboardingTemplateId).get()

                returnMemberOnboardingStepResponse(
                    lastStep,
                    personId,
                    hasFormResult = false,
                    hasDroppedOnboarding = hasDroppedOnboarding,
                    referralTaskId = checkpoint.referralTaskId,
                    staffId = staffId,
                    template = template,
                )
            },
            NotFoundException::class to { returnNotFoundStepErrorResponse(personId) },
        )
    }


    private suspend fun hasFormResult(
        checkpoint: MemberOnboardingCheckpoint,
        currentStep: MemberOnboardingStep,
        personId: PersonId
    ) =
        when (currentStep.healthFormNavigation) {
            HEALTH_DECLARATION -> hasRiskCalculated(personId)
            PRE_IMMERSION -> hasScoreMagentaCalculated(personId, checkpoint.createdAt)
            IMMERSION_SCHEDULE_LEAGUE -> hasImmersionAlreadyScheduled(personId, checkpoint.createdAt)
            else -> false
        }

    private suspend fun hasRiskCalculated(personId: PersonId) =
        riskService.getByPerson(personId).getOrNullIfNotFound()?.let { true } ?: false

    private suspend fun hasScoreMagentaCalculated(personId: PersonId, onboardingStartingDate: LocalDateTime) =
        healthScoreResultService.hasHealthScoreResult(personId, onboardingStartingDate).get()

    private suspend fun hasImmersionAlreadyScheduled(
        personId: PersonId,
        onboardingStartingDate: LocalDateTime
    ): Boolean {
        logger.info(
            "MemberOnboardingController.hasImmersionAlreadyScheduled: checking",
            "personId" to personId,
        )
        val immersions = scheduleService.findBy(
            AppointmentScheduleFilter(
                personId = personId,
                types = listOf(AppointmentScheduleType.IMMERSION),
                createdAt = onboardingStartingDate
            )
        ).getOrNullIfNotFound()

        val hasImmersionAlreadyScheduled = if (immersions.isNotNullOrEmpty()) {
            val healthcareTeam = healthcareTeamService.getHealthcareTeamByPerson(personId).getOrNullIfNotFound()
            if (healthcareTeam == null) {
                logger.warn(
                    "MemberOnboardingController.hasImmersionAlreadyScheduled: person doesn't have healthcare team",
                    "personId" to personId,
                )
                throw NotFoundException("error to find healthcare team")
            }
            immersions!!
                .filter { (it.isScheduled || it.isCompleted) && it.healthcareTeamId == healthcareTeam.id }
                .isNotNullOrEmpty()
        } else {
            false
        }

        logger.info(
            "MemberOnboardingController.hasImmersionAlreadyScheduled: checked",
            "personId" to personId,
            "hasImmersions" to immersions.isNotNullOrEmpty(),
            "immersions" to immersions,
            "hasImmersionAlreadyScheduled" to hasImmersionAlreadyScheduled
        )

        return hasImmersionAlreadyScheduled
    }

    private fun returnNotFoundCheckpointResponse(personId: PersonId): ErrorResponse {
        Metrics.incrementDropMemberOnboardingFailure()
        return ErrorResponse(
            "member_onboarding_checkpoint_not_found",
            "Member Onboarding Checkpoint were not found for person with id: $personId"
        )
    }

    private fun returnNotFoundStepErrorResponse(personId: PersonId): ErrorResponse {
        return ErrorResponse(
            "member_onboarding_step_not_found",
            "Member Onboarding Step were not found for person with id: $personId"
        )
    }

    private suspend fun returnMemberOnboardingStepResponse(
        currentStep: MemberOnboardingStep,
        personId: PersonId,
        hasFormResult: Boolean = false,
        hasDroppedOnboarding: Boolean = false,
        referralTaskId: UUID? = null,
        staffId: UUID? = null,
        isBegin: Boolean = false,
        template: MemberOnboardingTemplate,
    ): MemberOnboardingStepResponse {
        return buildMemberOnboardingStepResponse(
            currentStep,
            getMemberOnboardingProvidersByStep(
                currentStep,
                personId,
                hasFormResult,
                hasDroppedOnboarding,
                referralTaskId,
                staffId,
                isBegin
            ),
            template
        )
    }

    private fun buildMemberOnboardingStepResponse(
        step: MemberOnboardingStep,
        stepProviders: MemberOnboardingStepResponseProviders,
        template: MemberOnboardingTemplate,
    ) =
        MemberOnboardingStepResponseConverter.convert(step, stepProviders, template)

    private suspend fun getMemberOnboardingProvidersByStep(
        currentStep: MemberOnboardingStep,
        personId: PersonId,
        hasFormResult: Boolean = false,
        hasDroppedOnboarding: Boolean = false,
        referralTaskId: UUID? = null,
        staffId: UUID? = null,
        isBegin: Boolean = false
    ) =
        memberOnboardingProgressService.findActionsFromStep(currentStep).map { memberOnboardingActions ->
            val person = personService.get(personId).get()
            val immersionSchedulingUrl = if (referralTaskId != null) {
                getHighRiskImmersionSchedulingCalendarUrl(
                    person = person,
                    referralTaskId = referralTaskId,
                    staffId = staffId,
                    stepType = currentStep.type,
                )
            } else ""
            logger.info(
                "MemberOnboardingController.checkCalendarUrl: checked",
                "personId" to personId,
                "immersionSchedulingUrlWithoutToken" to immersionSchedulingUrl
            )
            MemberOnboardingStepResponseProviders(
                memberOnboardingActions = memberOnboardingActions,
                person = if (currentStep.titleShouldDisplayPersonName() || isBegin) person else null,
                hasFormResult = hasFormResult,
                hasDroppedOnboarding = hasDroppedOnboarding,
                immersionSchedulingUrl = immersionSchedulingUrl,
                isBegin = isBegin
            )
        }.get()

    private suspend fun getHighRiskImmersionSchedulingCalendarUrl(
        person: Person,
        referralTaskId: UUID? = null,
        staffId: UUID? = null,
        stepType: MemberOnboardingStepType = MemberOnboardingStepType.MID,
    ) =
        getCalendarLink() +
                renderVersionQueryStringPart() +
                renderStaffIdQueryStringPart(person.id, staffId) +
                renderReferralTaskIdQueryStringPart(referralTaskId) +
                "&appointmentScheduleEventTypeId=${getAppointmentScheduleEventTypeIdFromStepType(person, stepType)}" +
                renderTokenQueryStringPart() +
                "#/schedule"

    private suspend fun renderTokenQueryStringPart() =
        if (enableAppSessionToken()) "&token=true"
        else "&token=${idToken()}"

    private fun enableAppSessionToken() =
        FeatureService.get(
            namespace = FeatureNamespace.ONBOARDING,
            key = "enable_app_session_token",
            defaultValue = false
        )

    private fun renderVersionQueryStringPart() =
        FeatureService.get(
            namespace = FeatureNamespace.SCHEDULE,
            key = "sara_schedule_url_version",
            defaultValue = "1"
        ).let { "v=$it" }

    private suspend fun renderStaffIdQueryStringPart(personId: PersonId, staffId: UUID? = null): String {
        val healthProfessional = healthcareTeamService.getHealthcareTeamByPerson(personId)
            .fold(
                { it.physicianStaffId },
                { staffId ?: getLeagueStaffId() }
            )

        return "&staffId=${healthProfessional}"
    }

    private fun renderReferralTaskIdQueryStringPart(referralTaskId: UUID? = null) =
        if (referralTaskId != null) {
            "&healthPlanTaskId=${referralTaskId.toString().toSha256()}"
        } else {
            ""
        }

    private fun getLeagueStaffId(): UUID =
        FeatureService.get(
            namespace = FeatureNamespace.ONBOARDING,
            key = "member_onboarding_league_staff_id",
            defaultValue = "ba3bbe06-111d-4f33-95c3-9cf3e3672100"
        ).toUUID()

    private fun getCalendarLink() = FeatureService.get(
        namespace = FeatureNamespace.ONBOARDING,
        key = "calendar_immersion",
        defaultValue = "https://webview.alice.com.br/scheduler/?"
    )

    private fun getAppointmentScheduleEventTypeIdFromStepType(
        person: Person,
        stepType: MemberOnboardingStepType = MemberOnboardingStepType.MID,
    ): UUID? {
        val isLegacyMember = !person.isNewPortfolio
        val isChildrenSchedule = (person.isChild || stepType == MemberOnboardingStepType.CHILDREN_SCHEDULE)

        return if (isLegacyMember) when {
            isChildrenSchedule -> getLegacyChildrenAppointmentScheduleEventTypeId()
            stepType == MemberOnboardingStepType.MID -> getLegacyAppointmentScheduleEventTypeId()
            else -> null
        } else when {
            isChildrenSchedule -> getChildrenAppointmentScheduleEventTypeId()
            stepType == MemberOnboardingStepType.MID -> getAppointmentScheduleEventTypeId()
            else -> null
        }
    }

    private fun getLegacyAppointmentScheduleEventTypeId() =
        FeatureService.get(
            FeatureNamespace.ONBOARDING,
            "member_onboarding_appointment_schedule_event_type_id",
            "3bee107c-74a9-42c7-9f09-69fb3264f400"
        ).toUUID()

    private fun getLegacyChildrenAppointmentScheduleEventTypeId() =
        FeatureService.get(
            FeatureNamespace.ONBOARDING,
            "member_onboarding_children_appointment_schedule_event_type_id",
            "34afb517-fa75-4781-ad7b-5dfc554f7200"
        ).toUUID()

    private fun getAppointmentScheduleEventTypeId() =
        FeatureService.get(
            FeatureNamespace.ONBOARDING,
            "member_onboarding_appointment_schedule_event_type_id_v3",
            "3bee107c-74a9-42c7-9f09-69fb3264f400"
        ).toUUID()

    private fun getChildrenAppointmentScheduleEventTypeId() =
        FeatureService.get(
            FeatureNamespace.ONBOARDING,
            "member_onboarding_children_appointment_schedule_event_type_id_v3",
            "34afb517-fa75-4781-ad7b-5dfc554f7200"
        ).toUUID()
}
