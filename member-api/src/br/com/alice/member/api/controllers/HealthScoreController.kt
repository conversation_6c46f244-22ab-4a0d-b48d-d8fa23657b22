package br.com.alice.member.api.controllers

import br.com.alice.app.content.client.AppContentScreenDetailService
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.toResponse
import br.com.alice.healthlogic.client.HealthScoreResultService
import br.com.alice.member.api.converters.HealthScoreSingleResponseConverter
import br.com.alice.member.api.converters.ScoreMagentaOverviewConverter
import br.com.alice.member.api.metrics.Metrics
import br.com.alice.member.api.models.HealthScoreWelcomeActionResponse
import br.com.alice.member.api.models.HealthScoreWelcomeContentResponse
import br.com.alice.member.api.models.HealthScoreWelcomeTextResponse
import br.com.alice.membership.client.DeviceService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import io.ktor.http.Parameters

class HealthScoreController(
    private val healthScoreResultService : HealthScoreResultService,
    private val appContentScreenDetailService: AppContentScreenDetailService,
    private val deviceService: DeviceService
): Controller()  {

    private val minAppVersion = SemanticVersion("2.72.1")

    suspend fun getHealthScoreGeneralResult(queryParams: Parameters): Response {
        val personId = currentUid().toPersonId()
        val isWelcome = queryParams["is_welcome"].toBoolean()
        val queryLimit = if (isWelcome) 1 else 2

        logger.info(
            "getHealthScoreGeneralResult() result was requested.",
            "person_id" to personId.toString(),
            "is_welcome" to isWelcome.toString()
        )

        return healthScoreResultService.getHealthScoreHistoryResults(personId, queryLimit)
            .flatMap {
                if (it.isEmpty()) {
                    Metrics.incrementScoreMagentaGeneralResultFailure()
                    Result.failure(NotFoundException("List of entities is empty"))
                } else {
                    Metrics.incrementScoreMagentaGeneralResultSuccess()
                    Result.success(it)
                }
            }.coFoldResponse(
                { results ->
                    HealthScoreSingleResponseConverter.convert(
                        results,
                        isWelcome,
                        shouldShowHealthScoreCTA(personId)
                    )
                },
            NotFoundException::class to { ErrorResponse("not_found", "Health Score Result were not found for person with id: $personId") },
        )
    }

    suspend fun getHealthScoreOverviewResult(): Response =
        healthScoreResultService.getLatestHealthScoreWithPartialsByPersonId(currentUid().toPersonId())
            .map { ScoreMagentaOverviewConverter.convert(it) }
            .foldResponse()

    fun getWelcomeContent(): Response {
        val response = HealthScoreWelcomeContentResponse(
            welcomeText = listOf(
                HealthScoreWelcomeTextResponse(
                    title = "O que é essa pontuação?",
                    text = "Suas respostas geraram um resultado que, na Alice, chamamos de Score Magenta. Esse cálculo é baseado em métodos cientificos usados no mundo todo.",
                ),
                HealthScoreWelcomeTextResponse(
                    title = "Próximos passos",
                    text = "**Em até 2 dias úteis,** nossa equipe vai enviar a estratégia de saúde que foi feita só para você.",
                )
            ),

            action = HealthScoreWelcomeActionResponse(
                label = "Continuar"
            )
        )
        return response.toResponse()
    }

    private suspend fun shouldShowHealthScoreCTA(personId: PersonId): Boolean {
        val appVersion = getAppVersionByPerson(personId)
        return if (memberIsEligible(appVersion)) {
            appContentScreenDetailService.shouldShowHealthScoreCTA(personId).get()
        } else {
            false
        }
    }

    private suspend fun getAppVersionByPerson(personId: PersonId): SemanticVersion =
        deviceService.getDeviceByPerson(personId.toString())
            .fold(
                { result ->
                    if (result.appVersion != null && SemanticVersion.isValid(result.appVersion!!))
                        SemanticVersion(result.appVersion!!)
                    else
                        defaultAppVersion()
                },
                { error ->
                    logger.error("HealthScoreController.getAppVersionByPerson()", "person_id" to personId, error)
                    defaultAppVersion()
                }
            )

    private fun defaultAppVersion() =
        SemanticVersion("0.0.0")

    private fun memberHasTheValidAppVersion(appVersion: SemanticVersion): Boolean =
        appVersion >= minAppVersion

    private fun memberIsEligible(appVersion: SemanticVersion) =
        memberHasTheValidAppVersion(appVersion)
}

