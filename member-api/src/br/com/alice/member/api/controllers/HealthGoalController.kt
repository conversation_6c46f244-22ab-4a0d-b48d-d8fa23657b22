package br.com.alice.member.api.controllers

import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.foldResponse
import br.com.alice.member.api.models.HealthGoalResponse
import br.com.alice.member.api.models.ListHealthGoalsResponse
import br.com.alice.member.api.models.PersonHealthGoalResponse
import br.com.alice.member.api.models.SetHealthGoalsRequest
import br.com.alice.member.api.models.SetHealthGoalsResponse
import br.com.alice.membership.client.HealthGoalService
import com.github.kittinunf.result.map

class HealthGoalController(
    private val healthGoalService: HealthGoalService
) : Controller() {

    suspend fun listHealthGoals() =
        healthGoalService
            .listHealthGoals(currentUid().toPersonId())
            .mapEach { it.convertTo(HealthGoalResponse::class) }
            .map(::ListHealthGoalsResponse)
            .foldResponse()

    suspend fun setHealthGoals(request: SetHealthGoalsRequest) =
        healthGoalService
            .setHealthGoals(currentUid().toPersonId(), request.healthGoalIds)
            .map { it.convertTo(PersonHealthGoalResponse::class) }
            .map(::SetHealthGoalsResponse)
            .foldResponse()

}
