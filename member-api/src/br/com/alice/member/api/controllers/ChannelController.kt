package br.com.alice.member.api.controllers

import M
import br.com.alice.action.plan.client.DemandActionPlanService
import br.com.alice.action.plan.client.HealthConditionGroupService
import br.com.alice.channel.client.AvailabilityService
import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.client.FollowUpService
import br.com.alice.channel.client.FollowUpTriageChannelService
import br.com.alice.channel.models.ChannelMessageQuestionOptions
import br.com.alice.channel.models.ChannelMessageQuestionOptions.DO_NOT_ARCHIVE_CHANNEL
import br.com.alice.channel.models.ChannelMessageQuestionOptions.YES_ARCHIVE_CHANNEL
import br.com.alice.channel.models.ChannelResponse
import br.com.alice.channel.models.DemandChannelsCondition
import br.com.alice.channel.models.FollowUpAnswerRequest
import br.com.alice.channel.models.Origin
import br.com.alice.channel.models.SendMessageRequest
import br.com.alice.channel.services.FileUploadService
import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.coFoldResponse
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.fromJson
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.core.extensions.toUrlEncoded
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.resultOf
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.MemberAppVersioning
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.serialization.gson
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelScreeningNavigation
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.DemandActionPlan
import br.com.alice.data.layer.models.DemandActionPlanReferenceModel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Staff
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.converters.ChatAvailabilityResponseConverter
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.models.currentMemberAppVersion
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import io.opentelemetry.api.trace.StatusCode
import java.time.LocalDateTime
import java.util.UUID

class ChannelController(
    private val fileUploadService: FileUploadService,
    private val channelService: ChannelService,
    private val availabilityService: AvailabilityService,
    private val followUpService: FollowUpService,
    private val demandActionPlanService: DemandActionPlanService,
    private val healthConditionGroupService: HealthConditionGroupService,
    private val staffService: StaffService,
    private val followUpTriageChannelService: FollowUpTriageChannelService
) : Controller(), Spannable {

    suspend fun uploadFile(multipartRequest: MultipartRequest): Response {
        val personId = PersonId.fromString(currentUid())
        logger.info(
            "Receiving new file upload",
            "request_parameters" to multipartRequest.parameters,
            "request_file_name" to multipartRequest.file?.name?.toUrlEncoded(),
            "person_id" to personId
        )
        return fileUploadService.uploadFile(personId, multipartRequest).foldResponse()
    }

    suspend fun sendMessage(messageSource: String, sendMessageRequest: SendMessageRequest): Response {
        val origin = Origin.values().firstOrNull { it.name.equals(messageSource, ignoreCase = true) }
            ?: throw InvalidArgumentException(
                code = "invalid_message_origin",
                message = "Invalid given message origin"
            )

        val personId = currentUid().toPersonId()
        val message = sendMessageRequest.withOrigin(origin).parseContent()

        logger.info(
            "Sending message",
            "request_health_plan_item_id" to message.healthPlanItemId,
            "request_origin" to message.origin,
            "request_source" to message.source,
            "request_type" to message.type,
            "request_app_version" to message.appVersion,
            "person_id" to personId
        )

        return channelService.sendMessage(
            personId = personId,
            messageRequest = message
        ).coFoldResponse({ ChatResponse(it) })
    }

    suspend fun getChatAvailability(): Response {
        val personId = currentUid()
        val userAgent = userAgent()

        try {
            val requestApp = MemberAppVersioning(userAgent)

            logger.info(
                "ChannelController::getChatAvailability",
                "app_platform" to requestApp.platform,
                "app_version" to requestApp.version.version,
                "user_agent" to userAgent,
                "person_id" to personId,
            )
        } catch (ex: Exception) {
            logger.error("Error parsing app version", "user_agent" to userAgent, ex)
        }

        return availabilityService.getChatAvailability()
            .map { ChatAvailabilityResponseConverter.convert(it) }
            .mapError {
                logger.error("Error while getting chat availability", it)
                it
            }
            .foldResponse()
    }

    suspend fun processMemberFeedback(channelId: String, fupId: String, fupAnswerRequest: FollowUpAnswerRequest) =
        followUpService.processMemberFeedback(
            channelId = channelId,
            fupId = fupId,
            fupAnswerRequest = fupAnswerRequest,
            personId = currentUid()
        ).coFoldResponse({ ChatResponse(it) })

    suspend fun followUpAcknowledgement(channelId: String, fupId: String) =
        followUpService.memberAcknowledgement(
            channelId = channelId,
            fupId = fupId,
        ).coFoldResponse({ ChatResponse(it) })

    suspend fun getChannelsDemands() = span("getChannelsDemands") { span ->
        currentUid().toPersonId().let { personId ->
            span.setAttribute("person_id", personId)

            followUpTriageChannelService.getArchivedGroupedByHealthCondition(personId, getDateToListChannelDemands())
                .mapEach { demandChannelsCondition ->
                    DemandChannels(
                        title = demandChannelsCondition.friendlyName,
                        description = demandChannelsCondition.description(),
                        startedAt = demandChannelsCondition.startedAt.toSaoPauloTimeZone(),
                        channels = demandChannelsCondition.channels
                    )
                }.map { demandChannelsList ->
                    DemandsChannels(
                        demands = demandChannelsList,
                        resumeCount = demandChannelsList.size,
                        time = i18n(M.CHANNELS_DEMAND_TIME).format(getDaysToListChannelDemands()),
                    )
                }.coFoldNotFound {
                    DemandsChannels(
                        demands = emptyList(),
                        resumeCount = 0,
                        time = i18n(M.CHANNELS_DEMAND_TIME_EMPTY_STATE).format(getDaysToListChannelDemands()),
                    ).success()
                }

        }
            .recordResult(span)
            .foldResponse()
    }

    suspend fun getChannelsDetails(parameters: Parameters) = span("getChannelsDetails") { span ->
        currentUid().toPersonId().let { personId ->
            span.setAttribute("person_id", personId)
            val channelIdsQuery = parameters["channelIds"]
                ?: throw IllegalArgumentException("getChannels must have channelIds query param")
            val channelsIds = channelIdsQuery.split(",")
            span.setAttribute("channel_ids_size", channelsIds.size)

            channelService.findChannelsById(channelsIds).mapEach { channelResponse ->
                val staffInfo = channelResponse.staff?.values?.let { staffList ->
                    staffList.firstOrNull { it.owner } ?: staffList.firstOrNull()
                }

                ChannelResponseDetails(
                    id = channelResponse.id,
                    title = channelResponse.name,
                    timeLastMessage = channelResponse.timeLastMessage?.toSaoPauloTimeZone(),
                    createdAt = channelResponse.createdAt.toSaoPauloTimeZone(),
                    archivedAt = channelResponse.archivedAt?.toSaoPauloTimeZone(),
                    staffName = staffInfo?.name ?: staffInfo?.fullName() ?: "Informação não encontrada",
                    staffPicture = staffInfo?.profileImageUrl ?: "default_url",
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.CHANNEL,
                        properties = mapOf("channel_id" to channelResponse.id)
                    )
                )
            }.map { channelsResponseDetails ->
                ChannelsResponse(
                    startedAt = channelsResponseDetails.first().createdAt,
                    channels = channelsResponseDetails
                )
            }
        }
            .recordResult(span)
            .foldResponse()
    }

    suspend fun getChannelsByDemand(demandId: UUID) = span("getChannelsByDemand") { span ->
        val personId = currentUid().toPersonId()
        span.setAttribute("person_id", personId)
        span.setAttribute("demand_id", demandId)

        demandActionPlanService.get(demandId)
            .flatMap { getHealthConditionIds(it) }
            .map { healthConditionIds ->
                if (healthConditionIds.isEmpty()) return@map emptyState()

                val channels = followUpTriageChannelService.findChannelsByHealthConditionIds(healthConditionIds, personId)
                    .flatMapPair { getStaff(it) }
                    .map {  (staffs, channelResponses) ->
                        val staffMap = staffs.associateBy { it.id }

                        channelResponses.map { channelResponse ->
                            val staff = channelResponse.staffIds?.firstOrNull()?.let { staffMap[it.toUUID()] }
                            ChannelResponseDetails(
                                id = channelResponse.id,
                                title = channelResponse.name,
                                timeLastMessage = channelResponse.timeLastMessage,
                                createdAt = channelResponse.createdAt,
                                archivedAt = channelResponse.archivedAt,
                                staffName = staff?.firstName ?: "Informação não encontrada",
                                staffPicture = staff?.profileImageUrl ?: "default_url",
                                navigation = NavigationResponse(
                                    mobileRoute = MobileRouting.CHANNEL,
                                    properties = mapOf("channel_id" to channelResponse.id)
                                )
                            )
                        }
                    }.get()

                ChannelsResponse(
                    startedAt = channels.firstOrNull()?.createdAt ?: LocalDateTime.now(),
                    channels = channels
                )
            }.coFoldNotFound {
                span.setAttribute("demand_error_message", "Demand group not found")
                span.setStatus(StatusCode.ERROR)
                emptyState().success()
            }.foldResponse()
        }

    private suspend fun getStaff(channelResponse: List<ChannelResponse>): Result<List<Staff>, Throwable> =
        channelResponse.mapNotNull { channel -> channel.staffIds?.map { it.toUUID() } }.flatten()
            .takeIf { it.isNotEmpty() }
            ?.let { staffService.findByList(it) }
            ?: emptyList<Staff>().success()

    private fun emptyState() = ChannelsResponse(
        startedAt = LocalDateTime.now(),
        channels = emptyList()
    )

    suspend fun getOrCreateChannelByDemand(demandId: UUID) = span("getChannelDemandsFollowUpAction") { span ->
        val personId = currentUid().toPersonId()
        val appVersion = currentMemberAppVersion().version.version
        span.setAttribute("person_id", personId.toString())
        span.setAttribute("demand_id", demandId)
        span.setAttribute("app_version", appVersion)

        demandActionPlanService.get(demandId)
            .flatMap { getHealthConditionIds(it) }
            .flatMap { healthConditionIds ->
                if (healthConditionIds.isEmpty())
                    return@flatMap RemoteAction(mobileRoute = MobileRouting.ALICE_AGORA).success()

                followUpTriageChannelService.findOrCreateByHealthConditionIds(
                    personId,
                    healthConditionIds,
                    appVersion
                ).map { channelActionResponse ->
                    span.setAttribute("channel_id", channelActionResponse.channelResponse.id)
                    RemoteAction(
                        mobileRoute = MobileRouting.CHANNEL,
                        params = mapOf(
                            "channel_id" to channelActionResponse.channelResponse.id,
                            "pre_input_text" to (channelActionResponse.preInputText.orEmpty())
                        )
                    )
                }
            }.recordResult(span)
            .foldResponse()
        }


    suspend fun getChannelDemandsModuleEnabled() = span("getChannelDemandsModuleEnabled") { span ->
        currentUid().toPersonId().let { personId ->
            span.setAttribute("person_id", personId.toString())
            resultOf<ChannelsDemandsEnabledResponse, Throwable> {
                ChannelsDemandsEnabledResponse(
                    show = true
                )
            }
        }
            .recordResult(span)
            .foldResponse()
    }

    suspend fun getChannelDemandsFollowUpAction(channelId: String) = span("getChannelDemandsFollowUpAction") { span ->
        currentUid().toPersonId().let { personId ->
            val appVersion = currentMemberAppVersion().version.version
            span.setAttribute("person_id", personId.toString())
            span.setAttribute("request_channel_id", channelId)
            span.setAttribute("app_version", appVersion)

            followUpTriageChannelService.findOrCreate(
                personId,
                channelId,
                appVersion
            ).map { channelActionResponse ->
                span.setAttribute("channel_id", channelActionResponse.channelResponse.id)

                ChannelDemandsFollowUpAction(
                    action = NavigationResponse(
                        mobileRoute = MobileRouting.CHANNEL,
                        properties = mapOf(
                            "channel_id" to channelActionResponse.channelResponse.id,
                            "pre_input_text" to (channelActionResponse.preInputText ?: "")
                        )
                    )
                )
            }
        }.recordResult(span)
            .foldResponse()
    }

    suspend fun executeQuestion(channelId: String, type: String) = span("executeQuestion") { span ->
        span.setAttribute("channel_id", channelId)
        span.setAttribute("type", type)

        val origin = Origin.valueOf(type.uppercase())

        val (content, channelOptions) = when(origin) {
            Origin.ARCHIVE_CHANNEL -> Pair(getContentMessageQuestionArchiveChannel(), listOf(YES_ARCHIVE_CHANNEL, DO_NOT_ARCHIVE_CHANNEL))
            else -> throw UnsupportedOperationException("Type [$type] not mapped to send question message")
        }

        channelService.sendQuestionMessage(channelId, content, channelOptions, hideMemberInput = true).foldResponse()
    }

    private suspend fun getHealthConditionIds(demand: DemandActionPlan) =
        if (demand.healthConditionId != null) {
            listOfNotNull(demand.healthConditionId).success()
        } else getHealthConditionIdsByGroup(demand)

    private suspend fun getHealthConditionIdsByGroup(demand: DemandActionPlan) =
        if (demand.referencedModelClass == DemandActionPlanReferenceModel.HEALTH_DEMAND && demand.referencedModelId != null) {
            healthConditionGroupService.get(demand.referencedModelId!!).map {
                it.healthConditionIds
            }
        } else emptyList<UUID>().success()

    private fun parseMessage(message: String?) =
        if (message?.contains("(kind).*(category)".toRegex()) == true) {
            gson.fromJson<ChannelMessageEncodedRequest>(message).let {
                it.message() to it
            }
        } else message to ChannelMessageEncodedRequest()

    private fun SendMessageRequest.parseContent() =
        this.copy(content = parseMessage(this.content).first ?: this.content)

    private fun getDateToListChannelDemands() =
        LocalDateTime.now().minusDays(getDaysToListChannelDemands().toLong())

    private fun getDaysToListChannelDemands() =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "days_to_list_channel_demands",
            defaultValue = 90
        )

    private suspend fun DemandChannelsCondition.description() =
        channels.size.let { size ->
            val text = if (size == 1) M.CHANNELS_DEMAND_DESCRIPTION_SINGULAR
            else M.CHANNELS_DEMAND_DESCRIPTION_PLURAL

            i18n(text).format(size)
        }

    private fun getContentMessageQuestionArchiveChannel() = FeatureService.get(
        namespace = FeatureNamespace.ALICE_APP,
        key = "content_message_question_archive_channel",
        defaultValue = "Ao encerrar este atendimento, ele será arquivado na seção 'Conversas arquivadas' e não poderá ser reaberto." +
                "<br><br>Se precisar de algo mais, é só abrir um novo chat com a gente." +
                "<br><br>Tem certeza de que deseja encerrar o atendimento?"
    )

}

data class ChatResponse(
    val id: String,
)

data class ChannelMessageEncodedRequest(
    val category: ChannelCategory? = null,
    val subCategory: ChannelSubCategory? = null,
    val subCategoryClassifier: ChannelSubCategoryClassifier? = null,
    val tags: List<String>? = emptyList(),
    val content: String? = null,
    val origin: String? = null,
    val screeningNavigation: ChannelScreeningNavigation? = null,
) {
    fun message() = content ?: ((category?.description ?: "") +
            (subCategory?.description?.let { " > $it" } ?: "") +
            (subCategoryClassifier?.description?.let { " > $it" } ?: "")).nullIfBlank()
}

data class DemandsChannels(
    val demands: List<DemandChannels>,
    val resumeCount: Int,
    val time: String,
)

data class DemandChannels(
    val title: String,
    val description: String,
    val startedAt: LocalDateTime,
    val channels: List<String> = emptyList()
)

data class ChannelResponseDetails(
    val id: String,
    val title: String,
    val timeLastMessage: LocalDateTime?,
    val createdAt: LocalDateTime,
    val archivedAt: LocalDateTime?,
    val staffName: String,
    val staffPicture: String,
    val navigation: NavigationResponse
)

data class ChannelsResponse(
    val startedAt: LocalDateTime,
    val channels: List<ChannelResponseDetails>
)

data class ChannelsDemandsEnabledResponse(
    val show: Boolean
)

data class ChannelDemandsFollowUpAction(
    val action: NavigationResponse
)
