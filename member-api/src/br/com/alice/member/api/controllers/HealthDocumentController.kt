package br.com.alice.member.api.controllers

import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.common.thumbnail.ThumbnailUtil
import br.com.alice.data.layer.models.TestResultFile
import br.com.alice.data.layer.models.UploadedBy.MEMBER
import br.com.alice.member.api.converters.HealthDocumentResponseConverter
import br.com.alice.member.api.models.HealthDocumentListResponse
import br.com.alice.member.api.models.RenameRequest
import br.com.alice.membership.client.PersonTestResultFileService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.io.BufferedInputStream
import java.io.File
import java.time.LocalDateTime
import java.util.UUID

class HealthDocumentController(
    private val fileVaultStorage: FileVaultStorage,
    private val personTestResultFileService: PersonTestResultFileService
) : Controller() {

    suspend fun get(id: UUID): Response =
        personTestResultFileService.get(id)
            .map(HealthDocumentResponseConverter::convert)
            .foldResponse()

    suspend fun delete(id: UUID): Response =
        personTestResultFileService.delete(id)
            .map(HealthDocumentResponseConverter::convert)
            .foldResponse()

    suspend fun rename(id: UUID, renameRequest: RenameRequest): Response =
        personTestResultFileService.rename(id, renameRequest.name)
            .map(HealthDocumentResponseConverter::convert)
            .foldResponse()

    suspend fun upload(multipartRequest: MultipartRequest): Response {
        val personId = PersonId.fromString(currentUid())
        val documentName = multipartRequest.parameters.getValue("documentName")

        val fileContent = BufferedInputStream(multipartRequest.fileContent!!)
        val extension = multipartRequest.file!!.extension
        val thumbnail = ThumbnailUtil.generateThumbnail(fileContent, extension)

        val thumbnailFile = if (thumbnail != null) {
            val file = File(multipartRequest.file!!.nameWithoutExtension + "_thumb.${if (extension == "pdf") "png" else extension}")
            fileVaultStorage.store(
                personId = personId,
                domain = "personHealthDocument",
                multipartRequest = multipartRequest.copy(fileContent = thumbnail, file = file)
            ).get()
        } else null

        return fileVaultStorage.store(
            personId = personId,
            domain = "personHealthDocument",
            multipartRequest = multipartRequest.copy(fileContent = fileContent)
        ).then {
            logger.info("uploaded file", "documentName" to documentName, "file" to it)
        }.flatMap {
            personTestResultFileService.add(
                TestResultFile(
                    file = it,
                    thumbnail = thumbnailFile,
                    personId = personId,
                    description = documentName,
                    performedAt = LocalDateTime.now(),
                    fileExtension = it.type,
                    uploadedBy = MEMBER
                )
            )
        }.map(HealthDocumentResponseConverter::convert).then {
            logger.info(
                "person health document created",
                "documentName" to documentName,
                "healthDocumentResponse" to it
            )
        }.foldResponse()
    }

    suspend fun list(): Response {
        val personId = PersonId.fromString(currentUid())
        return personTestResultFileService.list(personId)
            .mapEach(HealthDocumentResponseConverter::convert)
            .map(::HealthDocumentListResponse)
            .foldResponse()
    }
}
