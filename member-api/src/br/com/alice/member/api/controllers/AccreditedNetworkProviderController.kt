package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getRequired
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.extensions.validateRequired
import br.com.alice.common.foldResponse
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.converters.accreditedNetwork.toAccreditedNetworkProfileTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderUnitRequestType
import br.com.alice.member.api.models.accreditedNetwork.TabOptions
import br.com.alice.member.api.models.accreditedNetwork.TabSelector
import br.com.alice.member.api.models.currentAppVersion
import br.com.alice.member.api.models.currentMemberAppVersion
import br.com.alice.member.api.services.AccreditedNetworkInternalService
import br.com.alice.member.api.services.AccreditedNetworkSpecialistInternalService
import br.com.alice.member.api.usecases.ValidatePreActivatedMemberUseCase
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.opentelemetry.api.trace.Span
import java.util.UUID

class AccreditedNetworkProviderController(
    private val accreditedProvidersNetworkService: AccreditedNetworkInternalService,
    private val accreditedNetworkSpecialistInternalService: AccreditedNetworkSpecialistInternalService,
    private val validatePreActivatedMemberUseCase: ValidatePreActivatedMemberUseCase
) : Controller() {

    suspend fun getUnitByType(queryParams: Parameters): Response {
        val requiredQuery = listOf("type", "lat", "lng")
        queryParams.validateRequired(requiredQuery)
        val (typeString, lat, lng) = queryParams.getRequired(requiredQuery)
        val types = listOf(ProviderUnitRequestType.valueOf(typeString))

        return accreditedProvidersNetworkService
            .getUnitsByTypes(currentUid().toPersonId(), types, lat, lng, currentMemberAppVersion().version)
            .foldResponse()
    }

    suspend fun getHealthSpecialists(queryParams: Parameters): Response {
        val requiredQuery = listOf("specialty_id", "lat", "lng")
        queryParams.validateRequired(requiredQuery)
        val (specialtyIdString, lat, lng) = queryParams.getRequired(requiredQuery)

        return accreditedProvidersNetworkService.getSpecialists(
            currentUid().toPersonId(),
            specialtyIdString.toUUID(),
            subSpecialtyId = null,
            suggestedSpecialist = null,
            lat,
            lng,
            currentMemberAppVersion().version
        ).foldResponse()
    }

    suspend fun getProviders(queryParams: Parameters): Response {
        val requiredQuery = listOf("health_plan_task_id", "lat", "lng")
        queryParams.validateRequired(requiredQuery)
        val (healthPlanTaskIdString, lat, lng) = queryParams.getRequired(requiredQuery)

        return accreditedProvidersNetworkService.getProvidersByHealthPlanTask(
            currentUid().toPersonId(),
            healthPlanTaskIdString.toUUID(),
            lat,
            lng,
            currentMemberAppVersion().version
        ).foldResponse()
    }

    suspend fun getProviderDetails(providerType: String, providerId: String, queryParams: Parameters): Response {
        val requiredQuery = listOf("lat", "lng")
        queryParams.validateRequired(requiredQuery)
        val providerTypeEnum = ConsolidatedAccreditedNetworkType.valueOf(providerType.uppercase())
        val providerIdUUID = providerId.toUUID()
        val (lat, lng) = queryParams.getRequired(requiredQuery)
        val isExamRedirect = queryParams["is_exam_redirect"]?.toBoolean() ?: false
        val specialtyId = queryParams["specialty_id"]?.toUUID()

        return accreditedProvidersNetworkService.getProviderDetails(
            personId = currentUid().toPersonId(),
            providerType = providerTypeEnum,
            providerId = providerIdUUID,
            appVersion = currentAppVersion(),
            lat = lat,
            lng = lng,
            isExamRedirect = isExamRedirect,
            specialtyId = specialtyId,
        ).foldResponse()
    }

    suspend fun getSpecialistDetails(specialistId: String, queryParams: Parameters): Response {
        val specialistIdUUID = specialistId.toUUID()
        val lat = queryParams["lat"]
        val lng = queryParams["lng"]
        val healthPlanTask = queryParams["health_plan_task_id"]?.toUUID()
        return accreditedNetworkSpecialistInternalService.getSpecialistDetails(
            currentUid().toPersonId(),
            specialistIdUUID,
            currentAppVersion(),
            lat?.toDouble(),
            lng?.toDouble(),
            healthPlanTask
        ).foldResponse()
    }

    suspend fun getProfileDetails(providerId: String, queryParams: Parameters): Response {
        val personId = currentUid().toPersonId()
        val appVersion = currentMemberAppVersion().version

        val isActiveMember = !validatePreActivatedMemberUseCase.isPreActivatedMember(personId, appVersion)

        val tabOptions = TabOptions(
            includeMembershipCard = isActiveMember
        )

        val requiredQuery = listOf("provider_type")
        queryParams.validateRequired(requiredQuery)

        val (providerType) = queryParams.getRequired(requiredQuery)
        val providerTypeEnum = ConsolidatedAccreditedNetworkType.valueOf(providerType.uppercase())
        val providerIdUUID = providerId.toUUID()

        val lat = queryParams["lat"]
        val lng = queryParams["lng"]
        val selectTab = queryParams["tab"]?.let { TabSelector.valueOf(it.uppercase()) }

        return when (providerTypeEnum) {
            ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
            ConsolidatedAccreditedNetworkType.PARTNER_HEALTH_PROFESSIONAL,
            ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL -> {
                val healthPlanTask = queryParams["health_plan_task_id"]?.toUUID()
                val isMfc = queryParams["is_mfc"]?.toBoolean() ?: false

                if (isMfc) {
                    accreditedNetworkSpecialistInternalService.getHealthcareTeamSpecialist(
                        currentUid().toPersonId(),
                        providerIdUUID,
                        lat?.toDouble(),
                        lng?.toDouble(),
                        currentAppVersion()
                    )
                } else {
                    accreditedNetworkSpecialistInternalService.getSpecialistDetails(
                        currentUid().toPersonId(),
                        providerIdUUID,
                        currentAppVersion(),
                        lat?.toDouble(),
                        lng?.toDouble(),
                        healthPlanTask
                    )
                }
                    .map { it.toAccreditedNetworkProfileTransport(selectTab, tabOptions) }
                    .fold(
                        { it.toResponse() },
                        {
                            if (it is UnsupportedOperationException)
                                Response(
                                    status = HttpStatusCode.TemporaryRedirect,
                                    message = "${ServiceConfig.baseUrl}/app_content/screen/empty_mfc",
                                )
                            else throw it
                        }
                    )
            }

            else -> {
                val isExamRedirect = queryParams["is_exam_redirect"]?.toBoolean() ?: false
                val specialtyId = queryParams["specialty_id"]?.toUUID()

                accreditedProvidersNetworkService.getProviderDetails(
                    personId = currentUid().toPersonId(),
                    providerType = providerTypeEnum,
                    providerId = providerIdUUID,
                    appVersion = currentAppVersion(),
                    lat = lat,
                    lng = lng,
                    isExamRedirect = isExamRedirect,
                    specialtyId = specialtyId,
                ).map {
                    it.toAccreditedNetworkProfileTransport(selectTab, tabOptions)
                }.foldResponse()
            }
        }
    }

    private suspend fun getMyHealthcareTeamPhysicianDetailsInternal(
        queryParams: Parameters,
        convertToProfile: Boolean
    ): Response = span("getMyHealthcareTeamPhysicianDetailsInternal") { span ->
        span.setHealthcareTeamPhysicianDetailsRequest(queryParams, convertToProfile)
        val lat = queryParams["lat"]
        val lng = queryParams["lng"]

        val personId = currentUid().toPersonId()
        val appVersion = currentMemberAppVersion().version

        val isActiveMember = !validatePreActivatedMemberUseCase.isPreActivatedMember(personId, appVersion)
        span.setAttribute("is_active_member", isActiveMember)

        val tabOptions = TabOptions(
            includeMembershipCard = isActiveMember
        )

        accreditedNetworkSpecialistInternalService.getMyHealthcareTeamSpecialist(
            currentUid().toPersonId(),
            lat?.toDouble(),
            lng?.toDouble(),
            currentMemberAppVersion().version,
        )
            .fold(
                {
                    val result =
                        it.takeIf { !convertToProfile } ?: it.toAccreditedNetworkProfileTransport(null, tabOptions)

                    result.success().foldResponse()
                },
                {
                    if (it is UnsupportedOperationException)
                        Response(
                            status = HttpStatusCode.TemporaryRedirect,
                            message = "${ServiceConfig.baseUrl}/app_content/screen/empty_mfc",
                        )
                    else throw it
                }
            )
    }

    suspend fun getMyHealthcareTeamPhysicianDetails(queryParams: Parameters): Response =
        span("getMyHealthcareTeamPhysicianDetails") {
            getMyHealthcareTeamPhysicianDetailsInternal(queryParams, false)
        }

    suspend fun getMyHealthcareTeamPhysicianDetailsV2(queryParams: Parameters): Response =
        span("getMyHealthcareTeamPhysicianDetailsV2") {
            getMyHealthcareTeamPhysicianDetailsInternal(queryParams, true)
        }

    suspend fun getHealthcareTeamPhysicianDetails(physicianId: UUID, queryParams: Parameters): Response {
        val lat = queryParams["lat"]
        val lng = queryParams["lng"]

        return accreditedNetworkSpecialistInternalService.getHealthcareTeamSpecialist(
            currentUid().toPersonId(),
            physicianId,
            lat?.toDouble(),
            lng?.toDouble(),
            currentMemberAppVersion().version,
        ).fold(
            {
                it.toResponse()
            },
            {
                if (it is UnsupportedOperationException)
                    Response(
                        status = HttpStatusCode.TemporaryRedirect,
                        message = "${ServiceConfig.baseUrl}/app_content/screen/empty_mfc",
                    )
                else throw it
            }
        )
    }

    suspend fun getFavorites(queryParams: Parameters): Response {
        val requiredQuery = listOf("lat", "lng")
        queryParams.validateRequired(requiredQuery)
        val (lat, lng) = queryParams.getRequired(requiredQuery)

        return accreditedProvidersNetworkService.getFavorites(
            currentUid().toPersonId(),
            lat,
            lng,
            currentMemberAppVersion().version
        ).foldResponse()
    }

    private suspend fun Span.setHealthcareTeamPhysicianDetailsRequest(
        queryParams: Parameters,
        convertToProfile: Boolean
    ) {
        setAttribute("person_id", currentUid().toPersonId().toString())
        setAttribute("query_params", queryParams.entries().joinToString(","))
        setAttribute("convert_to_profile", convertToProfile)
        setAttribute("app_version", currentMemberAppVersion().version.toString())
    }

}
