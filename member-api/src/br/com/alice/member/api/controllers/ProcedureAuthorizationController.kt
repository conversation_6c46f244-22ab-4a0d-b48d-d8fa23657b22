package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.member.api.services.ProcedureAuthorizationService
import java.util.UUID

class ProcedureAuthorizationController(
    private val procedureAuthorizationService: ProcedureAuthorizationService
) : Controller() {

    suspend fun getList(): Response = procedureAuthorizationService.getList(currentUid().toPersonId()).foldResponse()

    suspend fun getDetail(totvsGuiaId: UUID): Response =
        procedureAuthorizationService.getDetail(totvsGuiaId).foldResponse()

}
