package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.member.api.models.ExamResultCollectionResponse
import br.com.alice.testresult.client.ExamResultService
import br.com.alice.testresult.models.FileTransportType
import com.github.kittinunf.result.map
import io.ktor.content.ByteArrayContent
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode.Companion.OK
import io.ktor.http.HttpStatusCode.Companion.TemporaryRedirect

class ExamResultController(
    private val examResultService: ExamResultService
) : Controller() {
    suspend fun index(): Response =
        examResultService.getByPersonId(currentUid())
            .map { ExamResultCollectionResponse(it) }
            .foldResponse()

    suspend fun attachmentData(partner: String, externalId: String, itemId: String): Response {
        val dataTransport = examResultService.getAttachment(partner, externalId, itemId).get()
        return when (dataTransport.type) {
            FileTransportType.LINK -> Response(TemporaryRedirect, dataTransport.url!!)
            FileTransportType.BYTE_ARRAY -> Response(OK, ByteArrayContent(dataTransport.byteArray!!, ContentType.Application.Pdf, OK))
        }
    }
}
