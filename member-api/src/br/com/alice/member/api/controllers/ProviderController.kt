package br.com.alice.member.api.controllers

import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.foldResponse
import br.com.alice.common.observability.Spannable
import br.com.alice.member.api.converters.ProviderUnitResponseConverter
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.map
import java.util.UUID

class ProviderController(
    private val providerUnitService: ProviderUnitService,
) : Controller(), Spannable {

    suspend fun getProviderUnit(providerUnitId: UUID): Response {
        return providerUnitService.get(providerUnitId).map {
            ProviderUnitResponseConverter.convert(it)
        }.foldResponse()
    }
}
