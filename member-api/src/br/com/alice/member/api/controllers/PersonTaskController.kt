package br.com.alice.member.api.controllers

import br.com.alice.common.MultipartRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.PersonTask
import br.com.alice.data.layer.models.TaskStatus
import br.com.alice.data.layer.models.TaskType
import br.com.alice.member.api.models.PersonTaskAttachmentDeleteRequest
import br.com.alice.member.api.models.PersonTaskRequest
import br.com.alice.member.api.services.AppointmentScheduleService
import br.com.alice.membership.client.PersonTaskService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map

class PersonTaskController(
    private val personTaskService: PersonTaskService,
    private val fileVaultStorage: FileVaultStorage,
    private val appointmentScheduleService: AppointmentScheduleService
) : Controller() {

    suspend fun create(personTaskRequest: PersonTaskRequest): Response {
        logger.info("PersonTaskController::createFinancialData", "title" to personTaskRequest.title)
        val personId = PersonId.fromString(currentUid())
        return personTaskService.create(
            PersonTask(
                personId = personId,
                title = personTaskRequest.title ?: "",
                description = "Arquivo enviado pelo usuário",
                type = TaskType.TEST_REQUEST,
                status = TaskStatus.WAITING_REVIEW,
                attachmentIds = emptyList()
            ),
        ).foldResponse()
    }

    suspend fun upload(multipartRequest: MultipartRequest): Response {
        val personId = PersonId.fromString(currentUid())
        val testName = multipartRequest.parameters.getValue("testName")
        val personTaskId = multipartRequest.parameters["personTaskId"]

        return fileVaultStorage.store(
            personId = personId,
            domain = "personTask",
            multipartRequest = multipartRequest
        )
            .flatMapPair {
                logger.info(
                    "PersonTaskController::upload uploaded file",
                    "request_test_name" to testName
                )
                personTaskService.upsert(
                    PersonTask(
                        personId = personId,
                        title = testName,
                        description = "Arquivo enviado pelo usuário",
                        type = TaskType.TEST_REQUEST,
                        status = TaskStatus.WAITING_REVIEW,
                        attachmentIds = listOf(it.id),
                        id = personTaskId?.toSafeUUID() ?: RangeUUID.generate(),
                    ),
                )
            }
            .map { (personTask, file) ->
                logger.info(
                    "PersonTaskController::upload person task created",
                    "person_task_id" to personTask.id,
                    "person_task_title" to personTask.title
                )
                appointmentScheduleService.getAppointmentScheduleCalendar(
                    personId,
                    AppointmentScheduleType.TEST,
                    personTask,
                    file
                )
            }.foldResponse()
    }

    suspend fun deleteAttachment(request: PersonTaskAttachmentDeleteRequest): Response {
        val personId = currentUid()
        val domain = "personTask"

        return fileVaultStorage.delete(
            personId = personId,
            domain = domain,
            namespace = request.namespace,
            fileName = request.filePath
        )
            .flatMap {
                logger.info(
                    "PersonTaskController::deleteAttachment deleted attachment",
                    "success" to it.success,
                    "person_id" to personId,
                    "person_task_id" to request.personTaskId,
                    "domain" to domain,
                    "namespace" to request.namespace,
                    "file_name" to request.filePath
                )

                personTaskService.getById(request.personTaskId)
                    .flatMap { personTask ->
                        val deletedAttachmentId = request.filePath.substringBefore(".").toUUID()
                        val updatedAttachmentList = personTask.attachmentIds.minus(deletedAttachmentId).distinct()
                        val personTaskStatus =
                            if (updatedAttachmentList.isEmpty()) TaskStatus.CANCELED else TaskStatus.WAITING_REVIEW

                        personTaskService.update(
                            personTask.copy(
                                attachmentIds = updatedAttachmentList,
                                status = personTaskStatus
                            )
                        )
                    }
            }
            .foldResponse()
    }

}

