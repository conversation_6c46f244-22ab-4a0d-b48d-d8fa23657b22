package br.com.alice.member.api.controllers

import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenType
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.converters.appointmentSchedule.OnSiteScheduleProviderUnitScreenConverter
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.schedule.client.AppointmentSchedulePreTriageService
import br.com.alice.schedule.client.SchedulePreTriageFilters
import br.com.alice.schedule.client.TriageOnSiteProviderUnitFilters
import com.github.kittinunf.result.map
import java.util.UUID

class AppointmentScheduleContentController(
    private val appointmentSchedulePreTriageService: AppointmentSchedulePreTriageService,
    private val onSiteScheduleProviderUnitScreenConverter: OnSiteScheduleProviderUnitScreenConverter
) : Controller() {

    companion object {
        private val screenType = ScreenType.SCREENING_SCHEDULE_REDIRECT
        private const val PATH = "/bud/start/"
    }

    suspend fun getScheduleNavigation(): Response = span("getScheduleNavigation") { span ->
        val personId = currentUid().toPersonId()
        span.setAttribute("person_id", personId)
        getScheduleTriageRemoteAction(personId)
            .recordResult(span)
            .foldResponse()
    }

    suspend fun getScheduleOnSiteProviders(
        appointmentScheduleEventTypeId: UUID
    ): Response = span("getScheduleOnSiteProviders") { span ->
        currentUid().toPersonId().let { personId ->
            span.setAttribute("person_id", personId)
            span.setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId)

            appointmentSchedulePreTriageService.getProviderUnitDetailsByHealthcareTeamOnSiteSchedule(
                TriageOnSiteProviderUnitFilters(
                    personId = personId,
                    appointmentScheduleEventId = appointmentScheduleEventTypeId
                )
            ).map { providerUnitResponse ->
                onSiteScheduleProviderUnitScreenConverter.convert(
                    appointmentScheduleEventTypeId,
                    providerUnitResponse.providerUnits
                )
            }
                .recordResult(span)
                .foldResponse()
        }
    }

    private suspend fun getScheduleTriageRemoteAction(
        personId: PersonId
    ) = span("getScheduleTriageRemoteAction") { span ->
        span.setAttribute("person_id", personId)

        appointmentSchedulePreTriageService
            .getProtocolByFilters(SchedulePreTriageFilters(personId = personId))
            .map { preTriageResponse ->
                span.setAttribute("root_node_id", preTriageResponse.rootNodeId)

                RemoteAction(
                    mobileRoute = MobileRouting.CHESHIRE_SCREEN,
                    params = mapOf(
                        "action" to RemoteAction(
                            method = RemoteActionMethod.POST,
                            endpoint = ServiceConfig.url("${PATH}${preTriageResponse.rootNodeId}"),
                            params = mapOf(
                                "screen_id" to screenType.value
                            )
                        )
                    )
                )
            }
    }

}
