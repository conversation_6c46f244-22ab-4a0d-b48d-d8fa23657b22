package br.com.alice.member.api.controllers

import M
import br.com.alice.bottini.client.LeadService
import br.com.alice.common.ErrorResponse
import br.com.alice.common.MultipartRequest
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.andThen
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.foldResponse
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.models.Gender
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonAdditionalInfo
import br.com.alice.data.layer.models.Pronoun
import br.com.alice.ehr.client.PersonAdditionalInfoService
import br.com.alice.member.api.builders.PersonMissingDataNavigationBuilder
import br.com.alice.member.api.converters.CurrentUserResponseConverter
import br.com.alice.member.api.converters.PersonConverter
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.PersonLoginResponse
import br.com.alice.member.api.models.UpdateUserRequest
import br.com.alice.member.api.models.UserAddressResponse
import br.com.alice.member.api.models.UserPersonAdditionalInfoRequest
import br.com.alice.member.api.models.onboarding.CreatePersonRequest
import br.com.alice.member.api.models.onboarding.UpdateBasicPersonInfoRequest
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.membership.client.MembershipPersonService
import br.com.alice.membership.client.PersonLoginService
import br.com.alice.membership.model.events.PersonAgeDefinedEvent
import br.com.alice.moneyin.client.BillingAccountablePartyService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.time.LocalDateTime

class PersonController(
    private val membershipPersonService: MembershipPersonService,
    private val personService: PersonService,
    private val fileVaultStorage: FileVaultStorage,
    private val personAdditionalInfoService: PersonAdditionalInfoService,
    private val billingAccountablePartyService: BillingAccountablePartyService,
    private val leadService: LeadService,
    private val personLoginService: PersonLoginService,
    private val kafkaProducerService: KafkaProducerService,
) : Controller() {

    suspend fun currentUser(): Response {
        val currentPersonId = currentUid()
        val notFoundMessage = i18n(M.PERSON_NOT_FOUND)

        val personAdditionalInfo = personAdditionalInfoService.get(currentPersonId.toPersonId()).getOrNullIfNotFound()
        val personId = currentPersonId.toPersonId()

        return personService.get(personId).map {
            CurrentUserResponseConverter.convert(it, personAdditionalInfo)
        }.foldResponse(
            { it },
            NotFoundException::class to { ErrorResponse("person_not_found", notFoundMessage) }
        )
    }

    suspend fun updateUser(request: UpdateUserRequest): Response {
        val currentPersonId = currentUid()

        val personAdditionalInfo: PersonAdditionalInfo? = if (request.isSetAdditionalInformation()) {
            personAdditionalInfoService.getOrCreate(currentPersonId.toPersonId()).flatMap {
                val personAdditionalInfoUpdated = it.copy(
                    emergencyContactName = request.emergencyContactName,
                    emergencyContactRelationship = request.emergencyContactRelationship,
                    emergencyContactEmail = request.emergencyContactEmail,
                    emergencyContactPhone = request.emergencyContactPhone,
                    contactSharingDeclined = request.contactSharingDeclined
                )
                personAdditionalInfoService.update(personAdditionalInfoUpdated)
            }.getOrNullIfNotFound()
        } else {
            personAdditionalInfoService.get(currentPersonId.toPersonId()).getOrNullIfNotFound()
        }

        val personId = currentPersonId.toPersonId()

        return personService.get(personId)
            .flatMap { person ->
                val hasNickNameUpdate = request.nickName != person.nickName
                personService.update(person = person.updateFromRequest(request))
                    .then {
                        if (hasNickNameUpdate) updateAppState(personId)
                        val event = PersonAgeDefinedEvent(it)
                        kafkaProducerService.produce(event)
                    }
            }
            .map { CurrentUserResponseConverter.convert(it, personAdditionalInfo) }
            .foldResponse()
    }

    suspend fun updateProfilePicture(multipartRequest: MultipartRequest): Response {
        val personId = PersonId.fromString(currentUid())
        return fileVaultStorage.store(
            personId = personId,
            domain = "membership",
            namespace = "member-photo",
            multipartRequest = multipartRequest
        ).flatMap {
            logger.info("uploaded member profile picture", "file" to it)
            personService.updateProfilePicture(personId, it).then {
                updateAppState(personId)
            }
        }.foldResponse(
            { CurrentUserResponseConverter.convert(it) }
        )
    }

    suspend fun create(createPersonRequest: CreatePersonRequest): Response {
        val person = PersonConverter.convert(createPersonRequest)

        return withUnauthenticatedTokenWithKey(person.id.toString()) {
            membershipPersonService.addGasInformationIfNecessary(person)
                .flatMap {
                    val leadId = it.leadId

                    if (leadId != null)
                        leadService.createPerson(it).then { person ->
                            personLoginService.generateNewAccessCode(
                                person = person,
                                expirationInMinutes = LeadService.TWENTY_FOUR_HOURS,
                                firstAccess = true
                            )
                        }
                    else
                        personService.create(person)
                            .andThen { newPerson -> billingAccountablePartyService.createForPerson(newPerson) }
                            .then { person ->
                                personLoginService.generateNewAccessCode(
                                    person = person,
                                    expirationInMinutes = LeadService.TWENTY_FOUR_HOURS,
                                    firstAccess = true
                                )
                            }
                }
                .map {
                    PersonLoginResponse(
                        it.contactName,
                        it.obfuscatedEmail,
                        it.contactName,
                        it.obfuscatedPhoneNumber
                    )
                }
                .foldResponse(
                    { it },
                    DuplicatedItemException::class to {
                        ErrorResponse(
                            (it as DuplicatedItemException).code,
                            "Você já tem conta na Alice"
                        )
                    }
                )
        }
    }

    suspend fun getPersonNavigationMissingData(mobileRoute: String): Response {
        val currentPersonId = currentUid()
        return personService.get(currentPersonId.toPersonId()).map {
            PersonMissingDataNavigationBuilder.buildNavigation(
                MobileRouting.valueOf(mobileRoute),
                it
            )
        }.foldResponse()
    }

    suspend fun updateBasicInfo(basicPersonInfoRequest: UpdateBasicPersonInfoRequest): Response {
        val currentPersonId = currentUid()

        return personService.get(currentPersonId.toPersonId()).flatMap { person ->
            val address = Address.buildDefaultWithPostalCode(basicPersonInfoRequest.postalCode)
            personService.update(
                person.copy(
                    dateOfBirth = LocalDateTime.parse(basicPersonInfoRequest.dateOfBirth),
                    addresses = listOf(address)
                )
            )
        }.foldResponse()
    }

    suspend fun acceptTerms(): Response =
        personService.acceptTerms(currentUid().toPersonId()).foldResponse()

    suspend fun upsertPersonAdditionalInfo(request: UserPersonAdditionalInfoRequest): Response =
        personAdditionalInfoService.getOrCreate(currentUid().toPersonId()).flatMap {
            personAdditionalInfoService.update(
                it.copy(
                    emergencyContactName = request.emergencyContactName,
                    emergencyContactRelationship = request.emergencyContactRelationship,
                    emergencyContactEmail = request.emergencyContactEmail,
                    emergencyContactPhone = request.emergencyContactPhone,
                    contactSharingDeclined = request.contactSharingDeclined
                )
            )
        }.foldResponse()

    private fun updateAddresses(
        addresses: List<Address>,
        newAddress: UserAddressResponse?,
        postalCode: String?
    ): List<Address> {
        val address =
            newAddress?.convertTo(Address::class) ?: postalCode?.let { Address.buildDefaultWithPostalCode(it) }
        return when {
            address == null -> addresses
            addresses.size < 2 -> listOf(address)
            else -> listOf(address) + addresses.subList(1, addresses.size)
        }
    }

    private fun updateAppState(personId: PersonId) {
        AppStateNotifier.updateAppState(personId, AppState.MEMBER_PERSONAL_DATA)
    }

    private fun Person.updateFromRequest(request: UpdateUserRequest) =
        copy(
            phoneNumber = request.phoneNumber,
            nickName = request.nickName,
            socialName = request.socialName,
            gender = request.gender?.let { Gender.valueOf(it) },
            nonBinaryIdentity = request.nonBinaryIdentity,
            pronoun = request.pronoun?.let { Pronoun.valueOf(it) },
            addresses = updateAddresses(addresses, request.address, request.postalCode),
            dateOfBirth = request.dateOfBirth?.let { LocalDateTime.parse(it) } ?: dateOfBirth
        )

}
