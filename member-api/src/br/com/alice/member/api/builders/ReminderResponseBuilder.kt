package br.com.alice.member.api.builders

import M
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.reminder.ReminderActionResponse
import br.com.alice.member.api.models.reminder.ReminderImage
import br.com.alice.member.api.models.reminder.ReminderImageType
import br.com.alice.member.api.models.reminder.ReminderResponse
import br.com.alice.member.api.models.reminder.ReminderStyle

object ReminderResponseBuilder {
    suspend fun buildHealthDocumentsReminder(i18n: suspend (String) -> String): ReminderResponse {
        return ReminderResponse(
            title = i18n(M.HEALTH_DOCUMENTS_REMINDER_TITLE),
            message = i18n(M.HEALTH_DOCUMENTS_REMINDER_MESSAGE),
            style = ReminderStyle.TERTIARY.toString().lowercase(),
            image = ReminderImage(
                url = MobileApp.Assets.Reminder.HEALTH_DOCUMENTS_ICON,
                type = ReminderImageType.STATIC
            ),
            action = ReminderActionResponse(
                text = i18n(M.HEALTH_DOCUMENTS_REMINDER_ACTION_TEXT),
                navigation = NavigationResponse(
                    mobileRoute = MobileRouting.HEALTH_DOCUMENTS
                )
            )
        )
    }

}

