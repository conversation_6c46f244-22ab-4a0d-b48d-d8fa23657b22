package br.com.alice.member.api.builders

import br.com.alice.data.layer.models.QuestionnaireNew
import br.com.alice.member.api.ServiceConfig
import java.net.URLEncoder

class DeepLinkBuilder {
    companion object {
        private fun buildDeepLink(link: String) =
            "${ServiceConfig.appDeeplinkUrl}/${link}"

        fun buildLinkForQuestionnaireFromTask(task: QuestionnaireNew): String {
            val basePath = "questionnaire?"
            val paramMap = mapOf(
                "type" to task.questionnaireKey,
                "source_type" to task.healthFormAnswerSource?.type,
                "source_id" to task.healthFormAnswerSource?.id,
            )

            return buildDeepLink(basePath + paramMap.toQueryString())
        }

        private fun Map<String, Any?>.toQueryString(): String {
            return this.map { "${it.key}=${it.value}" }.joinToString("&")
        }
    }

}
