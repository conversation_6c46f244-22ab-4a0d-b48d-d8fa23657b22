package br.com.alice.member.api.builders

import br.com.alice.action.plan.model.ActionPlanTasksTransport
import br.com.alice.authentication.UserType
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.featureaccess.FeatureNavigation
import br.com.alice.common.featureaccess.NavigationRouting
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentScheduleOption
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonCalendly
import br.com.alice.healthplan.models.HealthPlanTransport
import br.com.alice.member.api.MobileApp
import br.com.alice.member.api.converters.ActionPlanResponseConverter
import br.com.alice.member.api.models.AppointmentScheduleHealthPlanOption
import br.com.alice.member.api.models.AppointmentScheduleOptionCalendar
import br.com.alice.member.api.models.AppointmentScheduleOptionGroup
import br.com.alice.member.api.models.AppointmentScheduleOptionGroup.Companion.mentalHealthCareOption
import br.com.alice.member.api.models.AppointmentScheduleOptionGroup.Companion.prescriptionsOption
import br.com.alice.member.api.models.AppointmentScheduleOptionGroup.Companion.symptomsWithoutDiagnosisOption
import br.com.alice.member.api.models.AppointmentScheduleOptionGroup.Companion.testResultOption
import br.com.alice.member.api.models.AppointmentScheduleOptionGroupsResponse
import br.com.alice.member.api.models.AppointmentScheduleOptionPrice
import br.com.alice.member.api.models.HealthPlanItemResponse
import br.com.alice.member.api.models.HealthPlanItemResponseStatus
import br.com.alice.member.api.models.HealthPlanResponse
import br.com.alice.member.api.models.HealthPlanSectionName
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.TestCode
import br.com.alice.schedule.client.PersonCalendlyService

class AppointmentScheduleOptionGroupsResponseBuilder(
    private val actionPlanResponseConverter: ActionPlanResponseConverter,
    private val schedulingUrlBuilder: SchedulingUrlBuilder,
    private val personCalendlyService: PersonCalendlyService,
) {

    suspend fun buildAppointmentScheduleOptionGroupsResponse(
        person: Person,
        healthPlanTransport: HealthPlanTransport?,
        tasks: ActionPlanTasksTransport,
        options: List<AppointmentScheduleOption>,
        testCodes: List<TestCode>,
        userType: UserType,
    ): AppointmentScheduleOptionGroupsResponse {
        val personCalendly = personCalendlyService.getOrCreate(person.id).get()
        val healthPlan =
            if (healthPlanTransport != null) actionPlanResponseConverter.convert(
                healthPlanTransport,
                tasks,
                person,
                options,
                testCodes,
                personCalendly = personCalendly,
            )
            else null
        val appointmentScheduleTypes = options.filter { it.showOnApp }.groupBy { it.type }

        return AppointmentScheduleOptionGroupsResponse(
            optionGroups = appointmentScheduleTypes.map {
                buildAppointmentScheduleGroup(
                    person,
                    healthPlan,
                    it.toPair(),
                )
            },
            priceInfo = buildPriceInfo(userType),
        )
    }

    suspend fun buildHealthCareTeamScreeningResponse(
        person: Person,
        nurseSchedule: AppointmentScheduleOption? = null,
        physicianSchedule: AppointmentScheduleOption? = null,
    ): AppointmentScheduleOptionGroupsResponse {
        val personCalendly = personCalendlyService.getOrCreate(person.id).get()
        val optionGroups = listOfNotNull(
            symptomsWithoutDiagnosisOption(),
            testResultOption(),
            prescriptionsOption(),
            mentalHealthCareOption(),
            nurseSchedule?.let {
                AppointmentScheduleOptionGroup(
                    title = "Consulta com enfermeiro(a) do meu Time de Saúde",
                    type = AppointmentScheduleType.HEALTHCARE_TEAM.toString(),
                    imageUrls = listOf(it.imageUrl),
                    options = listOf(
                        buildAppointmentScheduleOption(
                            person,
                            it,
                            personCalendly,
                        )
                    ),
                    screeningOptions = FeatureNavigation(
                        routing = NavigationRouting.APPOINTMENT_SCHEDULE_OPTION
                    )
                )
            },
            physicianSchedule?.let {
                AppointmentScheduleOptionGroup(
                    title = "Consulta com médico(a) do meu Time de Saúde",
                    type = AppointmentScheduleType.HEALTHCARE_TEAM.toString(),
                    imageUrls = listOf(it.imageUrl),
                    options = listOf(
                        buildAppointmentScheduleOption(
                            person,
                            it,
                            personCalendly,
                        )
                    ),
                    screeningOptions = FeatureNavigation(
                        routing = NavigationRouting.APPOINTMENT_SCHEDULE_OPTION
                    )
                )
            },
        )

        return AppointmentScheduleOptionGroupsResponse(optionGroups)
    }

    private fun buildPriceInfo(userType: UserType) = when (userType) {
        UserType.MEMBER -> null
        UserType.NON_MEMBER, UserType.PERSON, UserType.BENEFICIARY -> AppointmentScheduleOptionPrice()
    }

    private suspend fun buildAppointmentScheduleGroup(
        person: Person,
        healthPlan: HealthPlanResponse?,
        groupedOptions: Pair<AppointmentScheduleType, List<AppointmentScheduleOption>>,
    ): AppointmentScheduleOptionGroup {
        val personCalendly = personCalendlyService.getOrCreate(person.id).get()


        return when (groupedOptions.first) {
            AppointmentScheduleType.HEALTHCARE_TEAM -> AppointmentScheduleOptionGroup(
                title = "Time de Saúde",
                subtitle = "Médica(o) do Time de Saúde",
                type = groupedOptions.first.toString(),
                imageUrls = groupedOptions.second.map { it.imageUrl }.distinct(),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        option = it,
                        personCalendly,
                    )
                }
            )
            AppointmentScheduleType.IMMERSION -> AppointmentScheduleOptionGroup(
                title = "Imersão com o Time de Saúde",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.IMMERSION),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
                label = "RECOMENDADO"
            )
            AppointmentScheduleType.COMMUNITY -> AppointmentScheduleOptionGroup(
                title = "Médicos Especialistas",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.COMMUNITY),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.PHYSICAL_EDUCATOR,
            AppointmentScheduleType.FOLLOW_UP_PHYSICAL_EDUCATOR,
            -> AppointmentScheduleOptionGroup(
                title = "Preparador Físico",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.PHYSICAL_EDUCATOR),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.PHYSIOTHERAPIST -> AppointmentScheduleOptionGroup(
                title = "Fisioterapeuta",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.PHYSIOTHERAPIST),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.TEST -> AppointmentScheduleOptionGroup(
                title = "Exames e Procedimentos",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.TEST),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
                healthPlanOptions = healthPlan?.let {
                    buildHealthPlanOptions(it.items.filter { item ->
                        item.section == HealthPlanSectionName.TEST_REQUESTS && item.status == HealthPlanItemResponseStatus.TO_DO
                    })
                }
            )
            AppointmentScheduleType.NUTRITIONIST,
            AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST,
            -> AppointmentScheduleOptionGroup(
                title = "Nutricionista",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.NUTRITIONIST),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.HEALTH_DECLARATION -> AppointmentScheduleOptionGroup(
                title = "Declaração de Saúde",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.HEALTH_DECLARATION),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.CONNECTION -> AppointmentScheduleOptionGroup(
                title = "Conexão",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.CONNECTION),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.FOLLOW_UP -> AppointmentScheduleOptionGroup(
                title = "Retorno",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.FOLLOW_UP),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.OTHER -> AppointmentScheduleOptionGroup(
                title = "Outros",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.OTHER),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.PSYCHOLOGIST -> AppointmentScheduleOptionGroup(
                title = "Psicólogo",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.PSYCHOLOGIST),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.HOME_TEST -> AppointmentScheduleOptionGroup(
                title = "Exame domiciliar",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.HOME_TEST),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.NURSE -> AppointmentScheduleOptionGroup(
                title = "Enfermagem",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.NURSE),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.PHYSICIAN_ONSITE -> AppointmentScheduleOptionGroup(
                title = "Avaliação Presencial",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.PHYSICIAN_ONSITE),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.PROC_ONSITE -> AppointmentScheduleOptionGroup(
                title = "Procedimento",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.PROC_ONSITE),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.PROC_NURSE -> AppointmentScheduleOptionGroup(
                title = "Ginecológico",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.PROC_NURSE),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
            AppointmentScheduleType.NURSE_ONSITE -> AppointmentScheduleOptionGroup(
                title = "Avaliação enfermagem",
                type = groupedOptions.first.toString(),
                imageUrls = listOf(MobileApp.Assets.Scheduling.NURSE_ONSITE),
                options = groupedOptions.second.pmap {
                    buildAppointmentScheduleOption(
                        person,
                        it,
                        personCalendly
                    )
                },
            )
        }
    }

    private suspend fun buildAppointmentScheduleOption(
        person: Person,
        option: AppointmentScheduleOption,
        personCalendly: PersonCalendly,
    ): br.com.alice.member.api.models.AppointmentScheduleOption {
        val staffId = option.staffId?.toString().orEmpty()
        val eventName = option.title

        logger.info(
            "AppointmentScheduleOptionGroupsResponseBuilder::buildAppointmentScheduleOption",
            "staff_id" to staffId,
            "event_name" to eventName,
            "appointment_schedule_type" to option.type,
            "final_option_webview_url" to option.calendarUrl
        )

        return br.com.alice.member.api.models.AppointmentScheduleOption(
            title = option.title,
            description = option.description,
            imageUrl = option.imageUrl,
            calendar = buildAppointmentScheduleOptionCalendar(
                person,
                option,
                personCalendly,
            ),
            calendarUrl = schedulingUrlBuilder.buildScheduleUrlWithAppointmentScheduleOption(
                person,
                option,
                personCalendly
            ),
            scheduleOptions = schedulingUrlBuilder.buildScheduleOptions(option, person, personCalendly)
        )
    }

    private fun buildHealthPlanOptions(items: List<HealthPlanItemResponse>) =
        items.map {
            AppointmentScheduleHealthPlanOption(
                title = it.description,
                description = it.content,
                start = it.start,
                deadline = it.deadline,
                section = it.section,
                calendar = it.calendar,
                acknowledgedAt = it.acknowledgedAt,
                healthPlanTaskId = it.healthPlanTaskId,
                group = it.group
            )
        }

    private suspend fun buildAppointmentScheduleOptionCalendar(
        person: Person,
        option: AppointmentScheduleOption,
        personCalendly: PersonCalendly,
    ) =
        if (option.type == AppointmentScheduleType.COMMUNITY) {
            AppointmentScheduleOptionCalendar(
                navigation = NavigationResponse(MobileRouting.HEALTH_COMMUNITY)
            )
        } else {
            AppointmentScheduleOptionCalendar(
                calendarUrl = schedulingUrlBuilder.buildScheduleUrlWithAppointmentScheduleOption(
                    person,
                    option,
                    personCalendly,
                )
            )
        }
}

