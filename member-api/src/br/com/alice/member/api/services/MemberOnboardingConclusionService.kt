package br.com.alice.member.api.services

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.model.ActionPlanTaskFilters
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.HealthPlanTaskTemplate
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK_TEMPLATE
import br.com.alice.healthplan.client.HealthPlanTaskTemplateService
import br.com.alice.member.onboarding.model.PhysicianStaff
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class MemberOnboardingConclusionService(
   private val actionPlanTaskService: ActionPlanTaskService,
   private val healthPlanTaskTemplateService: HealthPlanTaskTemplateService,
   private val healthProfessionalService: HealthProfessionalService
) {
   companion object {
      private const val MAX_HEALTH_PLAN_TASKS = 2
   }

   suspend fun getTasks(
      memberOnboarding: MemberOnboarding
   ): Result<Pair<List<ActionPlanTask>, List<HealthPlanTaskTemplate>>, Throwable> =
      getActionPlanTasks(memberOnboarding)
         .flatMap { actionPlanTasks ->
            actionPlanTasks
               .takeIf { it.isNotNullOrEmpty() }
               ?.let { (it to emptyList<HealthPlanTaskTemplate>()).success() }
               ?: (emptyList<ActionPlanTask>() to getHealthPlanTaskTemplate(memberOnboarding).get()).success()
         }

   suspend fun getPhysicianStaff(
      physicianStaffId: UUID
   ) = healthProfessionalService.findByStaffId(physicianStaffId).map { healthProfessional ->
      PhysicianStaff(
         name = healthProfessional.name,
         council = healthProfessional.council.toString(),
         profileImageUrl = healthProfessional.imageUrl
      )
   }

   private suspend fun getHealthPlanTaskTemplate(
      memberOnboarding: MemberOnboarding
   ): Result<List<HealthPlanTaskTemplate>, Throwable> =
      getTasksIdsByModel(memberOnboarding, HEALTH_PLAN_TASK_TEMPLATE)
         .takeIf { it.isNotNullOrEmpty() }
         ?.let { healthPlanTaskTemplateService.findByIds(it) }
         ?: emptyList<HealthPlanTaskTemplate>().success()


   private suspend fun getActionPlanTasks(
      memberOnboarding: MemberOnboarding
   ): Result<List<ActionPlanTask>, Throwable> =
      getTasksIdsByModel(memberOnboarding, HEALTH_PLAN_TASK)
         .takeIf { it.isNotNullOrEmpty() }
         ?.let { actionPlanTaskService.getTasksByFilters(filters = ActionPlanTaskFilters(taskIds = it)) }
         ?: emptyList<ActionPlanTask>().success()

   private fun getTasksIdsByModel(
      memberOnboarding: MemberOnboarding,
      model: MemberOnboardingReferencedLinkModel
   ) =
      memberOnboarding.referencedLinks
         .filter { it.model == model }
         .map { it.id }
         .take(MAX_HEALTH_PLAN_TASKS)
}
