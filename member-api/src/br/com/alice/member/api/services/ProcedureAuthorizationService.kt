package br.com.alice.member.api.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.capitalizeEachWord
import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.ProfessionalIdentification.Companion.DEFAULT_PROFESSIONAL_IDENTIFICATION
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.extensions.cleanedGloss
import br.com.alice.member.api.extensions.cleanedStatus
import br.com.alice.member.api.extensions.procedureLabel
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.models.procedureAuthorization.AuthorizationTransport
import br.com.alice.member.api.models.procedureAuthorization.InstructionTransport
import br.com.alice.member.api.models.procedureAuthorization.ProcedureGlossTransport
import br.com.alice.member.api.models.procedureAuthorization.ProcedureTransport
import br.com.alice.member.api.models.procedureAuthorization.TimelineEventTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.time.LocalDate
import java.time.format.DateTimeParseException
import java.util.UUID

class ProcedureAuthorizationService(
    private val totvsGuiaService: TotvsGuiaService,
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService,
    private val healthcareResourceService: HealthcareResourceService,
) {

    companion object {
        private const val AUTHORIZATION_RECEIVED = "Solicitação recebida"
        private const val AUTHORIZATION_DISCLAIMER = "Conclusão em até 3 dias úteis"
        private const val AUTHORIZATION_EXTERNAL_CODE_PLACEHOLDER =
            "Estamos criando sua guia e em breve o número estará aqui"

        private const val PROCEDURES_ANALYSIS = "Análise de procedimentos"
        private const val PROCEDURES_ANALYSIS_FINISHED = "Análise concluída"
        private const val PROCEDURES_ANALYSIS_AUTHORIZED = "Análise concluída e autorizada"
        private const val PROCEDURES_ANALYSIS_PARTIALLY_AUTHORIZED = "Análise concluída e parcialmente autorizada"
        private const val PROCEDURES_ANALYSIS_UNAUTHORIZED = "Análise concluída e não autorizada"

        private const val PROCEDURE_AUTHORIZATION_RELEASE_DATE = "procedure_authorization_release_date"

        private const val AUTHORIZATION_LIST_TITLE = "Acompanhe a autorização das suas guias"
        private const val CALENDAR_INSTRUCTION_LABEL = "A análise pode levar até 3 dias úteis;"
        private const val CHECK_INSTRUCTION_LABEL = "Só vá até ao local com o pedido autorizado;"
        private const val ATTACHMENT_INSTRUCTION_LABEL =
            "Informe o número da guia no laboratório ou hospital para um atendimento mais rápido"

    }

    suspend fun getList(personId: PersonId): Result<ProcedureAuthorizationListResponse, Throwable> {
        logger.info("ProcedureAuthorizationService::getList begin process")

        val dateToSearch = getDateToSearch()

        dateToSearch ?: return Result.failure(
            InvalidArgumentException(code = "invalid_ff", message = "dateToSearch should be a valid LocalDate")
        )

        return totvsGuiaService.findActivesByPersonIdRequestedAfter(personId, dateToSearch)
            .map { totvsGuias ->
                ProcedureAuthorizationListResponse(
                    title = AUTHORIZATION_LIST_TITLE,
                    instructions = listOf(
                        InstructionTransport("calendar", CALENDAR_INSTRUCTION_LABEL),
                        InstructionTransport("check_outlined", CHECK_INSTRUCTION_LABEL),
                        InstructionTransport("attachment", ATTACHMENT_INSTRUCTION_LABEL)
                    ),
                    procedureAuthorizations = totvsGuias.map { it.toAuthorizationTransport() }
                )
            }
    }

    suspend fun getDetail(totvsGuiaId: UUID): Result<ProcedureAuthorizationDetailResponse, Throwable> =
        totvsGuiaService.get(totvsGuiaId).flatMap { totvsGuia ->
            mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuiaId).map { mvProcedures ->
                ProcedureAuthorizationDetailResponse(
                    authorization = totvsGuia.toAuthorizationTransport(),
                    events = listOf(
                        totvsGuia.requestEvent(),
                        totvsGuia.authorizationEvent(mvProcedures),
                        totvsGuia.conclusionEvent(mvProcedures)
                    )
                )
            }
        }

    private fun List<MvAuthorizedProcedure>.authorizedAt() =
        this.map { it.authorizedAt ?: it.createdAt }.minBy { it }

    private fun TotvsGuia.requestEvent() = TimelineEventTransport.finished(
        AUTHORIZATION_RECEIVED,
        this.requestedAt.atEndOfTheDay()
    )

    private suspend fun TotvsGuia.authorizationEvent(mvProcedures: List<MvAuthorizedProcedure>) =
        if (this.status == TotvsGuiaStatus.PENDING) TimelineEventTransport.inProgress(
            PROCEDURES_ANALYSIS,
            AUTHORIZATION_DISCLAIMER,
            mvProcedures.authorizedAt(),
            mvProcedures.toProcedureTransport(this)
        ) else TimelineEventTransport.finished(
            PROCEDURES_ANALYSIS,
            mvProcedures.authorizedAt(),
        )

    private suspend fun TotvsGuia.conclusionEvent(mvProcedures: List<MvAuthorizedProcedure>) =
        if (this.status == TotvsGuiaStatus.PENDING) TimelineEventTransport.notStarted(PROCEDURES_ANALYSIS_FINISHED) else TimelineEventTransport.finished(
            this.finishedLabel(),
            mvProcedures.authorizedAt(),
            mvProcedures.toProcedureTransport(this)
        )

    private suspend fun List<MvAuthorizedProcedure>.toProcedureTransport(totvsGuia: TotvsGuia) =
        this.getProcedures(totvsGuia)

    private suspend fun List<MvAuthorizedProcedure>.getProcedures(totvsGuia: TotvsGuia) =
        this.filter { it.procedureId != null }.map { mvProcedure ->
            val tussProcedure = healthcareResourceService.getByCode(mvProcedure.procedureId!!).getOrNullIfNotFound()

            val procedureDescription = tussProcedure?.description
                ?: "Procedimento não encontrado"

            ProcedureTransport(
                status = mvProcedure.cleanedStatus(totvsGuia).name,
                tussCode = tussProcedure?.tussCode ?: "Código não encontrado",
                description = procedureDescription,
                gloss = mvProcedure.cleanedGloss(totvsGuia)?.let {
                    ProcedureGlossTransport(title = it.title, description = it.description)
                }
            )
        }

    private fun TotvsGuia.toAuthorizationTransport() = AuthorizationTransport(
        id = this.id.toString(),
        externalCode = this.externalCode ?: AUTHORIZATION_EXTERNAL_CODE_PLACEHOLDER,
        status = this.status.name,
        label = this.procedureLabel(),
        requestedBy = if (this.requestedByProfessional.fullName.isNotNullOrBlank() &&
            this.requestedByProfessional.fullName != DEFAULT_PROFESSIONAL_IDENTIFICATION.fullName) {
            this.requestedByProfessional.fullName!!.capitalizeEachWord()
        } else "Alice",
        requestedAt = this.requestedAt.atEndOfTheDay(),
        clickAction = RemoteAction(
            mobileRoute = MobileRouting.PROCEDURE_AUTHORIZATION_DETAIL,
            params = mapOf("id" to this.id.toString())
        )
    )

    private fun TotvsGuia.finishedLabel() = when (this.status) {
        TotvsGuiaStatus.AUTHORIZED -> PROCEDURES_ANALYSIS_AUTHORIZED
        TotvsGuiaStatus.PARTIALLY_AUTHORIZED -> PROCEDURES_ANALYSIS_PARTIALLY_AUTHORIZED
        TotvsGuiaStatus.UNAUTHORIZED -> PROCEDURES_ANALYSIS_UNAUTHORIZED
        else -> PROCEDURES_ANALYSIS_FINISHED
    }

    private fun getDateToSearch(): LocalDate? {
        try {
            val featureFlag = FeatureService.get(
                FeatureNamespace.ALICE_APP,
                PROCEDURE_AUTHORIZATION_RELEASE_DATE,
                ""
            )
            return LocalDate.parse(featureFlag)
        } catch (e: DateTimeParseException) {
            logger.error("ProcedureAuthorizationService::getList fail to parse release date")
            return null
        }
    }
}

data class ProcedureAuthorizationListResponse(
    val title: String,
    val instructions: List<InstructionTransport>,
    val procedureAuthorizations: List<AuthorizationTransport>

)

data class ProcedureAuthorizationDetailResponse(
    val authorization: AuthorizationTransport,
    val events: List<TimelineEventTransport>
)
