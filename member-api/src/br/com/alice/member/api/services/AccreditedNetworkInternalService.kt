package br.com.alice.member.api.services

import br.com.alice.app.content.model.Caption
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.common.Brand
import br.com.alice.common.DistanceUtils
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.daysDiff
import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.core.extensions.notContains
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toBrazilianTimeFormat
import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.core.extensions.toSha256
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.extensions.mapOfNotNull
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.setAttribute
import br.com.alice.coverage.client.AccreditedNetworkFavoriteService
import br.com.alice.coverage.client.ConsolidatedAccreditedNetworkService
import br.com.alice.coverage.client.ConsolidatedAccreditedProvidersNetworkService
import br.com.alice.coverage.client.ConsolidatedAccreditedSpecialistNetworkService
import br.com.alice.coverage.converters.toConsolidatedSpecialistTransport
import br.com.alice.coverage.model.accredited_network.request.SpecialistRequestByProduct
import br.com.alice.coverage.model.accredited_network.request.UnitsRequest
import br.com.alice.coverage.model.accredited_network.response.ConsolidatedProviderLightTransport
import br.com.alice.coverage.model.accredited_network.response.ConsolidatedSpecialistLightTransport
import br.com.alice.data.layer.models.*
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.member.api.builders.NeedMedicalReferralCallOutBuilder
import br.com.alice.member.api.builders.NeedMedicalReferralCallOutBuilder.ScreenType
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.builders.provider.details.AliceClinicalProviderUnitDetailsBuilder
import br.com.alice.member.api.builders.provider.details.AliceProviderDetailsBuilder
import br.com.alice.member.api.builders.provider.details.CassiClinicalProviderUnitDetailsBuilder
import br.com.alice.member.api.builders.provider.details.CassiProviderUnitDetailsBuilder
import br.com.alice.member.api.builders.provider.details.CassiSpecialistProviderDetailsBuilder
import br.com.alice.member.api.builders.provider.details.ExamRedirectProviderDetailsBuilder
import br.com.alice.member.api.builders.provider.details.HealthProfessionalDetailsBuilder
import br.com.alice.member.api.extensions.filterByIdAndProviderId
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.accreditedNetwork.AccreditedNetworkProfileTransport
import br.com.alice.member.api.models.accreditedNetwork.EmptyCardTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderDetailsTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderResponse
import br.com.alice.member.api.models.accreditedNetwork.ProviderSection
import br.com.alice.member.api.models.accreditedNetwork.ProviderSectionType
import br.com.alice.member.api.models.accreditedNetwork.ProviderTransport
import br.com.alice.member.api.models.accreditedNetwork.ProviderTransport.ActionNavigation
import br.com.alice.member.api.models.accreditedNetwork.ProviderTransport.SubtitleInfo
import br.com.alice.member.api.models.accreditedNetwork.ProviderUnitRequestType
import br.com.alice.member.api.models.accreditedNetwork.SubSpecialtyTransport
import br.com.alice.member.api.models.appContent.Navigation
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.utils.TitleGenerator
import br.com.alice.membership.model.events.MemberAccreditedNetworkTrackerEvent
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.Filter
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.EventTypeProviderUnitService
import br.com.alice.schedule.client.StaffScheduleService
import br.com.alice.staff.client.CassiSpecialistService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.onFailure
import com.github.kittinunf.result.runCatching
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class AccreditedNetworkInternalService(
    private val consolidatedAccreditedNetworkService: ConsolidatedAccreditedNetworkService,
    private val consolidatedAccreditedProvidersNetworkService: ConsolidatedAccreditedProvidersNetworkService,
    private val consolidatedAccreditedSpecialistNetworkService: ConsolidatedAccreditedSpecialistNetworkService,
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
    private val staffScheduleService: StaffScheduleService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val personService: PersonService,
    private val memberService: MemberService,
    private val cassiSpecialistService: CassiSpecialistService,
    private val providerUnitService: ProviderUnitService,
    private val eventTypeProviderUnitService: EventTypeProviderUnitService,
    private val healthPlanTaskService: HealthPlanTaskService,
    private val healthCareTeamService: HealthcareTeamService,
    private val healthProfessionalService: HealthProfessionalService,
    private val schedulingUrlBuilder: SchedulingUrlBuilder,
    private val kafkaProducerService: KafkaProducerService,
    private val accreditedNetworkFavoriteService: AccreditedNetworkFavoriteService,
    private val labiIntegrationService: LabiIntegrationService,
    private val providerService: ProviderService,
    private val specialistSchedulingInternalService: SpecialistSchedulingInternalService,
    private val memberProductService: MemberProductService
) : Spannable {
    companion object {
        val SUGGESTED_SPECIALIST_TAG = Tag(
            text = "Indicado para você",
            colorScheme = TagColorScheme.MAGENTA,
            icon = "pin"
        )

        fun deAccreditationTag(date: LocalDate) = Tag(
            text = "Disponível até ${date.toCustomFormat("dd/MM")}",
            colorScheme = TagColorScheme.BLUE
        )

        val HAS_HOSPITAL_HEALTH_TEAM = Tag(
            text = "Time de Saúde no Hospital",
            colorScheme = TagColorScheme.MAGENTA,
            icon = "diamond"
        )

        private val DEFAULT_EMPTY_PROVIDER_LIST_ACTION_WITH_REDESIGN_AA = ProviderResponse.ActionNavigation(
            label = "Falar com Alice Agora",
            navigation = NavigationResponse(mobileRoute = MobileRouting.REDESIGN_ALICE_AGORA)
        )

        private val DUQUESA_EMPTY_PROVIDERS_LIST_ACTION = ProviderResponse.ActionNavigation(
            label = "Falar com Atendimento",
            navigation = NavigationResponse(mobileRoute = MobileRouting.DUQUESA_SERVICE)
        )

        private const val AVAILABILITY_IN_NEXT_DAYS_TODAY = "Hoje"
        private const val AVAILABILITY_IN_NEXT_DAYS_TOMORROW = "Amanhã"
        private const val AVAILABILITY_IN_NEXT_DAYS_DAY_AFTER_TOMORROW = "Depois de amanhã"

        private val availabilityInNextDaysOptions = listOf(
            AVAILABILITY_IN_NEXT_DAYS_TODAY,
            AVAILABILITY_IN_NEXT_DAYS_TOMORROW,
            AVAILABILITY_IN_NEXT_DAYS_DAY_AFTER_TOMORROW
        )

        private val specialistsConsolidatedTypes = listOf(
            ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
            ConsolidatedAccreditedNetworkType.PARTNER_HEALTH_PROFESSIONAL,
            ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL
        )

        const val DEFAULT_LIST_TITLE = "Rede Credenciada"
        const val ADVANCED_ACCESS_TITLE = "Agendar Consulta"
        const val SPECIALISTS_LIST_TITLE = "Especialistas e Clínicas"
        const val SPECIALIST_EMPTY_LIST_TITLE = "Nenhum resultado próximo a você"
        const val SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE = "Conte com nosso time do Alice Agora para te ajudar"
        const val SPECIALIST_EMPTY_LIST_DESCRIPTION_DUQUESA = "Conte com nosso time de Atendimento para te ajudar"
        const val ADVANCED_ACCESS_EMPTY_LIST_TITLE = "Não foi possível carregar as informações."

        const val FAVORITES_EMPTY_LIST_TITLE = "A lista de favoritos está vazia"
        const val FAVORITES_EMPTY_LIST_DESCRIPTION =
            "Você pode favoritar especialistas e clínicas pela rede credenciada"
        val FAVORITES_EMPTY_LIST_ACTION = ProviderResponse.ActionNavigation(
            label = "Ir para especialistas e clínicas",
            navigation = NavigationResponse(
                mobileRoute = MobileRouting.ACCREDITED_NETWORK,
                params = mapOf(
                    "endpoint" to "/accredited_network/medical_specialties",
                    "params" to mapOf(
                        "title" to "Especialistas e Clínicas",
                    )
                )
            )
        )

        const val PROFESSIONAL_PLACEHOLDER_IMAGE =
            "https://alice-member-app-assets.s3.amazonaws.com/placeholder/providers/Professional.svg"
        const val LAB_PLACEHOLDER_IMAGE =
            "https://alice-member-app-assets.s3.amazonaws.com/placeholder/providers/Lab.svg"
        const val HOSPITAL_PLACEHOLDER_IMAGE =
            "https://alice-member-app-assets.s3.amazonaws.com/placeholder/providers/Hospital.svg"
        const val LIMIT_FOR_SHOWING_ADVANCED_ACCESS_NEXT_SLOTS = 4
    }

    private val Person.isChildren: Boolean
        get() = this.age.let { actualAge ->
            this.dateOfBirth != null && actualAge < getChildrenAgeThreshold()
        }

    private val Person.isNotChildren: Boolean
        get() = this.age.let { it >= getChildrenAgeThreshold() }

    private fun getExpandedDistanceInMeters(): Int = FeatureService.get(
        FeatureNamespace.MEMBERSHIP,
        "expanded_distance_by_km",
        5000
    ).let { it * 1000 }

    private fun getDistanceInMeters(): Int =
        FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "distance_by_km",
            50
        ).let { it * 1000 }

    private fun getDefaultEmptyProvidersListAction(
        brand: Brand
    ): ProviderResponse.ActionNavigation =
        if (brand.isDuquesa() && !hasDuquesaMemberMigrateToAliceApp()) DUQUESA_EMPTY_PROVIDERS_LIST_ACTION else {
            DEFAULT_EMPTY_PROVIDER_LIST_ACTION_WITH_REDESIGN_AA
        }

    private fun getChildrenAgeThreshold(): Int =
        FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "children_age_threshold",
            0
        )

    private suspend fun List<ProviderTransport>.toProviderResponse(
        title: String,
        providersSubtypeId: UUID? = null,
        providersType: ProviderResponse.Type?,
        emptyListTitle: String? = null,
        emptyListDescription: String? = null,
        brand: Brand,
        highlights: List<ProviderTransport> = emptyList(),
        appVersion: SemanticVersion,
        otherSpecialists: List<ProviderTransport> = emptyList(),
        specialtyId: UUID? = null,
        subSpecialtyId: UUID? = null,
        subSpecialties: List<MedicalSpecialty> = emptyList(),
        emptyListAction: ProviderResponse.ActionNavigation? = null,
        callout: CalloutSection? = null,
        isAdvancedAccess: Boolean = false,
        member: Member
    ): ProviderResponse {

        logger.info(
            "toProviderResponse",
            "specialtyId" to specialtyId,
            "subSpecialtyId" to subSpecialtyId,
            "isAdvancedAccess" to isAdvancedAccess
        )

        val items = if (subSpecialtyId == null && otherSpecialists.isNotEmpty()) otherSpecialists else this

        if (items.isEmpty()) return ProviderResponse(
            title = title,
            items = emptyList(),
            action = getDefaultEmptyProvidersListAction(brand),
            providersType = providersType?.toCesProvidersType() ?: "",
            providersSubtypeId = providersSubtypeId?.toString(),
            emptyContent = ProviderResponse.EmptyContentTransport(
                title = emptyListTitle,
                description = emptyListDescription,
                action = emptyListAction ?: getDefaultEmptyProvidersListAction(brand),
            ),
            emptyListTitle = emptyListTitle,
            emptyListDescription = emptyListDescription,
            highlights = highlights,
            otherSectionTitle = providersType?.toOtherSectionTitle() ?: "Outros",
            isDuquesa = brand.isDuquesa(),
            subSpecialties = subSpecialties.map { it.toSubSpecialtyTransport() },
            providers = buildProviderSections(
                this,
                otherSpecialists,
                appVersion,
                highlights,
                specialtyId,
                subSpecialtyId,
                providersType,
                isAdvancedAccess,
                member
            ),
            callout = callout,
        )

        return ProviderResponse(
            title = title,
            items = buildProviderResponseItems(
                items,
                highlights.map { it.id },
                highlights.mapNotNull { it.providerUnitId },
                providersType,
                isAdvancedAccess
            ),
            action = emptyListAction ?: getDefaultEmptyProvidersListAction(brand),
            providersType = providersType?.toCesProvidersType() ?: "",
            providersSubtypeId = providersSubtypeId?.toString(),
            emptyContent = ProviderResponse.EmptyContentTransport(
                title = emptyListTitle,
                description = emptyListDescription,
                action = emptyListAction ?: getDefaultEmptyProvidersListAction(brand),
            ),
            emptyListTitle = emptyListTitle,
            emptyListDescription = emptyListDescription,
            highlights = highlights,
            otherSectionTitle = providersType?.toOtherSectionTitle() ?: "Outros",
            providers = if (providersType == ProviderResponse.Type.LABORATORY) {
                buildLaboratoryProviderSection(this)
            } else {
                buildProviderSections(
                    this,
                    otherSpecialists,
                    appVersion,
                    highlights,
                    specialtyId,
                    subSpecialtyId,
                    providersType,
                    isAdvancedAccess,
                    member
                )
            },
            isDuquesa = brand.isDuquesa(),
            subSpecialties = subSpecialties.map { it.toSubSpecialtyTransport() },
            callout = callout,
            availabilityInNextDays = availabilityInNextDaysOptions.takeIf { isAdvancedAccess },
        )
    }

    private fun MedicalSpecialty.toSubSpecialtyTransport() = SubSpecialtyTransport(
        id = this.id,
        name = this.name,
    )

    private fun ProviderResponse.Type.toOtherSectionTitle(): String = when (this) {
        ProviderResponse.Type.SPECIALIST,
        ProviderResponse.Type.HOSPITAL,
        ProviderResponse.Type.HOSPITAL_CHILDREN,
        ProviderResponse.Type.LABORATORY,
        ProviderResponse.Type.EMERGENCY_UNITY,
        ProviderResponse.Type.EMERGENCY_UNITY_CHILDREN -> "Outros ${this.title}"

        ProviderResponse.Type.VACCINE,
        ProviderResponse.Type.MATERNITY -> "Outras ${this.title}"

        ProviderResponse.Type.FAVORITE -> "Favoritos"
    }

    private fun ProviderResponse.Type.toCesProvidersType(): String = when (this) {
        ProviderResponse.Type.HOSPITAL,
        ProviderResponse.Type.HOSPITAL_CHILDREN,
        ProviderResponse.Type.EMERGENCY_UNITY,
        ProviderResponse.Type.EMERGENCY_UNITY_CHILDREN,
        ProviderResponse.Type.MATERNITY -> "HOSPITAL"

        ProviderResponse.Type.LABORATORY,
        ProviderResponse.Type.VACCINE -> "LABS"

        ProviderResponse.Type.SPECIALIST,
        ProviderResponse.Type.FAVORITE -> "HEALTH_PROFESSIONAL"
    }

    private suspend fun buildProviderResponseItems(
        units: List<ProviderTransport>,
        highlightedIds: List<UUID>,
        highlightsProvidersIds: List<UUID>,
        providersType: ProviderResponse.Type?,
        isAdvancedAccess: Boolean
    ): List<ProviderTransport> = units.filterByIdAndProviderId(highlightedIds, highlightsProvidersIds)
        .let { filteredUnits ->
            when (providersType) {
                ProviderResponse.Type.FAVORITE -> filteredUnits
                else -> filteredUnits.sortedByDescending { it.tag != null }
            }
        }
        .map { addDeAccreditationOrHealthTeamTag(it, isAdvancedAccess) }

    private suspend fun buildProviderSections(
        specialistsBySub: List<ProviderTransport>,
        otherSpecialists: List<ProviderTransport>,
        appVersion: SemanticVersion,
        highlights: List<ProviderTransport>,
        specialtyId: UUID?,
        subSpecialtyId: UUID?,
        providersType: ProviderResponse.Type?,
        isAdvancedAccess: Boolean = false,
        member: Member
    ): List<ProviderSection> = span("buildProviderSections") { span ->
        val specialtyName = specialtyId?.let { medicalSpecialtyService.getById(it).get().name }
        val subSpecialtyName = subSpecialtyId?.let { medicalSpecialtyService.getById(it).get().name }

        val highlightedIds = highlights.map { it.id }
        val highlightProviderUnitIds = highlights.mapNotNull { it.providerUnitId }

        val otherSpecialistsFiltered = otherSpecialists.filterByIdAndProviderId(
            highlightedIds,
            highlightProviderUnitIds
        )

        val specialistsBySubWithoutHighlights = specialistsBySub.filterByIdAndProviderId(
            highlightedIds,
            highlightProviderUnitIds
        )

        span.setAttribute("specialty_id", specialtyId)
        span.setAttribute("sub_specialty_id", subSpecialtyId)
        span.setAttribute("is_advanced_access", isAdvancedAccess)
        span.setAttribute("highlighted_ids", highlightedIds)

        buildList {
            if (highlights.isNotEmpty() && hasSupportToHighlightsOnProvidersList(appVersion)) {
                add(
                    ProviderSection(
                        title = highlights.first().subtitle,
                        description = null,
                        type = ProviderSectionType.HIGHLIGHTS,
                        items = buildProviderResponseItems(
                            highlights,
                            emptyList(),
                            emptyList(),
                            providersType,
                            isAdvancedAccess
                        ),
                    )
                )
            }

            if (shouldAddFamilyDoctorSection(specialtyId, member)) {
                add(
                    ProviderSection(
                        title = "Seu Médico de Família",
                        description = null,
                        type = ProviderSectionType.HIGHLIGHTS_EMPTY,
                        emptyCard = EmptyCardTransport(
                            title = "Você ainda não escolheu seu Médico",
                            description = "Após ${
                                member!!.activationDate!!.plusDays(60L).toBrazilianDateFormat()
                            } seu médico será atribuído automaticamente",
                            icon = "doctor",
                            action = ActionNavigation(
                                label = "Escolher Médico",
                                navigation = RemoteAction(
                                    mobileRoute = MobileRouting.MEMBER_ONBOARDING_V2_COVER,
                                    params = mapOf(
                                        "path" to "mfc_cover"
                                    )
                                )
                            ),
                        ),
                        items = emptyList(),
                    )
                )
            }

            if (specialistsBySubWithoutHighlights.isNotEmpty()) {
                add(
                    ProviderSection(
                        title = getSubWithoutHighlightsTitle(isAdvancedAccess, providersType, subSpecialtyName),
                        description = getSubWithoutHighlightsDescription(isAdvancedAccess, subSpecialtyName),
                        type = ProviderSectionType.STANDARD,
                        items = buildProviderResponseItems(
                            specialistsBySubWithoutHighlights,
                            highlightedIds,
                            highlightProviderUnitIds,
                            providersType,
                            isAdvancedAccess,
                        )
                    )
                )
            }

            if (otherSpecialistsFiltered.isNotEmpty()) {
                add(
                    ProviderSection(
                        title = if (subSpecialtyId != null) specialtyName?.let { "Outros em $specialtyName" } else null,
                        description = null,
                        type = ProviderSectionType.STANDARD,
                        items = buildProviderResponseItems(
                            otherSpecialistsFiltered,
                            highlightedIds,
                            highlightProviderUnitIds,
                            providersType,
                            isAdvancedAccess
                        ),
                    )
                )
            }
        }
    }

    private suspend fun shouldAddFamilyDoctorSection(
        specialtyId: UUID?,
        member: Member
    ): Boolean = shouldShowEmptyMFC() &&
            specialtyId != null &&
            isMfcOrPediatrician(specialtyId) &&
            !hasHealthcareTeam(member)

    private suspend fun hasHealthcareTeam(member: Member): Boolean =
        healthCareTeamService.getHealthcareTeamByPerson(member.personId).getOrNullIfNotFound() != null

    private fun getSubWithoutHighlightsTitle(
        isAdvancedAccess: Boolean,
        providersType: ProviderResponse.Type?,
        subSpecialtyName: String?
    ) = when {
        isAdvancedAccess -> null
        providersType == ProviderResponse.Type.FAVORITE -> "Favoritos"
        subSpecialtyName?.isGeneralist() == false -> "Especialistas em $subSpecialtyName"
        else -> null
    }

    private fun getSubWithoutHighlightsDescription(
        isAdvancedAccess: Boolean,
        subSpecialtyName: String?,
    ) =
        if (isAdvancedAccess) null
        else subSpecialtyName?.takeIf { it.isGeneralist().not() }
            ?.let { "Experientes na sua demanda, bem avaliados e currículos excepcionais" }

    private fun buildLaboratoryProviderSection(providers: List<ProviderTransport>): List<ProviderSection> {
        val (highlights, otherProviders) = providers.partition { it.isExamRedirect ?: false }

        return buildList {
            if (highlights.isNotEmpty()) {
                add(
                    ProviderSection(
                        title = null,
                        description = null,
                        type = ProviderSectionType.HIGHLIGHTS,
                        items = highlights
                    )
                )
            }
            if (highlights.isNotEmpty() && otherProviders.isNotEmpty()) {
                add(
                    ProviderSection(
                        title = "Todos os laboratórios",
                        description = null,
                        type = ProviderSectionType.STANDARD,
                        items = otherProviders
                    )
                )
            }
            if (highlights.isEmpty()) {
                add(
                    ProviderSection(
                        title = null,
                        description = null,
                        type = ProviderSectionType.STANDARD,
                        items = otherProviders
                    )
                )
            }
        }
    }

    private fun addDeAccreditationOrHealthTeamTag(
        providerTransport: ProviderTransport,
        isAdvancedAccess: Boolean
    ): ProviderTransport =
        if (isAdvancedAccess)
            providerTransport
        else if (providerTransport.deAccreditationDate != null)
            providerTransport.copy(
                tag = deAccreditationTag(providerTransport.deAccreditationDate),
            )
        else if (providerTransport.hasHospitalHealthTeam) {
            providerTransport.copy(
                tag = HAS_HOSPITAL_HEALTH_TEAM,
            )
        } else if (providerTransport.scheduleAvailabilityDays != null && providerTransport.scheduleAvailabilityDays > 0) {
            providerTransport.copy(
                tag = buildTagForScheduleAvailability(providerTransport)
            )
        } else providerTransport

    private fun buildProfileNavigationEndpoint(
        type: ConsolidatedAccreditedNetworkType,
        id: UUID,
        lat: String,
        lng: String,
        healthPlanTaskId: UUID?,
        isExamRedirect: Boolean? = null,
        specialtyId: UUID? = null,
        appVersion: SemanticVersion,
    ): Navigation =
        if (AccreditedNetworkProfileTransport.isSupportedInAppVersion(appVersion) && labiProvidersContains(id).not()) {
            Navigation(
                mobileRoute = MobileRouting.ACCREDITED_NETWORK_PROFILE,
                method = RemoteActionMethod.GET,
                endpoint = buildString {
                    append("/accredited_network/profile/$id?lat=$lat&lng=$lng&provider_type=$type")
                    append("&is_mfc=${specialtyId?.let { isMfc(it) } ?: false}")
                    healthPlanTaskId?.let { append("&health_plan_task_id=$healthPlanTaskId") }
                    isExamRedirect?.let { append("&is_exam_redirect=$it") }
                    specialtyId?.let { append("&specialty_id=$it") }
                }
            )
        } else {
            buildProviderNavigationEndpoint(type, id, lat, lng, healthPlanTaskId, isExamRedirect, specialtyId)
        }

    private fun buildProviderNavigationEndpoint(
        type: ConsolidatedAccreditedNetworkType,
        id: UUID,
        lat: String,
        lng: String,
        healthPlanTaskId: UUID?,
        isExamRedirect: Boolean? = null,
        specialtyId: UUID? = null,
    ) = Navigation(
        mobileRoute = MobileRouting.ACCREDITED_NETWORK_DETAIL,
        method = RemoteActionMethod.GET,
        endpoint = buildString {
            append("/accredited_network/providers/$type/$id?lat=$lat&lng=$lng")
            healthPlanTaskId?.let { append("&health_plan_task_id=$healthPlanTaskId") }
            isExamRedirect?.let { append("&is_exam_redirect=$it") }
            specialtyId?.let { append("&specialty_id=$it") }
        }
    )

    private suspend fun buildAdvancedAccessNavigation(
        provider: ConsolidatedSpecialistLightTransport,
        appointmentScheduleEventTypeId: UUID?,
        healthPlanTaskId: UUID?,
    ) = Navigation(
        mobileRoute = MobileRouting.WEBVIEW,
        params = mapOf(
            "link" to schedulingUrlBuilder.buildScheduleUrlLight(
                calendarUrl = null,
                extras = mapOfNotNull(
                    provider.staffId?.let { "staff_id" to it.toString() },
                    appointmentScheduleEventTypeId?.let { "appointment_schedule_event_type_id" to it.toString() },
                    provider.providerUnitId?.let { "provider_unit_id" to it.toString() },
                    healthPlanTaskId?.let { "health_plan_task_id" to it.toString().toSha256() }
                ),
                shouldUseInternalScheduler = true,
                isAdvancedAccess = true,
            ),
            "pop_on_complete" to true,
            "feedback_message" to "Agendamento efetuado com sucesso!",
            "token" to "true"
        ),
    )

    private fun buildHealthCareTeamSpecialistNavigationEndpoint(
        id: UUID,
        lat: String,
        lng: String,
        healthPlanTaskId: UUID?
    ) = Navigation(
        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
        method = RemoteActionMethod.GET,
        endpoint = buildString {
            append("/accredited_network/healthcare_team/physician/$id?lat=$lat&lng=$lng")
            if (healthPlanTaskId != null) {
                append("&health_plan_task_id=$healthPlanTaskId")
            }
        }
    )

    private fun buildSpecialistNavigationEndpoint(
        id: UUID,
        lat: String,
        lng: String,
        healthPlanTaskId: UUID?
    ) = Navigation(
        mobileRoute = MobileRouting.SPECIALIST_DETAILS,
        method = RemoteActionMethod.GET,
        endpoint = buildString {
            append("/accredited_network/specialists/$id?lat=$lat&lng=$lng")
            if (healthPlanTaskId != null) {
                append("&health_plan_task_id=$healthPlanTaskId")
            }
        }
    )

    private fun buildPriorityTags(
        provider: ConsolidatedSpecialistLightTransport,
        appVersion: SemanticVersion,
        suggestedSpecialistId: UUID?,
        person: Person?,
        isAdvancedAccess: Boolean,
    ): Tag? {
        val shouldCreateSuggestedOrHealthCareTeamTag = hasTitleAndSubtitleOnProviderTransport(appVersion).not()
        return when {
            isAdvancedAccess -> null
            provider.id == suggestedSpecialistId && shouldCreateSuggestedOrHealthCareTeamTag -> SUGGESTED_SPECIALIST_TAG
            provider.staffId != null && provider.staffId == suggestedSpecialistId && shouldCreateSuggestedOrHealthCareTeamTag -> SUGGESTED_SPECIALIST_TAG
            provider.segment != null && shouldCreateSuggestedOrHealthCareTeamTag -> buildTagForHealthCareTeamSegment(
                provider,
                person
            )

            else -> null
        }
    }


    private suspend fun ConsolidatedSpecialistLightTransport.toProviderTransport(
        suggestedSpecialistId: UUID?,
        lat: String,
        lng: String,
        person: Person? = null,
        appVersion: SemanticVersion,
        healthPlanTaskId: UUID?,
        memberFavorites: List<AccreditedNetworkFavorite>,
        providerType: ProviderResponse.Type?,
        appointmentScheduleEventTypeId: UUID? = null,
        isAdvancedAccess: Boolean = false,
        specialtyId: UUID? = null,
    ): ProviderTransport {
        val subtitle = when {
            this.id == suggestedSpecialistId -> "Indicado para você"
            this.staffId != null && this.staffId == suggestedSpecialistId -> "Indicado para você"
            this.segment != null -> TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
                this.gender,
                this.segment,
                person
            )

            else -> null
        }

        val specialtyId = specialtyId ?: specialtyIds.firstOrNull()

        return ProviderTransport(
            id = this.id,
            staffId = this.staffId,
            name = this.name,
            title = this.name,
            subtitle = subtitle,
            subtitleInfo = subtitle?.let {
                SubtitleInfo(text = it, colorScheme = SubtitleInfo.ColorSchema.MAGENTA)
            },
            description = buildProviderDescription(this.address, this.distance, isAdvancedAccess),
            imageUrl = getProviderImage(this.imageUrl, this.type),
            tag = buildPriorityTags(this, appVersion, suggestedSpecialistId, person, isAdvancedAccess),
            tags = buildProviderTags(isAdvancedAccess, this.nextAvailableScheduleSlots),
            tagsTitle = buildProviderTagsTitle(isAdvancedAccess, this.nextAvailableScheduleSlots),
            distance = this.distance,
            type = type,
            deAccreditationDate = this.deAccreditationDate,
            captions = buildCaptions(this.distance, this.appointmentTypes, isAdvancedAccess),
            hasHospitalHealthTeam = false,
            action = buildProviderAction(providerType, this, isAdvancedAccess),
            gender = this.gender,
            scheduleAvailabilityDays = this.scheduleAvailabilityDays?.takeIf { it > 0 },
            appointmentTypes = this.appointmentTypes,
            subSpecialtyIds = this.subSpecialtyIds,
            isFavorite = memberFavorites.any { it.referenceId == this.id },
            providerUnitId = this.providerUnitId,
            navigation = buildProviderTransportNavigation(
                provider = this,
                lat = lat,
                lng = lng,
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                healthPlanTaskId = healthPlanTaskId,
                isAdvancedAccess = isAdvancedAccess,
                specialtyId = specialtyId,
                appVersion = appVersion,
            ),
            availabilityInNextDays = takeIf { isAdvancedAccess }?.let { getAvailabilityInNextDays(this) },
        )
    }

    private fun getAvailabilityInNextDays(provider: ConsolidatedSpecialistLightTransport): List<String> {
        val dateTimeSlots = provider.nextAvailableScheduleSlots.orEmpty()
        if (dateTimeSlots.isEmpty()) return emptyList()

        return listOfNotNull(
            AVAILABILITY_IN_NEXT_DAYS_TODAY.takeIf { dateTimeSlots.any { it.isToday() } },
            AVAILABILITY_IN_NEXT_DAYS_TOMORROW.takeIf { dateTimeSlots.any { it.isTomorrow() } },
            AVAILABILITY_IN_NEXT_DAYS_DAY_AFTER_TOMORROW.takeIf { dateTimeSlots.any { it.isDayAfterTomorrow() } }
        )
    }

    private fun buildProviderDescription(
        address: StructuredAddress?,
        distance: Double?,
        isAdvancedAccess: Boolean
    ): String? {
        val formattedDistance = distance?.let { "**${DistanceUtils.formatDistance(it)}**" }
        val formattedAddress = address?.formattedAddress().takeIf { it?.isNotEmpty() == true }

        return if (isAdvancedAccess && formattedDistance != null) {
            "$formattedDistance - $formattedAddress"
        } else {
            formattedAddress
        }
    }

    private fun buildProviderTagsTitle(
        isAdvancedAccess: Boolean,
        availableDatetime: List<LocalDateTime>?
    ) =
        if (isAdvancedAccess && availableDatetime != null) "Próxima data: ${dateDetails(availableDatetime.firstOrNull())}"
        else null

    private fun dateDetails(date: LocalDateTime?): String =
        when {
            date == null -> ""
            date.isToday() -> "**Hoje ${date.toCustomFormat("dd/MM")}**"
            date.isTomorrow() -> "**Amanhã ${date.toCustomFormat("dd/MM")}**"
            else -> "**${date.toCustomFormat("dd/MM")}**"
        }

    private fun localDateInSaoPauloTimeZone() = LocalDateTime.now().toSaoPauloTimeZone().toLocalDate()

    private fun LocalDateTime.isToday() =
        this.toLocalDate().isEqual(localDateInSaoPauloTimeZone())

    private fun LocalDateTime.isTomorrow() =
        this.toLocalDate().isEqual(localDateInSaoPauloTimeZone().plusDays(1))

    private fun LocalDateTime.isDayAfterTomorrow() =
        this.toLocalDate().isEqual(localDateInSaoPauloTimeZone().plusDays(2))

    private suspend fun buildProviderTags(
        isAdvancedAccess: Boolean,
        availableDatetime: List<LocalDateTime>?
    ) =
        if (isAdvancedAccess)
            filterAvailableDatetimeByFirstDate(availableDatetime)?.map { Tag(text = it.toBrazilianTimeFormat()) }?.let {
                if (it.size > LIMIT_FOR_SHOWING_ADVANCED_ACCESS_NEXT_SLOTS) {
                    it.take(LIMIT_FOR_SHOWING_ADVANCED_ACCESS_NEXT_SLOTS).plus(Tag(text = "mais"))
                } else {
                    it
                }
            }
        else
            null

    private suspend fun buildProviderAction(
        providerType: ProviderResponse.Type?,
        consolidatedSpecialistLightTransport: ConsolidatedSpecialistLightTransport,
        isAdvancedAccess: Boolean,
    ) = if (providerType != ProviderResponse.Type.FAVORITE && !isAdvancedAccess)
        consolidatedSpecialistLightTransport.segment?.let {
            buildMFCProviderActionButton(
                consolidatedSpecialistLightTransport.staffId.toString(),
                it
            )
        } else null

    private suspend fun buildProviderTransportNavigation(
        provider: ConsolidatedSpecialistLightTransport,
        lat: String,
        lng: String,
        appointmentScheduleEventTypeId: UUID?,
        healthPlanTaskId: UUID?,
        isAdvancedAccess: Boolean,
        specialtyId: UUID? = null,
        appVersion: SemanticVersion,
    ): Navigation = when {
        isAdvancedAccess -> buildAdvancedAccessNavigation(
            provider = provider,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            healthPlanTaskId = healthPlanTaskId
        )

        isSpecialistTypeAndHealthcareTeamSpecialty(provider) && AccreditedNetworkProfileTransport.isSupportedInAppVersion(
            appVersion
        ).not() ->
            buildHealthCareTeamSpecialistNavigationEndpoint(provider.id, lat, lng, healthPlanTaskId)

        isSpecialistType(provider) && AccreditedNetworkProfileTransport.isSupportedInAppVersion(appVersion)
            .not() -> buildSpecialistNavigationEndpoint(
            provider.id,
            lat,
            lng,
            healthPlanTaskId
        )

        else -> buildProfileNavigationEndpoint(
            type = provider.type,
            id = provider.id,
            lat = lat,
            lng = lng,
            healthPlanTaskId = healthPlanTaskId,
            specialtyId = specialtyId,
            appVersion = appVersion,
        )
    }

    private fun isSpecialistType(provider: ConsolidatedSpecialistLightTransport): Boolean =
        provider.type in specialistsConsolidatedTypes

    private fun isSpecialistTypeAndHealthcareTeamSpecialty(provider: ConsolidatedSpecialistLightTransport): Boolean =
        provider.type in specialistsConsolidatedTypes &&
                (provider.segment != null || provider.specialtyIds.any {
                    it.toString() in getPedSpecialtyId() + getMfcSpecialtyId()
                })

    private fun isMfcOrPediatrician(specialtyId: UUID): Boolean =
        (getMfcSpecialtyId() + getPedSpecialtyId())
            .contains(specialtyId.toString())

    private fun isMfc(specialtyId: UUID): Boolean =
        getMfcSpecialtyId()
            .contains(specialtyId.toString())

    private fun getMfcSpecialtyId() =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "mfc_specialty_ids",
            defaultValue = emptyList<String>()
        )

    private fun getPedSpecialtyId() =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "ped_specialty_ids",
            defaultValue = emptyList<String>()
        )

    private fun getAppointmentScheduleEventTypeIdBySegment(
        segment: HealthcareTeam.Segment
    ): String = when (segment) {
        HealthcareTeam.Segment.PEDIATRIC -> FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "accredited_network_pediatric_schedule_event_type_id",
            defaultValue = ""
        )

        else -> FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "accredited_network_mfc_schedule_event_type_id",
            defaultValue = ""
        )
    }

    private suspend fun buildMFCProviderActionButton(
        staffId: String,
        segment: HealthcareTeam.Segment,
    ): ActionNavigation? = getAppointmentScheduleEventTypeIdBySegment(segment)
        .takeIf { it !== "" }
        ?.let { appointmentScheduleEventType ->
            schedulingUrlBuilder.buildScheduleUrlLight(
                calendarUrl = null,
                extras = mapOf(
                    "staff_id" to staffId,
                    "appointment_schedule_event_type_id" to appointmentScheduleEventType
                ),
                shouldUseInternalScheduler = true
            ).let { appointmentWebViewUrl ->
                ActionNavigation(
                    label = "Agendar consulta",
                    navigation = RemoteAction(
                        mobileRoute = MobileRouting.WEBVIEW,
                        removeUntilRouteName = "LOGGED_IN",
                        params = mapOf(
                            "link" to appointmentWebViewUrl,
                            "pop_on_complete" to true,
                            "feedback_message" to "Agendamento efetuado com sucesso!",
                            "token" to "true"
                        )
                    )
                )
            }
        }

    private fun buildTagForHealthCareTeamSegment(
        specialist: ConsolidatedSpecialistLightTransport,
        person: Person?
    ) = Tag(
        text = TitleGenerator.getAccreditedNetworkPhysicianDescriptionBySegment(
            specialist.gender,
            specialist.segment,
            person
        ),
        colorScheme = TagColorScheme.MAGENTA
    )

    private fun buildTagForScheduleAvailability(
        providerTransport: ProviderTransport
    ) = Tag(
        text = when (providerTransport.scheduleAvailabilityDays) {
            1 -> "Previsão de Agenda: Amanhã"
            else -> "Previsão de Agenda: ${providerTransport.scheduleAvailabilityDays} dias"
        },
        colorScheme = TagColorScheme.GRAY
    )

    private suspend fun ConsolidatedProviderLightTransport.toProviderTransport(
        lat: String,
        lng: String,
        healthPlanTaskId: UUID?,
        memberFavorites: List<AccreditedNetworkFavorite>? = null,
        subtitle: String? = null,
        subtitleInfo: SubtitleInfo? = null,
        tag: Tag? = null,
        captions: List<Caption>? = null,
        isExamRedirect: Boolean? = null,
        appVersion: SemanticVersion,
    ): ProviderTransport {
        return ProviderTransport(
            id = this.id,
            name = this.name,
            title = this.name,
            subtitle = subtitle,
            subtitleInfo = subtitleInfo,
            description = this.address?.formattedAddress(),
            imageUrl = getProviderImage(this.imageUrl, this.type),
            tag = tag,
            type = type,
            distance = this.distance,
            navigation = buildProfileNavigationEndpoint(
                type = this.type,
                id = this.id,
                lat = lat,
                lng = lng,
                healthPlanTaskId = healthPlanTaskId,
                isExamRedirect = isExamRedirect,
                appVersion = appVersion,
            ),
            deAccreditationDate = this.deAccreditationDate,
            captions = captions ?: buildCaptions(this.distance, this.appointmentTypes, false),
            hasHospitalHealthTeam = this.hasHospitalHealthTeam,
            appointmentTypes = this.appointmentTypes,
            subSpecialtyIds = this.subSpecialtyIds,
            isFavorite = memberFavorites?.any { it.referenceId == this.id } ?: false,
            isExamRedirect = isExamRedirect
        )
    }

    private fun getProviderImage(
        imageUrl: String?,
        providerType: ConsolidatedAccreditedNetworkType
    ): String = imageUrl?.trim().nullIfBlank() ?: when (providerType) {
        ConsolidatedAccreditedNetworkType.PARTNER_HEALTH_PROFESSIONAL,
        ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
        ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL,
        ConsolidatedAccreditedNetworkType.CASSI_SPECIALIST -> PROFESSIONAL_PLACEHOLDER_IMAGE

        ConsolidatedAccreditedNetworkType.LABORATORY,
        ConsolidatedAccreditedNetworkType.ALICE_HOUSE,
        ConsolidatedAccreditedNetworkType.VACCINE -> LAB_PLACEHOLDER_IMAGE

        else -> HOSPITAL_PLACEHOLDER_IMAGE
    }

    private fun buildCaptions(
        distance: Double?,
        appointmentTypes: List<ConsolidatedAccreditedNetworkAppointmentType>,
        isAdvancedAccess: Boolean,
    ): List<Caption> {
        if (isAdvancedAccess) {
            return emptyList()
        }

        val distanceCaption =
            distance?.let { listOf(Caption(text = DistanceUtils.formatDistance(distance), icon = "local_pin")) }
                ?: emptyList()
        val appointmentTypeCaptions = appointmentTypes
            .sortedBy { it.ordinal }
            .map { appointmentType ->
                Caption(
                    text = appointmentType.description,
                    icon = when (appointmentType) {
                        ConsolidatedAccreditedNetworkAppointmentType.REMOTE -> "video_on"
                        ConsolidatedAccreditedNetworkAppointmentType.PRESENTIAL -> "meeting"
                    }
                )
            }
        return if (
            distanceCaption.isEmpty() &&
            appointmentTypeCaptions.size == 1 &&
            appointmentTypeCaptions.first().text == "Vídeo"
        )
            listOf(appointmentTypeCaptions.first().copy(text = "Atendimento por Vídeo"))
        else
            distanceCaption + appointmentTypeCaptions
    }

    suspend fun getSpecialists(
        personId: PersonId,
        specialtyId: UUID,
        subSpecialtyId: UUID?,
        suggestedSpecialist: SuggestedSpecialist?,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion,
    ): Result<ProviderResponse, Throwable> = memberService.getCurrent(personId).flatMap {
        getSpecialists(it, specialtyId, subSpecialtyId, suggestedSpecialist, latitude, longitude, appVersion)
    }

    private suspend fun getSpecialists(
        member: Member,
        specialtyId: UUID,
        subSpecialtyId: UUID?,
        suggestedSpecialist: SuggestedSpecialist?,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion,
        healthPlanTaskId: UUID? = null
    ): Result<ProviderResponse, Throwable> =
        getSpecialists(
            member,
            specialtyId,
            getSubSpecialty(subSpecialtyId),
            suggestedSpecialist,
            latitude,
            longitude,
            appVersion,
            healthPlanTaskId
        )

    private suspend fun getSpecialists(
        member: Member,
        specialtyId: UUID,
        subSpecialty: MedicalSpecialty?,
        suggestedSpecialist: SuggestedSpecialist?,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion,
        healthPlanTaskId: UUID? = null
    ): Result<ProviderResponse, Throwable> = getSpecialistsByMember(
        member,
        specialtyId,
        null,
        suggestedSpecialist,
        latitude,
        longitude
    )
        .map { specialists -> specialists to subSpecialty }
        .map { (specialists, subSpecialty) ->
            subSpecialty?.takeIf { it.name.isGeneralist().not() }?.let {
                specialists.partition { specialist ->
                    subSpecialty.id in specialist.subSpecialtyIds
                }
            } ?: (specialists to emptyList())
        }
        .map { (specialistsBySub, otherSpecialists) ->
            val memberFavorites = accreditedNetworkFavoriteService.findByPersonId(
                member.personId,
                AccreditedNetworkFavoriteService.Filter(specialtyId = specialtyId)
            ).get()

            specialistsBySub.map {
                it.toProviderTransport(
                    suggestedSpecialist?.id,
                    latitude,
                    longitude,
                    null,
                    appVersion,
                    healthPlanTaskId,
                    memberFavorites,
                    null,
                    specialtyId = specialtyId,
                )
            } to otherSpecialists.map {
                it.toProviderTransport(
                    suggestedSpecialist?.id,
                    latitude,
                    longitude,
                    null,
                    appVersion,
                    healthPlanTaskId,
                    memberFavorites,
                    null,
                    specialtyId = specialtyId,
                )
            }
        }
        .map { (specialistsBySub, otherSpecialists) ->
            val suggested = suggestedSpecialist?.let { suggestedSpecialist ->
                (specialistsBySub + otherSpecialists).find {
                    it.id == suggestedSpecialist.id || it.staffId == suggestedSpecialist.id
                }
            }

            val subSpecialties = medicalSpecialtyService.findBy(
                filter = Filter(
                    parentSpecialtyId = specialtyId,
                    active = true,
                    internal = false,
                ),
                range = null,
            ).get()

            val medicalSpecialty = medicalSpecialtyService.getById(specialtyId).get()

            specialistsBySub.toProviderResponse(
                title = medicalSpecialty.name,
                brand = member.brand ?: Brand.ALICE,
                appVersion = appVersion,
                providersSubtypeId = specialtyId,
                providersType = ProviderResponse.Type.SPECIALIST,
                emptyListTitle = SPECIALIST_EMPTY_LIST_TITLE,
                emptyListDescription = if (member.isAlice) SPECIALIST_EMPTY_LIST_DESCRIPTION_ALICE else SPECIALIST_EMPTY_LIST_DESCRIPTION_DUQUESA,
                highlights = getHighlights(
                    member.personId,
                    specialtyId,
                    subSpecialty?.id,
                    latitude,
                    longitude,
                    appVersion,
                    healthPlanTaskId,
                    suggested,
                ),
                otherSpecialists = otherSpecialists,
                specialtyId = specialtyId,
                subSpecialtyId = subSpecialty?.id,
                subSpecialties = subSpecialties,
                callout = getCallOut(medicalSpecialty, member, specialtyId),
                member = member,
            )
        }.then { result ->
            sendTracker(
                member.personId,
                latitude,
                longitude,
                result.providers.flatMap { it.items },
                listOf(AccreditedNetworkTrackerFilterType.SPECIALIST_AND_CLINICAL),
                specialtyId,
                subSpecialty?.id,
                healthPlanTaskId
            )
        }

    private suspend fun getAdvancedAccessSpecialists(
        member: Member,
        specialtyId: UUID,
        subSpecialty: MedicalSpecialty,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion,
        healthPlanTaskId: UUID,
        appointmentScheduleEventTypeId: UUID,
        appointmentScheduleEventTypeProviderUnitsIds: List<UUID>,
        referralDeadlineDate: LocalDateTime? = null,
    ): Result<ProviderResponse, Throwable> = span("getAdvancedAccessSpecialists") { span ->

        span.setAttribute("specialty_id", specialtyId.toString())
        span.setAttribute("sub_specialty_id", subSpecialty.id.toString())
        span.setAttribute("appointment_event_id", appointmentScheduleEventTypeId.toString())
        span.setAttribute("referral_deadline_date", referralDeadlineDate.toString())
        span.setAttribute("latitude", latitude)
        span.setAttribute("longitude", longitude)
        span.setAttribute("health_plan_task_id", healthPlanTaskId.toString())
        span.setAttribute(
            "appointment_schedule_event_type_provider_units_ids",
            appointmentScheduleEventTypeProviderUnitsIds.joinToString(separator = ",") { it.toString() }
        )

        getSpecialistsByMember(
            member,
            specialtyId,
            subSpecialty.id,
            suggestedSpecialist = null,
            latitude,
            longitude,
            expandedDistance = true,
        )
            .map { specialists ->
                span.setAttribute("specialists_count", specialists.size)

                val enrichedSpecialists = getEnrichedAdvancedAccessSpecialists(
                    specialists = specialists,
                    appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                    appointmentScheduleEventTypeProviderUnitsIds = appointmentScheduleEventTypeProviderUnitsIds,
                    referralDeadlineDate = referralDeadlineDate,
                    latitude = latitude.toDouble(),
                    longitude = longitude.toDouble()
                ).also {
                    span.setAttribute("enriched_specialists_count", it.size)

                    if (it.isEmpty()) {
                        logger.info(
                            "Advanced access - specialists not found",
                            "health_plan_task_id" to healthPlanTaskId,
                            "specialty_id" to specialtyId,
                            "sub_specialty_id" to subSpecialty.id,
                            "referral_deadline_date" to referralDeadlineDate,
                            "person_id" to member.personId,
                        )
                    }
                }

                val providers = enrichedSpecialists
                    .map {
                        it.toProviderTransport(
                            suggestedSpecialistId = null,
                            latitude,
                            longitude,
                            person = null,
                            appVersion,
                            healthPlanTaskId,
                            emptyList(),
                            providerType = null,
                            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                            isAdvancedAccess = true,
                        )
                    }

                span.setAttribute("providers_count", providers.size)

                providers.toProviderResponse(
                    title = ADVANCED_ACCESS_TITLE,
                    brand = member.brand ?: Brand.ALICE,
                    appVersion = appVersion,
                    providersSubtypeId = specialtyId,
                    providersType = ProviderResponse.Type.SPECIALIST,
                    emptyListTitle = ADVANCED_ACCESS_EMPTY_LIST_TITLE,
                    emptyListDescription = null,
                    highlights = getHighlights(
                        member.personId,
                        specialtyId,
                        subSpecialty.id,
                        latitude,
                        longitude,
                        appVersion,
                        healthPlanTaskId,
                        suggestedSpecialist = null,
                        isAdvancedAccess = true,
                        appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                        referralDeadlineDate = referralDeadlineDate,
                        appointmentScheduleEventTypeProviderUnitsIds = appointmentScheduleEventTypeProviderUnitsIds,
                    ),
                    specialtyId = specialtyId,
                    subSpecialtyId = subSpecialty.id,
                    isAdvancedAccess = true,
                    member = member
                )
            }
            .then { result ->
                sendTracker(
                    member.personId,
                    latitude,
                    longitude,
                    result.providers.flatMap { it.items },
                    listOf(AccreditedNetworkTrackerFilterType.SPECIALIST_AND_CLINICAL),
                    specialtyId,
                    subSpecialty.id,
                    healthPlanTaskId
                )
            }
    }

    private suspend fun getEnrichedAdvancedAccessSpecialists(
        specialists: List<ConsolidatedSpecialistLightTransport>,
        appointmentScheduleEventTypeId: UUID?,
        appointmentScheduleEventTypeProviderUnitsIds: List<UUID>,
        referralDeadlineDate: LocalDateTime? = null,
        latitude: Double,
        longitude: Double,
    ): List<ConsolidatedSpecialistLightTransport> = span("getEnrichedAdvancedAccessSpecialists") { span ->
        span.setAttribute("specialists_count", specialists.size)
        span.setAttribute("appointment_event_id", appointmentScheduleEventTypeId.toString())
        span.setAttribute("referral_deadline_date", referralDeadlineDate.toString())
        span.setAttribute("latitude", latitude)
        span.setAttribute("longitude", longitude)
        val healthProfessionals =
            healthProfessionalService.getByIds(specialists.map { it.id })
                .getOrNull()
                ?: emptyList()

        span.setAttribute("health_professionals_count", healthProfessionals.size)

        return@span specialists.getSpecialistsWithStaffId(healthProfessionals)
            .also { span.setAttribute("specialists_with_staff_id_count", it.size) }
            .getWithProviderUnits(appointmentScheduleEventTypeId)
            .also { span.setAttribute("specialists_with_provider_units_count", it.size) }
            .filterByEventProviderUnitId(appointmentScheduleEventTypeProviderUnitsIds)
            .also { span.setAttribute("specialists_with_provider_units_filtered_count", it.size) }
            .getWithProviderUnitAddressAndDistance(latitude, longitude)
            .also { span.setAttribute("specialists_with_provider_unit_address_count", it.size) }
            .getWithNextAvailableDatetime(appointmentScheduleEventTypeId, referralDeadlineDate)
            .also { span.setAttribute("specialists_with_next_available_datetime_count", it.size) }
            .getWithAvailabilityByNextAvailableDatetime()
    }

    private fun List<ConsolidatedSpecialistLightTransport>.getSpecialistsWithStaffId(
        healthProfessionals: List<HealthProfessional>
    ) = mapNotNull { specialist ->
        healthProfessionals.find { it.id == specialist.id }?.let { specialist.copy(staffId = it.staffId) }
    }

    private fun List<ConsolidatedSpecialistLightTransport>.filterByEventProviderUnitId(
        appointmentScheduleEventTypeProviderUnitsIds: List<UUID>
    ) = filter { specialist ->
        appointmentScheduleEventTypeProviderUnitsIds.contains(specialist.providerUnitId)
    }

    private suspend fun List<ConsolidatedSpecialistLightTransport>.getWithNextAvailableDatetime(
        appointmentScheduleEventTypeId: UUID?,
        referralDeadlineDate: LocalDateTime? = null
    ) = mapNotNull { specialist ->
        getNextAvailableDatetimeForSpecialist(
            staffId = specialist.staffId,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            referralDeadlineDate = referralDeadlineDate,
            providerUnitId = specialist.providerUnitId
        )?.takeIf { it.isNotNullOrEmpty() }?.let {
            specialist.copy(nextAvailableScheduleSlots = it)
        }
    }

    private fun List<ConsolidatedSpecialistLightTransport>.getWithAvailabilityByNextAvailableDatetime() =
        map { specialist ->
            val today = LocalDateTime.now().toSaoPauloTimeZone().toLocalDate()
            specialist.copy(
                scheduleAvailabilityDays = specialist.nextAvailableScheduleSlots?.firstOrNull()?.let {
                    daysDiff(today, it.toLocalDate()).toInt() + 1
                }
            )
        }

    private suspend fun List<ConsolidatedSpecialistLightTransport>.getWithProviderUnits(
        appointmentScheduleEventTypeId: UUID?
    ) = flatMap { specialist ->
        staffScheduleService.getStaffSchedules(staffId = specialist.staffId!!).getOrNull()
            ?.let { staffSchedules ->
                staffSchedules
                    .filter {
                        it.type == StaffScheduleType.HAD
                                && it.status == StaffScheduleStatus.ACTIVE
                                && it.exceptionEventTypes.notContains(appointmentScheduleEventTypeId)
                    }
                    .mapNotNull { it.providerUnitId }
                    .distinct()
                    .map { specialist.copy(providerUnitId = it) }
            } ?: emptyList()
    }

    private suspend fun List<ConsolidatedSpecialistLightTransport>.getWithProviderUnitAddressAndDistance(
        latitude: Double,
        longitude: Double
    ) = mapNotNull { specialist ->
        specialist.providerUnitId?.let { providerUnitId ->
            providerUnitService.getByIds(listOf(providerUnitId)).getOrNull()?.firstOrNull()?.let { providerUnit ->
                providerUnit.address?.let { address ->
                    if (address.latitude != null && address.longitude != null) {
                        specialist.copy(
                            address = address,
                            distance = DistanceUtils.distanceInMeters(
                                address.latitude!!.toDouble(),
                                address.longitude!!.toDouble(),
                                latitude,
                                longitude
                            )
                        )
                    } else null
                }
            }
        }
    }

    private suspend fun getNextAvailableDatetimeForSpecialist(
        staffId: UUID?,
        appointmentScheduleEventTypeId: UUID?,
        referralDeadlineDate: LocalDateTime? = null,
        providerUnitId: UUID? = null,
    ): List<LocalDateTime>? = span("getNextAvailableDatetimeForSpecialist") { span ->
        span.setAttribute("staff_id", staffId.toString())
        span.setAttribute("appointment_event_id", appointmentScheduleEventTypeId.toString())
        span.setAttribute("referral_deadline_date", referralDeadlineDate.toString())
        span.setAttribute("provider_unit_id", providerUnitId.toString())

        if (staffId == null || appointmentScheduleEventTypeId == null) null
        else specialistSchedulingInternalService.getNextNAvailableDatetimeForSpecialistByEventType(
            staffId = staffId,
            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
            range = null,
            endDateTime = referralDeadlineDate?.toSaoPauloTimeZone()?.toLocalDate()?.atEndOfTheDay(),
            providerUnitId = providerUnitId,
        ).getOrNull().also { span.setAttribute("available_datetime_count", it?.size ?: 0) }
    }

    private suspend fun filterAvailableDatetimeByFirstDate(
        availableDatetime: List<LocalDateTime>?,
    ): List<LocalDateTime>? = span("filterAvailableDatetimeByFirstDate") { span ->
        span.setAttribute("available_datetime_count", availableDatetime?.size ?: 0)

        if (availableDatetime.isNullOrEmpty()) return@span null

        val firstDate = availableDatetime.min()

        return@span availableDatetime.filter { it.toLocalDate() == firstDate.toLocalDate() }.also {
            span.setAttribute("first_date", firstDate.toString())
            span.setAttribute("filtered_available_datetime_count", it.size)
        }
    }

    private suspend fun getSubSpecialty(subSpecialtyId: UUID?): MedicalSpecialty? = span("getSubSpecialty")
    { span ->
        span.setAttribute("sub_specialty_id", subSpecialtyId.toString())
        subSpecialtyId?.let { medicalSpecialtyService.getById(it).get() }
    }

    private fun matchSegmentAndSpecialty(segment: HealthcareTeam.Segment?, specialtyId: String) =
        (segment == HealthcareTeam.Segment.PEDIATRIC && getPedSpecialtyId().contains(specialtyId)) ||
                (segment == HealthcareTeam.Segment.DEFAULT && getMfcSpecialtyId().contains(specialtyId))

    private fun matchSubSpecialty(subSpecialtyIds: List<UUID>?, subSpecialtyId: UUID?): Boolean =
        subSpecialtyId != null && subSpecialtyIds?.contains(subSpecialtyId) ?: false

    private suspend fun getHighlights(
        personId: PersonId,
        specialtyId: UUID,
        subSpecialtyId: UUID?,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion,
        healthPlanTaskId: UUID?,
        suggestedSpecialist: ProviderTransport?,
        isAdvancedAccess: Boolean = false,
        appointmentScheduleEventTypeId: UUID? = null,
        referralDeadlineDate: LocalDateTime? = null,
        appointmentScheduleEventTypeProviderUnitsIds: List<UUID> = emptyList(),
    ): List<ProviderTransport> = coroutineScope {
        buildList {
            if (suggestedSpecialist != null) add(suggestedSpecialist)
            if (isMfcOrPediatrician(specialtyId)) {
                getPhysicianByPerson(personId, latitude, longitude)
                    .getOrNull()
                    .takeIf {
                        val matchSegmentAndSpecialty = matchSegmentAndSpecialty(it?.segment, specialtyId.toString())

                        if (isAdvancedAccess)
                            matchSegmentAndSpecialty && matchSubSpecialty(it?.subSpecialtyIds, subSpecialtyId)
                        else
                            matchSegmentAndSpecialty
                    }?.let { specialist ->

                        val enrichedSpecialist =
                            if (isAdvancedAccess)
                                getEnrichedAdvancedAccessSpecialists(
                                    specialists = listOf(specialist),
                                    appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                                    referralDeadlineDate = referralDeadlineDate,
                                    latitude = latitude.toDouble(),
                                    longitude = longitude.toDouble(),
                                    appointmentScheduleEventTypeProviderUnitsIds = appointmentScheduleEventTypeProviderUnitsIds,
                                ).minByOrNull { it.distance ?: Double.MAX_VALUE }
                            else
                                specialist

                        enrichedSpecialist?.toProviderTransport(
                            suggestedSpecialistId = null,
                            lat = latitude,
                            lng = longitude,
                            person = personService.get(personId).get(),
                            appVersion = appVersion,
                            healthPlanTaskId = healthPlanTaskId,
                            memberFavorites = accreditedNetworkFavoriteService.findByPersonId(
                                personId,
                                AccreditedNetworkFavoriteService.Filter(specialtyId = specialtyId)
                            ).get(),
                            providerType = null,
                            isAdvancedAccess = isAdvancedAccess,
                            appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                            specialtyId = specialtyId,
                        )?.let { add(it) }
                    }
            }
        }
    }

    private suspend fun getPhysicianByPerson(
        personId: PersonId,
        latitude: String,
        longitude: String,
    ): Result<ConsolidatedSpecialistLightTransport, Throwable> =
        healthCareTeamService.getHealthcareTeamByPerson(personId)
            .flatMap { team ->
                healthProfessionalService.findByStaffId(
                    team.physicianStaffId,
                    FindOptions(withStaff = false, withContact = true)
                )
                    .map { physician -> team to physician }
            }.map { (team, physician) ->
                physician.toConsolidatedSpecialistTransport(
                    latitude.toDouble(),
                    longitude.toDouble(),
                    team.segment
                )
            }

    private suspend fun getSpecialistsByMember(
        member: Member,
        specialtyId: UUID,
        subSpecialtyId: UUID?,
        suggestedSpecialist: SuggestedSpecialist?,
        latitude: String,
        longitude: String,
        expandedDistance: Boolean = false,
    ): Result<List<ConsolidatedSpecialistLightTransport>, Throwable> {
        logger.info(
            "Start listing specialists by member",
            "person_id" to member.personId,
            "specialty_id" to specialtyId,
            "sub_specialty_id" to subSpecialtyId,
            "suggested_specialist" to suggestedSpecialist
        )

        return consolidatedAccreditedSpecialistNetworkService.getSpecialist(
            SpecialistRequestByProduct(
                productId = member.productId,
                specialtyId = specialtyId,
                lat = latitude,
                lng = longitude,
                rangeInMeters = if (expandedDistance) getExpandedDistanceInMeters() else getDistanceInMeters(),
                subSpecialtyId = subSpecialtyId,
                suggestedSpecialist = suggestedSpecialist,
            )
        )
    }

    private fun String.isGeneralist() = this.trim().equals("Generalista", ignoreCase = true)

    private suspend fun getSpecialistsForReferral(
        member: Member,
        referral: Referral,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion,
        referralDeadlineDate: LocalDateTime?,
    ): Result<ProviderResponse, Throwable> {
        val specialtyId = referral.specialty?.id
        val subSpecialtyId = referral.subSpecialty?.id
        val suggestedSpecialist = referral.suggestedSpecialist

        logger.info(
            "Start listing specialists for referral",
            "person_id" to member.personId,
            "specialty_id" to specialtyId,
            "sub_specialty_id" to subSpecialtyId,
        )

        return if (specialtyId == null)
            ProviderResponse(
                title = SPECIALISTS_LIST_TITLE,
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = getDefaultEmptyProvidersListAction(member.brand ?: Brand.ALICE),
                ),
                action = getDefaultEmptyProvidersListAction(member.brand ?: Brand.ALICE),
                otherSectionTitle = ProviderResponse.Type.SPECIALIST.toOtherSectionTitle(),
                isDuquesa = member.isDuquesa,
            ).success()
        else {
            getSubSpecialty(subSpecialtyId).let { subSpecialty ->
                return if (subSpecialty?.isAdvancedAccess == true) {
                    logger.info(
                        "Advanced Access - Listing specialists for referral",
                        "person_id" to member.personId,
                        "specialty_id" to specialtyId,
                        "sub_specialty_id" to subSpecialty.id,
                    )

                    getAdvancedAccessSpecialistsForReferral(
                        member,
                        specialtyId,
                        subSpecialty,
                        latitude,
                        longitude,
                        appVersion,
                        referral.id,
                        referralDeadlineDate,
                    )
                } else {
                    logger.info(
                        "Listing specialists for referral",
                        "person_id" to member.personId,
                        "specialty_id" to specialtyId,
                        "sub_specialty_id" to subSpecialtyId,
                    )

                    getSpecialists(
                        member,
                        specialtyId,
                        subSpecialty,
                        suggestedSpecialist,
                        latitude,
                        longitude,
                        appVersion,
                        referral.id
                    )
                }
            }
        }
    }

    private suspend fun getAdvancedAccessSpecialistsForReferral(
        member: Member,
        specialtyId: UUID,
        subSpecialty: MedicalSpecialty,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion,
        healthPlanTaskId: UUID,
        referralDeadlineDate: LocalDateTime?,
    ): Result<ProviderResponse, Throwable> {
        val appointmentScheduleEventTypeId = getAppointmentScheduleEventTypeIdBySubSpecialty(subSpecialty.id)

        if (appointmentScheduleEventTypeId == null) {
            logger.error(
                "Appointment event id not found",
                "person_id" to member.personId,
                "specialty_id" to specialtyId,
                "sub_specialty_id" to subSpecialty.id,
            )

            return getAdvancedAccessEmptyResponse(member)
        }

        val appointmentScheduleEventTypeProviderUnitsIds =
            eventTypeProviderUnitService.getForEventType(appointmentScheduleEventTypeId)
                .getOrNull()
                ?.filter { it.status == Status.ACTIVE && it.providerUnitId != null }
                ?.map { it.providerUnitId!! }
                ?.distinct()

        if (appointmentScheduleEventTypeProviderUnitsIds.isNullOrEmpty()) {
            logger.error(
                "Appointment event provider units not found",
                "person_id" to member.personId,
                "specialty_id" to specialtyId,
                "sub_specialty_id" to subSpecialty.id,
                "appointment_schedule_event_type_id" to appointmentScheduleEventTypeId,
            )

            return getAdvancedAccessEmptyResponse(member)
        }

        return getAdvancedAccessSpecialists(
            member,
            specialtyId,
            subSpecialty,
            latitude,
            longitude,
            appVersion,
            healthPlanTaskId,
            appointmentScheduleEventTypeId,
            appointmentScheduleEventTypeProviderUnitsIds,
            referralDeadlineDate,
        )
    }

    private fun getAdvancedAccessEmptyResponse(member: Member): Result.Success<ProviderResponse> {
        return ProviderResponse(
            title = ADVANCED_ACCESS_TITLE,
            emptyContent = ProviderResponse.EmptyContentTransport(
                title = ADVANCED_ACCESS_EMPTY_LIST_TITLE,
                action = getDefaultEmptyProvidersListAction(member.brand ?: Brand.ALICE),
            ),
            action = getDefaultEmptyProvidersListAction(member.brand ?: Brand.ALICE),
            otherSectionTitle = "",
            isDuquesa = member.isDuquesa,
        ).success()
    }

    private suspend fun getAppointmentScheduleEventTypeIdBySubSpecialty(subSpecialtyId: UUID): UUID? {
        return appointmentScheduleEventTypeService.getBySubSpecialties(
            listOf(subSpecialtyId),
            AppointmentScheduleType.PHYSICIAN_ONSITE
        ).getOrNull()?.firstOrNull()?.id
    }

    suspend fun getProvidersByHealthPlanTask(
        personId: PersonId,
        healthPlanTaskId: UUID,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion
    ): Result<ProviderResponse, Throwable> {
        logger.info(
            "Start listing providers by healthPlanTask",
            "person_id" to personId,
            "health_plan_task_id" to healthPlanTaskId
        )

        val member = memberService.getCurrent(personId).get()

        val task = healthPlanTaskService.get(healthPlanTaskId).getOrElse {
            return ProviderResponse(
                title = DEFAULT_LIST_TITLE,
                emptyContent = ProviderResponse.EmptyContentTransport(
                    action = getDefaultEmptyProvidersListAction(member.brand ?: Brand.ALICE),
                ),
                action = getDefaultEmptyProvidersListAction(member.brand ?: Brand.ALICE),
                otherSectionTitle = ProviderResponse.Type.SPECIALIST.toOtherSectionTitle(),
                isDuquesa = member.isDuquesa,
            ).success()
        }

        logger.info(
            "Listing providers by healthPlanTask",
            "person_id" to personId,
            "health_plan_task" to task
        )

        return when (task.type) {
            HealthPlanTaskType.TEST_REQUEST ->
                getUnitsByTypes(
                    member = member,
                    types = listOf(ProviderUnitRequestType.LABORATORY),
                    lat = latitude,
                    lng = longitude,
                    appVersion = appVersion,
                    healthPlanTask = task
                )

            HealthPlanTaskType.REFERRAL ->
                getSpecialistsForReferral(
                    member,
                    task.specialize(),
                    latitude,
                    longitude,
                    appVersion,
                    task.deadline?.date,
                )

            HealthPlanTaskType.EMERGENCY -> {
                getProviderUnitsByAge(member, latitude, longitude, appVersion, task)
            }

            else -> {
                logger.error(
                    "HealthplanTask type not supported",
                    "person_id" to personId,
                    "health_plan_task" to task,
                    "health_plan_task"
                )
                ProviderResponse(
                    title = DEFAULT_LIST_TITLE,
                    emptyContent = ProviderResponse.EmptyContentTransport(
                        action = getDefaultEmptyProvidersListAction(member.brand ?: Brand.ALICE),
                    ),
                    action = getDefaultEmptyProvidersListAction(member.brand ?: Brand.ALICE),
                    otherSectionTitle = ProviderResponse.Type.SPECIALIST.toOtherSectionTitle(),
                    isDuquesa = member.isDuquesa,
                ).success()
            }
        }
    }

    private suspend fun getProviderUnitsByAge(
        member: Member,
        latitude: String,
        longitude: String,
        appVersion: SemanticVersion,
        healthPlanTask: HealthPlanTask?
    ): Result<ProviderResponse, Throwable> {
        val person = personService.get(member.personId).get()

        val types = when {
            person.isChildren -> listOf(ProviderUnitRequestType.EMERGENCY_UNITY_CHILDREN)
            person.isNotChildren -> listOf(ProviderUnitRequestType.EMERGENCY_UNITY)
            else -> {
                logger.info(
                    "AccreditedNetworkInternalService::getProviderUnitsByAge - Person::dateOfBirth is null",
                    "person_id" to member.personId,
                    "is_test_person" to person.isTest
                )

                listOf(ProviderUnitRequestType.EMERGENCY_UNITY, ProviderUnitRequestType.EMERGENCY_UNITY_CHILDREN)
            }
        }

        return getUnitsByTypes(member, types, latitude, longitude, appVersion, healthPlanTask)
    }

    private fun ProviderUnitRequestType.toConsolidatedType() = when (this) {
        ProviderUnitRequestType.HOSPITAL -> ConsolidatedAccreditedNetworkType.HOSPITAL
        ProviderUnitRequestType.HOSPITAL_CHILDREN -> ConsolidatedAccreditedNetworkType.HOSPITAL_CHILDREN
        ProviderUnitRequestType.LABORATORY -> ConsolidatedAccreditedNetworkType.LABORATORY
        ProviderUnitRequestType.EMERGENCY_UNITY -> ConsolidatedAccreditedNetworkType.EMERGENCY_UNITY
        ProviderUnitRequestType.EMERGENCY_UNITY_CHILDREN -> ConsolidatedAccreditedNetworkType.EMERGENCY_UNITY_CHILDREN
        ProviderUnitRequestType.MATERNITY -> ConsolidatedAccreditedNetworkType.MATERNITY
        ProviderUnitRequestType.VACCINE -> ConsolidatedAccreditedNetworkType.VACCINE
    }

    suspend fun getUnitsByTypes(
        personId: PersonId,
        types: List<ProviderUnitRequestType>,
        lat: String,
        lng: String,
        appVersion: SemanticVersion
    ): Result<ProviderResponse, Throwable> = memberService.getCurrent(personId).flatMap {
        getUnitsByTypes(it, types, lat, lng, appVersion)
    }

    private suspend fun getUnitsByTypes(
        member: Member,
        types: List<ProviderUnitRequestType>,
        lat: String,
        lng: String,
        appVersion: SemanticVersion,
        healthPlanTask: HealthPlanTask? = null,
    ): Result<ProviderResponse, Throwable> = consolidatedAccreditedProvidersNetworkService.getProviderUnits(
        UnitsRequest(
            productId = member.productId,
            types = types.map { it.toConsolidatedType() },
            lat = lat,
            lng = lng,
            rangeInMeters = getDistanceInMeters()
        )
    )
        .map {
            val providerTransports = buildProviderUnitTransports(healthPlanTask, it, lat, lng, member, appVersion)

            providerTransports.toProviderResponse(
                highlights = providerTransports.filter { provider -> provider.isExamRedirect ?: false },
                title = types.first().title,
                brand = member.brand ?: Brand.ALICE,
                appVersion = appVersion,
                providersType = types.firstOrNull()?.toProviderResponseType(),
                member = member,
            )
        }.then { result ->
            sendTracker(
                personId = member.personId,
                lat = lat,
                lng = lng,
                items = result.providers.flatMap { it.items },
                types = types.map { it.toAccreditedNetworkTrackerFilterType() },
                specialtyId = null,
                subSpecialtyId = null,
                healthPlanTaskId = healthPlanTask?.id
            )
        }

    private suspend fun buildProviderUnitTransports(
        healthPlanTask: HealthPlanTask?,
        providerUnits: List<ConsolidatedProviderLightTransport>,
        lat: String,
        lng: String,
        member: Member,
        appVersion: SemanticVersion,
    ): List<ProviderTransport> {
        val highlightsProviders = healthPlanTask?.let { getHighlightProvider(providerUnits, lat, lng, member, it) }
        val othersProviders = highlightsProviders?.let { providerUnits - it } ?: providerUnits

        val highlightTransport = highlightsProviders?.toProviderTransport(
            lat = lat,
            lng = lng,
            healthPlanTaskId = healthPlanTask.id,
            subtitle = "Menor coparticipação",
            subtitleInfo = SubtitleInfo(
                text = "Menor coparticipação",
                colorScheme = SubtitleInfo.ColorSchema.GREEN
            ),
            tag = Tag(text = "Indicado para você", colorScheme = TagColorScheme.MAGENTA),
            captions = listOf(Caption(text = "Coleta domiciliar ou presencial", icon = "home_outlined")),
            isExamRedirect = true,
            appVersion = appVersion,
        )

        val othersTransportList = othersProviders.map {
            it.toProviderTransport(
                lat = lat,
                lng = lng,
                healthPlanTaskId = healthPlanTask?.id,
                appVersion = appVersion,
            )
        }

        return highlightTransport?.let { othersTransportList + it } ?: othersTransportList
    }

    private fun labiProvidersContains(providerId: UUID?) =
        FeatureService.getList(
            namespace = FeatureNamespace.ALICE_APP,
            key = "labi_providers_ids",
            defaultValue = emptyList<UUID>()
        ).contains(providerId)

    private suspend fun getHighlightProvider(
        providerUnits: List<ConsolidatedProviderLightTransport>,
        lat: String,
        lng: String,
        member: Member,
        healthPlanTask: HealthPlanTask
    ): ConsolidatedProviderLightTransport? {
        val canLabiExamineProcedureCode = healthPlanTask.content?.get("code")
            ?.let { code ->
                val labiProcedureCodes = FeatureService.getList(
                    namespace = FeatureNamespace.ALICE_APP,
                    key = "labi_procedure_codes",
                    defaultValue = emptyList<String>()
                )

                labiProcedureCodes.contains(code)
            } ?: false

        if (!canLabiExamineProcedureCode) return null

        val matchLabiProvider = providerUnits.filter { labiProvidersContains(it.providerId) }

        val shouldShowHighlightParams = matchLabiProvider.isNotNullOrEmpty() &&
                labiIntegrationService.shouldShowHighlight(lat.toDouble(), lng.toDouble(), member)

        return takeIf { shouldShowHighlightParams }
            ?.let {
                matchLabiProvider.sortedBy {
                    it.address?.distanceInMetersTo(lat.toDouble(), lng.toDouble())
                }.first()
            }
    }

    private fun ProviderUnitRequestType.toProviderResponseType() = when (this) {
        ProviderUnitRequestType.HOSPITAL -> ProviderResponse.Type.HOSPITAL
        ProviderUnitRequestType.HOSPITAL_CHILDREN -> ProviderResponse.Type.HOSPITAL_CHILDREN
        ProviderUnitRequestType.LABORATORY -> ProviderResponse.Type.LABORATORY
        ProviderUnitRequestType.EMERGENCY_UNITY -> ProviderResponse.Type.EMERGENCY_UNITY
        ProviderUnitRequestType.EMERGENCY_UNITY_CHILDREN -> ProviderResponse.Type.EMERGENCY_UNITY_CHILDREN
        ProviderUnitRequestType.MATERNITY -> ProviderResponse.Type.MATERNITY
        ProviderUnitRequestType.VACCINE -> ProviderResponse.Type.VACCINE
    }

    suspend fun getProviderDetails(
        personId: PersonId,
        providerType: ConsolidatedAccreditedNetworkType,
        providerId: UUID,
        appVersion: SemanticVersion,
        lat: String? = null,
        lng: String? = null,
        isExamRedirect: Boolean = false,
        specialtyId: UUID?,
    ): Result<ProviderDetailsTransport, Throwable> = when (providerType) {
        ConsolidatedAccreditedNetworkType.CASSI_SPECIALIST -> getCassiSpecialistDetails(
            personId,
            providerId,
            appVersion,
            lat,
            lng
        )

        ConsolidatedAccreditedNetworkType.HEALTH_PROFESSIONAL,
        ConsolidatedAccreditedNetworkType.PARTNER_HEALTH_PROFESSIONAL,
        ConsolidatedAccreditedNetworkType.SPECIALIST_HEALTH_PROFESSIONAL -> getHealthProfessionalDetails(
            personId,
            providerId,
            appVersion,
            lat,
            lng
        )

        else -> getUnitProviderDetails(
            personId = personId,
            providerUnitId = providerId,
            type = providerType,
            appVersion = appVersion,
            lat = lat,
            lng = lng,
            isExamRedirect = isExamRedirect,
            specialtyId = specialtyId,
        )
    }

    private suspend fun getAliceUnitProviderDetails(
        providerUnit: ProviderUnit,
        person: Person,
        member: Member,
        product: Product,
        addresses: List<StructuredAddress>,
        type: ConsolidatedAccreditedNetworkType,
        referralList: List<Referral>,
        appVersion: SemanticVersion,
        lat: String?,
        lng: String?,
        isExamRedirect: Boolean,
        specialtyId: UUID?,
    ): Result<ProviderDetailsTransport, Throwable> = coroutineScope {
        val specialty = providerUnit.medicalSpecialtyIds
            .takeIf { it.isNotNullOrEmpty() }
            ?.let { specialtyIds -> specialtyIds.find { it == specialtyId } }
            ?.let { medicalSpecialtyService.getById(it).getOrNull() }

        if (providerUnit.type == ProviderUnit.Type.CLINICAL_COMMUNITY) {
            AliceClinicalProviderUnitDetailsBuilder.build(
                AliceClinicalProviderUnitDetailsBuilder.Params(
                    providerUnit = providerUnit,
                    specialty = specialty,
                    addresses = addresses,
                    person = person,
                    member = member,
                    product = product,
                    referrals = specialty?.let { referralList.filter { it.specialty?.id == specialty.id } }
                        ?: emptyList(),
                    favorite = getAccreditedNetworkFavoriteOrNull(person.id, providerUnit.id),
                    lat = lat,
                    lng = lng,
                    appVersion = appVersion,
                    showCalloutToDuquesaMember = shouldShowCallOutToDuquesaMember(member.isAlice, specialtyId),
                )
            )
        } else {
            val labiHighlightIsSupported = FeatureService.get(
                namespace = FeatureNamespace.ALICE_APP,
                key = "labi_details_min_version",
                defaultValue = "99.99.99"
            ).let { appVersion >= SemanticVersion(it) }

            if (labiHighlightIsSupported && isExamRedirect) {
                val provider = providerService.get(providerUnit.providerId)
                    .onFailure {
                        logger.error("Error retrieving provider ${providerUnit.providerId}. Assuming null.", it)
                    }.getOrNull()

                ExamRedirectProviderDetailsBuilder.build(
                    ExamRedirectProviderDetailsBuilder.Params(
                        providerUnit = providerUnit,
                        type = type,
                        addresses = addresses,
                        person = person,
                        member = member,
                        product = product,
                        lat = lat,
                        lng = lng,
                        provider = provider
                    )
                )
            } else {
                AliceProviderDetailsBuilder.build(
                    AliceProviderDetailsBuilder.Params(
                        providerUnit = providerUnit,
                        addresses = addresses,
                        person = person,
                        member = member,
                        product = product,
                        referrals = specialty?.let { referralList.filter { it.specialty?.id == specialty.id } }
                            ?: emptyList(),
                        favorite = getAccreditedNetworkFavoriteOrNull(person.id, providerUnit.id),
                        lat = lat,
                        lng = lng,
                        appVersion = appVersion,
                        type = type
                    )
                )
            }
        }.success()
    }

    private suspend fun getCassiUnitProviderDetails(
        providerUnit: ProviderUnit,
        member: Member,
        addresses: List<StructuredAddress>,
        type: ConsolidatedAccreditedNetworkType,
        referralList: List<Referral>,
        product: Product,
        person: Person,
        appVersion: SemanticVersion,
        lat: String?,
        lng: String?,
        specialtyId: UUID?,
    ): Result<ProviderDetailsTransport, Throwable> {
        if (member.cassiMember == null) {
            logger.info(
                "Member should have casssiMember, but it does not",
                "member_id" to member.id
            )
            return NotFoundException("Member ${member.id} should have casssiMember, but it does not").failure()
        }
        val specialty = providerUnit.medicalSpecialtyIds
            .takeIf { it.isNotNullOrEmpty() }
            ?.let { specialtyIds -> specialtyIds.find { it == specialtyId } }
            ?.let { medicalSpecialtyService.getById(it).get() }

        return if (providerUnit.type == ProviderUnit.Type.CLINICAL_COMMUNITY) {
            CassiClinicalProviderUnitDetailsBuilder.build(
                CassiClinicalProviderUnitDetailsBuilder.Params(
                    providerUnit = providerUnit,
                    specialty = specialty,
                    addresses = addresses,
                    member = member,
                    product = product,
                    favorite = getAccreditedNetworkFavoriteOrNull(person.id, providerUnit.id),
                    referrals = specialty?.let { referralList.filter { it.specialty?.id == specialty.id } }
                        ?: emptyList(),
                    person = person,
                    lat = lat,
                    lng = lng,
                    appVersion = appVersion,
                )
            )
        } else {
            CassiProviderUnitDetailsBuilder.build(
                CassiProviderUnitDetailsBuilder.Params(
                    providerUnit = providerUnit,
                    addresses = addresses,
                    member = member,
                    type = type,
                    favorite = getAccreditedNetworkFavoriteOrNull(person.id, providerUnit.id),
                    referrals = specialty?.let { referralList.filter { it.specialty?.id == specialty.id } }
                        ?: emptyList(),
                    person = person,
                    lat = lat,
                    lng = lng,
                    appVersion = appVersion,
                )
            )
        }.success()
    }

    private suspend fun getUnitProviderDetails(
        personId: PersonId,
        providerUnitId: UUID,
        type: ConsolidatedAccreditedNetworkType,
        appVersion: SemanticVersion,
        lat: String?,
        lng: String?,
        isExamRedirect: Boolean,
        specialtyId: UUID?
    ): Result<ProviderDetailsTransport, Throwable> = coroutineScope {
        coResultOf {
            val referralsDeferred = async { getActiveReferralTasks(personId) }
            val memberWithProductDeferred = async {
                memberProductService.getMembershipWithProduct(
                    personId,
                    MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
                ).get()
            }
            val providerUnitDeferred = async { providerUnitService.get(providerUnitId).get() }
            val personDeferred = async { personService.get(personId) }
            val (member, product) = memberWithProductDeferred.await()
            val providerUnit = providerUnitDeferred.await()
            val referrals = referralsDeferred.await().get()
            val person = personDeferred.await().get()

            when (providerUnit.contractOrigin) {
                ProviderUnit.Origin.ALICE -> {
                    getAliceUnitProviderDetails(
                        providerUnit = providerUnit,
                        person = person,
                        member = member,
                        product = product,
                        addresses = listOfNotNull(providerUnit.address),
                        type = type,
                        referralList = referrals,
                        appVersion = appVersion,
                        lat = lat,
                        lng = lng,
                        isExamRedirect = isExamRedirect,
                        specialtyId = specialtyId,
                    )
                }

                ProviderUnit.Origin.CASSI -> getCassiUnitProviderDetails(
                    providerUnit = providerUnit,
                    addresses = listOfNotNull(providerUnit.address),
                    member = member,
                    type = type,
                    referralList = referrals,
                    product = product,
                    person = person,
                    lat = lat,
                    lng = lng,
                    appVersion = appVersion,
                    specialtyId = specialtyId,
                )
            }.get()
        }
    }

    private suspend fun getCassiSpecialistDetails(
        personId: PersonId,
        specialistId: UUID,
        appVersion: SemanticVersion,
        lat: String?,
        lng: String?
    ): Result<ProviderDetailsTransport, Throwable> = coroutineScope {
        memberService.findActiveOrPendingMembership(
            personId,
            findOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
        ).map { member ->
            if (member.cassiMember == null) {
                logger.info(
                    "Member should have cassiMember, but it does not",
                    "member_id" to member.id
                )
                return@coroutineScope NotFoundException("Member ${member.id} should have cassiMember, but it does not").failure()
            }

            val referralsDeferred = async { getActiveReferralTasks(personId) }
            val specialistDeferred = async { cassiSpecialistService.get(specialistId).get() }
            val personDeferred = async { personService.get(personId) }

            val specialist = specialistDeferred.await()
            val specialty = medicalSpecialtyService.getById(specialist.specialtyId).get()
            val referrals = referralsDeferred.await().get()
            val person = personDeferred.await().get()

            CassiSpecialistProviderDetailsBuilder.build(
                CassiSpecialistProviderDetailsBuilder.Params(
                    specialist = specialist,
                    specialty = specialty,
                    addresses = specialist.address,
                    member = member,
                    favorite = getAccreditedNetworkFavoriteOrNull(person.id, specialist.id),
                    referrals = referrals.filter { it.specialty?.id == specialty.id },
                    person = person,
                    appVersion = appVersion,
                    lat = lat,
                    lng = lng
                )
            )
        }
    }

    private suspend fun getHealthProfessionalDetails(
        personId: PersonId,
        specialistId: UUID,
        appVersion: SemanticVersion,
        lat: String?,
        lng: String?
    ): Result<ProviderDetailsTransport, Throwable> = coroutineScope {
        val personDeferred = async { personService.get(personId) }
        val referralsDeferred = async { getActiveReferralTasks(personId) }
        val membershipFindOptions = MemberService.FindOptions(withPriceListing = false, withCassiMember = true)
        val membershipWithProduct = async {
            memberProductService.getMembershipWithProduct(personId, membershipFindOptions).get()
        }

        val specialistDeferred = async {
            healthProfessionalService.get(specialistId, FindOptions(withStaff = false, withContact = true)).get()
        }

        val healthProfessional = specialistDeferred.await()
        val specialtyDeferred = async {
            healthProfessional.specialtyId?.let { specialtyId ->
                medicalSpecialtyService.getById(specialtyId).get()
            }
        }
        val subSpecialtyNameDeferred = async {
            medicalSpecialtyService.getByIds(healthProfessional.subSpecialtyIds).get().map { it.name }
        }
        val (member, product) = membershipWithProduct.await()
        val specialty = specialtyDeferred.await()
        val subSpecialtyNames = subSpecialtyNameDeferred.await()
        val person = personDeferred.await().get()
        val referrals = referralsDeferred.await().get()

        HealthProfessionalDetailsBuilder.build(
            HealthProfessionalDetailsBuilder.Params(
                healthProfessional = healthProfessional,
                specialty = specialty,
                subSpecialtyNames = subSpecialtyNames,
                addresses = healthProfessional.addressesStructured.orEmpty(),
                person = person,
                favorite = getAccreditedNetworkFavoriteOrNull(person.id, healthProfessional.id),
                member = member,
                product = product,
                referrals = specialty?.let { referrals.filter { it.specialty?.id == specialty.id } } ?: emptyList(),
                appVersion = appVersion,
                lat = lat,
                lng = lng
            )
        ).success()
    }

    private suspend fun getAccreditedNetworkFavoriteOrNull(
        personId: PersonId,
        providerUnitId: UUID
    ): AccreditedNetworkFavorite? = accreditedNetworkFavoriteService
        .getByPersonIdAndReferenceId(personId, providerUnitId)
        .getOrNullIfNotFound()

    suspend fun getFavorites(
        personId: PersonId,
        lat: String,
        lng: String,
        appVersion: SemanticVersion,
    ): Result<ProviderResponse, Throwable> = coroutineScope {
        val memberDeferred = async { memberService.getCurrent(personId).get() }
        val memberPhysicianDeferred = async {
            getPhysicianByPerson(personId, lat, lng)
                .getOrNull()
                ?.toProviderTransport(
                    suggestedSpecialistId = null,
                    lat = lat,
                    lng = lng,
                    person = personService.get(personId).get(),
                    appVersion = appVersion,
                    healthPlanTaskId = null,
                    memberFavorites = emptyList(),
                    providerType = ProviderResponse.Type.FAVORITE,
                )
        }

        val favorites = accreditedNetworkFavoriteService
            .findByPersonId(personId)
            .map { favorites -> favorites.sortedByDescending { it.createdAt } }
            .get()
        val favoritesOrderIndex = favorites.withIndex().associate { it.value.referenceId to it.index }

        val specialistsDeferred = async {
            favorites.takeIf { it.isNotEmpty() }?.let { favorites ->
                consolidatedAccreditedNetworkService.findBy(
                    ConsolidatedAccreditedNetworkService.Filter(
                        referencedIds = favorites.map { it.referenceId },
                    )
                ).map { specialists ->
                    specialists.calculateDistance(lat, lng)
                        .distinctBy { it.referencedId }
                        .sortedBy { favoritesOrderIndex[it.referencedId] }
                }.get()
            } ?: emptyList()
        }

        val medicalSpecialtiesDeferred = async {
            medicalSpecialtyService.getByIds(favorites.flatMap { it.specialtyIds })
                .get()
                .associate { it.id to it.name }
        }

        val member = memberDeferred.await()
        val memberPhysician = memberPhysicianDeferred.await()
        val medicalSpecialties = medicalSpecialtiesDeferred.await()
        val specialists = specialistsDeferred.await()
            .map { specialist ->
                val medicalSpecialty = medicalSpecialties[specialist.specialtyIds.first()]

                specialist.toConsolidatedSpecialistTransport().toProviderTransport(
                    suggestedSpecialistId = null,
                    lat = lat,
                    lng = lng,
                    person = null,
                    appVersion = appVersion,
                    healthPlanTaskId = null,
                    memberFavorites = emptyList(),
                    providerType = null,
                ).copy(
                    isFavorite = true,
                    description = null,
                    subtitle = medicalSpecialty,
                    subtitleInfo = medicalSpecialty?.let {
                        SubtitleInfo(
                            text = it,
                            colorScheme = SubtitleInfo.ColorSchema.GRAY
                        )
                    },
                )
            }

        specialists.toProviderResponse(
            title = "Favoritos",
            providersType = ProviderResponse.Type.FAVORITE,
            brand = member.brand ?: Brand.ALICE,
            appVersion = appVersion,
            emptyListTitle = FAVORITES_EMPTY_LIST_TITLE,
            emptyListDescription = FAVORITES_EMPTY_LIST_DESCRIPTION,
            emptyListAction = FAVORITES_EMPTY_LIST_ACTION,
            highlights = listOfNotNull(memberPhysician),
            member = member
        ).success()
    }

    private suspend fun sendTracker(
        personId: PersonId,
        lat: String,
        lng: String,
        items: List<ProviderTransport>,
        types: List<AccreditedNetworkTrackerFilterType>,
        specialtyId: UUID? = null,
        subSpecialtyId: UUID? = null,
        healthPlanTaskId: UUID? = null
    ) = runCatching {
        kafkaProducerService.produce(
            buildTracker(
                personId,
                lat,
                lng,
                items,
                types,
                specialtyId,
                subSpecialtyId,
                healthPlanTaskId
            )
        )
    }.onFailure { logger.error("error to produced member accredited tracker", it) }

    private suspend fun getActiveReferralTasks(personId: PersonId) =
        healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(personId, HealthPlanTaskType.REFERRAL)
            .mapEach { it.specialize<Referral>() }

    private suspend fun getCallOut(medicalSpecialty: MedicalSpecialty, member: Member, specialtyId: UUID) =
        takeIf {
            !isMfcOrPediatrician(specialtyId)
                    && (shouldShowCallOutToDuquesaMember(
                member.isAlice,
                medicalSpecialty.id
            ) || (member.isAlice && medicalSpecialty.isTherapy))
        }
            ?.let {
                val referralSpecialtyIds = getActiveReferralTasks(member.personId).getOrNull()
                    ?.map { it.specialty }
                    ?.mapNotNull { it?.id }

                referralSpecialtyIds?.contains(medicalSpecialty.id)
                    ?.takeIf { !it }
                    ?.let {
                        NeedMedicalReferralCallOutBuilder.buildCalloutSection(ScreenType.SPECIALIST)
                    }
            }

    private fun buildTracker(
        personId: PersonId,
        lat: String,
        lng: String,
        providerResponse: List<ProviderTransport>,
        type: List<AccreditedNetworkTrackerFilterType>,
        specialtyId: UUID?,
        subSpecialtyId: UUID?,
        healthPlanTaskId: UUID?
    ) = MemberAccreditedNetworkTrackerEvent(
        personId = personId,
        latitude = lat,
        longitude = lng,
        range = getDistanceInMeters().toString(),
        results = providerResponse.map {
            AccreditedNetworkTrackerResult(
                id = it.id,
                type = it.type,
            )
        },
        types = type,
        specialities = specialtyId?.let { listOf(it) } ?: emptyList(),
        subSpecialities = subSpecialtyId?.let { listOf(it) } ?: emptyList(),
        healthPlanTaskId = healthPlanTaskId
    )

    private fun ProviderUnitRequestType.toAccreditedNetworkTrackerFilterType() = when (this) {
        ProviderUnitRequestType.HOSPITAL -> AccreditedNetworkTrackerFilterType.HOSPITAL
        ProviderUnitRequestType.HOSPITAL_CHILDREN -> AccreditedNetworkTrackerFilterType.HOSPITAL_CHILDREN
        ProviderUnitRequestType.LABORATORY -> AccreditedNetworkTrackerFilterType.LABORATORY
        ProviderUnitRequestType.EMERGENCY_UNITY -> AccreditedNetworkTrackerFilterType.EMERGENCY_UNITY
        ProviderUnitRequestType.EMERGENCY_UNITY_CHILDREN -> AccreditedNetworkTrackerFilterType.EMERGENCY_UNITY_CHILDREN
        ProviderUnitRequestType.MATERNITY -> AccreditedNetworkTrackerFilterType.MATERNITY
        ProviderUnitRequestType.VACCINE -> AccreditedNetworkTrackerFilterType.VACCINE
    }

    private fun hasSupportToHighlightsOnProvidersList(appVersion: SemanticVersion): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "app_version_with_highlight_on_providers_list",
            defaultValue = "5.0.0"
        ).let { appVersion >= SemanticVersion(it) }

    private fun hasTitleAndSubtitleOnProviderTransport(appVersion: SemanticVersion): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "app_version_with_title_and_subtitle_on_provider_transport",
            defaultValue = "5.0.0"
        ).let { appVersion >= SemanticVersion(it) }

    private fun List<ConsolidatedAccreditedNetwork>.calculateDistance(
        latitude: String,
        longitude: String
    ): List<ConsolidatedAccreditedNetwork> =
        this.mapNotNull { model ->
            runCatching {
                model.copy(
                    distance = DistanceUtils.distanceInMeters(
                        model.latitude!!.toDouble(),
                        model.longitude!!.toDouble(),
                        latitude.toDouble(),
                        longitude.toDouble()
                    )
                )
            }.getOrNull()
        }.sortedBy { it.distance }

    private fun shouldShowCallOutToDuquesaMember(isAliceMember: Boolean, specialtyId: UUID?) =
        !isAliceMember
                && hasDuquesaMemberMigrateToAliceApp()
                && specialtiesWithoutDuquesaGatekeeper().contains(specialtyId).not()

    private fun hasDuquesaMemberMigrateToAliceApp(): Boolean =
        FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "has_duquesa_member_migrate_to_alice_app",
            false
        )

    private fun specialtiesWithoutDuquesaGatekeeper(): List<UUID> =
        FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "specialties_without_duquesa_gatekeeper",
            defaultValue = emptyList<String>()
        ).map { it.toUUID() }

    private fun shouldShowEmptyMFC(): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.ALICE_APP,
            key = "should_show_empty_mfc",
            defaultValue = false
        )
}
