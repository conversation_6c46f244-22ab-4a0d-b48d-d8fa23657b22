package br.com.alice.member.api.services

import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.member.api.ServiceConfig
import io.ktor.client.HttpClient
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.content.TextContent
import io.ktor.http.withCharset

private val credentials  = ServiceConfig.FirebaseCredentials

class FirebaseClientImpl (
    private val httpClient: HttpClient = DefaultHttpClient({
        install(ContentNegotiation) {
            gsonSnakeCase()
        }
    }, timeoutInMillis = 15_000)
): FirebaseClient {
    override suspend fun getSignInTokens(accessToken: String) = coResultOf<SignInFirebaseResponse, Throwable> {
        val payload = SignInFirebaseRequest(true, accessToken)
        val payloadString = gson.toJson(payload)
        val response = post(
            "${credentials.url}/v1/accounts:signInWithCustomToken?key=${credentials.apiKey}",
            payloadString
        ).bodyAsText()

        val firebaseResponse = gson.fromJson(
            response, SignInFirebaseResponse::class.java
        )

        firebaseResponse.toResponse()
    }

    private suspend fun post(url: String, payload: String): HttpResponse {
        val response: HttpResponse = httpClient.post(url) {
            setBody(TextContent(payload, ContentType.Application.Json.withCharset(Charsets.UTF_8)))
        }

        return response
    }
}
