package br.com.alice.member.api.services

import br.com.alice.app.content.client.AppContentABTestService
import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.ScreenType
import br.com.alice.common.core.PersonId
import br.com.alice.common.logging.logger
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.communication.firebase.FirebasePush
import br.com.alice.communication.firebase.PathToNavigate
import br.com.alice.communication.firebase.PushService
import br.com.alice.member.api.ServiceConfig
import br.com.alice.membership.client.DeviceService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.map

class PushNotificationInternalService(
    private val personService: PersonService,
    private val deviceService: DeviceService,
    private val pushService: PushService,
    private val appContentABTestService: AppContentABTestService,
) {

    suspend fun sendNewTasksPushNotification(personId: PersonId, taskCount: Int = 1) =
        deviceService.getDeviceByPerson(personId.toString()).map { device ->
            logger.info("Sending PDA new tasks push notification", "person_id" to personId)

            pushService.send(
                FirebasePush(
                    deviceToken = device.deviceId,
                    title = "Plano de Ação.",
                    body = buildBody(taskCount),
                    data = getPushConfig(personId, device.appVersion),
                )
            ).get()
        }

    private fun buildBody(taskCount: Int) =
        if (taskCount > 1) "Você tem $taskCount tarefas novas no seu Plano de Ação."
        else "Você tem uma tarefa nova no seu Plano de Ação."

    private suspend fun getPushConfig(personId: PersonId, appVersion: String?) = mapOf(
        "path_to_navigate" to PathToNavigate.CHESHIRE_SCREEN.value.second,
        "properties" to "{\"action\": {\"method\": \"${RemoteActionMethod.GET}\", \"endpoint\": \"${
            getPushRedirectEndpoint(
                personId,
                appVersion
            )
        }\"}}"
    )

    private suspend fun getPushRedirectEndpoint(personId: PersonId, appVersion: String?) =
        personService.get(personId).map { person ->
            val isRedesignEnabledForUser = appContentABTestService.isRedesignEnabledForUser(
                personId = person.id,
                appVersion = appVersion?.let { SemanticVersion(it) }
            ).get()

            if (isRedesignEnabledForUser) {
                ServiceConfig.url("/app_content/screen/${ScreenType.REDESIGN_HEALTH_PLAN_HOME.value}")
            } else ServiceConfig.url("/app_content/screen/${ScreenType.HEALTH_ALL_DEMANDS.value}")
        }.get()
}
