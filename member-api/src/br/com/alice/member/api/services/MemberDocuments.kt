package br.com.alice.member.api.services

import br.com.alice.common.MultipartRequest
import br.com.alice.common.core.PersonId
import br.com.alice.common.storage.FileResponse
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileStoreRequest
import br.com.alice.common.storage.FileType
import br.com.alice.common.storage.FileVaultStorage
import br.com.alice.member.api.ServiceConfig
import java.io.InputStream
import java.time.Duration

class MemberDocuments(
    private val fileStorage: FileStorage,
    private val fileVaultStorage: FileVaultStorage
) {

    fun uploadSelfiePhoto(personId: PersonId, stream: InputStream): String {
        val path = "${personId.id}/${System.currentTimeMillis()}.${FileType.IMAGE_JPEG.extension}"

        val fileStoreRequest = FileStoreRequest(
            bucketName = ServiceConfig.bucket("onboardingPhotoBucket"),
            fileContent = stream,
            filePath = path,
            fileType = FileType.IMAGE_JPEG
        )

        return fileStorage.store(fileStoreRequest)
    }

    suspend fun uploadIdentityDocument(personId: PersonId, multipartRequest: MultipartRequest): String {
        val storageResult =  fileVaultStorage.store(
            personId = personId,
            multipartRequest = multipartRequest,
            domain = "member",
            namespace = "documents"
        )

        return storageResult.fold({ it.id.toString() }, { throw it })
    }

    suspend fun getExternalUrl(documentUrl: String, expireDuration: Duration = Duration.ofMinutes(5)) =
        if ("file-vault-service" in documentUrl) documentUrl.split("/").last()
            .let { documentId -> getFileFromVault(documentId)!!.url }
        else
            fileStorage.getSecuredLink(documentUrl, expireDuration)

    suspend fun getFileFromVault(id: String): FileResponse? {
        return fileVaultStorage.getFileById(id)
    }
}
