package br.com.alice.member.api.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.logging.logger
import com.google.cloud.Timestamp
import com.google.cloud.firestore.Firestore
import com.google.cloud.firestore.SetOptions
import com.google.firebase.cloud.FirestoreClient
import io.grpc.LoadBalancerRegistry
import io.grpc.internal.PickFirstLoadBalancerProvider


@Suppress("UNCHECKED_CAST")
object FeaturePreferencesNotifier {

    private val firestore: Firestore by lazy {
        LoadBalancerRegistry.getDefaultRegistry().register(PickFirstLoadBalancerProvider())
        FirestoreClient.getFirestore()
    }

    private const val COLLECTION_NAME = "feature_preferences"

    fun getFeaturePreferences(personId: PersonId, featurePreferences: FeaturePreferences): Map<String, Any>? =
        runCatching {
            firestore.collection(COLLECTION_NAME)
                .document(personId.toString())
                .get()
                .get()
                .data?.let {
                    logger.info(
                        "FeaturePreferencesNotifier.getFeaturePreferences: getting feature preferences",
                        "person_id" to personId,
                        "feature_preferences" to featurePreferences,
                        "data" to it
                    )
                    it[featurePreferences.toString().lowercase()] as Map<String, Any>?
                }
        }.onFailure {
            logger.error(
                "Error getting FeaturePreferences",
                "person_id" to personId.id,
                "feature_preferences" to featurePreferences.toString(),
                it
            )
        }.getOrNull()

    fun updateFeaturePreferences(
        personId: PersonId,
        featurePreferences: FeaturePreferences,
        data: Map<String, Any>?
    ) {
        runCatching {
            val properties = mapOf(
                featurePreferences.toString().lowercase() to data?.plus("timestamp" to Timestamp.now().toString())
            )

            firestore.collection(COLLECTION_NAME)
                .document(personId.toString())
                .set(
                    properties,
                    SetOptions.merge()
                ).let {
                    logger.info(
                        "FeaturePreferencesNotifier.updateFeaturePreferences: updating feature preferences",
                        "person_id" to personId,
                        "feature_preferences" to featurePreferences,
                        "updated_time" to it.get().updateTime,
                        "data" to data
                    )
                }
        }.onFailure {
            logger.error(
                "Error updating FeaturePreferences",
                "person_id" to personId.id,
                "feature_preferences" to featurePreferences.toString(),
                "data" to data,
                it
            )
        }
    }
}

enum class FeaturePreferences {
    APP_BAR_ITEM_POPOVER,
    STORE_REVIEW,
    WELCOME_SCREEN
}
