package br.com.alice.member.api.services

import br.com.alice.authentication.Authenticator
import br.com.alice.authentication.Authenticator.CHANNEL_PERSON_ID_KEY
import br.com.alice.authentication.Authenticator.CREATED_AT_KEY
import br.com.alice.authentication.Authenticator.HEALTHCARE_TEAM_TYPE
import br.com.alice.authentication.Authenticator.LEAD_ID
import br.com.alice.authentication.Authenticator.USER_SUB_TYPE_KEY
import br.com.alice.authentication.UserType
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.Person
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.models.AuthResponse
import br.com.alice.membership.client.PersonLoginService
import br.com.alice.membership.model.events.AuthenticationSource
import br.com.alice.membership.model.events.UserAuthenticatedEvent
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.person.client.UserTypeService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.time.LocalDate
import java.time.LocalDateTime

class AuthService(
    private val personService: PersonService,
    private val personLoginService: PersonLoginService,
    private val personInternalReferenceService: PersonInternalReferenceService,
    private val userTypeService: UserTypeService,
    private val kafkaProducerService: KafkaProducerService,
    private val memberService: MemberService
) {
    suspend fun getPersonAndSendNewAccessCodeByNationalId(nationalId: String): Result<Person, Throwable> {
        return withUnauthenticatedTokenWithKey(nationalId) {
            personLoginService.getLoginInformation(nationalId)
        }
    }

    suspend fun authenticateByNationalId(nationalId: String, oneTimePassword: String): Result<String, Throwable> {
        return withUnauthenticatedTokenWithKey(nationalId) {
            personLoginService.signInByNationalId(nationalId, oneTimePassword)
                .map { generateAccessToken(it.personId, AuthenticationSource.NATIONAL_ID) }
        }
    }

    suspend fun authenticateByNationalIdAndGetPerson(nationalId: String, oneTimePassword: String): Result<AuthResponse, Throwable> {
        return withUnauthenticatedTokenWithKey(nationalId) {
            personLoginService.signInByNationalId(nationalId, oneTimePassword)
                .map {
                    personService.get(it.personId).map { person ->
                        AuthResponse(
                            customToken = generateAccessToken(
                                it.personId,
                                AuthenticationSource.NATIONAL_ID,
                                buildClaimsWithLeadId(person)
                            ),
                            person = person
                        )
                    }.get()
                }
        }
    }

    suspend fun generateToken(personId: PersonId): Result<String, Throwable> =
        coResultOf { generateAccessToken(personId, AuthenticationSource.REFRESH_TOKEN) }

    suspend fun getPersonInfoUnauthenticated(personId: String) =
        withUnauthenticatedTokenWithKey(personId) {
            val person = personService.get(personId.toPersonId()).get()
            PersonInfoResponse(person.firstName, person.email, person.nationalId)
        }

    private fun buildClaimsWithLeadId(person: Person, claims: MutableMap<String,String> = mutableMapOf()) : MutableMap<String,String> {
        person.leadId?.let {
            claims.put(LEAD_ID, (person.leadId).toString())
        }

        return claims
    }

    private suspend fun generateAccessToken(personId: PersonId, source: AuthenticationSource, extraClaims: Map<String, String> = emptyMap()): String {
        val userType = getUserType(personId)
        val channelPersonIdClaims = getChannelPersonIdClaims(personId)
        val healthcareTeamType = getHealthcareTeamType(personId)

        val claims = mapOf(
            USER_SUB_TYPE_KEY to userType.toString(),
            CREATED_AT_KEY to LocalDateTime.now().toString(),
            HEALTHCARE_TEAM_TYPE to healthcareTeamType.toString(),
        ) + channelPersonIdClaims + extraClaims

        val token = Authenticator.generateCustomToken(
            userId = personId.toString(),
            kClass = Person::class, // TODO: Substituir pela entidade verdadeira relativa ao tipo do usuário (Person/Member) após migração das policies de Person para Member
            extraClaims = claims
        )

        logger.info("User authenticated", "person_id" to personId, "source" to source, "claims" to claims)

        kafkaProducerService.produce(UserAuthenticatedEvent(personId, source, claims))

        return token
    }

    private suspend fun getChannelPersonIdClaims(personId: PersonId) = withUnauthenticatedTokenWithKey(personId.toString()) {
        personInternalReferenceService.getForPerson(personId).fold(
            { mapOf(CHANNEL_PERSON_ID_KEY to it.channelPersonId.toString()) },
            {
                logger.warn("Error getting PersonInternalReference for personId=${personId}", it)
                emptyMap()
            }
        )
    }

    private suspend fun getUserType(personId: PersonId): UserType {
        return withUnauthenticatedTokenWithKey(personId.toString()) {
            userTypeService.get(personId, refreshed = true)
        }.get()
    }

    private suspend fun getHealthcareTeamType(personId: PersonId) =
        withUnauthenticatedTokenWithKey(personId.toString()) {
            memberService.findFirstMembership(personId).fold(
                {
                    val memberActivationDate = it.activationDate?.toLocalDate() ?: return@fold HealthcareTeam.Type.LEAGUE

                    val leagueStartDateFromFeatureFlag = FeatureService.get(
                        namespace = FeatureNamespace.MEMBERSHIP,
                        key = "channel_segmentation_for_new_journey_started_at",
                        defaultValue = "2022-09-29"
                    )

                    val leagueStartDate = LocalDate.parse(leagueStartDateFromFeatureFlag)

                    if (memberActivationDate < leagueStartDate)
                        HealthcareTeam.Type.STANDARD
                    else
                        HealthcareTeam.Type.LEAGUE
                },
                {
                    logger.error("Error finding membership for personId=${personId}", it)
                    HealthcareTeam.Type.LEAGUE
                }
            )
        }
}

data class PersonInfoResponse(
    val name: String,
    val email: String,
    val nationalId: String
)
