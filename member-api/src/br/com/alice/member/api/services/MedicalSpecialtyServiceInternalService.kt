package br.com.alice.member.api.services

import br.com.alice.app.content.model.RemoteActionMethod
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.TagColorScheme
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Referral
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.accreditedNetwork.MedicalSpecialtyResponse
import br.com.alice.member.api.models.accreditedNetwork.MedicalSpecialtyTransport
import br.com.alice.member.api.models.appContent.Navigation
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.MedicalSpecialtyService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class MedicalSpecialtyServiceInternalService(
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val healthPlanTaskService: HealthPlanTaskService,
    private val personService: PersonService,
    private val memberService: MemberService,
) {
    companion object {

        val THERAPY_TAG = Tag(
            icon = null,
            text = "Necessário pedido médico",
            colorScheme = TagColorScheme.RED
        )
        val HAS_REFERRAL = Tag(
            icon = null,
            text = "Agendamento disponivel",
            colorScheme = TagColorScheme.GREEN
        )

        private val DEFAULT_EMPTY_PROVIDERS_LIST_ACTION = MedicalSpecialtyResponse.ActionNavigation(
            label = "Ir para Alice Agora",
            navigation = NavigationResponse(mobileRoute = MobileRouting.ALICE_AGORA)
        )

        private val DUQUESA_EMPTY_PROVIDERS_LIST_ACTION = MedicalSpecialtyResponse.ActionNavigation(
            label = "Ir para Atendimento",
            navigation = NavigationResponse(mobileRoute = MobileRouting.DUQUESA_SERVICE)
        )

        private val FAVORITES_SHORTCUT = MedicalSpecialtyResponse.ShortcutTransport(
            name = "Especialistas e Clínicas Favoritas",
            icon = "star_filled",
            navigation = Navigation(
                method = RemoteActionMethod.GET,
                endpoint = "/accredited_network/favorites",
                mobileRoute = MobileRouting.ACCREDITED_NETWORK_FAVORITES,
                params = mapOf(
                    "params" to mapOf(
                        "has_app_state" to true
                    )
                )
            )
        )

        fun getDefaultEmptyProvidersListAction(brand: Brand): MedicalSpecialtyResponse.ActionNavigation? =
            if (brand.isDuquesa()) DUQUESA_EMPTY_PROVIDERS_LIST_ACTION else DEFAULT_EMPTY_PROVIDERS_LIST_ACTION
    }

    private val Person.isNotDuquesa get() = productInfo?.brand?.isDuquesa()?.not() ?: true

    suspend fun getAllSpecialty(
        personId: PersonId,
        appVersion: SemanticVersion,
    ): Result<MedicalSpecialtyResponse, Throwable> = coroutineScope {
        val referralTasksDeferred =
            async {
                healthPlanTaskService.getAllActiveByPersonAndTypeAndDueDate(personId, HealthPlanTaskType.REFERRAL)
                    .mapEach { it.specialize<Referral>() }
                    .get()
            }
        val specialitiesDeferred =
            async {
                medicalSpecialtyService.getActivesByType(type = MedicalSpecialtyType.SPECIALTY, excludeInternal = true)
                    .get()
            }

        val allSpecialities = specialitiesDeferred.await()
        val referralTasksIds = referralTasksDeferred.await().map { it.specialty }.mapNotNull { it?.id }
        val person = personService.get(personId).get()
        val memberDeferred = async { memberService.getCurrent(personId).get() }

        val containsReferral =
            allSpecialities.filter { referralTasksIds.contains(it.id) }
                .map { convertToMedicalTransport(it, HAS_REFERRAL, false) }

        val specialities = allSpecialities.map {
            if (it.isTherapy && person.isNotDuquesa)
                convertToMedicalTransport(it, THERAPY_TAG, true)
            else
                convertToMedicalTransport(it, null, false)
        }.sortedBy { it.name }


        val specialties = (containsReferral + specialities).distinctBy { it.id }
        MedicalSpecialtyResponse(
            title = "Especialidades",
            items = specialties,
            action = getDefaultEmptyProvidersListAction(if (person.isNotDuquesa) Brand.ALICE else Brand.DUQUESA),
            isDuquesa = memberDeferred.await().isDuquesa,
            shortcuts = if (showAccreditedNetworkFavorites(appVersion)) listOf(FAVORITES_SHORTCUT) else null
        ).success()
    }

    private fun convertToMedicalTransport(
        medicalSpecialty: MedicalSpecialty,
        tag: Tag?,
        needsConfirmation: Boolean,
    ) = MedicalSpecialtyTransport(
        id = medicalSpecialty.id,
        name = medicalSpecialty.name,
        title = medicalSpecialty.name,
        tag = tag,
        needsConfirmation = needsConfirmation,
        navigation = buildSpecialtyNavigation(medicalSpecialty.id),
        hasReferral = tag == HAS_REFERRAL
    )

    private fun buildSpecialtyNavigation(specialtyId: UUID) = Navigation(
        mobileRoute = MobileRouting.ACCREDITED_NETWORK,
        method = RemoteActionMethod.GET,
        endpoint = "/accredited_network/health_specialists?specialty_id=$specialtyId",
        params = mapOf(
            "params" to mapOf(
                "has_app_state" to true
            )
        ),
    )

    private fun showAccreditedNetworkFavorites(appVersion: SemanticVersion) = FeatureService.get(
        namespace = FeatureNamespace.MEMBERSHIP,
        key = "version_with_accredited_network_favorites",
        defaultValue = "4.20.0"
    ).let { appVersion >= SemanticVersion(it) }

}
