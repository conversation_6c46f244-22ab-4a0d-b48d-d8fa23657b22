package br.com.alice.member.api.services

import br.com.alice.app.content.client.AppointmentScheduleEventService
import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.CalloutAction
import br.com.alice.app.content.model.CalloutType
import br.com.alice.app.content.model.CalloutVariant
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.mobile.SemanticVersion
import br.com.alice.common.observability.Spannable
import br.com.alice.coverage.client.AccreditedNetworkFavoriteService
import br.com.alice.coverage.client.ConsolidatedRatingService
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.ReferralNew
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.ServiceConfig
import br.com.alice.member.api.builders.AccreditedNetworkSpecialistResponseBuilder
import br.com.alice.member.api.builders.NeedMedicalReferralCallOutBuilder
import br.com.alice.member.api.builders.NeedMedicalReferralCallOutBuilder.ScreenType
import br.com.alice.member.api.models.accreditedNetwork.SpecialistDetailsTransport
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.schedule.client.AppointmentSchedulePreTriageService
import br.com.alice.schedule.client.SchedulePreTriageFilters
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getFailureOrNull
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.success
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class AccreditedNetworkSpecialistInternalService(
    private val personService: PersonService,
    private val healthProfessionalService: HealthProfessionalService,
    private val healthcareTeamService: HealthcareTeamService,
    private val specialistSchedulingInternalService: SpecialistSchedulingInternalService,
    private val consolidatedRatingService: ConsolidatedRatingService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val appointmentSchedulePreTriageService: AppointmentSchedulePreTriageService,
    private val appointmentScheduleEventService: AppointmentScheduleEventService,
    private val accreditedNetworkFavoriteService: AccreditedNetworkFavoriteService,
    private val memberProductService: MemberProductService
) : Spannable {
    companion object {
        val PDA_CALLOUT = CalloutAction(
            type = CalloutType.NAVIGATION,
            label = "Ver encaminhamentos",
            onClickAction = RemoteAction(
                mobileRoute = ActionRouting.CHESHIRE_SCREEN,
                params = mapOf(
                    "action" to mapOf(
                        "method" to "GET",
                        "endpoint" to ServiceConfig.url("/app_content/screen/redesign_health_plan_home"),
                        "params" to mapOf(
                            "screen_id" to "redesign_health_plan_home"
                        ),
                    ),
                ),
            ),
        )
    }

    suspend fun getSpecialistDetails(
        personId: PersonId,
        specialistId: UUID,
        appVersion: SemanticVersion,
        lat: Double?,
        lng: Double?,
        healthPlanTask: UUID?,
    ): Result<SpecialistDetailsTransport, Throwable> = coroutineScope {
        val person = personService.get(personId).get()
        val membershipFilterOptions = MemberService.FindOptions(withCassiMember = true)
        val memberWithProduct = async {
            memberProductService.getMembershipWithProduct(personId, membershipFilterOptions).get()
        }

        val consolidatedRating = async {
            consolidatedRatingService.getByHealthProfessional(specialistId).getOrNullIfNotFound()
        }
        val specialist = healthProfessionalService.get(
            specialistId,
            HealthProfessionalService.FindOptions(withContact = true)
        ).getOrElse {
            healthProfessionalService.findByStaffId(
                specialistId,
                HealthProfessionalService.FindOptions(withContact = true)
            ).getOrNullIfNotFound()
        }

        if (specialist == null)
            return@coroutineScope NotFoundException("specialist not found").failure()
        val referralDeferred = async {
            specialistSchedulingInternalService.getUniquePlanTask(
                personId,
                healthPlanTask,
                specialist.specialtyId ?: return@async NotFoundException("specialty not found").failure()
            )
        }
        val serviceDays = async {
            specialistSchedulingInternalService.getSpecialistServiceDays(specialist.staffId).getOrElse { emptyList() }
        }
        val referral = referralDeferred.await()

        val scheduleDeferred = async {
            referral.flatMap { specialistSchedulingInternalService.getScheduleInformation(person, specialist, it) }
        }

        val specialty = async { specialist.specialtyId?.let { medicalSpecialtyService.getById(it).get() } }
        val subspecialties = async { medicalSpecialtyService.getByIds(specialist.subSpecialtyIds).get() }
        val (member, product) = memberWithProduct.await()
        val schedule = scheduleDeferred.await()

        val favoriteDeferred = async {
            accreditedNetworkFavoriteService
                .getByPersonIdAndReferenceId(personId, specialistId)
                .getOrNull()
        }

        AccreditedNetworkSpecialistResponseBuilder.buildSpecialistResponse(
            person = person,
            member = member,
            product = product,
            specialist = specialist,
            favorite = favoriteDeferred.await(),
            consolidatedRating = consolidatedRating.await(),
            specialty = specialty.await(),
            subSpecialties = subspecialties.await(),
            memberLat = lat,
            memberLng = lng,
            schedulingInfo = schedule.getOrNull(),
            serviceDays = serviceDays.await(),
            callout = buildCalloutSectionWithException(schedule.getFailureOrNull())
                ?: buildCalloutByReferral(referral.getOrNull(), specialist, specialty.await(), member.isAlice),
            appVersion = appVersion
        ).success()
    }

    suspend fun getMyHealthcareTeamSpecialist(
        personId: PersonId,
        lat: Double?,
        lng: Double?,
        appVersion: SemanticVersion
    ): Result<SpecialistDetailsTransport, Throwable> = span("getMyHealthcareTeamSpecialist") { span ->
        coroutineScope {
            span.setAttribute("person_id", personId.toString())
            span.setAttribute("lat", lat.toString())
            span.setAttribute("lng", lng.toString())
            span.setAttribute("app_version", appVersion.toString())

            val personDeferred = async { personService.get(personId).get() }
            val healthcareTeam = healthcareTeamService.getHealthcareTeamByPerson(personId).getOrNullIfNotFound()
                ?: return@coroutineScope UnsupportedOperationException("Member not to choose the HealthcareTeam").failure()

            val specialistDeferred = async {
                healthProfessionalService.findByStaffId(
                    healthcareTeam.physicianStaffId,
                    findOptions = HealthProfessionalService.FindOptions(
                        withContact = true
                    )
                ).get()
            }
            val serviceDays = async {
                specialistSchedulingInternalService.getSpecialistServiceDays(healthcareTeam.physicianStaffId).get()
            }
            val schedulingInfoDeferred = async {
                specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                    healthcareTeam.physicianStaffId,
                    healthcareTeam.segment
                ).get()
            }
            val specialist = specialistDeferred.await()
            val specialty = async { specialist.specialtyId?.let { medicalSpecialtyService.getById(it).get() } }

            val person = personDeferred.await()
            val isAppointmentHubEnabled = isAppointmentHubEnabled(appVersion)

            val rootNodeId = if (!isAppointmentHubEnabled)
                appointmentSchedulePreTriageService.getProtocolByFilters(SchedulePreTriageFilters(personId))
                    .getOrNull()?.rootNodeId else null

            val scheduleUrl = if (isAppointmentHubEnabled)
                appointmentScheduleEventService.buildSaraScheduleUrlVersion(personId).getOrNull() else null

            val schedulingInfo = schedulingInfoDeferred.await()
            span.setAttribute("root_node_id", rootNodeId.toString())
            span.setAttribute("is_appointment_hub_enabled", isAppointmentHubEnabled.toString())
            span.setAttribute("schedule_url", scheduleUrl.toString())
            span.setAttribute("scheduling_info", schedulingInfo.toString())

            AccreditedNetworkSpecialistResponseBuilder.buildHealthcareTeamSpecialistResponse(
                specialist = specialist,
                memberHealthcareTeam = healthcareTeam,
                person = person,
                schedulingInfo = schedulingInfo,
                serviceDays = serviceDays.await(),
                memberLat = lat,
                memberLng = lng,
                specialty = specialty.await(),
                scheduleUrl = scheduleUrl,
                rootNodeId = rootNodeId,
                appVersion = appVersion,
            ).success()
        }
    }

    suspend fun getHealthcareTeamSpecialist(
        personId: PersonId,
        physicianId: UUID,
        lat: Double?,
        lng: Double?,
        appVersion: SemanticVersion,
    ): Result<SpecialistDetailsTransport, Throwable> = coroutineScope {
        val personDeferred = async { personService.get(personId).get() }
        val healthcareTeam = healthcareTeamService.getHealthcareTeamByPerson(personId).getOrNullIfNotFound()
            ?: return@coroutineScope UnsupportedOperationException("Member is not associated with any healthcare team").failure()

        val specialist =
            healthProfessionalService.get(
                physicianId,
                HealthProfessionalService.FindOptions(withContact = true)
            ).getOrElse {
                healthProfessionalService.findByStaffId(
                    physicianId,
                    findOptions = HealthProfessionalService.FindOptions(
                        withContact = true
                    )
                ).get()
            }
        val serviceDays = async {
            specialistSchedulingInternalService.getSpecialistServiceDays(specialist.staffId).get()
        }
        val schedulingInfo = async {
            if (specialist.staffId == healthcareTeam.physicianStaffId)
                specialistSchedulingInternalService.getSpecialistRemoteScheduleInformation(
                    healthcareTeam.physicianStaffId,
                    healthcareTeam.segment
                ).get()
            else null
        }
        val specialty = async { specialist.specialtyId?.let { medicalSpecialtyService.getById(it).get() } }

        val person = personDeferred.await()
        val isPhysicianSpecialist = specialist.staffId == healthcareTeam.physicianStaffId
        val isAppointmentHubEnabled = isAppointmentHubEnabled(appVersion)

        val rootNodeId =
            if (isPhysicianSpecialist && !isAppointmentHubEnabled)
                appointmentSchedulePreTriageService.getProtocolByFilters(SchedulePreTriageFilters(personId))
                    .getOrNull()?.rootNodeId else null

        val scheduleUrl =
            if (isPhysicianSpecialist && isAppointmentHubEnabled)
                appointmentScheduleEventService.buildSaraScheduleUrlVersion(personId).getOrNull() else null

        AccreditedNetworkSpecialistResponseBuilder.buildHealthcareTeamSpecialistResponse(
            specialist = specialist,
            memberHealthcareTeam = healthcareTeam,
            person = person,
            schedulingInfo = schedulingInfo.await(),
            serviceDays = serviceDays.await(),
            memberLat = lat,
            memberLng = lng,
            specialty = specialty.await(),
            scheduleUrl = scheduleUrl,
            rootNodeId = rootNodeId,
            appVersion = appVersion,
        ).success()
    }

    fun buildCalloutByReferral(
        referral: ReferralNew?,
        healthProfessional: HealthProfessional,
        specialty: MedicalSpecialty?,
        isAliceMember: Boolean
    ): CalloutSection? = when {
        (referral == null && (shouldShowCallOutToDuquesaMember(
            isAliceMember,
            specialty?.id
        ) || (isAliceMember && specialty?.isTherapy == true)))
            -> NeedMedicalReferralCallOutBuilder.buildCalloutSection(ScreenType.PROVIDER)

        referral == null || referral.subSpecialty?.id == null -> null
        healthProfessional.subSpecialtyIds.contains(referral.subSpecialty?.id).not() -> CalloutSection(
            title = "Subespecialidade incorreta",
            calloutBody = "Não perca viagem! Esse profissional pode não atender a demanda específica do seu encaminhamento.",
            calloutVariant = CalloutVariant.TUTORIAL,
            calloutAction = PDA_CALLOUT,
        )

        else -> null
    }

    fun buildCalloutSectionWithException(
        ex: Throwable?,
    ): CalloutSection? =
        when (ex) {
            is ManyActionPlanTaskException -> {
                CalloutSection(
                    title = "Mais de um encaminhamento",
                    calloutBody = "Para agendar com esse profissional é necessário selecionar um dos encaminhamentos disponíveis",
                    calloutVariant = CalloutVariant.TUTORIAL,
                    calloutAction = PDA_CALLOUT,
                )
            }

            is SuggestionSpecialistIsDifferentException -> {
                CalloutSection(
                    title = "Verifique o profissional",
                    calloutBody = "Esse especialista é diferente do profissional indicado no seu encaminhamento",
                    calloutVariant = CalloutVariant.WARNING,
                    calloutAction = PDA_CALLOUT,
                )
            }

            else -> null
        }

    private fun shouldShowCallOutToDuquesaMember(isAliceMember: Boolean, specialtyId: UUID?) =
        !isAliceMember
                && hasDuquesaMemberMigrateToAliceApp()
                && specialtiesWithoutDuquesaGatekeeper().contains(specialtyId).not()

    private fun isAppointmentHubEnabled(appVersion: SemanticVersion): Boolean =
        appVersion >= SemanticVersion(
            FeatureService.get(
                namespace = FeatureNamespace.ALICE_APP,
                key = "app_version_with_appointment_hub",
                defaultValue = "9.0.0"
            )
        )

    private fun hasDuquesaMemberMigrateToAliceApp(): Boolean =
        FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "has_duquesa_member_migrate_to_alice_app",
            false
        )

    private fun specialtiesWithoutDuquesaGatekeeper() =
        FeatureService.get(
            FeatureNamespace.MEMBERSHIP,
            "specialties_without_duquesa_gatekeeper",
            defaultValue = emptyList<String>()
        ).map { it.toUUID() }

}
