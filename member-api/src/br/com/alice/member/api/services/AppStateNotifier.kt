package br.com.alice.member.api.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.logging.logger
import com.google.cloud.firestore.Firestore
import com.google.cloud.firestore.SetOptions
import com.google.firebase.cloud.FirestoreClient
import io.grpc.LoadBalancerRegistry
import io.grpc.internal.PickFirstLoadBalancerProvider
import java.util.Date


object AppStateNotifier {

    private val firestore: Firestore by lazy {
        LoadBalancerRegistry.getDefaultRegistry().register(PickFirstLoadBalancerProvider())
        FirestoreClient.getFirestore()
    }

    private const val collectionName = "app_state"

    fun invalidateAppState(personId: PersonId, appState: AppState) {
        firestore.collection(collectionName)
            .document(personId.toString())
            .set(
                mapOf(
                    appState.toString() to mapOf(
                        "lastUpdate" to null
                    )
                ),
                SetOptions.merge()
            )
    }

    fun updateAppState(personId: PersonId, vararg appState: AppState) {
        val properties = appState.associate { state ->
            state.toString() to mapOf(
                "lastUpdate" to Date().toString()
            )
        }

        firestore.collection(collectionName)
            .document(personId.toString())
            .set(
                properties,
                SetOptions.merge()
            ).let {
                logger.info(
                    "AppStateNotifier.updateAppState: updating appstate",
                    "personId" to personId,
                    "appState" to appState,
                    "updatedTime" to it.get().updateTime
                )
            }
    }
}

enum class AppState {
    HOME,
    APPOINTMENT_SCHEDULE,
    HOME_TEST_RESULTS,
    HEALTH_PLAN,
    REMINDERS,
    GAS_CONTRACT,
    TOKEN_EXPIRED,
    PERSON_HEALTH_EVENT,
    SCORE_MAGENTA_RESULT_EVENT,
    BENEFICIARY_ONBOARDING_PHASE_CHANGE,
    BENEFICIARY_MEMBERSHIP_ACTIVATED,
    APPOINTMENT_SCHEDULE_COLLISION_DETECTED,
    MEMBER_ONBOARDING,
    FIRST_PAYMENT_CREATED,
    PAYMENT_UPDATED,
    CHESHIRE_HOME,
    CHESHIRE_HEALTH,
    CHESHIRE_ALICE_NOW,
    CHESHIRE_CHANNELS,
    CHESHIRE_SCHEDULE,
    HEALTH_ALL_DEMANDS,
    UNIFIED_HEALTH,
    DUQUESA_HOME,
    REFUND_HISTORY,
    REDESIGN_UNIFIED_HEALTH,
    MEMBER_ONBOARDING_V2,
    IDENTITY_VALIDATION_UPDATED,
    REDESIGN_HEALTH_PLAN_HOME,
    REDESIGN_HEALTH_PLAN_DEMAND_DETAIL,
    REDESIGN_HEALTH_PLAN_HOME_TASK_LIST,
    REDESIGN_ALICE_AGORA,
    MEMBER_PERSONAL_DATA,
    REDESIGN_ALICE_AGORA_DEMAND_MENU_SECTION,
    APPOINTMENT_HUB,
    FAVORITES_UPDATE_ACC_NETWORK,
    HEALTH_PLAN_DETAILS,
    ACTIVATION_HOME,
    ACTIVATION_HEALTH_DECLARATIONS,
    ACTIVATION_CPTS,
    ACTIVATION_REVIEW_TERMS,
    CRM_CONTENT_CARDS,
}
