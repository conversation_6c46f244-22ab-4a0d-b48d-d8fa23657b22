package br.com.alice.member.api.services

import br.com.alice.common.core.extensions.classToString
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.common.PaymentMethod
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonPreferences
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.builders.ChildVideosPropertiesBuilder
import br.com.alice.member.api.builders.PersonMissingDataNavigationBuilder
import br.com.alice.member.api.models.Links
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.Navigation
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.currentMemberAppVersion


object OnboardingMobileRouter {
    suspend fun getNavigation(
        person: Person,
        phase: OnboardingPhase,
        portabilityRequested: Boolean,
        source: NavigationSource = NavigationSource.OTHER,
        preferences: PersonPreferences? = null,
    ): Navigation {
        logger.info(
            "OnboardingMobileRouter::getNavigation - Getting navigation",
            "person_id" to person.id,
            "phase" to phase,
            "source" to source,
            "portabilityRequested" to portabilityRequested
        )

        val navigator: OnboardingAppNavigator =  BaselineNavigator()

        val navigation = navigator.getNavigation(
            phase,
            portabilityRequested,
            source,
            person,
            preferences,
        )

        logger.info(
            "OnboardingMobileRouter::getNavigation - Navigation got",
            "person_id" to person.id,
            "phase" to phase,
            "navigation" to navigation,
            "navigator" to navigator.classToString()
        )

        return navigation
    }
}

interface OnboardingAppNavigator {
    suspend fun getNavigation(
        phase: OnboardingPhase,
        portabilityRequested: Boolean,
        source: NavigationSource,
        person: Person,
        preferences: PersonPreferences?,
    ): Navigation
}

enum class NavigationSource {
    START_SESSION,
    PORTABILITY_STATUS,
    OTHER,
}

class BaselineNavigator : OnboardingAppNavigator {
    override suspend fun getNavigation(
        phase: OnboardingPhase,
        portabilityRequested: Boolean,
        source: NavigationSource,
        person: Person,
        preferences: PersonPreferences?,
    ): Navigation {
        return when (phase) {
            OnboardingPhase.SHOPPING -> PersonMissingDataNavigationBuilder.buildNavigation(
                MobileRouting.SHOPPING, person)

            OnboardingPhase.LEGAL_GUARDIAN_REGISTER -> NavigationResponse(
                mobileRoute = MobileRouting.LEGAL_GUARDIAN_REGISTER,
                properties = mapOf("nickName" to person.contactName)
            )

            OnboardingPhase.CHILD_VIDEOS_REQUEST -> NavigationResponse(
                mobileRoute = MobileRouting.CHILD_VIDEOS_REQUEST,
                properties = ChildVideosPropertiesBuilder.getPropertiesForChild(person)
            )

            OnboardingPhase.PORTABILITY -> getPortabilityV3NavigationIfEnabled(
                person = person,
                fallback = NavigationResponse(
                    mobileRoute = MobileRouting.PORTABILITY_QUESTIONS,
                    link = Links.PORTABILITY_QUESTIONS_V2
                )
            )

            OnboardingPhase.PORTABILITY_REVIEW -> getPortabilityV3NavigationIfEnabled(
                person = person,
                fallback = NavigationResponse(
                    mobileRoute = MobileRouting.PORTABILITY_STATUS,
                    link = Links.PORTABILITY_STATUS
                )
            )

            OnboardingPhase.LEGAL_GUARDIAN_RESPONSIBILITY_TERM_SIGNING -> NavigationResponse(
                mobileRoute = MobileRouting.LEGAL_GUARDIAN_RESPONSIBILITY_TERM,
                properties = mapOf("email" to person.email)
            )

            OnboardingPhase.REGISTRATION -> NavigationResponse(
                mobileRoute = MobileRouting.REGISTRATION,
                link = Links.REGISTRATION
            )

            OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT, OnboardingPhase.WAITING_FOR_REVIEW -> NavigationResponse(
                mobileRoute = MobileRouting.HEALTH_DECLARATION_APPOINTMENT,
                link = Links.HEALTH_DECLARATION_APPOINTMENT
            )

            OnboardingPhase.CONTRACT -> NavigationResponse(
                mobileRoute = MobileRouting.CONTRACT_SIGNING,
                link = Links.CONTRACT
            )

            OnboardingPhase.PAYMENT -> NavigationResponse(
                mobileRoute = MobileRouting.GAS_PAYMENT,
                properties = mapOf("payment_method" to (preferences?.firstPaymentMethod ?: PaymentMethod.PIX))
            )

            OnboardingPhase.FINISHED -> NavigationResponse(
                mobileRoute = MobileRouting.HOME
            )
        }
    }

    private suspend fun getPortabilityV3NavigationIfEnabled(person: Person, fallback: NavigationResponse): Navigation {
        val appVersion = currentMemberAppVersion().version.toString()
        val hasMinimumAppVersion = appVersion >= PORTABILITY_MINIMUM_APP_VERSION
        val shouldUseV3 = FeatureService.get(FeatureNamespace.ALICE_APP, "should_go_to_portability_v3", false)

        return if (shouldUseV3 && hasMinimumAppVersion)
            PersonMissingDataNavigationBuilder.buildNavigation(MobileRouting.PORTABILITY_STEPS, person)
        else
            fallback
    }

    companion object {
        private const val PORTABILITY_MINIMUM_APP_VERSION = "2.32.0"
    }
}
