package br.com.alice.member.api.services

import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.OnboardingPhase
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonOnboarding
import br.com.alice.member.api.models.AllowedUsersList
import br.com.alice.membership.client.SessionsService
import br.com.alice.membership.client.onboarding.GasProcessService

class SessionNavigationService(
    private val sessionsService: SessionsService,
    private val gasProcessService: GasProcessService
) {
    suspend fun getCrmKey(nationalId: String) = sessionsService.getCrmKey(nationalId)

    suspend fun getOnboardingPhase(person: Person, onboarding: PersonOnboarding): OnboardingPhase {
        return if (AllowedUsersList.isGasProcessTester(person.nationalId)) {
            val onboarding = gasProcessService.clearGasProcess(person, onboarding).get()

            logger.info(
                "user is a test user and will start GAS process again",
                "national_id" to person.nationalId,
                "current_phase" to onboarding.currentPhase
            )

            onboarding.currentPhase
        } else {
            onboarding.currentPhase
        }
    }
}
