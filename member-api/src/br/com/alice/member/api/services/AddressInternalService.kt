package br.com.alice.member.api.services

import br.com.alice.common.convertTo
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldException
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.thenError
import br.com.alice.common.googlemaps.services.AutocompleteTransport
import br.com.alice.common.googlemaps.services.GoogleMapsService
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.Address
import br.com.alice.data.layer.models.Person
import br.com.alice.member.api.converters.AddressTransportConverter
import br.com.alice.member.api.converters.address.MapsAddressConverter
import br.com.alice.member.api.models.UserAddressResponse
import br.com.alice.member.api.models.address.AddressMapsTransport
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID
import br.com.alice.common.googlemaps.services.Address as MapsAddress

class AddressInternalService(
    private val googleMapsService: GoogleMapsService,
    private val personService: PersonService
) : Spannable {

    suspend fun getAddress(person: Person, withComplement: Boolean = false): Result<AddressMapsTransport, Throwable> =
        span("getAddress") { span ->
            span.setAttribute("person_id", person.id)
            span.setAttribute("with_complement", withComplement)
            person.validateAddressInfo()
                .map { person.mainAddress!! }
                .flatMapPair { address ->
                    val formattedAddress =
                        if (withComplement) address.toString() else address.toStringWithoutComplement()
                    googleMapsService.getAddressByQuery(formattedAddress)
                }
                .map { (mapsAddress: MapsAddress, address: Address) ->
                    AddressTransportConverter.convert(mapsAddress, "Principal", address)
                }
                .thenError { ex -> logger.error("error to convert address to address maps", ex) }
                .coFoldException(IllegalArgumentException::class) {
                    NotFoundException("address not found because is invalid").failure()
                }
        }

    suspend fun getAddress(lat: Double, lng: Double): Result<AddressMapsTransport, Throwable> =
        span("getAddress") { span ->
            span.setAttribute("lat", lat)
            span.setAttribute("lng", lng)
            googleMapsService.getAddressByLatLng(lat, lng)
                .map { AddressTransportConverter.convert(it, "Maps Service") }
        }

    suspend fun getByPlaceId(placeId: String): Result<AddressMapsTransport, Throwable> = span("getByPlaceId") { span ->
        span.setAttribute("place_id", placeId)
        googleMapsService.getAddressById(placeId)
            .map { AddressTransportConverter.convert(it, "Maps Service") }
    }

    suspend fun autocompletedAddress(query: String, session: UUID): Result<List<AutocompleteTransport>, Throwable> =
        span("autocompletedAddress") { span ->
            span.setAttribute("query", query)
            span.setAttribute("session", session)
            googleMapsService.autocompleteByText(query, session)
        }

    suspend fun updateAddress(requestAddress: UserAddressResponse, personId: PersonId) = span("updateAddress") { span ->
        personService.get(personId).flatMap { person ->
            requestAddress.convertTo(Address::class).let { address ->
                googleMapsService.getAddressByQuery(address.toStringWithoutComplement())
                    .flatMap { mapsAddress ->
                        val addresses = listOf(
                            MapsAddressConverter.convert(
                                mapsAddress,
                                requestAddress
                            )
                        )
                        personService.update(person.copy(addresses = addresses))
                            .map { updatedPerson ->
                                AddressTransportConverter.convert(
                                    mapsAddress,
                                    "Principal",
                                    updatedPerson.addresses.firstOrNull()
                                )
                            }
                    }
                    .recordResult(span)
            }
        }
    }

}
