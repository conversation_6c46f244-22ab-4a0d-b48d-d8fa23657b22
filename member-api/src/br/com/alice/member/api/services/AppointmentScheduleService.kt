package br.com.alice.member.api.services

import br.com.alice.channel.client.ChannelService
import br.com.alice.channel.models.CreateChatRequest
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.isAfterEq
import br.com.alice.common.core.extensions.isBeforeEq
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.models.AppointmentScheduleEventType
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.PersonCalendly
import br.com.alice.data.layer.models.PersonTask
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.models.HealthPlanItemCalendar
import br.com.alice.member.api.models.MobileRouting
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleEventTypeService
import br.com.alice.schedule.client.AppointmentScheduleOptionService
import br.com.alice.schedule.client.PersonCalendlyService
import br.com.alice.schedule.client.StaffAvailabilityService
import br.com.alice.schedule.model.SlotForSpecificDuration
import br.com.alice.schedule.model.StaffAvailabilityResponse
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.map
import com.github.kittinunf.result.mapError
import com.github.kittinunf.result.success
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.StatusCode
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

class AppointmentScheduleService(
    private val personService: PersonService,
    private val staffService: StaffService,
    private val healthProfessionalService: HealthProfessionalService,
    private val appointmentScheduleOptionService: AppointmentScheduleOptionService,
    private val channelService: ChannelService,
    private val schedulingUrlBuilder: SchedulingUrlBuilder,
    private val personCalendlyService: PersonCalendlyService,
    private val appointmentScheduleEventTypeService: AppointmentScheduleEventTypeService,
    private val staffAvailabilityService: StaffAvailabilityService,
) : Spannable {

    suspend fun getAppointmentScheduleCalendar(
        personId: PersonId,
        type: AppointmentScheduleType,
        task: PersonTask,
        file: AliceFile
    ): HealthPlanItemCalendar = coroutineScope {
        val filePath = "${file.id}.${file.type}"

        val personDeferred = async { personService.get(personId) }
        val optionsDeferred = async {
            appointmentScheduleOptionService.listByPerson(personId = personId)
        }
        val person = personDeferred.await().get()
        val options = optionsDeferred.await().get()

        val personCalendly = personCalendlyService.getOrCreate(personId).get()
        val option = options.find { option -> option.type == type && option.title == task.title }
        val calendarUrl = option?.calendarUrl
        val shouldUseSara = option?.shouldUseInternalScheduler ?: false

        logger.info(
            "AppointmentScheduleService::getAppointmentScheduleCalendar",
            "file_id" to file.id,
            "file_path" to filePath,
            "task_id" to task.id,
            "appointment_schedule_option_id" to option?.id.toString(),
            "person_calendly_id" to personCalendly.id,
            "task_title" to task.title,
            "calendar_url" to calendarUrl,
            "should_use_sara" to shouldUseSara
        )

        return@coroutineScope buildHealthPlanItemCalendar(
            task = task,
            person = person,
            personCalendly = personCalendly,
            calendarUrl = calendarUrl,
            appointmentScheduleOption = option,
            shouldUseSara = shouldUseSara,
            file = file,
            filePath = filePath
        )
    }

    suspend fun getStaffAvailabilityForStaffForPeriod(
        staffId: UUID,
        personId: String,
        fromDate: LocalDate,
        toDate: LocalDate,
        appointmentScheduleEventTypeId: UUID,
        providerUnitId: String?
    ): Result<StaffAvailabilityResponse, Throwable> = span("getStaffAvailabilityForStaffForPeriod") { span ->
        coroutineScope {
            span.setAvailabilityParameters(
                staffId = staffId,
                personId = personId,
                fromDate = fromDate,
                toDate = toDate,
                appointmentScheduleEventTypeId = appointmentScheduleEventTypeId,
                providerUnitId = providerUnitId?.toSafeUUID()
            )
            val emptyAvailability = StaffAvailabilityResponse(emptyList())
            val staff = getStaff(staffId) ?: return@coroutineScope emptyAvailability.success()
            val healthProfessionalDeferred = async { healthProfessionalService.findByStaffId(staff.id).getOrNull() }

            appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
                .map { appointmentScheduleEventType ->
                    if (appointmentScheduleEventType.isActive().not()) {
                        span.setStatus(StatusCode.ERROR, "Appointment schedule event type is not active")
                        return@map emptyList()
                    }

                    val healthProfessional = healthProfessionalDeferred.await()

                    getStaffAvailabilityBasedOnVacation(
                        personId = personId,
                        healthProfessional = healthProfessional,
                        fromDate = fromDate,
                        toDate = toDate,
                        appointmentScheduleEventType = appointmentScheduleEventType,
                        staffId = staffId,
                        providerUnitId = providerUnitId,
                        span = span
                    )
                }
                .map { StaffAvailabilityResponse(it) }
        }
    }

    suspend fun getAvailabilityForAppointmentScheduleEventTypeForPeriod(
        appointmentScheduleEventTypeId: UUID,
        personId: String,
        fromDate: LocalDate,
        toDate: LocalDate,
        providerUnitIds: List<UUID>?
    ) = span("getAvailabilityForAppointmentScheduleEventTypeForPeriod") { span ->
        appointmentScheduleEventTypeService.get(appointmentScheduleEventTypeId)
            .flatMap { appointmentScheduleEventType ->
                if (appointmentScheduleEventType.isActive().not()) {
                    span.setStatus(StatusCode.ERROR, "Appointment schedule event type is not active")
                    return@flatMap StaffAvailabilityResponse(emptyList()).success()
                }
                staffAvailabilityService.getForEventTypeAndPeriod(
                    startDate = fromDate,
                    endDate = toDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    appointmentScheduleEventType = appointmentScheduleEventType,
                    providerUnitIds = providerUnitIds,
                    personId = personId.toPersonId()
                )
                    .map { StaffAvailabilityResponse(it) }
            }
    }

    suspend fun getEventIdIfHealthProfessionalIsOnVacation(
        personId: PersonId,
        staffId: UUID,
        numberOfDaysFromNowToAllowScheduling: Int
    ) =
        healthProfessionalService.findByStaffId(staffId).map {
            val hasVacationOverlap = it.hasVacationOverlapWith(
                LocalDate.now(),
                LocalDate.now().plusDays(numberOfDaysFromNowToAllowScheduling.toLong())
            )
            if (hasVacationOverlap) { getPoolOnSiteEventTypeId(personId) } else null
        }

    private suspend fun buildHealthPlanItemCalendar(
        task: PersonTask,
        person: Person,
        personCalendly: PersonCalendly,
        calendarUrl: String?,
        appointmentScheduleOption: br.com.alice.data.layer.models.AppointmentScheduleOption?,
        shouldUseSara: Boolean,
        file: AliceFile,
        filePath: String
    ) = when {
        calendarUrl != null -> {
            buildScheduleUrlByCalendarUrl(
                task = task,
                person = person,
                calendarUrl = calendarUrl,
                personCalendly = personCalendly,
                appointmentScheduleOption = appointmentScheduleOption,
                filePath = filePath
            )
        }

        appointmentScheduleOption != null && shouldUseSara -> {
            buildScheduleUrlWithAppointmentScheduleOption(
                task = task,
                person = person,
                personCalendly = personCalendly,
                appointmentScheduleOption = appointmentScheduleOption,
                filePath = filePath
            )
        }

        else -> {
            buildChannelRouteNavigation(
                task = task,
                personId = person.id,
                file = file,
                filePath = filePath
            ).get()
        }
    }

    private suspend fun buildScheduleUrlByCalendarUrl(
        task: PersonTask,
        person: Person,
        calendarUrl: String,
        personCalendly: PersonCalendly,
        appointmentScheduleOption: br.com.alice.data.layer.models.AppointmentScheduleOption?,
        filePath: String
    ): HealthPlanItemCalendar {
        val extras = mapOf("person_task_id" to task.id.toString())
        return HealthPlanItemCalendar(
            calendarUrl = schedulingUrlBuilder.buildScheduleUrl(
                person = person,
                calendarUrl = calendarUrl,
                personCalendly = personCalendly,
                extras = extras
            ),
            scheduleOptions = schedulingUrlBuilder.buildScheduleOptions(
                scheduleOption = appointmentScheduleOption,
                person = person,
                personCalendly = personCalendly,
                extras = extras
            ),
            filePath = filePath
        )
    }

    private suspend fun buildScheduleUrlWithAppointmentScheduleOption(
        task: PersonTask,
        person: Person,
        personCalendly: PersonCalendly,
        appointmentScheduleOption: br.com.alice.data.layer.models.AppointmentScheduleOption,
        filePath: String
    ): HealthPlanItemCalendar {
        val extras = mapOf("person_task_id" to task.id.toString())
        return HealthPlanItemCalendar(
            calendarUrl = schedulingUrlBuilder.buildScheduleUrlWithAppointmentScheduleOption(
                person = person,
                appointmentScheduleOption = appointmentScheduleOption,
                personCalendly = personCalendly,
                extras = extras
            ),
            scheduleOptions = schedulingUrlBuilder.buildScheduleOptions(
                scheduleOption = appointmentScheduleOption,
                person = person,
                personCalendly = personCalendly,
                extras = extras
            ),
            filePath = filePath
        )
    }

    private suspend fun buildChannelRouteNavigation(
        task: PersonTask,
        personId: PersonId,
        file: AliceFile,
        filePath: String
    ): Result<HealthPlanItemCalendar, Throwable> =
        channelService.addChatV2(CreateChatRequest(personId = personId))
            .map { channelDocument ->
                val channelId = channelDocument.id.orEmpty()
                logger.info(
                    "AppointmentScheduleService::buildChannelRouteNavigation chat created",
                    "task_id" to task.id,
                    "channel_id" to channelId
                )
                HealthPlanItemCalendar(
                    navigation = NavigationResponse(
                        mobileRoute = MobileRouting.CHANNEL,
                        properties = mapOf(
                            "msg" to "Olá, gostaria de ajuda para agendar o exame \"${task.title}\" solicitado por um profissional fora da rede da Alice. Segue o pedido de exame:",
                            "url" to file.url,
                            "type" to file.type,
                            "channel_id" to channelId
                        )
                    ),
                    filePath = filePath
                )
            }
            .mapError {
                logger.info(
                    "AppointmentScheduleService::buildChannelRouteNavigation error while creating chat",
                    "task_id" to task.id,
                    "error_message" to it.message
                )
                it
            }

    private suspend fun getStaff(staffId: UUID) = span("getStaff") { span ->
        staffService.get(staffId).get().let { staff ->
            if (staff.active.not()) {
                span.setStatus(StatusCode.ERROR, "Staff is not active: $staffId")
                null
            } else staff
        }
    }

    private suspend fun getPoolAvailability(
        personId: String,
        healthProfessional: HealthProfessional,
        fromDate: LocalDate,
        toDate: LocalDate,
        providerUnitId: String?
    ): List<SlotForSpecificDuration> {
        val onVacationStart = healthProfessional.onVacationStart?.toLocalDate()
        val onVacationUntil = healthProfessional.onVacationUntil?.toLocalDate()

        return when {
            onVacationStart == null -> emptyList()
            onVacationUntil == null -> emptyList()
            else -> {
                when {
                    toDate.isBefore(onVacationStart) || fromDate.isAfter(onVacationUntil) -> emptyList()
                    else -> {
                        val startDateForPool =
                            if (onVacationStart.isAfter(fromDate)) onVacationStart else fromDate
                        val endDateForPool =
                            if (onVacationUntil.isBefore(toDate)) onVacationUntil else toDate

                        val poolEventTypeId = try {
                            getPoolEventTypeId(personId, providerUnitId)
                        } catch (ex: Throwable) {
                            logger.error(
                                "AppointmentScheduleService::getPoolAvailability error while getting pool appointment schedule event id",
                                ex.message
                            )
                            return emptyList()
                        }

                        staffAvailabilityService.getForEventTypeAndPeriod(
                            startDate = startDateForPool,
                            endDate = endDateForPool,
                            appointmentScheduleEventTypeId = poolEventTypeId,
                            appointmentScheduleEventType = null,
                            providerUnitIds = listOfNotNull(providerUnitId?.toSafeUUID()),
                            personId = personId.toPersonId()
                        )
                            .map { poolAvailability ->
                                poolAvailability.pmap {
                                    it.copy(poolEventTypeId = poolEventTypeId)
                                }
                            }
                            .get()
                    }
                }
            }
        }
    }

    private suspend fun getPoolEventTypeId(
        personId: String,
        providerUnitId: String?
    ) = personService.get(personId.toPersonId())
        .map { person ->
            val isDigitalSchedule = providerUnitId?.trim().isNullOrBlank()
            val featureFlagKey = when {
                isDigitalSchedule -> {
                    if (person.isChild) "child_digital_pool_appointment_schedule_event"
                    else "adult_digital_pool_appointment_schedule_event"
                }

                else -> {
                    if (person.isChild) "child_on_site_pool_appointment_schedule_event"
                    else "adult_on_site_pool_appointment_schedule_event"
                }
            }
            FeatureService.get(
                namespace = FeatureNamespace.SCHEDULE,
                key = featureFlagKey,
                defaultValue = ""
            ).trim().toUUID()
        }
        .get()

    private suspend fun getPoolOnSiteEventTypeId(
        personId: PersonId,
    ) = personService.get(personId)
        .map { person ->
            val featureFlagKey = if (person.isChild) "child_on_site_pool_appointment_schedule_event"
            else "adult_on_site_pool_appointment_schedule_event"
            FeatureService.get(
                namespace = FeatureNamespace.SCHEDULE,
                key = featureFlagKey,
                defaultValue = ""
            ).trim().toUUID()
        }
        .getOrNull()

    private suspend fun getStaffAvailabilityBasedOnVacation(
        personId: String,
        healthProfessional: HealthProfessional?,
        fromDate: LocalDate,
        toDate: LocalDate,
        appointmentScheduleEventType: AppointmentScheduleEventType,
        staffId: UUID,
        providerUnitId: String?,
        span: Span
    ) = coroutineScope {
        val hpHasVacationOverlapWithPeriod =
            healthProfessional?.hasVacationOverlapWith(fromDate, toDate) ?: false
        span.setHealthProfessionalVacation(healthProfessional, hpHasVacationOverlapWithPeriod)
        if (healthProfessional == null || !hpHasVacationOverlapWithPeriod) {
            return@coroutineScope staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                staffId = staffId,
                startDateTime = fromDate.atBeginningOfTheDay(),
                endDateTime = toDate.atEndOfTheDay(),
                appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                appointmentScheduleEventType = appointmentScheduleEventType,
                providerUnitId = providerUnitId?.toSafeUUID()
            ).get()
        }

        val poolAvailabilityDeferred = async {
            getPoolAvailability(
                personId = personId,
                healthProfessional = healthProfessional,
                fromDate = fromDate,
                toDate = toDate,
                providerUnitId = providerUnitId
            )
        }

        val onVacationStart = healthProfessional.onVacationStart?.toLocalDate() ?: return@coroutineScope emptyList()
        val onVacationUntil = healthProfessional.onVacationUntil?.toLocalDate() ?: return@coroutineScope emptyList()

        buildBaseStaffAvailabilitySlots(
            onVacationStart,
            onVacationUntil,
            fromDate,
            toDate,
            staffId,
            appointmentScheduleEventType,
            providerUnitId
        )
            .plus(poolAvailabilityDeferred.await())
            .sortedBy { slot -> slot.startTime }
    }

    private suspend fun buildBaseStaffAvailabilitySlots(
        onVacationStart: LocalDate,
        onVacationUntil: LocalDate,
        fromDate: LocalDate,
        toDate: LocalDate,
        staffId: UUID,
        appointmentScheduleEventType: AppointmentScheduleEventType,
        providerUnitId: String?
    ) = mutableListOf<Pair<LocalDateTime, LocalDateTime>>()
        .let { dateMap ->
            val dayBeforeVacationStart = onVacationStart.minusDays(1)
            val dayAfterVacationEnd = onVacationUntil.plusDays(1)
            if (fromDate.isBeforeEq(onVacationStart)) {
                dateMap.add(fromDate.atBeginningOfTheDay() to dayBeforeVacationStart.atEndOfTheDay())
            }
            if (toDate.isAfterEq(onVacationUntil)) {
                dateMap.add(dayAfterVacationEnd.atBeginningOfTheDay() to toDate.atEndOfTheDay())
            }
            dateMap
        }
        .let { dateMap ->
            dateMap.map {
                val startDate = it.first
                val endDate = it.second
                staffAvailabilityService.getForStaffAndEventTypeAndPeriod(
                    staffId = staffId,
                    startDateTime = startDate,
                    endDateTime = endDate,
                    appointmentScheduleEventTypeId = appointmentScheduleEventType.id,
                    appointmentScheduleEventType = appointmentScheduleEventType,
                    providerUnitId = providerUnitId?.toSafeUUID()
                ).get()
            }
        }
        .flatten()

    private fun Span.setAvailabilityParameters(
        staffId: UUID,
        personId: String,
        fromDate: LocalDate,
        toDate: LocalDate,
        appointmentScheduleEventTypeId: UUID,
        providerUnitId: UUID?
    ) {
        setAttribute("staff_id", staffId.toString())
        setAttribute("person_id", personId)
        setAttribute("from_date", fromDate.toString())
        setAttribute("to_date", toDate.toString())
        setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventTypeId.toString())
        setAttribute("provider_unit_id", providerUnitId.toString())
    }

    private fun Span.setHealthProfessionalVacation(
        healthProfessional: HealthProfessional?,
        hpHasVacationOverlapWithPeriod: Boolean
    ) {
        setAttribute("health_professional_id", healthProfessional?.id.toString())
        setAttribute("health_professional_on_vacation", healthProfessional?.isOnVacation().toString())
        setAttribute("has_vacation_overlap_with_period", hpHasVacationOverlapWithPeriod)
    }

}
