package br.com.alice.member.api.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.coverage.client.AccreditedNetworkFavoriteService
import br.com.alice.coverage.client.ConsolidatedAccreditedNetworkService
import br.com.alice.data.layer.models.AccreditedNetworkFavorite
import br.com.alice.member.api.builders.FavoriteInfoTransportBuilder
import br.com.alice.member.api.models.accreditedNetwork.FavoriteInfoTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.util.UUID

class AccreditedNetworkFavoriteInternalService(
    private val accreditedNetworkFavoriteService: AccreditedNetworkFavoriteService,
    private val consolidatedAccreditedNetworkService: ConsolidatedAccreditedNetworkService,
) {
    suspend fun addFavorite(
        personId: PersonId,
        referenceId: UUID,
    ): Result<FavoriteInfoTransport, Throwable> =
        accreditedNetworkFavoriteService.getByPersonIdAndReferenceId(personId, referenceId)
            .map { favorite ->
                FavoriteInfoTransportBuilder.buildTransport(favorite.id, true)
            }
            .coFoldNotFound {
                consolidatedAccreditedNetworkService.getByReferencedId(referenceId)
                    .map {
                        accreditedNetworkFavoriteService.add(
                            AccreditedNetworkFavorite(
                                personId = personId,
                                referenceId = referenceId,
                                referenceType = it.first().type,
                                specialtyIds = it.first().specialtyIds
                            )
                        )
                    }
                    .map {
                        FavoriteInfoTransportBuilder.buildTransport(
                            referenceId = referenceId,
                            isFavorite = true,
                            favoriteId = it.get().id
                        )
                    }
            }


    suspend fun removeFavorite(
        favoriteId: UUID,
        referenceId: UUID
    ): Result<FavoriteInfoTransport, Throwable> =
        accreditedNetworkFavoriteService.deleteById(favoriteId)
            .map {
                FavoriteInfoTransportBuilder.buildTransport(
                    referenceId = referenceId,
                    isFavorite = false,
                    favoriteId = favoriteId
                )
            }
}
