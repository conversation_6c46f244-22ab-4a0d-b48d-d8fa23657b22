package br.com.alice.member.api.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import com.github.kittinunf.result.Result
import com.google.gson.annotations.SerializedName

@RemoteService
interface FirebaseClient: Service {
    override val namespace get() = "member"
    override val serviceName get() = "firebaseClient"

    suspend fun getSignInTokens(accessToken: String): Result<SignInFirebaseResponse, Throwable>
}

data class SignInFirebaseRequest(
    val returnSecureToken: Boolean,
    val token: String
)

data class SignInFirebaseResponse(
    @SerializedName("kind") val kind: String,
    @SerializedName("idToken") val idToken: String,
    @SerializedName("refreshToken") val refreshToken: String,
    @SerializedName("expiresIn") val expiresIn: String,
    @SerializedName("isNewUser") val isNewUser: Boolean,
) {
    fun toResponse(): SignInFirebaseResponse {
        return SignInFirebaseResponse(
            kind = kind,
            idToken = idToken,
            refreshToken = refreshToken,
            expiresIn = expiresIn,
            isNewUser = isNewUser
        )
    }
}
