package br.com.alice.member.api.services

import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.data.layer.models.ActionPlanTask
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.member.api.extensions.mergedStatus
import br.com.alice.member.api.models.procedureAuthorization.ActionPlanTaskAuthorization
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.flatMapError
import com.github.kittinunf.result.getOrElse
import com.github.kittinunf.result.map

class ActionPlanTaskAuthorizationService(
    private val totvsGuiaService: TotvsGuiaService,
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService
) {

    suspend fun findAuthorizationByActionPlanTask(actionPlanTask: ActionPlanTask): Result<ActionPlanTaskAuthorization, Throwable> =
        if (actionPlanTask.hasAuthorizationEnabled()) {
            mvAuthorizedProcedureService.findByTestRequestId(actionPlanTask.id).flatMap { procedure ->
                totvsGuiaService.get(procedure.totvsGuiaId!!).map { totvsGuia ->
                    if (totvsGuia.status != TotvsGuiaStatus.CANCELLED) {
                        return@map totvsGuia.toActionPlanTaskAuthorization()
                    }
                    throw CannotFindAuthorizationException()
                }
            }.flatMapError { CannotFindAuthorizationException().failure() }
        } else Result.failure(CannotFindAuthorizationException())

    suspend fun findAuthorizationByActionPlanTasks(actionPlanTasks: List<ActionPlanTask>): Result<ActionPlanTaskAuthorization, Throwable> {

        val authorizations = actionPlanTasks.mapNotNull { findAuthorizationByActionPlanTask(it).getOrElse { null } }
            .groupBy { it.totvsGuiaId }

        if (authorizations.isEmpty()) return Result.failure(CannotFindAuthorizationException())
        if (authorizations.size == 1) return Result.success(authorizations.values.first().reduceWithSameTotvsGuiaId())
        return Result.success(authorizations.values.flatten().reduceWithoutTotvsGuiaId())
    }

    private fun ActionPlanTask.hasAuthorizationEnabled() = this.isTestRequest()

    private fun TotvsGuia.toActionPlanTaskAuthorization() =
        ActionPlanTaskAuthorization(
            totvsGuiaStatus = this.status ?: TotvsGuiaStatus.PENDING,
            totvsGuiaId = this.id
        )

    private fun List<ActionPlanTaskAuthorization>.reduceWithSameTotvsGuiaId() =
        ActionPlanTaskAuthorization(
            totvsGuiaStatus = this.map { it.totvsGuiaStatus }.mergedStatus(),
            totvsGuiaId = this.first().totvsGuiaId
        )

    private fun List<ActionPlanTaskAuthorization>.reduceWithoutTotvsGuiaId() =
        ActionPlanTaskAuthorization(
            totvsGuiaStatus = this.map { it.totvsGuiaStatus }.mergedStatus(),
            totvsGuiaId = null
        )
}

open class CannotFindAuthorizationException(
    message: String = "Cannot find authorization for action plan task",
    code: String = "cannot_find_authorization",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)
