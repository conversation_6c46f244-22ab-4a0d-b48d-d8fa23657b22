package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.schedule.model.events.AppointmentScheduleCollisionDetectedEvent
import com.github.kittinunf.result.Result

class AppointmentScheduleCollisionDetectedConsumer : Consumer() {

    suspend fun updateAppState(event: AppointmentScheduleCollisionDetectedEvent): Result<Unit, Throwable> = coResultOf {
        val personId = event.payload.personId
        val appointmentScheduleEvent = event.payload.appointmentScheduleEvent

        logger.info(
            "Process send scheduling collision detected notification",
            "event_id" to event.messageId,
            "appointment_schedule_data" to appointmentScheduleEvent,
            "person_id" to personId,
        )


        AppStateNotifier.updateAppState(personId, AppState.HOME)
        AppStateNotifier.updateAppState(personId, AppState.APPOINTMENT_SCHEDULE_COLLISION_DETECTED)
    }
}
