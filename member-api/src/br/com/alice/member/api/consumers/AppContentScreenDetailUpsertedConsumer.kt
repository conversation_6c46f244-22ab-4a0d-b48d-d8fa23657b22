package br.com.alice.member.api.consumers

import br.com.alice.app.content.notifier.AppContentScreenDetailUpsertedEvent
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.ScreenDetailType
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import com.github.kittinunf.result.Result

class AppContentScreenDetailUpsertedConsumer {

    suspend fun notifyAppContentScreenDetailUpsertion(
        event: AppContentScreenDetailUpsertedEvent
    ): Result<Unit, Throwable> = coResultOf {
        logger.info(
            "Consuming APP-CONTENT-SCREEN-DETAIL-UPSERTED event on notifyAppContentScreenDetailUpsertion operation",
            "event" to event
        )
        val appContentScreenDetail = event.payload.screenDetail
        val personId = event.payload.screenDetail.personId

        val appStateToNotify = checkAppStateToNotify(appContentScreenDetail.screenType)

        AppStateNotifier.updateAppState(
            personId,
            appStateToNotify
        )

        logger.info(
            "Screen Detail notified to AppState.",
            "event" to event,
            "person_id" to personId,
            "app_state_to_notify" to appStateToNotify
        )
    }

    private fun checkAppStateToNotify(screenType: ScreenDetailType) =
        when (screenType) {
            ScreenDetailType.HOME -> AppState.CHESHIRE_HOME
            ScreenDetailType.ALICE_NOW -> AppState.CHESHIRE_ALICE_NOW
            ScreenDetailType.HEALTH -> AppState.CHESHIRE_HEALTH
            ScreenDetailType.CHANNELS -> AppState.CHESHIRE_CHANNELS
            ScreenDetailType.SCHEDULE -> AppState.CHESHIRE_SCHEDULE
            ScreenDetailType.UNIFIED_HEALTH -> AppState.UNIFIED_HEALTH
        }

}
