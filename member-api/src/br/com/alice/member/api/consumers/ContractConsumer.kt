package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.membership.model.events.ContractGeneratedEvent
import com.github.kittinunf.result.Result

class ContractConsumer {

    suspend fun updateAppState(event: ContractGeneratedEvent): Result<Any, Throwable> = coResultOf {
        AppStateNotifier.updateAppState(
            event.payload.contract.personId,
            AppState.GAS_CONTRACT
        )
    }
}
