package br.com.alice.member.api.consumers

import br.com.alice.action.plan.client.ActionPlanTaskService
import br.com.alice.action.plan.notifier.ActionPlanTasksAcknowledgedEvent
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.Logger
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import java.time.LocalDateTime

class ActionPlanTasksAcknowledgedConsumer(
    private val actionPlanTaskService: ActionPlanTaskService
): Consumer() {

    suspend fun updateAppState(event: ActionPlanTasksAcknowledgedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            coResultOf {
                event.payload.takeIf { it.tasks.isNotEmpty() }?.tasks!![0].let {
                    Logger.info(
                        "ActionPlanTasksAcknowledgedConsumer.updateAppState: Updating HEALTH_PLAN AppState",
                        "person_id" to it.personId.id.toString(),
                        "action_plan_task_id" to it.id,
                        "action_plan_task_status" to it.status
                    )

                    AppStateNotifier.updateAppState(it.personId, AppState.UNIFIED_HEALTH)
                    AppStateNotifier.updateAppState(it.personId, AppState.HEALTH_ALL_DEMANDS)

                    if (canUpdateAppStateRedesignUnifiedHealth(it.personId)) {
                        AppStateNotifier.updateAppState(it.personId, AppState.REDESIGN_UNIFIED_HEALTH)
                    }

                }
            }
        }

    private suspend fun canUpdateAppStateRedesignUnifiedHealth(personId: PersonId) =
        actionPlanTaskService.getUnacknowlegedTasksByDate(personId, LocalDateTime.now().atBeginningOfTheDay()).get()
            .isEmpty()
}
