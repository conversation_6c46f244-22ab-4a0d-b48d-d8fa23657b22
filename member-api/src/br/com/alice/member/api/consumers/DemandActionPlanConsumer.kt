package br.com.alice.member.api.consumers

import br.com.alice.action.plan.notifier.DemandActionPlanUpsertedEvent
import br.com.alice.common.extensions.coResultOf
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import com.github.kittinunf.result.Result

class DemandActionPlanConsumer {

    suspend fun updateAppStateOnUpsert(event: DemandActionPlanUpsertedEvent): Result<Any, Throwable> = coResultOf {
        AppStateNotifier.updateAppState(
            event.payload.demand.personId,
            AppState.REDESIGN_ALICE_AGORA,
            AppState.REDESIGN_ALICE_AGORA_DEMAND_MENU_SECTION
        )
    }
}
