package br.com.alice.member.api.consumers

import br.com.alice.common.asyncLayer
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldException
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.healthplan.events.HealthPlanTaskGroupPublishedEvent
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.api.services.PushNotificationInternalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import com.google.firebase.messaging.FirebaseMessagingException

class HealthPlanTaskGroupPublishedConsumer(
    private val pushNotificationService: PushNotificationInternalService,
) : Consumer() {

    suspend fun updateAppState(event: HealthPlanTaskGroupPublishedEvent): Result<Unit, Throwable> = coResultOf {
        if (event.payload.tasks.isEmpty()) return@coResultOf

        val personId = event.payload.tasks.first().personId

        logger.info(
            "Updating HEALTH_PLAN AppState",
            "person_id" to personId.id.toString()
        )

        AppStateNotifier.updateAppState(personId, AppState.HEALTH_PLAN)
        AppStateNotifier.updateAppState(personId, AppState.UNIFIED_HEALTH)
        AppStateNotifier.updateAppState(personId, AppState.HEALTH_ALL_DEMANDS)
    }

    suspend fun sendPush(event: HealthPlanTaskGroupPublishedEvent): Result<Any, Throwable> = asyncLayer {
        if (event.payload.tasks.isEmpty()) return@asyncLayer true.success()

        val personId = event.payload.tasks.first().personId
        val healthPlanTasks = event.payload.tasks

        return@asyncLayer authenticated(personId) {
            pushNotificationService.sendNewTasksPushNotification(personId, healthPlanTasks.count())
                .coFoldException(NotFoundException::class) {
                    logger.error("Failed to send push notification", it)
                    false.success()
                }
        }
    }

}
