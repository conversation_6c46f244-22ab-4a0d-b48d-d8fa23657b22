package br.com.alice.member.api.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.DealChannel
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.sales_channel.service.OngoingCompanyDealService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.getOrNull
import com.github.kittinunf.result.success
import java.util.UUID

class MoveToPhaseConsumer(
   private val memberOnboardingService: MemberOnboardingService,
   private val beneficiaryService: BeneficiaryService,
   private val ongoingCompanyDealService: OngoingCompanyDealService
): Spannable, Consumer() {

   suspend fun createOnboardingWhenWaitingForReview(event: MoveToPhaseEvent) =
      span("createOnboardingWhenWaitingForReview") { span ->
         withSubscribersEnvironment {
            if(shouldUseNewOnboardingConsumer()) return@withSubscribersEnvironment false.success()

            if (!event.payload.requiredPhaseType.shouldCreateOnboarding()) return@withSubscribersEnvironment false.success()

            beneficiaryService.get(event.payload.beneficiaryId)
               .flatMap { beneficiary ->
                  span.setAttribute("personId", beneficiary.personId)

                  if(beneficiary.companyId.isBrokerChannel()) return@withSubscribersEnvironment false.success()
                  span.setAttribute("isBrokerChannel", false)

                  memberOnboardingService.createMemberOnboardingIfNecessary(
                     personId = beneficiary.personId
                  ).recordResult(span)
               }
               .coFoldNotFound {
                  false.success()
               }
         }.recordResult(span)
      }

   private suspend fun UUID.isBrokerChannel() =
      ongoingCompanyDealService.getByCompanyId(this)
         .getOrNull()
         ?.maxByOrNull { it.createdAt }
         ?.channel == DealChannel.BROKER

   private fun BeneficiaryOnboardingPhaseType.shouldCreateOnboarding() =
      shouldUseOnboardingV2() && this == BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW

   private fun shouldUseOnboardingV2(): Boolean =
      FeatureService.get(
         namespace = FeatureNamespace.MEMBERSHIP,
         key = "should-use-onboarding-v2",
         defaultValue = false
      )

   private fun shouldUseNewOnboardingConsumer(): Boolean =
      FeatureService.get(
         namespace = FeatureNamespace.MEMBERSHIP,
         key = "should-use-new-onboarding-consumer",
         defaultValue = false
      )
}
