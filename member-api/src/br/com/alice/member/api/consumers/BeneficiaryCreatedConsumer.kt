package br.com.alice.member.api.consumers

import br.com.alice.business.events.BeneficiaryCreatedEvent
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import com.github.kittinunf.result.Result

class BeneficiaryCreatedConsumer {

    suspend fun expireToken(event: BeneficiaryCreatedEvent): Result<Unit, Throwable> = coResultOf {
        logger.info(
            "Consuming BENEFICIARY-CREATED event on expireToken operation",
            "beneficiary_id" to event.payload.beneficiary.id,
            "person_id" to event.payload.beneficiary.personId,
            "member_id" to event.payload.beneficiary.memberId,
        )

        AppStateNotifier.updateAppState(
            event.payload.beneficiary.personId,
            AppState.TOKEN_EXPIRED
        )
    }
}
