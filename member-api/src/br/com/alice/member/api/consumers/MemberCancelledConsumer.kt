package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.thenError
import br.com.alice.common.logging.logger
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.MemberCancelledEvent
import com.github.kittinunf.result.success

class MemberCancelledConsumer(
    private val personService: PersonService
) : Consumer() {

    suspend fun expireToken(event: MemberCancelledEvent) = withSubscribersEnvironment {
        logger.info("Consuming MEMBER-CANCELLED event on expireToken operation", "event" to event)
        personService.terminateSession(event.payload.member.personId)
            .coFoldNotFound {
                false.success()
            }.thenError {
                logger.error("MemberCancelledConsumer::expireToken: error to expire member token", it)
            }
    }
}
