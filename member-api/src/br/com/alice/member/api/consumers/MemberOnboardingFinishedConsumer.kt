package br.com.alice.member.api.consumers

import br.com.alice.common.logging.logger
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.member.onboarding.client.MemberOnboardingTemplateService
import br.com.alice.person.client.PersonService
import br.com.alice.person.events.MemberOnboardingFinishedEvent
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map

class MemberOnboardingFinishedConsumer(
    private val crmAnalyticsTracker: CrmAnalyticsTracker,
    private val personService: PersonService,
    private val memberOnboardingTemplateService: MemberOnboardingTemplateService
) : Consumer() {

    suspend fun sendTriggerToSatisfactionQuestionnaire(event: MemberOnboardingFinishedEvent) =
        withSubscribersEnvironment {
            personService.get(event.payload.personId).flatMap { person ->
                memberOnboardingTemplateService.findByPerson(person.id).map { memberOnboardingTemplate ->
                    val analyticsEvent = AnalyticsEvent(
                        name = AnalyticsEventName.MEMBER_ONBOARDING_FINISHED,
                        properties = mapOf(
                            "template_type" to memberOnboardingTemplate.type.toString()
                        )
                    )
                    crmAnalyticsTracker.sendEvent(
                        nationalId = person.nationalId,
                        event = analyticsEvent
                    ).also {
                        logger.info("MemberOnboardingFinishedConsumer sendTriggerToSatisfactionQuestionnaire",
                            "person_id" to person.id,
                            "event" to analyticsEvent
                        )
                    }
                }
            }
        }
}
