package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.PersonHealthEventCategory.Companion.timelineCategories
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.wanda.event.PersonHealthEventCreatedEvent
import com.github.kittinunf.result.Result

class PersonHealthEventCreatedConsumer {
    suspend fun updateAppState(event: PersonHealthEventCreatedEvent): Result<Unit, Throwable> = coResultOf {
        logger.info(
            "Consuming person health event created",
            "person_health_event_id" to event.payload.personHealthEvent.id
        )
        if (!timelineCategories.contains(event.payload.personHealthEvent.category)) {
            logger.info(
                "Person health event not relevant on mobile app",
                "person_health_event_id" to event.payload.personHealthEvent.id
            )
            return@coResultOf
        }
        AppStateNotifier.updateAppState(
            event.payload.personHealthEvent.personId,
            AppState.PERSON_HEALTH_EVENT
        )
    }
}
