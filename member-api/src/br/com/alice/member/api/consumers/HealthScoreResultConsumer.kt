package br.com.alice.member.api.consumers

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.event.ClinicalOutcomeRecordCreatedEvent
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppState.MEMBER_ONBOARDING_V2
import br.com.alice.member.api.services.AppState.REDESIGN_UNIFIED_HEALTH
import br.com.alice.member.api.services.AppState.UNIFIED_HEALTH
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.member.onboarding.client.MemberOnboardingService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import java.util.UUID

class HealthScoreResultConsumer(
    private val memberOnboardingService: MemberOnboardingService
): Consumer() {
    companion object {
        private val HEALTH_SCORE_ONBOARDING_STEP_TYPE = MemberOnboardingStepType.SCORE_MAGENTA
    }

    suspend fun notifyHealthScoreResult(event: ClinicalOutcomeRecordCreatedEvent): Result<Unit, Throwable> =
        coResultOf {
            logger.info(
                "Consuming CLINICAL-OUTCOME-RECORD-CREATED event on notifyHealthScoreResult operation",
                "event" to event
            )

            val clinicalOutcomeRecord = event.payload.clinicalOutcomeRecord

            if (isScoreMagenta(clinicalOutcomeRecord.outcomeConfId)) {
                val personId = clinicalOutcomeRecord.personId

                AppStateNotifier.updateAppState(
                    personId,
                    AppState.SCORE_MAGENTA_RESULT_EVENT
                )

                logger.info(
                    "Score Magenta result notified to AppState.",
                    "event" to event,
                    "person_id" to personId
                )
            }
        }

    suspend fun finishHealthScoreOnboardingStep(event: ClinicalOutcomeRecordCreatedEvent): Result<Any, Throwable> =
        span("finishHealthScoreOnboardingStep") { span ->
            val personId = event.payload.clinicalOutcomeRecord.personId
            span.setAttribute("person_id", personId)

            withSubscribersEnvironment {
                event.payload.clinicalOutcomeRecord.takeIf { shouldUseOnboardingV2() && isScoreMagenta(it.outcomeConfId) }
                    ?.let {
                        memberOnboardingService.getByPersonIdAndCompleted(
                            personId = personId,
                            completed = false
                        ).flatMap { memberOnboarding ->
                            span.setAttribute("member_onboarding_id", memberOnboarding.id)
                            memberOnboardingService.updateStepStatus(
                                memberOnboarding.id,
                                HEALTH_SCORE_ONBOARDING_STEP_TYPE
                            )
                        }.then {
                            AppStateNotifier.updateAppState(
                                personId,
                                MEMBER_ONBOARDING_V2, UNIFIED_HEALTH, REDESIGN_UNIFIED_HEALTH
                            )
                        }.coFoldNotFound { false.success() }
                    } ?: false.success()

            }.recordResult(span)
        }

    private fun isScoreMagenta(id: UUID): Boolean {
        val scoreMagentaId = FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "score-magenta-outcome-conf-id",
            defaultValue = "bec90d96-4d94-4f66-9cb3-5a8956062600"
        )

        return scoreMagentaId.toUUID() == id
    }

    private fun shouldUseOnboardingV2(): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "should-use-onboarding-v2",
            defaultValue = false
        )
}
