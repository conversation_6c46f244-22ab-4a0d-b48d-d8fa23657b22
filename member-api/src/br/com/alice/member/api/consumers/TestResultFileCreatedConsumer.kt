package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.ehr.event.TestResultFileCreatedEvent
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import com.github.kittinunf.result.Result

class TestResultFileCreatedConsumer {

    suspend fun updateAppState(event: TestResultFileCreatedEvent): Result<Unit, Throwable> = coResultOf {
        logger.info("received test result file created", "event" to event)
        AppStateNotifier.updateAppState(event.payload.personId, AppState.HOME_TEST_RESULTS)
    }
}
