package br.com.alice.member.api.consumers

import br.com.alice.authentication.Authenticator
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.person.model.events.MemberActivatedEvent
import com.google.firebase.auth.AuthErrorCode
import com.google.firebase.auth.FirebaseAuthException

class MemberActivatedConsumer: Spannable {

    suspend fun expireTokenAndUpdateAppState(
        event: MemberActivatedEvent
    ) = span("expireTokenAndUpdateAppState") { span ->
        coResultOf<Unit, Throwable> {
            val personId = event.payload.personId
            span.setAttribute("person_id", personId)

            revokeToken(event.payload.personId)

            AppStateNotifier.updateAppState(personId, AppState.TOKEN_EXPIRED)
            AppStateNotifier.updateAppState(personId, AppState.BENEFICIARY_MEMBERSHIP_ACTIVATED)
        }
            .recordResult(span)
    }

    private suspend fun revokeToken(personId: PersonId) = span("revokeToken") { span ->
        try {
            Authenticator.revokeTokenByUserId(personId.toString())
        } catch (authException : FirebaseAuthException) {
            span.setAttribute("firebase_auth_error_code", authException.authErrorCode)
            span.setAttribute("firebase_error_code", authException.errorCode)
            if (authException.authErrorCode != AuthErrorCode.USER_NOT_FOUND) throw authException
        }
    }
}
