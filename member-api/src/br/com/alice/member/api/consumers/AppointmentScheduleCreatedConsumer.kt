package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.member.api.services.AppState.APPOINTMENT_HUB
import br.com.alice.member.api.services.AppState.REDESIGN_UNIFIED_HEALTH
import br.com.alice.member.api.services.AppState.APPOINTMENT_SCHEDULE
import br.com.alice.member.api.services.AppState.HEALTH_PLAN
import br.com.alice.member.api.services.AppState.HOME
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import com.github.kittinunf.result.Result

class AppointmentScheduleCreatedConsumer {

    suspend fun updateAppState(event: AppointmentScheduleCreatedEvent): Result<Any, Throwable> = coResultOf {
        val eventId = event.payload.appointmentSchedule.eventId
        val personId = event.payload.person.personId
        val healthPlanTaskId = event.payload.appointmentSchedule.healthPlanTaskId

        logger.info(
            "AppointmentScheduleCreatedConsumer.updateAppState: Updating app's `APPOINTMENT_SCHEDULE_CREATED` state",
            "event_id" to eventId,
            "person_id" to personId,
            "health_plan_task_id" to healthPlanTaskId
        )

        val appStates = arrayOf(
            HOME,
            APPOINTMENT_SCHEDULE,
            REDESIGN_UNIFIED_HEALTH,
            APPOINTMENT_HUB
        ).let {
            if (healthPlanTaskId != null) it.plus(HEALTH_PLAN)
            else it
        }

        AppStateNotifier.updateAppState(personId, *appStates)
        true
    }

}
