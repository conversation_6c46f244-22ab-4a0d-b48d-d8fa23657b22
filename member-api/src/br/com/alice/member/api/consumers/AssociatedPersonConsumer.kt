package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.member.api.services.AppState.MEMBER_ONBOARDING
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.sortinghat.event.PersonTeamAssociationEvent
import com.github.kittinunf.result.Result

class AssociatedPersonConsumer : Consumer() {
    suspend fun updateAppState(event: PersonTeamAssociationEvent): Result<Unit, Throwable> = coResultOf {
        logger.info(
            "AssociatedPersonConsumer.updateAppState: updating MEMBER_ONBOARDING appstate",
            "personId" to event.payload.personId,
            "risk" to event.payload.risk,
        )

        event.payload.personId.let {
            AppStateNotifier.updateAppState(event.payload.personId, MEMBER_ONBOARDING)
        }
    }
}
