package br.com.alice.member.api.consumers

import br.com.alice.common.redis.GenericCache
import br.com.alice.schedule.model.events.AppointmentScheduleEventTypeUpdated
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class AppointmentScheduleEventTypeUpdatedConsumer(val cache: GenericCache) : Consumer() {

    suspend fun invalidateCache(event: AppointmentScheduleEventTypeUpdated): Result<Any, Throwable> =
        span("invalidateCache") { span ->
            withSubscribersEnvironment {
                val appointmentScheduleEventType = event.payload.appointmentScheduleEventType
                span.setAttribute("appointment_schedule_event_type_id", appointmentScheduleEventType.id.toString())
                val cacheKeyToInvalidate = "appointmentScheduleEventTypeById#${appointmentScheduleEventType.id}"
                span.setAttribute("cache_key_to_invalidate", cacheKeyToInvalidate)

                val invalidatedKeys = cache.invalidateAndReturnKeys(cacheKeyToInvalidate)
                span.setAttribute("invalidated_keys", invalidatedKeys.joinToString(","))

                invalidatedKeys.success()
            }
        }

}
