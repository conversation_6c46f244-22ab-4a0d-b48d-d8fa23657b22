package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.PaymentReason
import br.com.alice.member.api.services.AppState.FIRST_PAYMENT_CREATED
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.moneyin.event.InvoicePaymentCreatedEvent
import com.github.kittinunf.result.Result

class InvoicePaymentCreatedConsumer {

    suspend fun updateAppState(event: InvoicePaymentCreatedEvent): Result<Unit, Throwable> = coResultOf {
        logger.info(
            "InvoicePaymentCreatedConsumer.InvoicePaymentCreatedEvent received",
            "event" to event
        )

        if (hasNoPersonId(event)) {
            logger.warn("Has no person id, so ignoring it")
            return@coResultOf
        }
        if (isNotFirstPayment(event)) {
            logger.warn("It's not the first payment, so ignoring it")
            return@coResultOf
        }
        val personId = event.payload.member?.personId

        logger.info(
            "Updating app state",
            "person_id" to personId,
            "FIRST_PAYMENT_CREATED" to FIRST_PAYMENT_CREATED,
        )
        AppStateNotifier.updateAppState(personId!!, FIRST_PAYMENT_CREATED)
    }

    private fun hasNoPersonId(event: InvoicePaymentCreatedEvent): Boolean = event.payload.member?.personId == null

    private fun isNotFirstPayment(event: InvoicePaymentCreatedEvent): Boolean =
        event.payload.paymentReason != PaymentReason.FIRST_PAYMENT
}
