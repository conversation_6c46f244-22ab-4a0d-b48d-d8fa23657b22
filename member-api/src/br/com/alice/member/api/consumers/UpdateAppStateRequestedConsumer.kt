package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.member.api.services.AppState
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.membership.model.events.UpdateAppStateRequestedEvent
import com.github.kittinunf.result.Result

class UpdateAppStateRequestedConsumer {

    suspend fun updateAppState(event: UpdateAppStateRequestedEvent): Result<Unit, Throwable> =
        coResultOf {
            try {
                val personId = event.payload.personId
                val appState = event.payload.appState
                logger.info(
                    "UpdateAppStateRequestedConsumer::updateAppState",
                    "app_state" to appState,
                    "person_id" to personId
                )

                val appStateToUpdate = AppState.valueOf(appState.uppercase())
                AppStateNotifier.updateAppState(personId, appStateToUpdate)
            } catch (throwable: Throwable) {
                logger.error(
                    "UpdateAppStateRequestedConsumer::updateAppState error",
                    "error_message" to throwable.message.orEmpty()
                )
            }

        }

}
