package br.com.alice.member.api.consumers

import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.logging.logger
import br.com.alice.member.api.services.AppState.PAYMENT_UPDATED
import br.com.alice.member.api.services.AppStateNotifier
import br.com.alice.moneyin.event.PaymentDetailCreatedEvent
import com.github.kittinunf.result.Result

class PaymentDetailCreatedConsumer {

    suspend fun updateAppState(event: PaymentDetailCreatedEvent): Result<Unit, Throwable> = coResultOf {
        logger.info(
            "PaymentDetailCreatedConsumer.PaymentDetailCreatedEvent received",
            "event" to event
        )
        val billingPersonId = event.payload.personId
        if (billingPersonId == null) {
            logger.warn("Has no billingPerson id, so ignoring it")
            return@coResultOf
        } else {
            logger.info(
                "Updating app state",
                "billing_person_id" to billingPersonId,
                "PAYMENT_UPDATED" to PAYMENT_UPDATED,
            )

            AppStateNotifier.updateAppState(billingPersonId!!, PAYMENT_UPDATED)
        }
    }
}
