package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import java.time.LocalDateTime
import java.util.UUID

data class AppContentScreenDetail(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val screenType: ScreenDetailType,
    val sectionType: ScreenDetailSectionType,
    val sectionContent: String,
    val sectionContext: ScreenDetailSectionContext = ScreenDetailSectionContext.QUESTIONNAIRE,
    val healthFormSource: HealthFormAnswerSource? = null,
    val healthDemandName: String? = null,
    val startDate: LocalDateTime? = null,
    val endDate: LocalDateTime? = null,
    val status : ScreenDetailStatus,
    val position: Int = 0, // Not implemented yet
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
) : Model, HealthInformation, PersonReference

enum class ScreenDetailType{
    HOME,
    HEALTH,
    ALICE_NOW,
    CHANNELS,
    SCHEDULE,
    UNIFIED_HEALTH
}

enum class ScreenDetailSectionType(val key: String){
    QUESTIONNAIRE_SECTION("questionnaire_section"),
    MODULE_SECTION("module_section"),
    CARD_SECTION("card_section")
}

enum class ScreenDetailSectionContext(val position: Int){
    ONBOARDING(0),
    SCORE_MAGENTA(0),
    PDA(1),
    EXAM(2),
    QUESTIONNAIRE(3)
}

enum class ScreenDetailStatus{
    ACTIVE, INACTIVE, FINISHED, OVERDUE
}
