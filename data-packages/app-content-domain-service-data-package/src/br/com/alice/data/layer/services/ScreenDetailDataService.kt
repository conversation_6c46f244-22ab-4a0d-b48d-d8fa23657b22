package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.JsonSearchPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AppContentScreenDetail
import br.com.alice.data.layer.models.HealthFormAnswerSource
import br.com.alice.data.layer.models.ScreenDetailSectionContext
import br.com.alice.data.layer.models.ScreenDetailSectionType
import br.com.alice.data.layer.models.ScreenDetailStatus
import br.com.alice.data.layer.models.ScreenDetailType
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface ScreenDetailDataService: Service,
    Finder<ScreenDetailDataService.FieldOptions, ScreenDetailDataService.OrderingOptions, AppContentScreenDetail>,
    Counter<ScreenDetailDataService.FieldOptions, ScreenDetailDataService.OrderingOptions, AppContentScreenDetail>,
    Getter<AppContentScreenDetail>,
    Adder<AppContentScreenDetail>,
    Updater<AppContentScreenDetail>,
    AdderList<AppContentScreenDetail>,
    UpdaterList<AppContentScreenDetail> {

    override val namespace: String
        get() = "app_content"
    override val serviceName: String
        get() = "screen_detail"

    class Id: Field.TableIdField(AppContentScreenDetail::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class PersonIdField: Field.TextField(AppContentScreenDetail::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class SectionContentField: Field.TextField(AppContentScreenDetail::sectionContent) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class ScreenTypeField: Field.TextField(AppContentScreenDetail::screenType) {
        fun eq(value: ScreenDetailType) = Predicate.eq(this, value)
        fun inList(value: List<ScreenDetailType>) = Predicate.inList(this, value)
    }
    
    class SectionTypeField: Field.TextField(AppContentScreenDetail::sectionType){
        fun eq(value: ScreenDetailSectionType) = Predicate.eq(this, value)
        fun inList(value: List<ScreenDetailSectionType>) = Predicate.inList(this, value)
    }

    class SectionContextField: Field.TextField(AppContentScreenDetail::sectionContext){
        fun eq(value: ScreenDetailSectionContext) = Predicate.eq(this, value)
        fun inList(value: List<ScreenDetailSectionContext>) = Predicate.inList(this, value)
    }

    class StatusField: Field.TextField(AppContentScreenDetail::status){
        fun eq(value: ScreenDetailStatus) = Predicate.eq(this, value)
        fun inList(value: List<ScreenDetailStatus>) = Predicate.inList(this, value)
    }

    class StartDateField: Field.DateTimeField(AppContentScreenDetail::startDate) {
        fun isNull() = Predicate.isNull(this)
        fun greaterEq(value: LocalDateTime?) =
            if (value == null) Predicate.isNull(this)
            else Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime?) =
            if (value == null) Predicate.isNull(this)
            else Predicate.lessEq(this, value)
    }

    class EndDateField: Field.DateTimeField(AppContentScreenDetail::endDate) {
        fun isNull() = Predicate.isNull(this)
        fun greaterEq(value: LocalDateTime?) =
            if (value == null) Predicate.isNull(this)
            else Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime?) =
            if (value == null) Predicate.isNull(this)
            else Predicate.lessEq(this, value)
    }
    class CreatedAtField: Field.DateTimeField(AppContentScreenDetail::createdAt)

    class PositionField: Field.IntegerField(AppContentScreenDetail::position)

    class HealthFormAnswerSourceField: Field.JsonbField(AppContentScreenDetail::healthFormSource) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun eq(value: HealthFormAnswerSource) = Predicate.jsonSearch(this, "{\"id\":\"${value.id}\", \"type\":\"${value.type.name}\"}")
    }

    class FieldOptions {
        val id = Id()
        val personId = PersonIdField()
        val screenType = ScreenTypeField()
        val status = StatusField()
        val sectionType = SectionTypeField()
        val sectionContent = SectionContentField()
        val startDate = StartDateField()
        val endDate = EndDateField()
        val sectionContext = SectionContextField()
        val healthFormAnswerSourceField = HealthFormAnswerSourceField()
    }

    class OrderingOptions{
        val createdAt = CreatedAtField()
        val position = PositionField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<AppContentScreenDetail>, Throwable>
    override suspend fun add(model: AppContentScreenDetail): Result<AppContentScreenDetail, Throwable>
    override suspend fun addList(models: List<AppContentScreenDetail>): Result<List<AppContentScreenDetail>, Throwable>
    override suspend fun update(model: AppContentScreenDetail): Result<AppContentScreenDetail, Throwable>
    override suspend fun updateList(models: List<AppContentScreenDetail>, returnOnFailure: Boolean): Result<List<AppContentScreenDetail>, Throwable>
    override suspend fun get(id: UUID): Result<AppContentScreenDetail, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>


}

