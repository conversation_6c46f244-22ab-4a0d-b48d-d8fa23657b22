package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.HLAdherence.AcceptedRecommendation.PENDING
import br.com.alice.data.layer.models.HLAdherence.AdherenceReferenceModel
import br.com.alice.data.layer.models.HLAdherence.AdherenceResultType
import java.time.LocalDateTime
import java.util.UUID

data class HLAdherence(
    val taskId: UUID,
    val validationHistory: List<AdherenceValidation> = emptyList(),
    val acceptedRecommendation: AcceptedRecommendation = PENDING,
    val comment: String? = null,
    val status: AdherenceStatus,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val personId: PersonId,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0
): Model, PersonReference, HealthInformation {

    enum class AdherenceStatus{
        PENDING,
        VALIDATED,
        CANCELED
    }

    enum class AcceptedRecommendation {
        PENDING,
        ACCEPTED,
        REFUSED,
        WITHOUT_RECOMMENDATION
    }

    enum class AdherenceReferenceModel {
        PROTOCOL,
        PROGRAM
    }

    enum class AdherenceResultType {
        ADHERENT,
        NON_ADHERENT,
        WITHOUT_REFERENCE,
        NOT_ALLOWED
    }
}

data class AdherenceValidation(
    val staffId: UUID,
    val resultType: AdherenceResultType,
    val healthConditionId: UUID? = null,
    val validationModel: AdherenceRecommendedAction? = null,
): JsonSerializable

data class AdherenceRecommendedAction(
    val id: UUID,
    val model: AdherenceReferenceModel,
    val healthLogicId: UUID? = null
) : JsonSerializable

