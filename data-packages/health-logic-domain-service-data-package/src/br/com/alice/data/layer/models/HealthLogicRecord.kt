package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.HealthLogicStatus.ACTIVE
import br.com.alice.data.layer.models.HealthLogicStatus.FLEXIBILIZATION
import br.com.alice.data.layer.models.HealthLogicStatus.RENOVATION
import java.time.LocalDateTime
import java.util.UUID

data class HealthLogicRecord(
    override val personId: PersonId,
    val healthLogicNodeId: UUID,
    val currentNodeId: UUID,
    val addedByStaffId: UUID? = null,
    val comment: String? = null,
    val status: HealthLogicStatus = ACTIVE,
    val addedAt: LocalDateTime = LocalDateTime.now(),
    val startedAt: LocalDateTime = LocalDateTime.now(),
    val referencedLink: ReferencedLink? = null,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
) : Model, PersonReference, HealthInformation {
    fun getNodeIds() = listOf(healthLogicNodeId, currentNodeId)
    fun hasValidStatus() = listOf(ACTIVE, FLEXIBILIZATION, RENOVATION).contains(status)

    data class ReferencedLink(
        val id: UUID,
        val model: ReferenceModel
    ) : JsonSerializable

    enum class ReferenceModel {
        SCRIPT_ACTION,
        SCRIPT_RELATIONSHIP
    }
}

enum class HealthLogicStatus(val description: String) {
    ACTIVE("Ativo"),
    CANCELLED("Cancelado"),
    DISCHARGED("Alta"),
    FLEXIBILIZATION("Flexibilização"),
    RENOVATION("Renovação"),
    WITHDRAWAL("Desistência pelo membro");
}
