package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class OutcomeRequestScheduling(
    override val personId: PersonId,
    val status : SchedulingStatus,
    val scheduledFor: LocalDateTime,
    val processedAt: LocalDateTime? = null,
    val referencedLink: ReferencedLink? = null,
    val caseRecordIds: Set<UUID>, //caseRecord.id
    val healthDemandMonitoringId: UUID,

    val version: Int = 0,
    override val id: UUID = RangeUUID.generate()
) : Model, PersonReference, JsonSerializable {

    enum class SchedulingStatus {
        PENDING,
        PROCESSING,
        PROCESSED,
        FAILED,
        CANCELLED
    }

    data class ReferencedLink(
        val id: UUID?,
        val model: ReferenceLinkModel
    ) : JsonSerializable

    enum class ReferenceLinkModel {
        WANDA,
        QUESTIONNAIRE
    }
}
