package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.models.Sex
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.CaseSeverity.INACTIVE
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit.MONTHS
import java.util.UUID

data class HDataOverview(
    // Map<CaseRecord.caseId, HDemand>
    val healthDemands: Map<UUID, HDemand> = emptyMap(),

    // Map<OutcomeConf.id, ClinicalOutcome>
    val outcomeRecords: Map<UUID, ClinicalOutcome> = emptyMap(),

    // All monthYearBirths will be a date at the first day of the month
    val monthYearBirth: LocalDate? = null,

    val risk: RiskDescription? = null,
    val sex: Sex? = null,

    val version: Int = 0,
    override val personId: PersonId,
    override val id: UUID = RangeUUID.generate(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model, PersonReference, HealthInformation {
    fun ageInMonths() = monthYearBirth?.let { MONTHS.between(monthYearBirth, LocalDate.now()) }

    abstract class HDataOverviewDetail

    data class ClinicalOutcome(
        val id: UUID,
        val addedAt: LocalDateTime,
        val outcomeConfId: UUID,
        val outcome: BigDecimal,
        val overviewUpdatedAt: LocalDateTime = LocalDateTime.now(),
        val caseRecordId: UUID? = null,
    ): HDataOverviewDetail(), JsonSerializable

    data class HDemand(
        val id: UUID, // caseRecord.id

        val addedAt: LocalDateTime,
        val severity: CaseSeverity,
        val status: CaseStatus,
        val healthConditionId: UUID,
        val seriousness: CaseSeriousness? = null,
        val overviewUpdatedAt: LocalDateTime = LocalDateTime.now()
    ): HDataOverviewDetail(), JsonSerializable {
        fun isActive() = listOf(CaseStatus.ACTIVE, CaseStatus.CONTEMPLATING).contains(this.status) && this.severity != INACTIVE
    }
}
