package br.com.alice.data.layer.models

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class PersonPreferences(
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val firstPaymentMethod: PaymentMethod = PaymentMethod.BOLETO
): PersonReference
