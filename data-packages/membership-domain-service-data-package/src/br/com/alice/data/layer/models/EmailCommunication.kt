package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class EmailCommunication(
    val id: UUID = RangeUUID.generate(),
    val sender: String,
    val recipient: String,
    val template: String,
    val idempotencyKey: String,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
)
