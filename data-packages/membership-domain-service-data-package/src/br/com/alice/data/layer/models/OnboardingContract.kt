package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class OnboardingContract(
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val documentUrl: String,
    val ansOrientationLetterSignature: ContractSignature? = null,
    val healthDeclarationSignature: ContractSignature? = null,
    val contractSignature: ContractSignature? = null,
    val sentAt: LocalDateTime? = null,
    val signatureSentAt: LocalDateTime? = null,
    val selectedProduct: MemberProduct,
    val productPriceListingId: UUID? = null,
    val certificateUrl: String? = null,
    val version: Int = 0,
    val archived: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : PersonReference {

    val productId get() = selectedProduct.id

    val alreadySent get() = signatureSentAt != null

    val alreadySigned get() = ansOrientationLetterSignature != null
            && healthDeclarationSignature != null
            && contractSignature != null

    fun isPartSigned(contractPart: ContractPart) = when (contractPart) {
        ContractPart.ANS_ORIENTATION_LETTER -> ansOrientationLetterSignature != null
        ContractPart.HEALTH_DECLARATION -> healthDeclarationSignature != null
        ContractPart.CONTRACT -> contractSignature != null
    }

    fun sign(contractPart: ContractPart, signature: ContractSignature) = when (contractPart) {
        ContractPart.ANS_ORIENTATION_LETTER -> copy(ansOrientationLetterSignature = signature)
        ContractPart.HEALTH_DECLARATION -> copy(healthDeclarationSignature = signature)
        ContractPart.CONTRACT -> copy(contractSignature = signature)
    }

    fun sendSignature() = copy(signatureSentAt = LocalDateTime.now())
    fun archive() = copy(archived = true)

}

data class ContractSignature(
    val id: UUID = RangeUUID.generate(),
    val fullName: String,
    val nationalId: String,
    val ipAddress: String?,
    val signedAt: String,
    val idToken: String,
    val productContract: ProductContract
) : JsonSerializable

data class ProductContract(
    val name: String,
    val ansNumber: String? = null
) : JsonSerializable

enum class ContractPart(val startPage: Int, val endPage: Int? = null) {
    ANS_ORIENTATION_LETTER(1, 4),
    HEALTH_DECLARATION(3, 6),
    CONTRACT(5);

    companion object {
        fun fromString(part: String) = valueOf(part.uppercase())
    }
}
