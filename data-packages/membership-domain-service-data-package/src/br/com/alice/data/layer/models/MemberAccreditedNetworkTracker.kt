package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class MemberAccreditedNetworkTracker(
    val specialities: List<SpecialityAccreditedNetworkTracker>,
    val subSpecialities: List<SpecialityAccreditedNetworkTracker>,
    val types: List<AccreditedNetworkTrackerFilterType>,
    val latitude: String,
    val longitude: String,
    val range: String,
    val results: List<AccreditedNetworkTrackerResult>,
    val quantityResults: Int = 0,
    val healthPlanTaskId: UUID? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
) : PersonReference

data class AccreditedNetworkTrackerResult(
    val id: UUID,
    val type: ConsolidatedAccreditedNetworkType,
) : JsonSerializable

data class SpecialityAccreditedNetworkTracker(
    val id: UUID,
    val name: String,
) : JsonSerializable

enum class AccreditedNetworkTrackerFilterType {
    SPECIALIST_AND_CLINICAL,
    HOSPITAL,
    HOSPITAL_CHILDREN,
    LABORATORY,
    EMERGENCY_UNITY,
    EMERGENCY_UNITY_CHILDREN,
    MATERNITY,
    VACCINE,
}
