package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class PersonBenefit(
    override val personId: PersonId,
    val campaignId: String,
    val partner: CampaignPartners,
    val optedInAt: LocalDateTime = LocalDateTime.now(),
    val optInStatus: PersonBenefitOptInStatus = PersonBenefitOptInStatus.ACCEPTED,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : PersonReference

enum class PersonBenefitOptInStatus(val description: String) {
    ACCEPTED("Aceito"),
    REVOKED("Revogado"),
    UNKNOWN("Desconhecido"),
}

enum class CampaignPartners {
    MEDIPRECO,
    RAIADROGASIL
}

