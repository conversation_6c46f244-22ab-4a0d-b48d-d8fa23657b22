package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class PromoCode(
    val code: String,
    val ownerPersonId: PersonId? = null,
    val type: PromoCodeType,
    val enabled: Boolean,
    val description: String? = null,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    val benefit
        get() = when (type) {
            PromoCodeType.MEMBER_GET_MEMBER -> Benefit.DISCOUNT
            PromoCodeType.PARTNERSHIP -> Benefit.DISCOUNT
        }

    val discountPercentage: BigDecimal
        get() = when (benefit) {
            Benefit.DISCOUNT -> BigDecimal(20)
            Benefit.GIFT -> BigDecimal.ZERO
        }

    val message
        get() = when (benefit) {
            Benefit.GIFT -> "Você terá a chance de escolher o presente que tem mais a sua cara"
            Benefit.DISCOUNT -> description ?: "+$discountPercentage% de desc. nos 3 primeiros meses"
        }

}

enum class PromoCodeType {
    MEMBER_GET_MEMBER,
    PARTNERSHIP
}

enum class Benefit {
    DISCOUNT,
    GIFT
}
