package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID


data class PersonTask(
    override val personId: PersonId,
    val title: String,
    val description: String?,
    val type: TaskType,
    val status: TaskStatus = TaskStatus.WAITING_REVIEW,
    val attachmentIds: List<UUID> = emptyList(),
    val version: Int = 0,
    val id: UUID = RangeUUID.generate(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
): PersonReference

enum class TaskType(val description: String) {
    TEST_REQUEST("pedido de exame");
}

enum class TaskStatus(val description: String) {
    WAITING_REVIEW("aguardando revisão"),
    ACTIVE("ativo"),
    CANCELED("cancelado"),
    DONE("concluído")
}
