package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class MemberLifeCycleEvents(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    var updatedBy: UpdatedBy? = null,

    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val memberId: UUID,
    val reason: MemberLifecycleReasonEvents,
    val observation: String? = null,
    val type: MemberLifecycleEventType,
    val actionAt: LocalDate,
)

enum class MemberLifecycleReasonEvents(
    val externalCode: String,
    val description: String,
    val type: MemberLifecycleEventType
) {
    IMPROPER_SHUTDOWN("009", "Desligamento Indevido", MemberLifecycleEventType.REACTIVATION),
    COURT_ORDER("140", "Liminar", MemberLifecycleEventType.REACTIVATION),
    CANCELLATION_RECONSIDERED("141", "Desconsideração do Cancelamento", MemberLifecycleEventType.REACTIVATION),
    REACTIVATION_OPERATIONAL_FAILURE("142", "Reativação por Falha Operacional", MemberLifecycleEventType.REACTIVATION),
    REACTIVATION_SUSPENSION("143", "Reativação por Suspensão", MemberLifecycleEventType.REACTIVATION),
    REACTIVATION_NON_PAYMENT("144", "Reativação por Inadimplência", MemberLifecycleEventType.REACTIVATION),
    REACTIVATION_FIRST_INSTALLMENT_PAYMENT(
        "146",
        "Reativação por Pagamento da Primeira Parcela",
        MemberLifecycleEventType.REACTIVATION
    ),
    UNBLOCK_ADDITION("148", "Desbloqueio na Inclusão", MemberLifecycleEventType.REACTIVATION),
    UNBLOCK_SUSPENSION_LICENSE("150", "Desbloqueio por Suspensão de Licença", MemberLifecycleEventType.REACTIVATION),
    WITHDRAWAL("158", "Desistência", MemberLifecycleEventType.REACTIVATION),
}

enum class MemberLifecycleEventType {
    CANCEL, REACTIVATION
}
