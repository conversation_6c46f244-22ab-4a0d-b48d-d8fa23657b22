package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import java.time.LocalDateTime
import java.util.UUID


data class PersonHealthGoal(
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val healthGoalIds: List<UUID>,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0
) : PersonReference, HealthInformation
