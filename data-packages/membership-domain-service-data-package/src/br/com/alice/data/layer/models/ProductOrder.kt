package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class ProductOrder(
    override val personId: PersonId,
    val price: BigDecimal,
    val archived: Boolean = false,
    val selectedProduct: MemberProduct,
    val productPriceListingId: UUID? = null,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : PersonReference {

    val productId get() = selectedProduct.id

    // TODO: Transformar em propriedades e jogar pra tabela
    private var promoCode: PromoCode? = null

    fun setPromoCode(promoCode: PromoCode?): ProductOrder {
        this.promoCode = promoCode
        return this
    }

    fun getPromoCode() = promoCode
    fun getPrice(age: Int) = selectedProduct.getPrice(age)
    fun getPriceWithDiscount(age: Int, discountPercentage: Int) =
        selectedProduct.getPriceWithDiscount(age, discountPercentage)

    fun archive() = copy(archived = true)
}

fun ProductOrder.withPriceListing(priceListing: PriceListing?): ProductOrder =
    copy(selectedProduct = this.selectedProduct.copy(priceListing = priceListing))
