package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class PersonLogin(
    val nationalId: String,
    val email: String? = null,
    override val personId: PersonId,
    val accessCode: String,
    val saltAccessCode: String,
    val expirationDate: LocalDateTime,
    val expiredAt: LocalDateTime? = null,
    val expireReason: ExpireReason? = null,
    val firstAccess: Boolean = false,
    val attempts: Int? = null,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now()
) : PersonReference {

    fun expire(reason: ExpireReason) = copy(
        expiredAt = LocalDateTime.now(),
        expireReason = reason
    )

    fun archive() = copy(expireReason = ExpireReason.ARCHIVED)
    fun incrementAttempts() = copy(attempts = (attempts ?: 0) + 1)
    fun hasReachedMaxAttempts(maxAttempts: Int) = (attempts ?: 0) > maxAttempts

    val alreadyExpired = expiredAt != null || expirationDate.isBefore(LocalDateTime.now())
}

enum class ExpireReason {
    USED,
    NEW_ACCESS,
    ARCHIVED,
    MAX_LOGIN_ATTEMPTS
}
