package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.models.Gender
import java.util.UUID

data class MemberRegistration(
    val personId: String,
    val name: String,
    val email: String? = null,
    val birthDate: String,
    val nickName: String? = null,
    val gender: Gender? = null,
    val mothersName: String,
    val pisPasep: Long?,
    val cpf: Long,
    val rg: String? = null,
    val rgIssuingAuthority: String? = null,
    val postalCode: String,
    val address: String? = null,
    val cns: String? = null,
    val phoneNumber: String,
    val registrationDate: String,
    val saleDate: String? = null,
    val planId: Long,
    val pricingGroupId: Long,
    val gracePeriodId: Long? = null,
    val educationLevelId: Long? = null,
    val nationalityId: Long? = null,
    val cidCodes: List<String>? = null,
    val addressNumber: String? = null,
    val addressComplement: String? = null,
    val addressNeighbourhood: String? = null,
    val city: String? = null,
    val state: String? = null,
    val id: UUID = RangeUUID.generate()
)
