package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.storage.AliceFile
import java.time.LocalDateTime
import java.util.UUID

data class PersonDocumentsUpload(
    override val personId: PersonId,
    val category: PersonDocumentsUploadCategory,
    val description: String,
    val eventDate: LocalDateTime = LocalDateTime.now(),
    val attachments: List<AliceFileAttachment> = emptyList(),
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : PersonReference

enum class PersonDocumentsUploadCategory(val description: String) {
    VACCINE("Vacina"),
    TEST_RESULT("Resultado de exame"),
}

data class AliceFileAttachment(
    val originalFile: <PERSON><PERSON>ile,
    val thumbnail: AliceFile?,
)
