package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class UpdatedPersonContactInfoTemp (
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val type: Type,
    val newValue: String,
    val used: Boolean,
    val accepted: Boolean? = null,
    val token: String,
    val validUntil: LocalDateTime,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
): PersonReference {
    enum class Type {
        EMAIL,
        PHONE
    }
}
