package br.com.alice.data.layer.models

import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class PersonOnboardingTest {

    @Test
    fun `#nextPhases - given an OnboardingPhase, should return next phases as expected`() {
        val phase = OnboardingPhase.REGISTRATION
        val nextPhases = phase.nextPhases().toList()

        val expectedNextPhases = listOf(
            OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT,
            OnboardingPhase.WAITING_FOR_REVIEW,
            OnboardingPhase.CONTRACT,
            OnboardingPhase.PAYMENT,
            OnboardingPhase.FINISHED
        )

        assertThat(nextPhases).isEqualTo(expectedNextPhases)
    }

    @Test
    fun `#nextPhase - given SHOPPING, should return expected next phase`() {
        val nextPhase = OnboardingPhase.SHOPPING.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.PORTABILITY)
    }

    @Test
    fun `#nextPhase - given LEGAL_GUARDIAN_RESPONSIBILITY_TERM_SIGNING, should return expected next phase`() {
        val nextPhase = OnboardingPhase.LEGAL_GUARDIAN_RESPONSIBILITY_TERM_SIGNING.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.CONTRACT)
    }

    @Test
    fun `#nextPhase - given CHILD_VIDEOS_REQUEST, should return expected next phase`() {
        val nextPhase = OnboardingPhase.CHILD_VIDEOS_REQUEST.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT)
    }

    @Test
    fun `#nextPhase - given LEGAL_GUARDIAN_REGISTER, should return expected next phase`() {
        val nextPhase = OnboardingPhase.LEGAL_GUARDIAN_REGISTER.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.PORTABILITY)
    }

    @Test
    fun `#nextPhase - given PORTABILITY, should return expected next phase`() {
        val nextPhase = OnboardingPhase.PORTABILITY.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.PORTABILITY_REVIEW)
    }

    @Test
    fun `#nextPhase - given PORTABILITY_REVIEW, should return expected next phase`() {
        val nextPhase = OnboardingPhase.PORTABILITY_REVIEW.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.REGISTRATION)
    }

    @Test
    fun `#nextPhase - given REGISTRATION, should return expected next phase`() {
        val nextPhase = OnboardingPhase.REGISTRATION.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT)
    }

    @Test
    fun `#nextPhase - given HEALTH_DECLARATION_APPOINTMENT, should return expected next phase`() {
        val nextPhase = OnboardingPhase.HEALTH_DECLARATION_APPOINTMENT.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.WAITING_FOR_REVIEW)
    }

    @Test
    fun `#nextPhase - given WAITING_FOR_REVIEW, should return expected next phase`() {
        val nextPhase = OnboardingPhase.WAITING_FOR_REVIEW.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.CONTRACT)
    }

    @Test
    fun `#nextPhase - given CONTRACT, should return expected next phase`() {
        val nextPhase = OnboardingPhase.CONTRACT.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.PAYMENT)
    }

    @Test
    fun `#nextPhase - given PAYMENT, should return expected next phase`() {
        val nextPhase = OnboardingPhase.PAYMENT.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.FINISHED)
    }

    @Test
    fun `#nextPhase - given FINISHED, should return expected next phase`() {
        val nextPhase = OnboardingPhase.FINISHED.nextPhase()
        assertThat(nextPhase).isEqualTo(OnboardingPhase.FINISHED)
    }
}
