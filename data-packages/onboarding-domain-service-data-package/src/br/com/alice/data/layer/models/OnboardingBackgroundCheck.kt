package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class OnboardingBackgroundCheck(
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val reportStatus: ReportStatus = ReportStatus.PENDING,
    val report: BackgroundCheckReport? = null,
    val notes: String? = null,
    val checklist: List<ChecklistItem>? = null,
    val rawData: String? = null,
    val finishedAt: LocalDateTime? = null,
    val externalReportId: String? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now()
) : PersonReference {
    val isFinished get() = finishedAt != null
    val alreadyProcessed get() = reportStatus == ReportStatus.PROCESSED
}

enum class ReportStatus {
    PENDING, PROCESSED, INVALID
}

data class BackgroundCheckReport(
    val fullName: String,
    val mothersName: String,
    val dateOfBirth: LocalDate? = null,
    val income: String,
    val legalProceedings: List<LegalProceeding>,
    val financialRestrictions: List<FinancialRestriction>? = null,
    val claims: List<Claim>? = null,
    val pendingIssues: List<PendingIssue>? = null,
) : JsonSerializable

data class LegalProceeding(
    val type: String? = null,
    val link: String?
) : JsonSerializable

data class PendingIssue(
    val name: String,
    val cnpj: String,
    val amount: String,
    val createdAt: String,
    val eventType: String
) : JsonSerializable

data class Claim(
    val name: String,
    val amount: String,
    val createdAt: String,
    val annotationDate: String,
) : JsonSerializable

data class FinancialRestriction(
    val name: String,
    val amount: String,
    val createdAt: String,
) : JsonSerializable

data class ChecklistItem(
    val question: String,
    val answer: Boolean = false
) : JsonSerializable
