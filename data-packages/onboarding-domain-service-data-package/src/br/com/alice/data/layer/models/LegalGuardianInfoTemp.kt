package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class LegalGuardianInfoTemp(
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val firstName: String,
    val lastName: String,
    val socialFirstName: String? = null,
    val socialLastName: String? = null,
    val degreeOfKinship: DegreeOfKinship,
    val identityDocument: String,
    val identityDocumentIssuingBody: String,
    val nationalId: String,
    val email: String,
    val archived: Boolean = false,
    val address: Address,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
): PersonReference {
    fun getFullName(): String {
        return if (!socialFirstName.isNullOrBlank()) {
            "$socialFirstName $socialLastName"
        } else {
            "$firstName $lastName"
        }
    }
}
