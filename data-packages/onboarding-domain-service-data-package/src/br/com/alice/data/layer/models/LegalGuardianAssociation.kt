package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class LegalGuardianAssociation(
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val guardianId: PersonId,
    val degreeOfKinship: DegreeOfKinship,
    val statusHistory: List<LegalGuardianAssociationStatus>? = emptyList(),
    val status: LegalGuardianAssociationStatusType,
    val isSigned: Boolean? = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
) : PersonReference {
    val isValidated = this.status == LegalGuardianAssociationStatusType.VALID
    val isNotValidated = !isValidated
}

data class LegalGuardianAssociationStatus(
    val legalGuardianAssociationStatusType: LegalGuardianAssociationStatusType,
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

enum class DegreeOfKinship {
    MOTHER,
    FATHER,
    GUARDIAN,
}

enum class LegalGuardianAssociationStatusType {
    PENDING,
    INVALID,
    VALID,
    ARCHIVED,
    EXPIRED,
}

fun LegalGuardianAssociation.changeStatus(
    newStatus: LegalGuardianAssociationStatusType, updatedAt: LocalDateTime
) = copy(
    status = newStatus,
    statusHistory = statusHistory?.plus(listOf(LegalGuardianAssociationStatus(newStatus, updatedAt))),
    updatedAt = updatedAt
)
fun LegalGuardianAssociation.signResponsibilityTerm() = copy(
    isSigned = true,
    updatedAt = LocalDateTime.now()
)
