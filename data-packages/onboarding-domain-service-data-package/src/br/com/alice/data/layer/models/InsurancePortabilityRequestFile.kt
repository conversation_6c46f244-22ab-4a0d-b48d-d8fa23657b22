package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.storage.AliceFile
import java.time.LocalDateTime
import java.util.UUID

data class InsurancePortabilityRequestFile(
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val insurancePortabilityRequestId: UUID,
    val file: AliceFile,
    val type: InsurancePortabilityRequestFileType,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : PersonReference


data class InsurancePortabilityMissingDocumentFile(
    val type: InsurancePortabilityRequestFileType,
    val month: Int? = null
)

enum class InsurancePortabilityRequestFileType {
    LETTER,
    PAYMENT_RECEIPT
}
