package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.extensions.replace
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.APPROVED
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.DECLINED
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.PENDING
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class InsurancePortabilityRequest(
    val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val healthInsuranceId: UUID? = null,
    val type: InsurancePortabilityRequestType? = InsurancePortabilityRequestType.NORMAL,
    val step: InsurancePortabilityRequestStep? = InsurancePortabilityRequestStep.HAS_HEALTH_INSURANCE,
    val declinedReasons: List<InsurancePortabilityRequestDeclineReason>? = emptyList(),
    val secondPortabilityRequirement: Boolean? = null,
    val currentTermPeriod: InsurancePortabilityRequestCurrentTermPeriod? = null,
    val minimumTermRequirement: Boolean? = null,
    val minimumTermRequirementEntryDate: LocalDate? = null,
    val priceCompatibilityRequirement: Boolean? = null,
    val priceCompatibilityRequirementPrice: BigDecimal? = null,
    val paymentFulfillmentRequirement: Boolean? = null,
    val hasCpt: Boolean? = null,
    val hasFulfilledCpt: Boolean? = null,
    val ansProtocolCode: String? = null,
    val activePlanRequirement: Boolean? = null,
    val hospitalCompatibility: Boolean? = null,
    val hospitalCompatibilityAnsCode: String? = null,
    val healthInsuranceCode: String? = null,
    val suggestedAction: InsurancePortabilitySuggestedAction? = null,
    val suggestedDeclineReasons: List<InsurancePortabilityRequestDeclineReason>? = emptyList(),
    val approvedPackage: InsurancePortabilityRequestApprovedPackage? = null,
    val answers: List<InsurancePortabilityRequestAnswer> = emptyList(),
    val answersV2: List<InsurancePortabilityRequestAnswerV2> = emptyList(),
    val status: InsurancePortabilityRequestStatus = InsurancePortabilityRequestStatus.CREATED,
    val missingDocuments: List<InsurancePortabilityMissingDocumentFile>? = emptyList(),
    val declineReason: InsurancePortabilityRequestDeclineReason? = null,
    val notes: String? = null,
    val productId: UUID? = null,
    val adhesionContract: Boolean = false,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val archived: Boolean = false,
    val pendingAt: LocalDateTime? = null,
    val finishedAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : PersonReference {

    val approved get() = status == APPROVED

    val declined get() = status == DECLINED

    val pending get() = status == PENDING

    val isFinished get() = approved || declined

    @Deprecated("Version 3 requires approvedPackage, healthInsuranceCode and hospitalCompatibilityAnsCode")
    fun approve(notes: String? = null) = copy(status = APPROVED, notes = notes)

    fun approve(
        approvedPackage: InsurancePortabilityRequestApprovedPackage,
        healthInsuranceCode: String,
        hospitalCompatibilityAnsCode: String
    ) = copy(
        status = APPROVED,
        approvedPackage = approvedPackage,
        healthInsuranceCode = healthInsuranceCode,
        hospitalCompatibilityAnsCode = hospitalCompatibilityAnsCode,
        finishedAt = LocalDateTime.now(),
    )

    fun submitToReview(notes: String? = null) = copy(
        status = PENDING,
        notes = notes,
        pendingAt = LocalDateTime.now()
    )

    @Deprecated("Version 3 uses a list of reasons")
    fun decline(reason: InsurancePortabilityRequestDeclineReason, notes: String? = null) = copy(
        status = DECLINED,
        declineReason = reason,
        notes = notes
    )

    fun decline(
        reasons: List<InsurancePortabilityRequestDeclineReason>,
        notes: String? = null,
        approvedPackage: InsurancePortabilityRequestApprovedPackage? = null
    ) = copy(
        status = DECLINED,
        declinedReasons = reasons,
        approvedPackage = approvedPackage,
        notes = notes,
        archived = true,
        finishedAt = LocalDateTime.now(),
    )
    
    fun cancel() = copy(
        status = DECLINED,
        declinedReasons = listOf(InsurancePortabilityRequestDeclineReason.DROPPED),
        archived = true,
        finishedAt = LocalDateTime.now(),
    )

    fun requestDocuments(
        missingDocuments: List<InsurancePortabilityMissingDocumentFile>,
        notes: String? = null,
    ) = copy(
        status = DECLINED,
        declinedReasons = listOf(InsurancePortabilityRequestDeclineReason.MISSING_DOCUMENTS),
        missingDocuments = missingDocuments,
        notes = notes,
        archived = true,
        finishedAt = LocalDateTime.now(),
    )

    fun updateSuggestion(): InsurancePortabilityRequest {
        if (
            listOf(minimumTermRequirement, activePlanRequirement, priceCompatibilityRequirement, paymentFulfillmentRequirement, hasCpt)
                .any { it == null }
        ) return this

        val suggestedReasons = suggestDeclineReasons()
        val suggestedAction =
            if (suggestedReasons.isNotEmpty()) InsurancePortabilitySuggestedAction.DECLINE
            else if (!priceCompatibilityRequirement!!) InsurancePortabilitySuggestedAction.APPROVE_OTHER_PRODUCT
            else InsurancePortabilitySuggestedAction.APPROVE

        return copy(
            suggestedDeclineReasons = suggestedReasons,
            suggestedAction = suggestedAction
        )
    }

    private fun suggestDeclineReasons(): List<InsurancePortabilityRequestDeclineReason> {
        val reasons = mutableListOf<InsurancePortabilityRequestDeclineReason>()

        if (!minimumTermRequirement!!) reasons.add(InsurancePortabilityRequestDeclineReason.MIN_GRACE_PERIOD_NOT_ACHIEVED)
        if (!paymentFulfillmentRequirement!!) reasons.add(InsurancePortabilityRequestDeclineReason.NO_PAYMENT)
        if (!activePlanRequirement!!) reasons.add(InsurancePortabilityRequestDeclineReason.NO_HEALTH_INSURANCE)
        if (hasCpt!! && hasFulfilledCpt != true) reasons.add(InsurancePortabilityRequestDeclineReason.NO_CPT_FULFILLMENT)

        return reasons
    }

    fun answer(answer: InsurancePortabilityRequestAnswer): InsurancePortabilityRequest {
        val existingAnswer = getAnswer(answer.questionType)

        return if (existingAnswer != null)
            copy(status = InsurancePortabilityRequestStatus.IN_PROGRESS, answers = answers.replace(answer) { it.questionType == answer.questionType })
        else
            copy(status = InsurancePortabilityRequestStatus.IN_PROGRESS, answers = answers + answer)
    }

    fun answer(answer: InsurancePortabilityRequestAnswerV2): InsurancePortabilityRequest {
        val existingAnswer = getAnswer(answer.questionType)

        return if (existingAnswer != null)
            copy(status = InsurancePortabilityRequestStatus.IN_PROGRESS, answersV2 = answersV2.replace(answer) { it.questionType == answer.questionType })
        else
            copy(status = InsurancePortabilityRequestStatus.IN_PROGRESS, answersV2 = answersV2 + answer)
    }

    fun nextQuestion(): InsurancePortabilityRequestQuestionType? =
        if (answers.isEmpty()) {
            InsurancePortabilityRequestQuestionType.firstQuestion()
        } else {
            answers
                .maxByOrNull { it.questionType.order }
                ?.questionType
                ?.nextQuestion()
        }

    fun nextQuestionV2(): InsurancePortabilityRequestQuestionTypeV2? =
        if (answersV2.isEmpty()) {
            InsurancePortabilityRequestQuestionTypeV2.firstQuestion()
        } else {
            answersV2
                .maxByOrNull { it.questionType.order }
                ?.questionType
                ?.nextQuestion()
        }

    fun getAnswer(questionType: InsurancePortabilityRequestQuestionType) = answers
        .filter { it.questionType == questionType }
        .map(InsurancePortabilityRequestAnswer::answer)
        .firstOrNull()

    fun getAnswer(questionType: InsurancePortabilityRequestQuestionTypeV2) = answersV2
        .filter { it.questionType == questionType }
        .map(InsurancePortabilityRequestAnswerV2::answer)
        .firstOrNull()

    fun archive() = copy(archived = true)

    fun changeStep(step: InsurancePortabilityRequestStep) = copy(step = step)
}

data class InsurancePortabilityRequestAnswer(
    val answer: String,
    val questionType: InsurancePortabilityRequestQuestionType
)

enum class InsurancePortabilityRequestQuestionType(val order: Int) {
    MIN_GRACE_PERIOD(0),
    CURRENT_HEALTH_INSURANCE(1),
    CURRENT_HEALTH_INSURANCE_HOSPITALS(2);

    companion object {
        fun firstQuestion() = values().minByOrNull { it.order }
    }

    fun nextQuestion() = when(this) {
        MIN_GRACE_PERIOD -> CURRENT_HEALTH_INSURANCE
        CURRENT_HEALTH_INSURANCE -> CURRENT_HEALTH_INSURANCE_HOSPITALS
        CURRENT_HEALTH_INSURANCE_HOSPITALS -> null
    }
}

data class InsurancePortabilityRequestAnswerV2(
    val answer: String,
    val questionType: InsurancePortabilityRequestQuestionTypeV2
)

enum class InsurancePortabilityRequestQuestionTypeV2(val order: Int) {
    MIN_GRACE_PERIOD(0),
    CURRENT_HEALTH_INSURANCE(1),
    ACKNOWLEDGE_CHECK(2);

    companion object {
        fun firstQuestion() = values().minByOrNull { it.order }
    }

    fun nextQuestion() = when(this) {
        MIN_GRACE_PERIOD -> CURRENT_HEALTH_INSURANCE
        CURRENT_HEALTH_INSURANCE -> ACKNOWLEDGE_CHECK
        ACKNOWLEDGE_CHECK -> null
    }
}

enum class InsurancePortabilityRequestStatus {
    CREATED,
    IN_PROGRESS,
    PENDING,
    APPROVED,
    DECLINED,
    SKIPPED,
}

enum class InsurancePortabilitySuggestedAction {
    APPROVE,
    DECLINE,
    APPROVE_OTHER_PRODUCT,
}

enum class InsurancePortabilityRequestDeclineReason {
    MIN_GRACE_PERIOD_NOT_ACHIEVED,
    INCOMPATIBLE_HOSPITALS,
    DROPPED,
    NO_HEALTH_INSURANCE,
    INCOMPATIBLE_PRODUCTS,
    NO_CPT_FULFILLMENT,
    NO_PAYMENT,
    MISSING_DOCUMENTS,
    APPROVED_TO_OTHER_PRODUCT,
    OTHER,
}

enum class InsurancePortabilityRequestCurrentTermPeriod {
    MORE_THAN_ONE_YEAR,
    LESS_THAN_ONE_YEAR,
    NONE,
}

enum class InsurancePortabilityRequestMinGracePeriodAnswer(val description: String) {
    MORE_THAN_TWO_YEARS("Mais de 2 anos"),
    BETWEEN_ONE_AND_TWO_YEARS("Entre 1 e 2 anos"),
    LESS_THAN_ONE_YEAR("Até 1 ano"),
    NONE("Não tenho plano de saúde"),
}

enum class InsurancePortabilityRequestType {
    NORMAL,
    SPECIFIC,
}

enum class InsurancePortabilityRequestStep {
    HAS_HEALTH_INSURANCE,
    ELIGIBILITY,
    GUIDELINE,
    GUIDELINE_DECISION,
    TYPE,
    PRE_LETTER_EXPLANATION,
    LETTER_EXPLANATION,
    ADHESION_CONTRACT,
    DOCUMENT_UPLOAD_LETTER,
    DOCUMENT_UPLOAD_LETTER_PAYMENT_RECEIPT,
    ANALYSIS,
    FINISHED,
    EXITED,
}

enum class InsurancePortabilityRequestApprovedPackage(val description: String) {
    A1("BP Enfermaria"),
    B1("BP + HAOC V"),
    C1("BP + HAOC P"),
    D1("Einstein"),
}
