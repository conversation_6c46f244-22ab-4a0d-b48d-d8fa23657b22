package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class InsurancePortabilityHealthInsurance(
    val id: UUID = RangeUUID.generate(),
    val name: String,
    val portabilityLetterGuide: List<InsurancePortabilityLetterGuideStep> = emptyList(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val color: String,
)

data class InsurancePortabilityLetterGuideStep(
    val order: Int,
    val description: String,
): JsonSerializable
