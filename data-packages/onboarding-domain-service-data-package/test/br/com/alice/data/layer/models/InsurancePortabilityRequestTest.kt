package br.com.alice.data.layer.models

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.InsurancePortabilityRequestDeclineReason.INCOMPATIBLE_HOSPITALS
import br.com.alice.data.layer.models.InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE
import br.com.alice.data.layer.models.InsurancePortabilityRequestQuestionType.CURRENT_HEALTH_INSURANCE_HOSPITALS
import br.com.alice.data.layer.models.InsurancePortabilityRequestQuestionType.MIN_GRACE_PERIOD
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.APPROVED
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.DECLINED
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.IN_PROGRESS
import br.com.alice.data.layer.models.InsurancePortabilityRequestStatus.PENDING
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class InsurancePortabilityRequestTest {

    private val person = TestModelFactory.buildPerson()

    private val request = TestModelFactory.buildInsurancePortabilityRequest(person.id)

    @Test
    fun `#approve should change status to APPROVED`() {
        val updated = request.approve()
        assertThat(updated.status).isEqualTo(APPROVED)
    }

    @Test
    fun `#decline should change status to DECLINED with a decline reason`() {
        val updated = request.decline(reason = INCOMPATIBLE_HOSPITALS)
        assertThat(updated.status).isEqualTo(DECLINED)
        assertThat(updated.declineReason).isEqualTo(INCOMPATIBLE_HOSPITALS)
    }

    @Test
    fun `#updateSuggestion shouldn't change if any requirement is missing`() {
        val withRequirements = request.copy(
            secondPortabilityRequirement = true,
            activePlanRequirement = true,
            minimumTermRequirement = true,
            paymentFulfillmentRequirement = true,
            hasCpt = true,
            hasFulfilledCpt = true,
        )

        val updated = withRequirements.updateSuggestion()
        assertThat(updated.suggestedAction).isNull()
        assertThat(updated.suggestedDeclineReasons).isEqualTo(emptyList<InsurancePortabilityRequestDeclineReason>())
    }

    @Test
    fun `#updateSuggestion should approve when all requirements are true`() {
        val withRequirements = request.copy(
            secondPortabilityRequirement = true,
            activePlanRequirement = true,
            minimumTermRequirement = true,
            priceCompatibilityRequirement = true,
            paymentFulfillmentRequirement = true,
            hasCpt = true,
            hasFulfilledCpt = true,
        )

        val updated = withRequirements.updateSuggestion()
        assertThat(updated.suggestedAction).isEqualTo(InsurancePortabilitySuggestedAction.APPROVE)
        assertThat(updated.suggestedDeclineReasons).isEqualTo(emptyList<InsurancePortabilityRequestDeclineReason>())
    }

    @Test
    fun `#updateSuggestion should suggest decline when at least one requirement is false`() {
        val withRequirements = request.copy(
            secondPortabilityRequirement = true,
            activePlanRequirement = true,
            minimumTermRequirement = true,
            priceCompatibilityRequirement = true,
            paymentFulfillmentRequirement = false,
            hasCpt = true,
            hasFulfilledCpt = true,
        )

        val updated = withRequirements.updateSuggestion()
        assertThat(updated.suggestedAction).isEqualTo(InsurancePortabilitySuggestedAction.DECLINE)
        assertThat(updated.suggestedDeclineReasons).isEqualTo(listOf(InsurancePortabilityRequestDeclineReason.NO_PAYMENT))
    }

    @Test
    fun `#updateSuggestion should suggest decline when has cpt and not fulfilled`() {
        val withRequirements = request.copy(
            secondPortabilityRequirement = true,
            activePlanRequirement = true,
            minimumTermRequirement = true,
            priceCompatibilityRequirement = true,
            paymentFulfillmentRequirement = true,
            hasCpt = true,
            hasFulfilledCpt = false,
        )

        val updated = withRequirements.updateSuggestion()
        assertThat(updated.suggestedAction).isEqualTo(InsurancePortabilitySuggestedAction.DECLINE)
        assertThat(updated.suggestedDeclineReasons).isEqualTo(listOf(InsurancePortabilityRequestDeclineReason.NO_CPT_FULFILLMENT))
    }

    @Test
    fun `#updateSuggestion should suggest approve other product when the priceCompatibilityRequirement is the only false`() {
        val withRequirements = request.copy(
            secondPortabilityRequirement = true,
            activePlanRequirement = true,
            minimumTermRequirement = true,
            priceCompatibilityRequirement = false,
            paymentFulfillmentRequirement = true,
            hasCpt = true,
            hasFulfilledCpt = true,
        )

        val updated = withRequirements.updateSuggestion()
        assertThat(updated.suggestedAction).isEqualTo(InsurancePortabilitySuggestedAction.APPROVE_OTHER_PRODUCT)
        assertThat(updated.suggestedDeclineReasons).isEqualTo(emptyList<InsurancePortabilityRequestDeclineReason>())
    }

    @Test
    fun `#submitToReview should change status to PENDING`() {
        val updated = request.submitToReview()
        assertThat(updated.status).isEqualTo(PENDING)
    }

    @Test
    fun `#getAnswer should return an answer for a specific question`() {
        val expectedAnswer = "Sim"

        val requestWithAnswers = request.copy(
            answers = listOf(
                InsurancePortabilityRequestAnswer(
                    answer = expectedAnswer,
                    questionType = MIN_GRACE_PERIOD
                )
            )
        )

        val actualAnswer = requestWithAnswers.getAnswer(MIN_GRACE_PERIOD)
        assertThat(actualAnswer).isEqualTo(expectedAnswer)
    }

    @Test
    fun `#answer should add new answer when there is answers but with different types`() {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            answers = listOf(TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = CURRENT_HEALTH_INSURANCE))
        )

        val newAnswer = TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = MIN_GRACE_PERIOD)

        val expectedAnswer = portabilityRequest.copy(
            answers = portabilityRequest.answers + newAnswer,
            status = IN_PROGRESS,
        )

        val actualAnswer = portabilityRequest.answer(newAnswer)
        assertThat(actualAnswer).isEqualTo(expectedAnswer)
    }

    @Test
    fun `#answer should replace new answer when there is already an answer with the same type`() {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            answers = listOf(TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = CURRENT_HEALTH_INSURANCE))
        )

        val newAnswer = TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = CURRENT_HEALTH_INSURANCE)

        val expectedAnswer = portabilityRequest.copy(
            answers = portabilityRequest.answers,
            status = IN_PROGRESS,
        )

        val actualAnswer = portabilityRequest.answer(newAnswer)
        assertThat(actualAnswer).isEqualTo(expectedAnswer)
    }

    @Test
    fun `#answer should add new answer when answers is empty`() {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(answers = emptyList())
        val newAnswer = TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = CURRENT_HEALTH_INSURANCE)

        val expectedAnswer = portabilityRequest.copy(
            answers = listOf(newAnswer),
            status = IN_PROGRESS,
        )

        val actualAnswer = portabilityRequest.answer(newAnswer)
        assertThat(actualAnswer).isEqualTo(expectedAnswer)
    }

    @Test
    fun `#nextQuestion should return CURRENT_HEALTH_INSURANCE_HOSPITALS when has MIN_GRACE_PERIOD and CURRENT_HEALTH_INSURANCE answered`() {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            answers = listOf(
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = MIN_GRACE_PERIOD),
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = CURRENT_HEALTH_INSURANCE),
            )
        )

        val actual = portabilityRequest.nextQuestion()
        assertThat(actual).isEqualTo(CURRENT_HEALTH_INSURANCE_HOSPITALS)
    }

    @Test
    fun `#nextQuestion should return null when has MIN_GRACE_PERIOD, CURRENT_HEALTH_INSURANCE answered and CURRENT_HEALTH_INSURANCE_HOSPITALS`() {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest(
            answers = listOf(
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = MIN_GRACE_PERIOD),
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = CURRENT_HEALTH_INSURANCE),
                TestModelFactory.buildInsurancePortabilityRequestAnswer(questionType = CURRENT_HEALTH_INSURANCE_HOSPITALS),
            )
        )

        val actual = portabilityRequest.nextQuestion()
        assertThat(actual).isEqualTo(null)
    }

    @Test
    fun `#nextQuestion should return MIN_GRACE_PERIOD when has empty answers`() {
        val portabilityRequest = TestModelFactory.buildInsurancePortabilityRequest()
        val actual = portabilityRequest.nextQuestion()

        assertThat(actual).isEqualTo(MIN_GRACE_PERIOD)
    }
}
