package br.com.alice.data.layer.models

import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.Test


class CompanyProductConfigurationTest {
    companion object {
        @JvmStatic
        fun getSizeAndRange() = listOf(
            Arguments.of("1", CompanySizeConfiguration.ME_1),
            Arguments.of("2", CompanySizeConfiguration.ME_2),
            Arguments.of("2 a 5", CompanySizeConfiguration.ME_2_5),
            Arguments.of("3 a 5", CompanySizeConfiguration.ME_3_5),
            Arguments.of("6 a 29", CompanySizeConfiguration.P),
            Arguments.of("30 a 99", CompanySizeConfiguration.M)
        )

        @JvmStatic
        fun getCompanyProductType() = listOf(
            Arguments.of(CompanySizeConfiguration.MEI_1_5, CompanyContractType.OPTIONAL, CompanyProductType.MEI_1_5_OPTIONAL),
            Arguments.of(CompanySizeConfiguration.ME_1, CompanyContractType.OPTIONAL, CompanyProductType.MICRO_1_OPTIONAL),
            Arguments.of(CompanySizeConfiguration.ME_2, CompanyContractType.OPTIONAL, CompanyProductType.MICRO_2_OPTIONAL),
            Arguments.of(CompanySizeConfiguration.ME_2_5, CompanyContractType.OPTIONAL, CompanyProductType.MICRO_2_5_OPTIONAL),
            Arguments.of(CompanySizeConfiguration.ME_3_5, CompanyContractType.OPTIONAL, CompanyProductType.MICRO_3_5_OPTIONAL),
            Arguments.of(CompanySizeConfiguration.ME_3_5, CompanyContractType.MANDATORY, CompanyProductType.MICRO_3_5_MANDATORY),
            Arguments.of(CompanySizeConfiguration.P, CompanyContractType.OPTIONAL, CompanyProductType.SMALL_6_29_OPTIONAL),
            Arguments.of(CompanySizeConfiguration.P, CompanyContractType.MANDATORY, CompanyProductType.SMALL_6_29_MANDATORY),
            Arguments.of(CompanySizeConfiguration.M, CompanyContractType.OPTIONAL, CompanyProductType.MEDIUM_30_99_OPTIONAL),
            Arguments.of(CompanySizeConfiguration.M, CompanyContractType.MANDATORY, CompanyProductType.MEDIUM_30_99_MANDATORY)
        )
    }

    @ParameterizedTest
    @MethodSource("getSizeAndRange")
    fun `get the CompanySizeConfiguration type from range of beneficiaries`(range: String, expected: CompanySizeConfiguration) = runBlocking<Unit> {
        val result = CompanySizeConfiguration.getFromRangeOfBeneficiaries(range)
        assertThat(result).isEqualTo(expected)
    }

    @ParameterizedTest
    @MethodSource("getCompanyProductType")
    fun `get the CompanyProductType from CompanySizeConfiguration and CompanyContractType`(
        companySizeConfiguration: CompanySizeConfiguration,
        companyContractType: CompanyContractType,
        expected: CompanyProductType
    ) = runBlocking<Unit> {
        val result = CompanyProductType.getFromCompanyConfiguration(companySizeConfiguration, companyContractType)
        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#getFromContractTypeDescription from possible contract type descriptions as expected`() {
        val possibleContractTypeDescriptions =
            listOf("Compulsório", "Compulsória", "Compulsorio", "Compulsoria", "compulsorio", "compulsoria")
        val convertedContractsType =
            possibleContractTypeDescriptions.map { CompanyContractType.getFromContractTypeDescription(it) }

        assertThat(convertedContractsType.size).isEqualTo(possibleContractTypeDescriptions.size)
        assertThat(convertedContractsType).allMatch { it == CompanyContractType.MANDATORY }
    }

    @Test
    fun `#getFromContractTypeDescription from optional description`() {
        val contractTypeDescription = "Opcional"
        val convertedContractType = CompanyContractType.getFromContractTypeDescription(contractTypeDescription)

        assertThat(convertedContractType).isEqualTo(CompanyContractType.OPTIONAL)
    }
}
