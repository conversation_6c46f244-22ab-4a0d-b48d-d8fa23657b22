package br.com.alice.data.layer.models

import br.com.alice.common.core.extensions.classSimpleName
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import kotlin.test.Test

class FullRiskFlowTest {

    @Test
    fun `#previousPhase given READY_TO_ONBOARD phase should return READY_TO_ONBOARD`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        val previousPhase = FullRiskFlow.previousPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        assertThat(previousPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#stepsForPhase count the steps between READY_TO_ONBOARD and SEND_CPTS`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        val steps = FullRiskFlow.stepsForPhase(currentPhase, BeneficiaryOnboardingPhaseType.SEND_CPTS)

        assertThat(steps).isEqualTo(5)
    }

    @Test
    fun `#stepsForPhase count the steps between SEND_CPTS and READY_TO_ONBOARD `() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.SEND_CPTS
        val steps = FullRiskFlow.stepsForPhase(currentPhase, BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD)

        assertThat(steps).isEqualTo(-5)
    }

    @Test
    fun `#stepsForPhase count the steps between HEALTH_DECLARATION_APPOINTMENT_SCHEDULED and HEALTH_DECLARATION_APPOINTMENT `() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED
        val steps = FullRiskFlow.stepsForPhase(currentPhase, BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT)

        assertThat(steps).isEqualTo(-1)
    }

    @Test
    fun `#previousPhase given SEND_HEALTH_DECLARATION should throw PhaseOutOfOnboardingFlowException`() =
        runBlocking<Unit> {
            val currentPhase = BeneficiaryOnboardingPhaseType.SEND_HEALTH_DECLARATION

            assertThatThrownBy {
                PartialRiskFlow.previousPhase(currentPhase)
            }.isInstanceOf(PhaseOutOfOnboardingFlowException::class.java)
                .hasMessage("Phase is not part of flow. Unable to find previous phase - Flow: ${PartialRiskFlow.classSimpleName()} - Phase: $currentPhase")
        }

    @Test
    fun `#previousPhase given HEALTH_DECLARATION phase should return READY_TO_ONBOARD`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
        val previousPhase = FullRiskFlow.previousPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        assertThat(previousPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#previousPhase given HEALTH_DECLARATION_APPOINTMENT phase should return HEALTH_DECLARATION`() =
        runBlocking<Unit> {
            val currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT
            val previousPhase = FullRiskFlow.previousPhase(currentPhase)

            val expectedPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
            assertThat(previousPhase).isEqualTo(expectedPhase)
        }

    @Test
    fun `#previousPhase given HEALTH_DECLARATION_APPOINTMENT_SCHEDULED phase should return HEALTH_DECLARATION_APPOINTMENT`() =
        runBlocking<Unit> {
            val currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED
            val previousPhase = FullRiskFlow.previousPhase(currentPhase)

            val expectedPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT
            assertThat(previousPhase).isEqualTo(expectedPhase)
        }

    @Test
    fun `#previousPhase given WAITING_CPTS_APPLICATION phase should return HEALTH_DECLARATION_APPOINTMENT_SCHEDULED`() =
        runBlocking<Unit> {
            val currentPhase = BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION
            val previousPhase = FullRiskFlow.previousPhase(currentPhase)

            val expectedPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION_APPOINTMENT_SCHEDULED
            assertThat(previousPhase).isEqualTo(expectedPhase)
        }

    @Test
    fun `#previousPhase given SEND_CPTS phase should return WAITING_CPTS_APPLICATION`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.SEND_CPTS
        val previousPhase = FullRiskFlow.previousPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.WAITING_CPTS_APPLICATION
        assertThat(previousPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#previousPhase given CPTS_CONFIRMATION phase should return SEND_CPTS`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION
        val previousPhase = FullRiskFlow.previousPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.SEND_CPTS
        assertThat(previousPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#previousPhase given SEND_REGISTRATION should throw PhaseOutOfOnboardingFlowException`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.SEND_REGISTRATION

        assertThatThrownBy {
            PartialRiskFlow.previousPhase(currentPhase)
        }.isInstanceOf(PhaseOutOfOnboardingFlowException::class.java)
            .hasMessage("Phase is not part of flow. Unable to find previous phase - Flow: ${PartialRiskFlow.classSimpleName()} - Phase: $currentPhase")
    }

    @Test
    fun `#previousPhase given WAITING_FOR_REVIEW phase should return REGISTRATION`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
        val previousPhase = FullRiskFlow.previousPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.REGISTRATION
        assertThat(previousPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#previousPhase given HEALTH_CARE_TEAM_SELECTION phase should throw PhaseOutOfOnboardingFlowException`() =
        runBlocking<Unit> {
            val currentPhase = BeneficiaryOnboardingPhaseType.HEALTH_CARE_TEAM_SELECTION

            assertThatThrownBy {
                PartialRiskFlow.previousPhase(currentPhase)
            }.isInstanceOf(PhaseOutOfOnboardingFlowException::class.java)
                .hasMessage("Phase is not part of flow. Unable to find previous phase - Flow: ${PartialRiskFlow.classSimpleName()} - Phase: $currentPhase")
        }

    @Test
    fun `#previousPhase given FINISHED phase should return WAITING_FOR_REVIEW`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.FINISHED
        val previousPhase = FullRiskFlow.previousPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
        assertThat(previousPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#previousPhase given REGISTRATION phase should return CONTRACT_SIGNATURE`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.REGISTRATION
        val previousPhase = PartialRiskFlow.previousPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE
        assertThat(previousPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#nextPhase given CPTS_CONFIRMATION phase should return CONTRACT_SIGNATURE`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.CPTS_CONFIRMATION
        val nextPhase = PartialRiskFlow.nextPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.CONTRACT_SIGNATURE
        assertThat(nextPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#nextPhase given READY_TO_ONBOARD phase should return HEALTH_DECLARATION`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
        val nextPhase = FullRiskFlow.nextPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.HEALTH_DECLARATION
        assertThat(nextPhase).isEqualTo(expectedPhase)
    }

    @Test
    fun `#nextPhase given WAITING_FOR_REVIEW phase should return FINISHED`() = runBlocking<Unit> {
        val currentPhase = BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW
        val nextPhase = PartialRiskFlow.nextPhase(currentPhase)

        val expectedPhase = BeneficiaryOnboardingPhaseType.FINISHED
        assertThat(nextPhase).isEqualTo(expectedPhase)
    }
}
