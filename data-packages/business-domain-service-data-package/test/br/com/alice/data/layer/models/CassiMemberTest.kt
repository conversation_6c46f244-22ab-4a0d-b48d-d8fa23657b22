package br.com.alice.data.layer.models

import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class CassiMemberTest {

    @Test
    fun `#isEmptyCardDetail should return true when the accountNumber is null`() {
        val member = TestModelFactory.buildCassiMember(accountNumber = null)

        val result = member.isEmptyCardDetail()
        assertThat(result).isTrue
    }

    @Test
    fun `#isEmptyCardDetail should return false when the accountNumber is not null`() {
        val member = TestModelFactory.buildCassiMember()

        val result = member.isEmptyCardDetail()
        assertThat(result).isFalse
    }
}
