package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class CompanyActivationFiles(
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val fileVaultId: UUID,
    val companyId: UUID,
    val owner: String? = null,
    val fileName: String,
    val type: CompanyActivationFileType
) : Model

enum class CompanyActivationFileType(val value: String) {
    NATIONAL_ID("RG, CPF ou CNH"),
    FGTS("Fundo de garantia"),
    WORK_REGISTER_BOOKLET("Carteira de trabalho"),
    MARRIAGE_OR_STABLE_UNION_CERTIFICATE_SPOUSE("Certidão de casamento ou união estável"),
    MARRIAGE_CERTIFICATE_STEPSON("Certidão de casamento"),
    SOCIAL_CONTRACT("Contrato social"),
    PORTABILITY_LETTER("Carta de portabilidade"),
    PROOF_OF_PLAN("Comprovante de plano"),
    PROOF_OF_PAYMENT("Comprovante de pagamento"),
    SOCIAL_FRAMEWORK("Quadro Social da Empresa"),
    PREVIOUS_PLAN_INVOICE_OR_BENEFICIARIES_LIST("Fatura do Plano Anterior ou Lista de Beneficiários do Plano Anterior"),
    NON_ADHERENCE_LETTER("Carta de Não Adesão"),
    PROOF_OF_ENROLLMENT_OR_RESIDENCE_ABROAD("Comprovante de Adesão em Outro Plano ou Comprovante de Residência no Exterior"),
    PARENTS_IDENTIFICATION_DOCUMENT_DEPENDENT_SAME_FILATION("Documento de Identificação dos Pais do Dependente onde Conste a Mesma Filiação do Titular"),
    PARENTS_IDENTIFICATION_DOCUMENT_DEPENDENT("Documento de Identificação dos Pais do Dependente"),
    MARRIAGE_OR_STABLE_UNION_CERTIFICATE_DEPENDENT("Certidão de Casamento ou União Estável do Dependente"),
    NATIONAL_ID_CHILD_OF_HOLDER("RG ou CNH do Filho/Filha do Titular"),
    REMAINING_ON_PREVIOUS_PLAN_LETTER("Carta de Permanência no Plano Anterior")
}
