package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.exceptions.BadRequestException
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

data class BeneficiaryOnboarding(
    override val id: UUID = RangeUUID.generate(),
    val beneficiaryId: UUID,
    val flowType: BeneficiaryOnboardingFlowType,
    val initialProductId: UUID,
    val acceptedDataProcessingTermsAt: LocalDateTime? = null,
    val version: Int = 0,
    val phases: List<BeneficiaryOnboardingPhase> = emptyList()
) : Model {

    val currentPhase: BeneficiaryOnboardingPhase?
        get() = phases.maxByOrNull { it.transactedAt }

    val allPhases: List<BeneficiaryOnboardingPhaseType>
        get() = OnboardingFlowFactory.get(flowType).allPhases()

    fun nextPhase(): BeneficiaryOnboardingPhase = currentPhase?.let {
        it.newPhase(it.getNextPhase())
    } ?: throw InvalidBeneficiaryOnboardingNextPhaseException()

    fun getPreviousPhasesFromInitial() = sequence {
        val onboardingFactory = OnboardingFlowFactory.get(flowType)
        currentPhase?.let {
            var nextPhase = BeneficiaryOnboardingPhaseType.READY_TO_ONBOARD
            while (nextPhase != it.phase) {
                yield(nextPhase)
                nextPhase = onboardingFactory.nextPhase(nextPhase)
            }
        } ?: throw BeneficiaryWithoutPhaseException()
    }

    private fun BeneficiaryOnboardingPhase.getNextPhase() = OnboardingFlowFactory.get(flowType).nextPhase(this.phase)
}

fun BeneficiaryOnboardingPhase.stepsForPhase(
    flowType: BeneficiaryOnboardingFlowType,
    target: BeneficiaryOnboardingPhaseType
) = OnboardingFlowFactory.get(flowType).stepsForPhase(phase, target)

fun BeneficiaryOnboarding.withPhases(phases: List<BeneficiaryOnboardingPhase>): BeneficiaryOnboarding =
    copy(phases = phases)

enum class BeneficiaryOnboardingFlowType(val description: String) {
    UNDEFINED("Não definido"),
    FULL_RISK_FLOW("DS + VC obrigatório"),
    PARTIAL_RISK_FLOW("Somente DS obrigatório"),
    NO_RISK_FLOW("Sem fluxo de risco"),
}

class InvalidBeneficiaryOnboardingNextPhaseException(
    message: String = "BeneficiaryOnboarding has no current phase",
    code: String = "invalid_next_phase",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class BeneficiaryWithoutPhaseException(
    message: String = "BeneficiaryOnboarding has no current phase",
    code: String = "invalid_current_phase",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class InvalidBeneficiaryOnboardingToPhaseException(
    message: String,
    code: String = "invalid_to_phase",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(maxSteps: Int, requestedPhase: BeneficiaryOnboardingPhaseType) : this(
        message = "BeneficiaryOnboarding next phases with max steps [$maxSteps] does not reach the requested phase [$requestedPhase]"
    )
}

class BeneficiaryAlreadyOnCorrectPhaseException(
    message: String,
    code: String = "already_on_correct_phase",
    cause: Throwable? = null
) : BadRequestException(message, code, cause) {
    constructor(nextPhase: BeneficiaryOnboardingPhaseType, requestedPhase: BeneficiaryOnboardingPhaseType) : this(
        message = "BeneficiaryOnboarding already on correct phase: [$nextPhase]. Requested phase: [$requestedPhase]"
    )
}
