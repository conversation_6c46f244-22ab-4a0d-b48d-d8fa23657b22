package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class B2bBatchInvoiceReport(
    override val id: UUID = RangeUUID.generate(),
    val billingAccountablePartyId: UUID,
    val memberId: UUID,
    val totalAmount: BigDecimal,
    val productPrice: BigDecimal? = null,
    val proRation: BigDecimal? = null,
    val copay: BigDecimal? = null,
    val productChange: BigDecimal? = null,
    val promoCode: BigDecimal? = null,
    val promoCodeResult: BigDecimal? = null,
    val sales: BigDecimal? = null,
    val salesResult: BigDecimal? = null,
    val generatedAt: LocalDateTime,
    val version: Int = 0,
) : Model
