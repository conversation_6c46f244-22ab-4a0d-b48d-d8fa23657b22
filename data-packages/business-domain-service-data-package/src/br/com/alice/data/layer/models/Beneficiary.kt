package br.com.alice.data.layer.models

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.extensions.atEndOfTheDay
import br.com.alice.common.core.extensions.isBeforeEq
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class Beneficiary(
    override val id: UUID = RangeUUID.generate(),
    override val parentBeneficiary: UUID? = null,
    override val personId: PersonId,
    val memberId: UUID,
    val companyId: UUID,
    override val type: BeneficiaryType,
    override val contractType: BeneficiaryContractType? = null,
    override val parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
    val activatedAt: LocalDateTime,
    val canceledAt: LocalDateTime? = null,
    override val hiredAt: LocalDateTime? = null,
    val parentBeneficiaryRelatedAt: LocalDateTime? = null,
    val canceledReason: BeneficiaryCancelationReason? = null,
    val canceledDescription: String? = null,
    override val cnpj: String? = null,
    val hasContributed: Boolean? = null,
    val archived: Boolean = false,
    override val version: Int = 0,
    val onboarding: BeneficiaryOnboarding? = null,
    val dependents: List<Beneficiary>? = null,
    val brand: Brand? = Brand.ALICE,
    val companySubContractId: UUID? = null,
    val memberStatus: MemberStatus? = null,
    val parentPerson: PersonId? = null,
    val gracePeriodType: GracePeriodType? = null,
    val gracePeriodTypeReason: GracePeriodTypeReason? = null,
    val gracePeriodBaseDate: LocalDate? = null,
    override var updatedBy: UpdatedBy? = null,
) : Model, PersonReference, BeneficiaryBase, UpdatedByReference {

    val isCanceled get() = canceledAt != null && canceledAt.isBeforeEq(LocalDateTime.now().atEndOfTheDay())

    fun toNewMembershipBeneficiary(newMemberId: UUID, newMemberStatus: MemberStatus) = this.copy(
        id = RangeUUID.generate(),
        memberId = newMemberId,
        memberStatus = newMemberStatus,
    )

    fun withNewParent(newParent: Beneficiary) = this.copy(
        parentPerson = newParent.personId,
        parentBeneficiary = newParent.id,
        companySubContractId = newParent.companySubContractId,
    )
}

interface BeneficiaryBase {
    val parentBeneficiary: UUID?
    val type: BeneficiaryType
    val contractType: BeneficiaryContractType?
    val parentBeneficiaryRelationType: ParentBeneficiaryRelationType?
    val hiredAt: LocalDateTime?
    val cnpj: String?
}

enum class BeneficiaryContractType(val description: String) {
    CLT("Colaborador contratado sob regime CLT"),
    PJ("Colaborador sob regime de pessoa jurídica"),
    PARTNER("Sócio"),
    AFFILIATE("Coligado"),
}

enum class ParentBeneficiaryRelationType(val description: String) {
    CHILD("Filha ou filho"),
    SPOUSE("Cônjuge"),
    GRANDCHILD("Neta ou neto"),
    GREATGRANDCHILD("Bisneta ou bisneto"),
    STEPCHILD("Enteada ou enteado"),
    PARTNER("Parceira ou parceiro"),
    FOSTER_CHILD("Filha ou filho adotivo"),
    BROTHER_SISTER("Irmão ou Irmã"),
    MOTHER_FATHER("Mãe ou Pai"),
    GRANDMOTHER_GRANDFATHER("Avó ou Avô"),
    SON_DAUGHTER_IN_LAW("Genro ou Nora"),
    STEP_MOTHER_FATHER("Madrasta ou Padrasto"),
    NIECE_NEPHEW("Sobrinha ou Sobrinho");

    fun isDirectDependency() = this in listOf(
        CHILD,
        PARTNER,
        SPOUSE,
        FOSTER_CHILD,
        STEPCHILD,
    )
}

enum class GracePeriodType(val description: String) {
    TOTAL_GRACE_PERIOD("Carencia Total"),
    PARTIAL_GRACE_PERIOD("Compra parcial"),
    GRACE_PERIOD_EXEMPTION("Compra total de carencias"),
    TOTAL_EXEMPTION("Isenção total"),
    TOTAL_GRACE_PERIOD_202405("Carência Total 2024/05"),
    PARTIAL_GRACE_PERIOD_202405("Compra parcial 2024/05"),
}

enum class GracePeriodTypeReason(val description: String) {
    MLA("Empresas MLA devem ter isenção total"),
    PORTABILITY("Portabilidade ANS"),
    MANDATORY("Adesão compulsória"),
    ANALYSIS("Análise de compra de carência"),
    DEFAULT("Caso padrão de carências"),
    PARTIAL_PORTABILITY("Portabilidade parcial")
}

enum class BeneficiaryCancelationReason(val description: String) {
    RESIGNATION_WITHOUT_CAUSE("Demissão ou exoneração sem justa causa"),
    RESIGNATION_WITHOUT_CAUSE_DEPENDENT("Dependente - Demissão ou exoneração sem justa causa"),
    RESIGNATION_WITH_CAUSE("Demissão por justa causa"),
    RESIGNATION_WITH_CAUSE_DEPENDENT("Dependente - Demissão por justa causa"),
    RESIGNATION_REQUEST("Demissão a pedido do colaborador"),
    RESIGNATION_REQUEST_DEPENDENT("Dependente - Demissão a pedido do colaborador"),
    RETIREMENT("Aposentadoria"),
    RETIREMENT_DEPENDENT("Dependente - Aposentadoria"),
    EXCLUSION_REQUEST("Exclusão do plano a pedido do funcionário"),
    EXCLUSION_REQUEST_DEPENDENT("Dependente - Exclusão do plano a pedido do funcionário"),
    PRIOR_ACTIVATION("Cancelamento anterior a ativação"),
    PRIOR_ACTIVATION_DEPENDENT("Dependente - Cancelamento anterior a ativação"),
    ALICE_DECISION("Decisão de negócio - Alice"),
    ALICE_DECISION_DEPENDENT("Dependente - Decisão de negócio - Alice"),
    OPERATIONAL_ERROR_ALICE("Erro operacional - Alice"),
    OPERATIONAL_ERROR_ALICE_DEPENDENT("Dependente - Erro operacional - Alice"),
    OPERATIONAL_ERROR_COMPANY("Erro operacional - Cliente"),
    OPERATIONAL_ERROR_COMPANY_DEPENDENT("Dependente - Erro operacional - Cliente"),
    ANOTHER_COMPANY("Outro - Decisão da Empresa"),
    ANOTHER_COMPANY_DEPENDENT("Dependente - Outro - Decisão da Empresa"),
    TERMINATION_PAYMENT("Rescisão de contrato - Inadimplência da empresa"),
    TERMINATION_PAYMENT_DEPENDENT("Dependente - Rescisão de contrato - Inadimplência da empresa"),
    TERMINATION_REQUEST_COMPANY("Rescisão de contrato - Decisão da empresa"),
    TERMINATION_REQUEST_COMPANY_DEPENDENT("Dependente - Rescisão de contrato - Decisão da empresa"),
    ANOTHER("Outro"),
    ANOTHER_DEPENDENT("Dependente - Outro"),
    DEATH("Óbito"),
    DEATH_DEPENDENT("Dependente - Óbito"),
    ACCREDITED_NETWORK_DISSATISFACTION("Insatisfação com rede credenciada"),
    ANOTHER_PLAN_DISSATISFACTION("Outra insatisfação com o plano ");

    fun getDependentCounterpart() = when (this) {
        RESIGNATION_WITHOUT_CAUSE -> RESIGNATION_WITHOUT_CAUSE_DEPENDENT
        RESIGNATION_WITHOUT_CAUSE_DEPENDENT -> RESIGNATION_WITHOUT_CAUSE_DEPENDENT
        RESIGNATION_WITH_CAUSE -> RESIGNATION_WITH_CAUSE_DEPENDENT
        RESIGNATION_WITH_CAUSE_DEPENDENT -> RESIGNATION_WITH_CAUSE_DEPENDENT
        RESIGNATION_REQUEST -> RESIGNATION_REQUEST_DEPENDENT
        RESIGNATION_REQUEST_DEPENDENT -> RESIGNATION_REQUEST_DEPENDENT
        RETIREMENT -> RETIREMENT_DEPENDENT
        RETIREMENT_DEPENDENT -> RETIREMENT_DEPENDENT
        EXCLUSION_REQUEST -> EXCLUSION_REQUEST_DEPENDENT
        EXCLUSION_REQUEST_DEPENDENT -> EXCLUSION_REQUEST_DEPENDENT
        PRIOR_ACTIVATION -> PRIOR_ACTIVATION_DEPENDENT
        PRIOR_ACTIVATION_DEPENDENT -> PRIOR_ACTIVATION_DEPENDENT
        ALICE_DECISION -> ALICE_DECISION_DEPENDENT
        ALICE_DECISION_DEPENDENT -> ALICE_DECISION_DEPENDENT
        OPERATIONAL_ERROR_ALICE -> OPERATIONAL_ERROR_ALICE_DEPENDENT
        OPERATIONAL_ERROR_ALICE_DEPENDENT -> OPERATIONAL_ERROR_ALICE_DEPENDENT
        OPERATIONAL_ERROR_COMPANY -> OPERATIONAL_ERROR_COMPANY_DEPENDENT
        OPERATIONAL_ERROR_COMPANY_DEPENDENT -> OPERATIONAL_ERROR_COMPANY_DEPENDENT
        ANOTHER_COMPANY -> ANOTHER_COMPANY_DEPENDENT
        ANOTHER_COMPANY_DEPENDENT -> ANOTHER_COMPANY_DEPENDENT
        TERMINATION_PAYMENT -> TERMINATION_PAYMENT_DEPENDENT
        TERMINATION_PAYMENT_DEPENDENT -> TERMINATION_PAYMENT_DEPENDENT
        TERMINATION_REQUEST_COMPANY -> TERMINATION_REQUEST_COMPANY_DEPENDENT
        TERMINATION_REQUEST_COMPANY_DEPENDENT -> TERMINATION_REQUEST_COMPANY_DEPENDENT
        ANOTHER -> ANOTHER_DEPENDENT
        ANOTHER_DEPENDENT -> ANOTHER_DEPENDENT
        DEATH -> DEATH_DEPENDENT
        DEATH_DEPENDENT -> DEATH_DEPENDENT
        ACCREDITED_NETWORK_DISSATISFACTION -> ACCREDITED_NETWORK_DISSATISFACTION
        ANOTHER_PLAN_DISSATISFACTION -> ANOTHER_PLAN_DISSATISFACTION
    }
}
