package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompanySubContract(
    override val id: UUID = RangeUUID.generate(),
    val externalId: String? = null,
    val title: String,
    val billingAccountablePartyId: UUID? = null,
    val companyId: UUID,
    val contractId: UUID,
    val isProRata: Boolean? = null,
    val flexBenefit: Boolean? = null,
    val hasEmployeesAbroad: Boolean,
    val defaultProductId: UUID? = null,
    val availableProducts: List<UUID>? = null,
    val defaultFlowType: BeneficiaryOnboardingFlowType? = null,
    val dueDate: Int = 10,
    val isBillingLevel: Boolean = false,
    val paymentType: PaymentModel? = null,
    val nature: String? = null,
    val billingGroup: String? = null,
    val avaliableCompanyProductPriceListing: List<UUID>? = null,
    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val status: CompanySubContractStatus? = null,
    val integrationStatus: CompanySubContractIntegrationStatus? = null,
    val hasRetroactiveCharge: Boolean? = null,
    val blockedAt: LocalDate? = null,
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference

enum class CompanySubContractStatus(val description: String) {
    ACTIVE("Ativo na TOTVS"),
    DRAFT("Rascunho"),

    @Deprecated("Use o CompanyContractIntegrationStatus.PROCESSING para representar a integração")
    PROCESSING("Em Processamento"),

    @Deprecated("Use o CompanyContractIntegrationStatus.FAILED para representar a integração")
    FAILED("Falha ao inserir na TOTVS"),
    BLOCKED("Bloqueado"),
    CANCELED("Cancelado"),
}

enum class CompanySubContractIntegrationStatus(val description: String) {
    PROCESSING("Em Processamento"),
    FAILED("Falha ao inserir na TOTVS"),
    PROCESSED("Ativo na TOTVS"),
}


enum class TotvsBillingGroup(val code: String) {
    ALICE_INDIVIDUAL_PRE_PAY_VENC_1_14("0001"),
    ALICE_INDIVIDUAL_PRE_PAY_VENC_15_31("0002"),
    ALICE_GROUP_PRE_PAY_VENC_1_14("0003"),
    ALICE_GROUP_PRE_PAY_VENC_15_31("0004"),
    ALICE_GROUP_POST_PAY_VENC_1_31("0005")
}
