package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.util.UUID

data class BeneficiaryHubspot(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val beneficiaryId: UUID,
    val companyId: UUID,
    val externalContactId: String,
    val externalDealId: String,
    val version: Int = 0,
) : Model, PersonReference
