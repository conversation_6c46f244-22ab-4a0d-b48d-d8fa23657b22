package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.models.Sex
import br.com.alice.common.serialization.JsonSerializable
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class StandardCost(
    override val id: UUID = RangeUUID.generate(),
    val companyBusinessUnit: CompanyBusinessUnit,
    val companySize: CompanySize,
    val adhesion: Adhesion,
    val ansNumber: String,
    val costByPersonDetail: CostByPersonDetail,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val version: Int = 0,
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference {

    fun getValue(isDependent: Boolean, sex: Sex, age: Int): BigDecimal {
        val genderDetail = if (isDependent) this.costByPersonDetail.dependent else this.costByPersonDetail.holder

        val costByAge = when (sex) {
            Sex.MALE -> genderDetail.male
            Sex.FEMALE -> genderDetail.female
            else -> throw InvalidArgumentException("Sex invalid for standard cost calculation")
        }
        return costByAge.first { it.minAge <= age && it.maxAge >= age }.cost
    }
}

enum class Adhesion {
    OPTIONAL, MANDATORY
}

data class CostByPersonDetail(
    val holder: GenderDetail,
    val dependent: GenderDetail
) : JsonSerializable

data class GenderDetail(
    val male: List<CostByAgeRange>,
    val female: List<CostByAgeRange>
)

data class CostByAgeRange(
    val minAge: Int,
    val maxAge: Int,
    val cost: BigDecimal
)
