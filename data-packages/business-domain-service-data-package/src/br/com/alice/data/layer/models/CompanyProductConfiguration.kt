package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.data.layer.models.CompanyContractType.MANDATORY
import br.com.alice.data.layer.models.CompanyContractType.OPTIONAL
import br.com.alice.data.layer.models.CompanySizeConfiguration.M
import br.com.alice.data.layer.models.CompanySizeConfiguration.MEI_1_5
import br.com.alice.data.layer.models.CompanySizeConfiguration.ME_1
import br.com.alice.data.layer.models.CompanySizeConfiguration.ME_2
import br.com.alice.data.layer.models.CompanySizeConfiguration.ME_3_5
import br.com.alice.data.layer.models.CompanySizeConfiguration.ME_2_5
import br.com.alice.data.layer.models.CompanySizeConfiguration.P
import java.util.UUID

data class CompanyProductConfiguration(
    override val id: UUID = RangeUUID.generate(),
    val companyProductType: CompanyProductType,
    val availableProductIds: List<UUID>,
    val productPriceListIds: List<ProductPriceListingConfiguration> = emptyList(),
    val configurationVersion: ConfigurationVersion = ConfigurationVersion.STABLE,
    val version: Int = 0
) : Model

data class ProductPriceListingConfiguration(
    val productId: UUID,
    val priceListingId: UUID,
)

enum class ConfigurationVersion {
    STABLE,
    TRANSITORY,
    DEPRECATED
}

enum class CompanyProductType(val description: String) {
    MEI_1_5_OPTIONAL(description = "MEI 1 a 5 vidas, adesão opcional"),
    MICRO_1_OPTIONAL(description = "ME 1 vida, adesão opcional"),
    MICRO_2_OPTIONAL(description = "ME 2 vidas, adesão opcional"),
    MICRO_3_5_OPTIONAL(description = "ME 3 a 5 vidas, adesão opcional"),
    MICRO_3_5_MANDATORY(description = "ME 3 a 5 vidas, adesão compulsória"),

    @Deprecated("Use other type instead")
    MICRO_2_5_OPTIONAL(description = "ME 2 a 5 vidas, adesão opcional"),
    SMALL_6_29_OPTIONAL(description = "P 6 a 29 vidas, adesão opcional"),
    SMALL_6_29_MANDATORY(description = "P 6 a 29 vidas, adesão compulsória"),
    MEDIUM_30_99_OPTIONAL(description = "M 30 a 99 vidas, adesão opcional"),
    MEDIUM_30_99_MANDATORY(description = "M 30 a 99 vidas, adesão compulsória");

    companion object {
        fun getFromCompanyConfiguration(
            companySizeConfiguration: CompanySizeConfiguration,
            companyContractType: CompanyContractType
        ) = when (companySizeConfiguration) {
            MEI_1_5 -> getForMEI()
            ME_1, ME_2, ME_2_5 -> getForMicro(companySizeConfiguration)
            ME_3_5 -> getForMicro3_5(companyContractType)
            P -> getForSmall(companyContractType)
            M -> getForMedium(companyContractType)
        }

        private fun getForMEI() = MEI_1_5_OPTIONAL

        private fun getForMicro(
            companySizeConfiguration: CompanySizeConfiguration
        ) = when (companySizeConfiguration) {
            ME_1 -> MICRO_1_OPTIONAL
            ME_2 -> MICRO_2_OPTIONAL
            ME_2_5 -> MICRO_2_5_OPTIONAL
            else -> null
        }

        private fun getForMicro3_5(
            companyContractType: CompanyContractType,
        ) = when (companyContractType) {
            OPTIONAL -> MICRO_3_5_OPTIONAL
            MANDATORY -> MICRO_3_5_MANDATORY
        }

        private fun getForSmall(
            companyContractType: CompanyContractType
        ) = with(companyContractType) {
            when {
                equals(OPTIONAL) -> SMALL_6_29_OPTIONAL
                equals(MANDATORY) -> SMALL_6_29_MANDATORY
                else -> null
            }
        }

        private fun getForMedium(
            companyContractType: CompanyContractType
        ) = with(companyContractType) {
            when {
                equals(OPTIONAL) -> MEDIUM_30_99_OPTIONAL
                equals(MANDATORY) -> MEDIUM_30_99_MANDATORY
                else -> null
            }
        }

    }

}

enum class CompanySizeConfiguration(val description: String) {
    MEI_1_5(description = "MEI 1 a 5 vidas"),
    ME_1(description = "ME 1 vida"),
    ME_2(description = "ME 2 vidas"),
    ME_3_5(description = "ME 3 a 5 vidas"),

    @Deprecated("Use other type instead")
    ME_2_5(description = "ME 2 a 5 vidas"),
    P(description = "P 6 a 29 vidas"),
    M(description = "M 30 a 99 vidas");

    companion object {
        fun getFromRangeOfBeneficiaries(rangeOfBeneficiaries: String): CompanySizeConfiguration? =
            with(rangeOfBeneficiaries) {
                when {
                    equals("1", ignoreCase = true) -> ME_1
                    equals("2", ignoreCase = true) -> ME_2
                    contains("2 a 5", ignoreCase = true) -> ME_2_5
                    contains("3 a 5", ignoreCase = true) -> ME_3_5
                    contains("6 a 29", ignoreCase = true) -> P
                    contains("30 a 99", ignoreCase = true) -> M
                    else -> null
                }
            }

        fun getFromBeneficiariesCountAtDayZero(
            beneficiariesCountAtDayZero: Int,
            shouldUseStable: Boolean = true
        ): CompanySizeConfiguration {
            val range2to5Type = if (shouldUseStable) {
                ME_2_5
            } else {
                if (beneficiariesCountAtDayZero == 2) ME_2 else ME_3_5
            }

            return when (beneficiariesCountAtDayZero) {
                in 0..1 -> ME_1
                in 2..5 -> range2to5Type
                in 6..29 -> P
                else -> M
            }
        }
    }

    fun toCompanySize() = when (this) {
        ME_1, ME_2, ME_3_5, ME_2_5,
        MEI_1_5 -> CompanySize.MICRO

        P -> CompanySize.SMALL
        M -> CompanySize.MLA
    }

}

enum class CompanyContractType(val description: String) {
    OPTIONAL(description = "Livre adesão"),
    MANDATORY(description = "Compulsório");

    companion object {
        private val COMPANY_CONTRACT_TYPE_DESCRIPTION_REGEX = Regex("[C-c]ompuls[o-ó]ri[ao]")

        fun getFromContractTypeDescription(contractTypeDescription: String?): CompanyContractType =
            with(contractTypeDescription) {
                when {
                    this?.contains(COMPANY_CONTRACT_TYPE_DESCRIPTION_REGEX) == true -> MANDATORY
                    else -> OPTIONAL
                }
            }
    }

}
