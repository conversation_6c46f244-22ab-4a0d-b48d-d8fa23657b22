package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class BeneficiaryOnboardingPhase(
    override val id: UUID = RangeUUID.generate(),
    val beneficiaryOnboardingId: UUID,
    val phase: BeneficiaryOnboardingPhaseType,
    val transactedAt: LocalDateTime = LocalDateTime.now(),
    val finishedAt: LocalDateTime? = null,
    val version: Int = 0,
) : Model {

    fun newPhase(phase: BeneficiaryOnboardingPhaseType, transactedAt: LocalDateTime = LocalDateTime.now()) =
        BeneficiaryOnboardingPhase(
            beneficiaryOnboardingId = this.beneficiaryOnboardingId,
            phase = phase,
            transactedAt = transactedAt
        )
}

enum class BeneficiaryOnboardingPhaseType(val description: String, val order: Int) {
    READY_TO_ONBOARD("Pronto para iniciar fluxo", 1),
    HEALTH_DECLARATION("Declaração de saúde enviada", 2),

    @Deprecated("This phase is not used for any flow anymore")
    SEND_HEALTH_DECLARATION("Enviar declaração de saúde", 996),
    HEALTH_DECLARATION_APPOINTMENT("Agendamento de Video Call enviado", 3),
    HEALTH_DECLARATION_APPOINTMENT_SCHEDULED("Video Call agendado", 4),
    WAITING_CPTS_APPLICATION("Aguardando aplicação de CPTS por Risco", 5),
    SEND_CPTS("Enviar CPTS", 6),
    CPTS_CONFIRMATION("Aguardando assinatura do CPTS", 7),
    CONTRACT_SIGNATURE("Assinatura do contrato", 8),

    @Deprecated("This phase is not used for any flow anymore")
    SEND_HEALTH_DECLARATION_APPOINTMENT("Enviar agendamento de Video Call", 997),
    REGISTRATION("Onboarding no aplicativo", 9),
    WAITING_FOR_REVIEW("Aguardando revisão de dados", 10),

    @Deprecated("This phase is not used for any flow anymore")
    SEND_REGISTRATION("Enviar email de boas-vindas", 998),

    @Deprecated("This phase is not used for any flow anymore")
    HEALTH_CARE_TEAM_SELECTION("Selecionando Time de Saúde", 999),
    FINISHED("Fluxo finalizado", 11);
}
