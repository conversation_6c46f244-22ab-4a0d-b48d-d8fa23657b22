package br.com.alice.data.layer.models

import br.com.alice.common.Brand
import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.extensions.isValidCnpj
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.logging.logger
import br.com.alice.common.models.State
import br.com.alice.common.serialization.JsonSerializable
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.util.UUID

data class Company(
    override val id: UUID = RangeUUID.generate(),
    val parentId: UUID? = null,
    val externalCode: String? = null,
    val name: String,
    val legalName: String,
    val cnpj: String,
    val email: String,
    val phoneNumber: String,
    val address: CompanyAddress,
    val priceAdjustmentType: PriceAdjustmentType? = null,
    val contractType: CompanyContractType? = null,
    val contractStartedAt: LocalDateTime? = null,
    val beneficiariesCountAtDayZero: Int? = null,
    val bankingInfo: CompanyBankingInfo? = null,
    val availableProducts: List<UUID>? = null,
    val totalEmployees: Int? = null,
    val billingAccountablePartyId: UUID? = null,
    val defaultProductId: UUID? = null,
    val flexBenefit: Boolean? = null,
    val hasEmployeesAbroad: Boolean? = null,
    val contractsUrls: List<String> = emptyList(),
    val defaultFlowType: BeneficiaryOnboardingFlowType? = null,
    val totvsContract: String? = null,
    val totvsContractVersion: String? = null,
    val totvsSubContract: String? = null,
    val totvsSubContractVersion: String? = null,
    override val version: Int = 0,
    val brand: Brand? = Brand.ALICE,
    val externalBrandId: String? = null,
    val contractIds: List<UUID> = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val status: CompanyStatus? = null,
    val companySize: CompanySize? = null,
    val companyBusinessUnit: CompanyBusinessUnit? = null,
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference {

    fun validateNationalId(): Result<Company, Throwable> {
        if (!cnpj.isValidCnpj()) return IllegalArgumentException("Invalid national ID format").failure()
        return this.success()
    }

    fun sanitizeCompany() =
        this.copy(
            address = this.address.copy(postalCode = this.address.postalCode.onlyNumbers()),
            email = this.email.lowercase()
        )

}

data class CompanyAddress(
    val postalCode: String,
    val street: String,
    val number: Int,
    val complement: String? = null,
    val neighborhood: String? = null,
    val city: String,
    val State: String
) : JsonSerializable

data class CompanyBankingInfo(
    val bankCode: Int,
    val agencyNumber: String,
    val accountNumber: String
) : JsonSerializable

enum class PriceAdjustmentType(val description: String) {
    POOL_SMALL_COMPANY("Pool para empresas com < 30 funcionários"),
    POOL("Pool para empresas com >= 30 funcionários"),
    INDIVIDUAL("Individual para empresas com >= 30 funcionários"),
    UNKNOWN("Desconhecido"),
}

enum class CompanyStatus(val description: String) {
    ACTIVE("Ativo na TOTVS"),
    DRAFT("Rascunho"),
    PROCESSING("Em Processamento"),
    FAILED("Falha ao inserir na TOTVS")
}

enum class CompanyBusinessUnit {
    SMALL_BIZ,
    CORRETORES,
    MLA
}

fun Company.getAddress() = try {
    Address(
        state = State.valueOf(this.address.State),
        city = this.address.city,
        street = this.address.street,
        number = this.address.number.toString(),
        complement = this.address.complement,
        postalCode = this.address.postalCode,
        neighbourhood = this.address.neighborhood,
    )
} catch (exception: Exception) {
    if (this.address.State.isNotBlank()) logger.error(
        "CompanyConverter::getAddress error",
        "error_message" to exception.message
    )

    null
}
