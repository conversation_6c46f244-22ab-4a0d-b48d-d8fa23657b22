package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class CompanyProductPriceListing(
    override val id: UUID = RangeUUID.generate(),
    @Deprecated("Do not use this field, use priceListItems instead")
    val priceListingId: UUID? = null,
    val productId: UUID,
    val ansNumber: String,
    val companyId: UUID,
    val companySubContractId: UUID,
    val startDate: LocalDate,
    val priceListItems: List<PriceListingItem> = emptyList(),
    val endDate: LocalDate? = null,
    val isBlockedForSale: Boolean? = null,
    val product: Product? = null,
    @Deprecated("Do not use this field, use priceListItems instead")
    val priceListing: PriceListing? = null,
    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override var updatedBy: UpdatedBy? = null,
) : Model, UpdatedByReference
