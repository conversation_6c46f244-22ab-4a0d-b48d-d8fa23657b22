package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class InvoiceLiquidationTaxReceipt(
    override val id: UUID = RangeUUID.generate(),
    val status: TaxReceiptStatus,
    val issuedAt: LocalDateTime,
    val pdfUrl: String,
    val invoiceLiquidationId: UUID,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model

