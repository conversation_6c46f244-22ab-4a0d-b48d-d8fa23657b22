package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class FleuryProcess(
    override val id: UUID = RangeUUID.generate(),
    val claimId: String? = null,
    val executionGroupId: UUID?,
    val totvsGuiaId: UUID?,
    override val personId: PersonId,
    val status: FleuryProcessType = FleuryProcessType.PENDING,
    val lastProcessAt: LocalDateTime? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, PersonReference

enum class FleuryProcessType {
    PENDING, DONE, CANCELLED, CANCELLED_BY_PERSON
}
