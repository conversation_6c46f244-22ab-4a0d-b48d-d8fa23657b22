package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.ExternalHealthInformation
import java.time.LocalDateTime
import java.util.UUID

data class FleuryTestResultFile(
    override val personId: PersonId,
    val idFicha: String,
    val idItem: String,
    val siglaExame: String,
    val fileId: UUID,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate()
) : Model, PersonReference, ExternalHealthInformation
