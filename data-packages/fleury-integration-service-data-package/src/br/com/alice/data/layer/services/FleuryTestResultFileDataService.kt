package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.FleuryTestResultFile
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface FleuryTestResultFileDataService : Service,
    Finder<FleuryTestResultFileDataService.FieldOptions, FleuryTestResultFileDataService.OrderingOptions, FleuryTestResultFile>,
    Adder<FleuryTestResultFile>,
    Updater<FleuryTestResultFile>,
    Getter<FleuryTestResultFile> {

    override val namespace: String
        get() = "fleury"
    override val serviceName: String
        get() = "test_result_file"

    class PersonIdField : Field.TableIdField(FleuryTestResultFile::personId)
    class Ficha : Field.TextField(FleuryTestResultFile::idFicha) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class Item : Field.TextField(FleuryTestResultFile::idItem) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class CreatedAt: Field.DateTimeField(FleuryTestResultFile::createdAt)

    class FieldOptions {
        val personId = PersonIdField()
        val idFicha = Ficha()
        val idItem = Item()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    suspend fun findByPersonId(personId: String) = find { where { this.personId.eq(personId) } }

    override suspend fun findByQuery(query: Query): Result<List<FleuryTestResultFile>, Throwable>
    override suspend fun add(model: FleuryTestResultFile): Result<FleuryTestResultFile, Throwable>
    override suspend fun get(id: UUID): Result<FleuryTestResultFile, Throwable>
    override suspend fun update(model: FleuryTestResultFile): Result<FleuryTestResultFile, Throwable>

}
