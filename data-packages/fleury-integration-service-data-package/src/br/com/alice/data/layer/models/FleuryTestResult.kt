package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.serialization.JsonSerializable
import com.google.gson.annotations.Expose
import java.time.LocalDateTime
import java.util.UUID

data class FleuryTestResult(
    @Expose
    override val personId: PersonId,
    val idFicha: String,
    val idProduto: String?,
    val idUnidade: String?,
    val idItem: String,
    val claimId: String?,
    val procedimento: String,
    val type: FleuryResultType,
    val laudos: List<FleuryLaudo> = emptyList(),
    val laudoFormatado: String? = null,
    val executionGroupId: UUID? = null,
    val version: Int = 0,
    val dataColeta: LocalDateTime = LocalDateTime.now(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate()
) : Model, PersonReference, ExternalHealthInformation

enum class FleuryResultType {
    IMAGE, STRUCTURED
}

data class FleuryLaudo(
    val analitoId: String,
    val nome: String,
    val nomeReduzido: String,
    val unidadeMedida: String? = null,
    val listaFaixaReferenciaCritica: List<FaixaReferenciaCritica>? = emptyList(),
    val listaFaixaReferenciaNormal: List<FaixaReferenciaNormal>? = emptyList(),
    val resultado: String,
    val indicativoVisualizacao: Boolean,
    val codigoCondicaoResultado: String,
    val codigoTipoResultado: String
) : JsonSerializable {
    fun friendlyDescription(laudoFormatado: String? = null): String =
        if (listaFaixaReferenciaNormal.isNullOrEmpty()) laudoFormatado ?: ""
        else listaFaixaReferenciaNormal.joinToString { ref ->
            val ageRef = ref.descricaoFaixaEtaria?.let { "Descrição Faixa Etária: $it\n" } ?: ""
            val unitRef = ref.unidadeMedida?.let { "Unidade de medida: $it\n" } ?: ""
            val laudoRef = laudoFormatado?.let { "Laudo formatado: $it\n" } ?: ""
            "Descrição: ${ref.descricao}\n$ageRef\n$unitRef\n$laudoRef"
        }
}

data class FaixaReferenciaCritica(
    val limiteInferior: String
)

data class FaixaReferenciaNormal(
    val descricao: String? = null,
    val limiteInferior: String? = null,
    val limiteSuperior: String? = null,
    val idicativoFaixaReferenciaQualitativa: Boolean,
    val idicativoFaixaNormal: Boolean,
    val ordemExibicao: String,
    val descricaoFaixaEtaria: String? = null,
    val unidadeMedida: String? = null
)
