package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class ScreenData(
    val screenId: UUID,
    val placeholderValues: Map<String, String>,
    val referencedModelClass: ScreenDataReferenceModel,
    val referencedModelId: UUID,
    val answerMapping: Map<String, UUID>,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0
) : Model

enum class ScreenDataReferenceModel {
    SERVICE_SCRIPT_NODE,
}

data class ChannelCreationParameters(
    val content: String? = null,
    val kind: ChannelKind,
    val category: ChannelCategory,
    val subCategory: ChannelSubCategory? = null,
    val subCategoryClassifier: ChannelSubCategoryClassifier? = null,
    val hideMemberInput: Boolean? = false,
    val tags: List<String> = emptyList(),
    val budNodeId: UUID? = null
) : JsonSerializable
