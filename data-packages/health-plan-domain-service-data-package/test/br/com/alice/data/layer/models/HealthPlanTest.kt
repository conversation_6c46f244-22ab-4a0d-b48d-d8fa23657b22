package br.com.alice.data.layer.models

import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HealthPlanTest {

    @Test
    fun `#sanitize values`() {
        val healthPlan = TestModelFactory.buildHealthPlan()

        val sanitizedHealthPlan = healthPlan.sanitize()

        assertThat(sanitizedHealthPlan.healthGoal).isNotNull

        val healthPlanWithBlankIntro = TestModelFactory.buildHealthPlan().copy(healthGoal = " ")

        val sanitizedHealthPlanWithBlankIntro = healthPlanWithBlankIntro.sanitize()

        assertThat(sanitizedHealthPlanWithBlankIntro.healthGoal).isNull()
    }

}
