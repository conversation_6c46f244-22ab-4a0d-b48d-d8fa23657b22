package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class PrescriptionMedicineTest {

    private val medicine = PrescriptionMedicine(
        name = "Amoxil",
        unit = MedicineUnit.CAPSULE,
        quantity =  "15",
        concentration = "mg",
        concentrationQuantity = "500",
        drug = "Amoxilina",
        type = PrescriptionMedicineType.SIMPLE,
        id = RangeUUID.generate(),
        drugId = 1000
    )

    @Test
    fun `#getFullName should return medicine full name`() {
        assertThat(medicine.getFullName()).isEqualTo("Amoxil 500mg, 15 Cápsulas")
    }

    @Test
    fun `#getUnit should return medicine unit correct name plural or singular`() {
        assertThat(medicine.getUnit()).isEqualTo("cápsulas")
        assertThat(medicine.copy(quantity = "1").getUnit()).isEqualTo("cápsula")
    }

    @Test
    fun `#getUnit should return medicine unit correct name plural or singular even with the wrong quantity`() {
        assertThat(medicine.copy(quantity = "S40").getUnit()).isEqualTo("cápsulas")
        assertThat(medicine.copy(quantity = "S1").getUnit()).isEqualTo("cápsula")
    }
}
