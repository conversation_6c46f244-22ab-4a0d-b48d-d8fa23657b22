package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.localDateTimeComparator
import br.com.alice.common.extensions.mapOfNotNull
import br.com.alice.data.layer.models.HealthPlanTaskType.EATING
import br.com.alice.data.layer.models.HealthPlanTaskType.PRESCRIPTION
import br.com.alice.data.layer.models.HealthPlanTaskType.REFERRAL
import br.com.alice.data.layer.models.HealthPlanTaskType.TEST_REQUEST
import br.com.alice.data.layer.models.SpecialistType.STAFF
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class HealthPlanTaskConversionTest {

    private fun buildTask(type: HealthPlanTaskType) =
        HealthPlanTask(
            personId = PersonId(),
            healthPlanId = RangeUUID.generate(),
            lastRequesterStaffId = RangeUUID.generate(),
            type = type,
            groupId = RangeUUID.generate(),
            favorite = false,
        )

    @Test
    fun `#REFERRAL conversions`() {
        val task = buildTask(REFERRAL)

        val suggestedSpecialist = SuggestedSpecialist(
            name = "name",
            id = RangeUUID.generate(),
            type = STAFF
        )

        val referral = Referral(
            suggestedSpecialist = suggestedSpecialist,
            diagnosticHypothesis = "hypo",
            referenceLetterSentDate = LocalDateTime.of(2020, 1, 1, 1, 1, 1),
            task = task
        )

        val generalized = referral.generalize()

        val expectedContent = mapOf(
            "suggestedSpecialist" to mapOf(
                "name" to suggestedSpecialist.name,
                "id" to suggestedSpecialist.id.toString(),
                "origin" to suggestedSpecialist.origin?.name,
                "type" to suggestedSpecialist.type.name
            ),
            "diagnosticHypothesis" to referral.diagnosticHypothesis,
            "referenceLetterSentDate" to referral.referenceLetterSentDate.toString()
        )

        assertThat(generalized.content).isEqualTo(expectedContent)

        val specialized = generalized.specialize<Referral>()

        assertThat(specialized)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(referral)

    }

    @Test
    fun `#PRESCRIPTION conversions`() {
        val task = buildTask(PRESCRIPTION)
        val dose = Dose(
            unit = MedicineUnit.CAPSULE,
            quantity = 1.0f
        )
        val medicine = PrescriptionMedicine(
            id = RangeUUID.generate(),
            drugId = 123,
            name = "medicine",
            unit = MedicineUnit.PILLS,
            quantity = "30",
            concentration = "MG",
            concentrationQuantity = "100",
            drug = "drug",
            type = PrescriptionMedicineType.SIMPLE
        )
        val medicineEndingDate = LocalDateTime.now().withNano(0)

        val prescription = Prescription(
            dose = dose,
            action = ActionType.TAKE,
            routeOfAdministration = RouteOfAdministration.INTRAVENOUS,
            medicine = medicine,
            packing = 1,
            task = task,
            sentenceEdited = true,
            medicineEndAt = medicineEndingDate
        )

        val generalized = prescription.generalize()

        val expectedContent = mapOf(
            "dose" to mapOf(
                "unit" to dose.unit.name,
                "quantity" to dose.quantity.toDouble()
            ),
            "medicine" to mapOfNotNull(
                "id" to medicine.id.toString(),
                "drugId" to medicine.drugId,
                "name" to medicine.name,
                "unit" to medicine.unit?.name,
                "quantity" to medicine.quantity,
                "concentration" to medicine.concentration,
                "concentrationQuantity" to medicine.concentrationQuantity,
                "drug" to medicine.drug,
                "type" to medicine.type.name,
            ),
            "action" to ActionType.TAKE.name,
            "routeOfAdministration" to RouteOfAdministration.INTRAVENOUS.name,
            "packing" to 1,
            "sentenceEdited" to true,
            "medicineEndAt" to medicineEndingDate.toString(),
        )

        assertThat(generalized.content).isEqualTo(expectedContent)

        val specialized = generalized.specialize<Prescription>()

        assertThat(specialized)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(prescription)
    }

    @Test
    fun `#Generic Task conversions`() {
        val task = buildTask(EATING)

        val eating = GenericTask(task = task)

        val generalized = eating.generalize()
        assertThat(generalized.content).isEmpty()

        val specialized = generalized.specialize<GenericTask>()
        assertThat(specialized.task)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(eating.task)

    }

    @Test
    fun `#TEST_REQUEST conversions`() {
        val task = buildTask(TEST_REQUEST)

        val testRequest = TestRequest(
            code = "code",
            task = task
        )

        val generalized = testRequest.generalize()

        assertThat(generalized.content?.get("code")).isEqualTo("code")

        val specialized = generalized.specialize<TestRequest>()

        assertThat(specialized.code).isEqualTo(testRequest.code)

    }

    @Test
    fun `#FOLLOW_UP_REQUEST conversions`() {
        val task = buildTask(HealthPlanTaskType.FOLLOW_UP_REQUEST)
        val interval = FollowUpInterval(
            type = FollowUpIntervalType.AFTER_MEDICAL_TREATMENT,
            unit = PeriodUnit.DAY,
            quantity = 1
        )

        val followUpRequest = FollowUpRequest(
            providerType = FollowUpProviderType.REMOTE,
            followUpInterval = interval,
            task = task,
        )

        val generalized = followUpRequest.generalize()

        val expectedContent = mapOf(
            "providerType" to FollowUpProviderType.REMOTE.name,
            "followUpInterval" to mapOf(
                "type" to interval.type.name,
                "unit" to interval.unit?.name,
                "quantity" to interval.quantity
            )
        )

        assertThat(generalized.content).isEqualTo(expectedContent)

        val specialized = generalized.specialize<FollowUpRequest>()

        assertThat(specialized)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(followUpRequest)
    }

    @Test
    fun `#SURGERY_PRESCRIPTION conversions`() {
        val task = buildTask(HealthPlanTaskType.SURGERY_PRESCRIPTION)

        val hospital = Hospital(name = "Hospital da Criança", id = RangeUUID.generate())
        val procedure = SurgicalProcedure(description = "Corte", tussCode = "tussCode12412")

        val surgeryPrescription = SurgeryPrescription(
            task = task,
            reason = "Muitos braços",
            expectedDate = LocalDate.of(2024, 11, 21).toString(),
            provider = hospital,
            procedures = listOf(procedure)
        )

        val generalized = surgeryPrescription.generalize()

        val expectedContent = mapOf(
            "reason" to surgeryPrescription.reason,
            "expectedDate" to surgeryPrescription.expectedDate.toString(),
            "provider" to mapOf(
                "name" to hospital.name,
                "id" to hospital.id.toString()
            ),
            "procedures" to listOf(
                mapOf(
                    "description" to procedure.description,
                    "tussCode" to procedure.tussCode
                )
            )
        )

        assertThat(generalized.content).isEqualTo(expectedContent)

        val specialized = generalized.specialize<SurgeryPrescription>()

        assertThat(specialized)
            .usingComparatorForType(localDateTimeComparator, LocalDateTime::class.java)
            .usingRecursiveComparison()
            .isEqualTo(surgeryPrescription)
    }

}
