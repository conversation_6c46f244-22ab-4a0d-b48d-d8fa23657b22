package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import java.util.UUID

data class HealthPlanTaskGroup(
    val healthPlanId: UUID? = null,
    val name: String,
    val searchTokens: String? = null,
    val descriptions: List<HealthPlanTaskGroupDescription> = emptyList(),
    val internalAttachments: List<HealthPlanTaskGroupAttachment> = emptyList(),
    val externalAttachments: List<HealthPlanTaskGroupAttachment> = emptyList(),
    val version: Int = 0,
    override val personId: PersonId,
    override val id: UUID = RangeUUID.generate(),
) : Model, HealthInformation, PersonReference

data class HealthPlanTaskGroupAttachment(
    val id: UUID,
    val fileName: String,
    val type: HealthPlanTaskType
)

data class HealthPlanTaskGroupDescription(
    val description: String,
    val type: HealthPlanTaskType
)
