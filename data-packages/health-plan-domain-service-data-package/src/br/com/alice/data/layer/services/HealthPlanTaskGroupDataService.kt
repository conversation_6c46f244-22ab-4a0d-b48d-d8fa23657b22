package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthPlanTaskGroup
import br.com.alice.data.layer.services.HealthPlanTaskGroupDataService.FieldOptions
import br.com.alice.data.layer.services.HealthPlanTaskGroupDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthPlanTaskGroupDataService : Service,
    Finder<FieldOptions, OrderingOptions, HealthPlanTaskGroup>,
    Adder<HealthPlanTaskGroup>,
    Getter<HealthPlanTaskGroup>,
    Updater<HealthPlanTaskGroup> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "health_plan_task_group"

    class IdField: Field.UUIDField(HealthPlanTaskGroup::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class PersonIdField: Field.UUIDField(HealthPlanTaskGroup::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class HealthPlanIdField: Field.UUIDField(HealthPlanTaskGroup::healthPlanId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class SearchTokensField: Field.TextField(HealthPlanTaskGroup::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class NameField: Field.TextField(HealthPlanTaskGroup::name) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val healthPlanId = HealthPlanIdField()
        val personId = PersonIdField()
        val searchTokens = SearchTokensField()
        val name = NameField()
    }

    class OrderingOptions {
        val name = NameField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<HealthPlanTaskGroup, Throwable>
    override suspend fun add(model: HealthPlanTaskGroup): Result<HealthPlanTaskGroup, Throwable>
    override suspend fun update(model: HealthPlanTaskGroup): Result<HealthPlanTaskGroup, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthPlanTaskGroup>, Throwable>
}
