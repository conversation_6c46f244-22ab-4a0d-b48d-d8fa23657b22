package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthPlan
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface HealthPlanDataService : Service,
    Finder<HealthPlanDataService.FieldOptions, HealthPlanDataService.OrderingOptions, HealthPlan>,
    Updater<HealthPlan>,
    Adder<HealthPlan>,
    Getter<HealthPlan> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "health_plan"

    class CreatedAt: Field.DateTimeField(HealthPlan::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
    }

    class UpdatedAt: Field.DateTimeField(HealthPlan::updatedAt) {
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }

    class PersonIdField : Field.TableIdField(HealthPlan::personId)

    class Id : Field.UUIDField(HealthPlan::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun eq(value: String) = eq(UUID.fromString(value))
    }

    class FieldOptions {
        val personId = PersonIdField()
        val createdAt = CreatedAt()
        val updatedAt = UpdatedAt()
        val id = Id()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<HealthPlan>, Throwable>
    override suspend fun update(model: HealthPlan): Result<HealthPlan, Throwable>
    override suspend fun add(model: HealthPlan): Result<HealthPlan, Throwable>
    override suspend fun get(id: UUID): Result<HealthPlan, Throwable>
}
