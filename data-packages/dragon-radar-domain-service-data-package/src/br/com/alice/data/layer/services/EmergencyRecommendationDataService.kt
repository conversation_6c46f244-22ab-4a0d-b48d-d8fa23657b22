package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.EmergencyRecommendation
import br.com.alice.data.layer.services.EmergencyRecommendationDataService.FieldOptions
import br.com.alice.data.layer.services.EmergencyRecommendationDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface EmergencyRecommendationDataService :
    Service,
    Finder<FieldOptions, OrderingOptions, EmergencyRecommendation>,
    Adder<EmergencyRecommendation>,
    Updater<EmergencyRecommendation>,
    Getter<EmergencyRecommendation> {

    override val namespace: String get() = "dragon_radar"
    override val serviceName: String get() = "emergency_recommendation"

    class PersonIdField : Field.TableIdField(EmergencyRecommendation::personId)
    class CreatedAt : Field.DateTimeField(EmergencyRecommendation::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class HealthPlanTaskId : Field.UUIDField(EmergencyRecommendation::healthPlanTaskId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val healthPlanTaskId = HealthPlanTaskId()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<EmergencyRecommendation, Throwable>
    override suspend fun add(model: EmergencyRecommendation): Result<EmergencyRecommendation, Throwable>
    override suspend fun update(model: EmergencyRecommendation): Result<EmergencyRecommendation, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<EmergencyRecommendation>, Throwable>
}
