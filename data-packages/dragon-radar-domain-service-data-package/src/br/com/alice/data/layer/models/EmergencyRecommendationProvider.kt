package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class EmergencyRecommendationProvider(
    override val id: UUID = RangeUUID.generate(),
    val providerUnitId: UUID,
    val specialties: List<EmergencyRecommendationProviderSpecialty> = emptyList(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model

data class EmergencyRecommendationProviderSpecialty(
    val index: Int,
    val medicalSpecialtyId: UUID,
)
