package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class EmergencyRecommendation(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val healthPlanTaskId: UUID,
    val recommendedProviderUnitId: UUID? = null,
    val emergencySpecialty: EmergencySpecialty? = null,
    val cityId: String? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, PersonReference
