package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.City
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface CityDataService : Service,
    Adder<City>,
    Finder<CityDataService.FieldOptions, CityDataService.OrderingOptions, City>,
    Updater<City>,
    Getter<City> {

    override val namespace: String
        get() = "coverage"
    override val serviceName: String
        get() = "city"

    class Id : Field.UUIDField(City::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Name : Field.TextField(City::name) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class IbgeId : Field.TextField(City::ibgeId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class CreatedAt : Field.DateTimeField(City::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class FieldOptions {
        val id = Id()
        val name = Name()
        val ibgeId = IbgeId()
    }

    class OrderingOptions {
        val name = Name()
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: City): Result<City, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<City>, Throwable>
    override suspend fun get(id: UUID): Result<City, Throwable>
    override suspend fun update(model: City): Result<City, Throwable>

    suspend fun findByIbgeId(ibgeId: String) = findOneOrNull { where { this.ibgeId.eq(ibgeId) } }
}
