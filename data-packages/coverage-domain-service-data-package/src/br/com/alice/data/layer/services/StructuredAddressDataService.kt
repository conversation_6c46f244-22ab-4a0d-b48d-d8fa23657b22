package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface StructuredAddressDataService : Service,
    Adder<StructuredAddress>,
    AdderList<StructuredAddress>,
    Finder<StructuredAddressDataService.FieldOptions, StructuredAddressDataService.OrderingOptions, StructuredAddress>,
    Counter<StructuredAddressDataService.FieldOptions, StructuredAddressDataService.OrderingOptions, StructuredAddress>,
    Updater<StructuredAddress>,
    Getter<StructuredAddress> {

    override val namespace: String
        get() = "provider"
    override val serviceName: String
        get() = "structured_address"

    class Id : Field.UUIDField(StructuredAddress::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ReferencedModelId : Field.UUIDField(StructuredAddress::referencedModelId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun isNotNull() = Predicate.isNotNull(this)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ReferencedModelClass : Field.TextField(StructuredAddress::referencedModelClass) {
        fun eq(value: StructuredAddressReferenceModel) = Predicate.eq(this, value)
        fun inList(value: List<StructuredAddressReferenceModel>) = Predicate.inList(this, value)
    }

    class Street : Field.TextField(StructuredAddress::street) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class Neighborhood : Field.TextField(StructuredAddress::neighborhood) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class City : Field.TextField(StructuredAddress::city) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class State : Field.TextField(StructuredAddress::state) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class Zipcode : Field.TextField(StructuredAddress::zipcode) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class CreatedAt : Field.TextField(StructuredAddress::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class GeoLocation : Field.GeographyField(StructuredAddress::geoLocation) {
        fun withinRange(range: Int, latitude: String, longitude: String) =
            Predicate.withinRange(this, latitude, longitude, range)
    }

    class Active : Field.BooleanField(StructuredAddress::active)

    class LatitudeField : Field.TextField(StructuredAddress::latitude) {
        fun isNull() = Predicate.isNull(this)
    }

    class LongitudeField : Field.TextField(StructuredAddress::longitude) {
        fun isNull() = Predicate.isNull(this)
    }

    class FieldOptions {
        val id = Id()
        val referencedModelId = ReferencedModelId()
        val referencedModelClass = ReferencedModelClass()
        val street = Street()
        val neighborhood = Neighborhood()
        val city = City()
        val state = State()
        val zipcode = Zipcode()
        val active = Active()
        val geoLocation = GeoLocation()
        val latitude = LatitudeField()
        val longitude = LongitudeField()
    }

    class OrderingOptions {
        val neighborhood = Neighborhood()
        val city = City()
        val state = State()
        val createdAt = CreatedAt()
        val geoLocation = GeoLocation()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: StructuredAddress): Result<StructuredAddress, Throwable>
    override suspend fun addList(models: List<StructuredAddress>): Result<List<StructuredAddress>, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<StructuredAddress>, Throwable>
    override suspend fun get(id: UUID): Result<StructuredAddress, Throwable>
    override suspend fun update(model: StructuredAddress): Result<StructuredAddress, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
}
