package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ConsolidatedRating
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ConsolidatedRatingDataService : Service,
    Adder<ConsolidatedRating>,
    AdderList<ConsolidatedRating>,
    Updater<ConsolidatedRating>,
    Finder<ConsolidatedRatingDataService.FieldOptions, ConsolidatedRatingDataService.OrderingOptions, ConsolidatedRating>,
    Getter<ConsolidatedRating> {

    override val namespace: String
        get() = "coverage"
    override val serviceName: String
        get() = "consolidated_rating"

    class HealthProfessionalId : Field.UUIDField(ConsolidatedRating::healthProfessionalId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val healthProfessionalId = HealthProfessionalId()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: ConsolidatedRating): Result<ConsolidatedRating, Throwable>
    override suspend fun addList(models: List<ConsolidatedRating>): Result<List<ConsolidatedRating>, Throwable>
    override suspend fun get(id: UUID): Result<ConsolidatedRating, Throwable>
    override suspend fun update(model: ConsolidatedRating): Result<ConsolidatedRating, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ConsolidatedRating>, Throwable>
}
