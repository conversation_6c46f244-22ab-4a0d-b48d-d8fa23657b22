package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class ZipcodeAddress(
    override val id: UUID = RangeUUID.generate(),
    val zipcode: String? = null,
    val streetType: String? = null,
    val street: String? = null,
    val complement: String? = null,
    val place: String? = null,
    val neighbourhood: String? = null,
    val city: String? = null,
    val cityCode: String? = null,
    val federativeUnit: String? = null,
    val state: String? = null,
    val stateCode: String? = null,
    val latitude: Double? = null,
    val longitude: Double? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
): Model

