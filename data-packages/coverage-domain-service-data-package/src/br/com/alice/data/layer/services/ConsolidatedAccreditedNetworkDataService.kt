package br.com.alice.data.layer.services

import br.com.alice.common.Brand
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.DeleterList
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetwork
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkAppointmentType
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ConsolidatedAccreditedNetworkDataService : Service,
    Adder<ConsolidatedAccreditedNetwork>,
    AdderList<ConsolidatedAccreditedNetwork>,
    Finder<ConsolidatedAccreditedNetworkDataService.FieldOptions, ConsolidatedAccreditedNetworkDataService.OrderingOptions, ConsolidatedAccreditedNetwork>,
    Updater<ConsolidatedAccreditedNetwork>,
    Deleter<ConsolidatedAccreditedNetwork>,
    DeleterList<ConsolidatedAccreditedNetwork>,
    Counter<ConsolidatedAccreditedNetworkDataService.FieldOptions, ConsolidatedAccreditedNetworkDataService.OrderingOptions, ConsolidatedAccreditedNetwork>,
    Getter<ConsolidatedAccreditedNetwork> {

    override val namespace: String
        get() = "coverage"
    override val serviceName: String
        get() = "consolidated_accredited_network"

    class Id : Field.UUIDField(ConsolidatedAccreditedNetwork::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ReferencedId : Field.UUIDField(ConsolidatedAccreditedNetwork::referencedId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun notInList(value: List<UUID>) = Predicate.notInList(this, value)
    }

    class BundleIds : Field.JsonbField(ConsolidatedAccreditedNetwork::bundleIds) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)

        @OptIn(Predicate.Companion.ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<UUID>) = Predicate.containsAny(this, value)

        fun isEmpty() = Predicate.isEmptyList(this)
    }

    class Type : Field.TextField(ConsolidatedAccreditedNetwork::type) {
        fun eq(value: ConsolidatedAccreditedNetworkType) = Predicate.eq(this, value)
        fun inList(value: List<ConsolidatedAccreditedNetworkType>) = Predicate.inList(this, value)
        fun notInList(value: List<ConsolidatedAccreditedNetworkType>) = Predicate.notInList(this, value)
    }

    class AppointmentTypes : Field.TextField(ConsolidatedAccreditedNetwork::appointmentTypes) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: ConsolidatedAccreditedNetworkAppointmentType) = Predicate.contains(this, value)
    }

    class BrandField : Field.TextField(ConsolidatedAccreditedNetwork::brand) {
        fun eq(value: Brand) = Predicate.eq(this, value)
        fun inList(value: List<Brand>) = Predicate.inList(this, value)
    }

    class Tiers : Field.TextField(ConsolidatedAccreditedNetwork::tiers) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: SpecialistTier) = Predicate.contains(this, value)

        @OptIn(Predicate.Companion.ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<SpecialistTier>) = Predicate.containsAny(this, value)
    }

    class SpecialtyIds : Field.JsonbField(ConsolidatedAccreditedNetwork::specialtyIds) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)

        @OptIn(Predicate.Companion.ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<UUID>) = Predicate.containsAny(this, value)

        fun isEmpty() = Predicate.isEmptyList(this)
    }

    class SubSpecialtyIds : Field.JsonbField(ConsolidatedAccreditedNetwork::subSpecialtyIds) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)
    }

    class GeoLocation : Field.GeographyField(ConsolidatedAccreditedNetwork::geoLocation) {
        fun withinRange(range: Int, latitude: String, longitude: String) =
            Predicate.withinRange(this, latitude, longitude, range)
    }

    class CityField : Field.JsonbField(ConsolidatedAccreditedNetwork::additionalInfo) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: String) = Predicate.jsonSearch(this, "{\"address\":{\"city\":\"$value\"}}")
    }

    class AdditionalInfoField : Field.JsonbField(ConsolidatedAccreditedNetwork::additionalInfo) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class ProviderNameField : Field.JsonbField(ConsolidatedAccreditedNetwork::additionalInfo) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: String) = Predicate.jsonSearch(this, "{\"name\":\"$value\"}")
    }

    class FlagshipField : Field.BooleanField(ConsolidatedAccreditedNetwork::flagship) {
        override fun eq(value: Boolean) = Predicate.eq(this, value)
    }

    class CreatedAtField: Field.DateTimeField(ConsolidatedAccreditedNetwork::createdAt)

    class FieldOptions {
        val id = Id()
        val referencedId = ReferencedId()
        val geoLocation = GeoLocation()
        val bundleIds = BundleIds()
        val type = Type()
        val appointmentTypes = AppointmentTypes()
        val brand = BrandField()
        val tiers = Tiers()
        val specialtyIds = SpecialtyIds()
        val subSpecialtyId = SubSpecialtyIds()
        val city = CityField()
        val additionalInfo = AdditionalInfoField()
        val providerName = ProviderNameField()
    }

    class OrderingOptions {
        val geoLocation = GeoLocation()
        val flagship = FlagshipField()
        val createdAt = CreatedAtField()
        val additionalInfo = AdditionalInfoField()
        val providerName = ProviderNameField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: ConsolidatedAccreditedNetwork): Result<ConsolidatedAccreditedNetwork, Throwable>
    override suspend fun addList(models: List<ConsolidatedAccreditedNetwork>): Result<List<ConsolidatedAccreditedNetwork>, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ConsolidatedAccreditedNetwork>, Throwable>
    override suspend fun get(id: UUID): Result<ConsolidatedAccreditedNetwork, Throwable>
    override suspend fun update(model: ConsolidatedAccreditedNetwork): Result<ConsolidatedAccreditedNetwork, Throwable>
    override suspend fun delete(model: ConsolidatedAccreditedNetwork): Result<Boolean, Throwable>
    override suspend fun deleteList(models: List<ConsolidatedAccreditedNetwork>): Result<List<Boolean>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
