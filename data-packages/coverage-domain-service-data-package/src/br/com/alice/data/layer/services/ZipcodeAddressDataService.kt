package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ZipcodeAddress
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ZipcodeAddressDataService : Service,
    Finder<ZipcodeAddressDataService.FieldOptions, ZipcodeAddressDataService.OrderingOptions, ZipcodeAddress>,
    Getter<ZipcodeAddress>,
    Adder<ZipcodeAddress> {

    override val namespace: String
        get() = "member"

    override val serviceName: String
        get() = "zipcode_address"

    class Zipcode: Field.TextField(ZipcodeAddress::zipcode) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val zipcode = Zipcode()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<ZipcodeAddress>, Throwable>

    override suspend fun add(model: ZipcodeAddress): Result<ZipcodeAddress, Throwable>

    override suspend fun get(id: UUID): Result<ZipcodeAddress, Throwable>
}
