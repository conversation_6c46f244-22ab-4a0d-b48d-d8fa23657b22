package br.com.alice.data.layer.models

import br.com.alice.common.DistanceUtils
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.models.Geography
import br.com.alice.common.models.GeographyReference
import br.com.alice.common.models.State
import java.time.LocalDateTime
import java.util.UUID

data class StructuredAddress(
    override val id: UUID = RangeUUID.generate(),
    val street: String,
    val number: String,
    val complement: String?,
    val neighborhood: String,
    val city: String,
    val state: String,
    val zipcode: String,
    val referencedModelId: UUID? = null,
    val referencedModelClass: StructuredAddressReferenceModel? = null,
    val active: Boolean = true,
    val label: String? = "",
    override val latitude: String? = null,
    override val longitude: String? = null,
    override val geoLocation: Geography? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, GeographyReference {

    override fun sanitize(): StructuredAddress {
        val sanitized = this.copy(
            label = label?.trim(),
            street = street.trim(),
            number = number.trim(),
            complement = complement?.trim(),
            neighborhood = neighborhood.trim(),
            city = city.trim(),
            state = state.trim(),
            geoLocation = GeographyReference.getGeoLocation(latitude = latitude, longitude = longitude)
        )
        if (State.fromString(sanitized.state) == null) {
            throw InvalidArgumentException(
                code = "invalid_state",
                message = "Invalid State: ${sanitized.state}. It should be a state abbreviation."
            )
        }
        return sanitized
    }

    fun isHealthCommunitySpecialist() =
        referencedModelClass == StructuredAddressReferenceModel.HEALTH_COMMUNITY_SPECIALIST

    fun isProviderUnit() = referencedModelClass == StructuredAddressReferenceModel.PROVIDER_UNIT
    fun formattedAddress() = "$street, $number${formattedComplement()} - $neighborhood, $city/$state - $zipcode"

    fun distanceInMetersTo(other: StructuredAddress): Double? {
        if (latitude == null || longitude == null || other.latitude == null || other.longitude == null) return null

        return DistanceUtils.distanceInMeters(
            latitude.toDouble(), longitude.toDouble(),
            other.latitude.toDouble(), other.longitude.toDouble()
        )
    }

    fun distanceInMetersTo(lat: Double?, lng: Double?): Double? =
        if (latitude == null || longitude == null || lat == null || lng == null) null
        else DistanceUtils.distanceInMeters(
            latitude1 = latitude.toDouble(),
            longitude1 = longitude.toDouble(),
            latitude2 = lat,
            longitude2 = lng
        )

    private fun formattedComplement() = if (complement != null) ", $complement" else ""
}

enum class StructuredAddressReferenceModel {
    HEALTH_COMMUNITY_SPECIALIST,
    PROVIDER_UNIT,
    CASSI_SPECIALIST,
    HEALTHCARE_TEAM,
    STAFF,
    HEALTH_PROFESSIONAL
}
