package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class AccreditedNetworkFavorite(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val referenceId: UUID,
    val referenceType: ConsolidatedAccreditedNetworkType,
    val specialtyIds: List<UUID>,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val deletedAt: LocalDateTime? = null,
    val version: Int = 0,
) : Model, PersonReference
