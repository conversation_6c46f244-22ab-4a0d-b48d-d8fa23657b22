package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.SoftDeleter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AccreditedNetworkFavorite
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AccreditedNetworkFavoriteDataService : Service,
    Adder<AccreditedNetworkFavorite>,
    Updater<AccreditedNetworkFavorite>,
    Getter<AccreditedNetworkFavorite>,
    Finder<AccreditedNetworkFavoriteDataService.FieldOptions, AccreditedNetworkFavoriteDataService.OrderingOptions, AccreditedNetworkFavorite>,
    SoftDeleter<AccreditedNetworkFavorite> {

    override val namespace: String
        get() = "coverage"
    override val serviceName: String
        get() = "accredited_network_favorite"

    class IdField : Field.UUIDField(AccreditedNetworkFavorite::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class PersonIdField : Field.TableIdField(AccreditedNetworkFavorite::personId) {
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class ReferenceIdField : Field.UUIDField(AccreditedNetworkFavorite::referenceId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ReferenceTypeFiled : Field.TextField(AccreditedNetworkFavorite::referenceType) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class SpecialtyIdsField : Field.JsonbField(AccreditedNetworkFavorite::specialtyIds) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)

        @OptIn(Predicate.Companion.ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<UUID>) = Predicate.containsAny(this, value)

        fun isEmpty() = Predicate.isEmptyList(this)
    }

    class CreatedAtField : Field.DateTimeField(AccreditedNetworkFavorite::createdAt)

    class DeletedAtField : Field.DateTimeField(AccreditedNetworkFavorite::deletedAt) {
        fun isNotNull() = Predicate.isNotNull(this)
        fun isNull() = Predicate.isNull(this)
    }

    class FieldOptions {
        val id = IdField()
        val personId = PersonIdField()
        val referenceId = ReferenceIdField()
        val referenceType = ReferenceTypeFiled()
        val specialtyIds = SpecialtyIdsField()
        val deletedAt = DeletedAtField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: AccreditedNetworkFavorite): Result<AccreditedNetworkFavorite, Throwable>
    override suspend fun update(model: AccreditedNetworkFavorite): Result<AccreditedNetworkFavorite, Throwable>
    override suspend fun get(id: UUID): Result<AccreditedNetworkFavorite, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AccreditedNetworkFavorite>, Throwable>
    override suspend fun softDelete(model: AccreditedNetworkFavorite): Result<Boolean, Throwable>
}
