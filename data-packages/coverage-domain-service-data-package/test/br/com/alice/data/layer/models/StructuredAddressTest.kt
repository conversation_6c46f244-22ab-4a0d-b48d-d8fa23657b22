package br.com.alice.data.layer.models

import br.com.alice.common.DistanceUtils
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import io.mockk.every
import io.mockk.mockkObject
import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class StructuredAddressTest {

    private val casaAlice =
        TestModelFactory.buildStructuredAddress(latitude = "-23.57182895", longitude = "-46.69271973")
    private val office = TestModelFactory.buildStructuredAddress(latitude = "-23.57228442", longitude = "-46.69272687")

    @Test
    fun `#sanitize values`() {
        val structuredAddress = TestModelFactory.buildStructuredAddress(
            label = "\n Endereço Principal \n",
            street = "\n R. Treze de <PERSON> \n",
            number = "\n 1815 \n",
            complement = "\n sala 3 \n ",
            neighborhood = "\n Bela Vista \n",
            city = "\n São Paulo \n",
            state = "\n SP \n",
        )
        val sanitizedStructuredAddress = structuredAddress.sanitize()
        assertThat(sanitizedStructuredAddress.label).isEqualTo("Endereço Principal")
        assertThat(sanitizedStructuredAddress.street).isEqualTo("R. Treze de Maio")
        assertThat(sanitizedStructuredAddress.number).isEqualTo("1815")
        assertThat(sanitizedStructuredAddress.complement).isEqualTo("sala 3")
        assertThat(sanitizedStructuredAddress.neighborhood).isEqualTo("Bela Vista")
        assertThat(sanitizedStructuredAddress.city).isEqualTo("São Paulo")
        assertThat(sanitizedStructuredAddress.state).isEqualTo("SP")
    }

    @Test
    fun `#sanitize values validates state for HEALTH_COMMUNITY_SPECIALIST`() {
        val structuredAddressHc1 = TestModelFactory.buildStructuredAddress(
            state = "São Paulo",
            referencedModelClass = StructuredAddressReferenceModel.HEALTH_COMMUNITY_SPECIALIST
        )
        Assertions.assertThatThrownBy {
            structuredAddressHc1.sanitize()
        }.isInstanceOf(InvalidArgumentException::class.java)
            .withFailMessage("Invalid State: São Paulo. It should be a state abbreviation.")

        val structuredAddressHc2 = TestModelFactory.buildStructuredAddress(
            state = "SP",
            referencedModelClass = StructuredAddressReferenceModel.HEALTH_COMMUNITY_SPECIALIST
        )
        val sanitizedStructuredAddressHc2 = structuredAddressHc2.sanitize()
        assertThat(sanitizedStructuredAddressHc2.state).isEqualTo("SP")


        val structuredAddressPu1 = TestModelFactory.buildStructuredAddress(
            state = "São Paulo",
            referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT
        )
        Assertions.assertThatThrownBy {
            structuredAddressPu1.sanitize()
        }.isInstanceOf(InvalidArgumentException::class.java)
            .withFailMessage("Invalid State: São Paulo. It should be a state abbreviation.")

        val structuredAddressPu2 = TestModelFactory.buildStructuredAddress(
            state = "SP",
            referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT
        )
        val sanitizedStructuredAddressPu2 = structuredAddressPu2.sanitize()
        assertThat(sanitizedStructuredAddressPu2.state).isEqualTo("SP")


        val structuredAddressCassi1 = TestModelFactory.buildStructuredAddress(
            state = "São Paulo",
            referencedModelClass = StructuredAddressReferenceModel.CASSI_SPECIALIST
        )
        Assertions.assertThatThrownBy {
            structuredAddressCassi1.sanitize()
        }.isInstanceOf(InvalidArgumentException::class.java)
            .withFailMessage("Invalid State: São Paulo. It should be a state abbreviation.")

        val structuredAddressCassi2 = TestModelFactory.buildStructuredAddress(
            state = "SP",
            referencedModelClass = StructuredAddressReferenceModel.CASSI_SPECIALIST
        )
        val sanitizedStructuredAddressCassi2 = structuredAddressCassi2.sanitize()
        assertThat(sanitizedStructuredAddressCassi2.state).isEqualTo("SP")
    }

    @Test
    fun `#formattedAddress formats address`() {
        val structuredAddress = TestModelFactory.buildStructuredAddress()
        assertThat(structuredAddress.formattedAddress()).isEqualTo(
            "R. Treze de Maio, 1815 - Bela Vista, São Paulo/SP - 01323-020"
        )
    }

    @Test
    fun `#formattedAddress formats address with complement`() {
        val structuredAddress = TestModelFactory.buildStructuredAddress(complement = "ap 21")
        assertThat(structuredAddress.formattedAddress()).isEqualTo(
            "R. Treze de Maio, 1815, ap 21 - Bela Vista, São Paulo/SP - 01323-020"
        )
    }

    @Test
    fun `#distanceInMetersTo returns a double for distance by other address`() {
        val expected = 11.0

        mockkObject(DistanceUtils) {
            every {
                DistanceUtils.distanceInMeters(
                    casaAlice.latitude!!.toDouble(), casaAlice.longitude!!.toDouble(),
                    office.latitude!!.toDouble(), office.longitude!!.toDouble()
                )
            } returns expected

            val result = casaAlice.distanceInMetersTo(office)!!
            assertThat(result).isEqualTo(expected)

            verifyOnce { DistanceUtils.distanceInMeters(any(), any(), any(), any()) }
        }
    }

    @Test
    fun `#distanceInMetersTo returns a double for distance using given lat and lng`() {
        val expected = 11.0

        mockkObject(DistanceUtils) {
            every {
                DistanceUtils.distanceInMeters(
                    casaAlice.latitude!!.toDouble(), casaAlice.longitude!!.toDouble(),
                    office.latitude!!.toDouble(), office.longitude!!.toDouble()
                )
            } returns expected

            val result = casaAlice.distanceInMetersTo(office.latitude!!.toDouble(), office.longitude!!.toDouble())!!
            assertThat(result).isEqualTo(expected)

            verifyOnce { DistanceUtils.distanceInMeters(any(), any(), any(), any()) }
        }
    }

    @Test
    fun `#distanceInMetersTo returns null when some latitude or longitude is null`() {
        val resultOtherLatitudeNull = casaAlice.distanceInMetersTo(office.copy(latitude = null))
        assertThat(resultOtherLatitudeNull).isNull()

        val resultOtherLongitudeNull = casaAlice.distanceInMetersTo(office.copy(longitude = null))
        assertThat(resultOtherLongitudeNull).isNull()

        val resultMainLatitudeNull = casaAlice.copy(latitude = null).distanceInMetersTo(office)
        assertThat(resultMainLatitudeNull).isNull()

        val resultMainLongitudeNull = casaAlice.copy(longitude = null).distanceInMetersTo(office)
        assertThat(resultMainLongitudeNull).isNull()

        val resultAllFieldsNull = casaAlice.copy(latitude = null, longitude = null)
            .distanceInMetersTo(office.copy(latitude = null, longitude = null))
        assertThat(resultAllFieldsNull).isNull()
    }

}
