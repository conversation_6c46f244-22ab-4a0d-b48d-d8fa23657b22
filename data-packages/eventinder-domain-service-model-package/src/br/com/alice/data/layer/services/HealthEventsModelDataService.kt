package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.AdderList
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.client.UpdaterList
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.EventReferenceModel
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.HealthEventsModel
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface HealthEventsModelDataService :
    Service,
    Finder<HealthEventsModelDataService.FieldOptions, HealthEventsModelDataService.OrderingOptions, HealthEventsModel>,
    Getter<HealthEventsModel>,
    Adder<HealthEventsModel>,
    AdderList<HealthEventsModel>,
    Updater<HealthEventsModel>,
    UpdaterList<HealthEventsModel>,
    Deleter<HealthEventsModel> {

    override val namespace get() = "eventinder"
    override val serviceName get() = "health_events"

    class Id : Field.UUIDField(HealthEventsModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class UpdatedAt : Field.DateTimeField(HealthEventsModel::updatedAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class OriginReferenceField : Field.JsonbField(HealthEventsModel::originReferences) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: EventReferenceModel) =
            Predicate.jsonSearch(this, "[{\"id\": \"${value.id}\", \"location\":\"${value.location}\"}]")

        fun inList(value: List<EventReferenceModel>) =
            Predicate.inList(this, value.map { "[{\"id\":\"${it.id}\", \"location\":\"${it.location}\"}]" })
    }

    class OriginReferenceLocationField : Field.JsonbField(HealthEventsModel::originReferences) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: HealthEventLocationEnum) =
            Predicate.jsonSearch(this, "[{\"location\":\"${value}\"}]")
    }

    class PersonIdField : Field.TableIdField(HealthEventsModel::personId)
    class FieldOptions {
        val id = Id()
        val originReferenceField = OriginReferenceField()
        val personId = PersonIdField()
        val originReferenceLocation = OriginReferenceLocationField()
    }

    class OrderingOptions {
        val updatedAt = UpdatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<HealthEventsModel, Throwable>
    override suspend fun add(model: HealthEventsModel): Result<HealthEventsModel, Throwable>
    override suspend fun addList(models: List<HealthEventsModel>): Result<List<HealthEventsModel>, Throwable>
    override suspend fun update(model: HealthEventsModel): Result<HealthEventsModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthEventsModel>, Throwable>
    override suspend fun updateList(models: List<HealthEventsModel>, returnOnFailure: Boolean): Result<List<HealthEventsModel>, Throwable>
    override suspend fun delete(model: HealthEventsModel): Result<Boolean, Throwable>
}
