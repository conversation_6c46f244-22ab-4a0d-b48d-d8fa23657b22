package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class HealthEventsModel(
    override val personId: PersonId,
    val eventType: HealthEventTypeEnum,
    val caseIds: List<String>? = emptyList(),
    val caseRecordIds: List<String>? = emptyList(),
    val requestedAt: LocalDateTime,
    val executedAt: LocalDateTime? = null,
    val originReferences: List<EventReferenceModel>? = emptyList(),
    val executionReferences: List<EventReferenceModel>? = emptyList(),
    val origin: HealthEventOriginEnum,
    val healthProfessionalId: UUID? = null,
    val procedureIds: List<String>? = emptyList(),
    val createdAt: LocalDateTime = LocalDateTime.now(),

    override val id: UUID = RangeUUID.generate(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0
) : Model, PersonReference

data class EventReferenceModel(
    val id: String,
    val location: HealthEventLocationEnum
) : JsonSerializable
