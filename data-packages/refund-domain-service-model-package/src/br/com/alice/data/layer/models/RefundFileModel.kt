package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class RefundFileModel(
    override val personId: PersonId,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val refundId: UUID,
    val type: RefundFileType,
    val fileVaultId: UUID,
    val sizeBytes: Int = 1,
    val extension: String = "JPG",
    val filename: String,
    val active: Boolean = true,
    val version: Int = 0,
    override val id: UUID = RangeUUID.generate(),
) : Model, PersonReference
