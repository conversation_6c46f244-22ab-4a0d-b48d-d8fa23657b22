package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.RefundModel
import br.com.alice.data.layer.services.RefundModelDataService.FieldOptions
import br.com.alice.data.layer.services.RefundModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface RefundModelDataService: Service,
    Finder<FieldOptions, OrderingOptions, RefundModel>,
    Adder<RefundModel>,
    Getter<RefundModel>,
    Updater<RefundModel>{

    override val namespace: String
        get() = "refund"
    override val serviceName: String
        get() = "refund"

    class PersonIdField: Field.TextField(RefundModel::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class CreatedAtField: Field.DateTimeField(RefundModel::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<RefundModel, Throwable>
    override suspend fun add(model: RefundModel): Result<RefundModel, Throwable>
    override suspend fun update(model: RefundModel): Result<RefundModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<RefundModel>, Throwable>
}
