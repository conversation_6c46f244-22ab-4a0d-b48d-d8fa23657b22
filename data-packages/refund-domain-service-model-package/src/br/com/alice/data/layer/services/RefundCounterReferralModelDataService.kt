package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.RefundCounterReferralModel
import br.com.alice.data.layer.services.RefundCounterReferralModelDataService.FieldOptions
import br.com.alice.data.layer.services.RefundCounterReferralModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface RefundCounterReferralModelDataService: Service,
    Finder<FieldOptions, OrderingOptions, RefundCounterReferralModel>,
    Adder<RefundCounterReferralModel>,
    Getter<RefundCounterReferralModel>,
    Updater<RefundCounterReferralModel>{

    override val namespace: String
        get() = "refund"
    override val serviceName: String
        get() = "refund_counter_referral"

    class IdField: Field.UUIDField(RefundCounterReferralModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class RefundIdField: Field.UUIDField(RefundCounterReferralModel::refundId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class PersonIdField: Field.TextField(RefundCounterReferralModel::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class CreatedAtField: Field.DateTimeField(RefundCounterReferralModel::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val personId = PersonIdField()
        val refundId = RefundIdField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<RefundCounterReferralModel, Throwable>
    override suspend fun add(model: RefundCounterReferralModel): Result<RefundCounterReferralModel, Throwable>
    override suspend fun update(model: RefundCounterReferralModel): Result<RefundCounterReferralModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<RefundCounterReferralModel>, Throwable>
}
