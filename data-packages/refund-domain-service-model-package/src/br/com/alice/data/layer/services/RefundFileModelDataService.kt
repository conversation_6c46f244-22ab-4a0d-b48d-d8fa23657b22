package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.RefundFileModel
import br.com.alice.data.layer.models.RefundFileType
import br.com.alice.data.layer.services.RefundFileModelDataService.FieldOptions
import br.com.alice.data.layer.services.RefundFileModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface RefundFileModelDataService: Service,
    Finder<FieldOptions, OrderingOptions, RefundFileModel>,
    Adder<RefundFileModel>,
    Getter<RefundFileModel>,
    Updater<RefundFileModel>{

    override val namespace: String
        get() = "refund"
    override val serviceName: String
        get() = "refund_file"

    class IdField: Field.UUIDField(RefundFileModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }
    class PersonIdField: Field.TextField(RefundFileModel::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class RefundIdField: Field.UUIDField(RefundFileModel::refundId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class RefundFileTypeField: Field.TextField(RefundFileModel::type) {
        fun inList(value: List<RefundFileType>) = Predicate.inList(this, value)
    }

    class RefundActiveField: Field.BooleanField(RefundFileModel::active)

    class CreatedAtField: Field.DateTimeField(RefundFileModel::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val personId = PersonIdField()
        val refundId = RefundIdField()
        val type = RefundFileTypeField()
        val active = RefundActiveField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<RefundFileModel, Throwable>
    override suspend fun add(model: RefundFileModel): Result<RefundFileModel, Throwable>
    override suspend fun update(model: RefundFileModel): Result<RefundFileModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<RefundFileModel>, Throwable>
}
