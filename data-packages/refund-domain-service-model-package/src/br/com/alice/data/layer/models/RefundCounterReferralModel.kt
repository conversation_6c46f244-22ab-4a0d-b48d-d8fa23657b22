package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.State
import java.time.LocalDateTime
import java.util.UUID

data class RefundCounterReferralModel(
    override val personId: PersonId,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val refundId: UUID,
    val medicalSpecialtyId: UUID? = null,
    val attendanceAt: LocalDateTime? = null,
    val healthConditionIds: List<UUID> = emptyList(),
    val staffName: String? = null,
    val staffCouncilState: State? = null,
    val staffCouncilNumber: String? = null,
    val clinicalEvaluation: String? = null,
    val medicalOrientation: RefundMedicalOrientation? = null,
    val diagnosticHypothesis: String? = null,
    val sessionsQuantity: Int? = null,
    val version: Int = 0,
    override val id: UUID = RangeUUID.generate(),
) : Model, PersonReference
