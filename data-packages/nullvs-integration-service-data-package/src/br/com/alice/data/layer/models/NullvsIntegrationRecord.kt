package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class NullvsIntegrationRecord(
    val id: UUID = RangeUUID.generate(),
    val internalId: UUID,
    val internalModelName: InternalModelType,
    val externalId: String,
    val externalModelName: ExternalModelType,
    val integratedAt: LocalDateTime,
    val externalCreatedAt: LocalDateTime? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val canceledAt: LocalDateTime? = null
)
