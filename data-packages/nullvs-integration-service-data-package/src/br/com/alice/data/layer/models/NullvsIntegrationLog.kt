package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import java.time.LocalDateTime
import java.util.UUID

data class NullvsIntegrationLog(
    val id: UUID = RangeUUID.generate(),
    val eventId: UUID,
    val eventName: String,
    val integrationEventName: String,
    val internalId: UUID,
    val internalModelName: InternalModelType,
    val externalModelName: ExternalModelType,
    val dependsOnId: UUID? = null,
    val dependsOnModel: InternalModelType? = null,
    val batchId: String,
    val idSoc: String,
    val batchType: BatchType,
    val payloadSequenceId: Int,
    val description: String?,
    val status: LogStatus = LogStatus.PENDING,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val hash: String? = null,
    val groupId: UUID? = null
) {
    val isFailed get() = status == LogStatus.FAILURE
}

enum class LogStatus(val description: String) {
    TOTVS_NOT_CALLED("Não chamou a Totvs"),
    PENDING("Pendente"),
    FAILURE("Falha"),
    FINISHED("Finalizado"),
    WAITING("Aguardando sincronização"),
    SYNC_CANCELLED("Sincronização Cancelada"),
}

enum class BatchType(val description: String) {
    CREATE("Criação"),
    UPDATE("Atualização"),
    CANCEL("Cancelamento"),
    REACTIVATION("Reativação")
}

enum class ExternalModelType(val description: String) {
    CLIENT("Client"),
    BENEFICIARY("Beneficiary"),
    INVOICE("Invoice"),
    INVOICE_ITEM("Invoice Item"),
    CONTRACT("Contract"),
    SUBCONTRACT("Subcontract"),
    SUBCONTRACT_PRICE_LIST("Subcontract Price List"),
    FAMILY_PRICE_LIST("Family Price List"),
    BENEFICIARY_PRICE_LIST("Beneficiary Price List"),
}

enum class InternalModelType(val description: String) {
    MEMBER("Member"),
    BILLING_ACCOUNTABLE_PARTY("BillingAccountableParty"),
    MEMBER_INVOICE_GROUP("MemberInvoiceGroup"),
    MEMBER_INVOICE("MemberInvoice"),
    INVOICE_ITEM("InvoiceItem"),
    CONTRACT("Contract"),
    SUBCONTRACT("Subcontract"),
    INVOICE_LIQUIDATION("InvoiceLiquidation"),
    PRE_ACTIVATION_PAYMENT("PreActivationPayment");
}
