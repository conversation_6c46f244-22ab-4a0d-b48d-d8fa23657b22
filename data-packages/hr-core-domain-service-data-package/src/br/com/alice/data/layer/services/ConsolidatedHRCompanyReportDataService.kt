package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ConsolidatedHRCompanyReport
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.*

@RemoteService
interface ConsolidatedHRCompanyReportDataService : Service,
        Adder<ConsolidatedHRCompanyReport>,
        Getter<ConsolidatedHRCompanyReport>,
        Updater<ConsolidatedHRCompanyReport>,
        Finder<ConsolidatedHRCompanyReportDataService.FieldOptions, ConsolidatedHRCompanyReportDataService.OrderingOptions, ConsolidatedHRCompanyReport>
{
    override val namespace: String
        get() = "hr-core"
    override val serviceName: String
        get() = "consolidated_hr_company_report"


    class CompanyIdField : Field.UUIDField(ConsolidatedHRCompanyReport::companyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class CreatedAtField : Field.DateTimeField(ConsolidatedHRCompanyReport::createdAt) {
        fun eq(value: LocalDateTime) = Predicate.eq(this, value)
        fun inList(value: List<LocalDateTime>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val companyId = CompanyIdField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )


    override suspend fun get(id: UUID): Result<ConsolidatedHRCompanyReport, Throwable>
    override suspend fun add(model: ConsolidatedHRCompanyReport): Result<ConsolidatedHRCompanyReport, Throwable>
    override suspend fun update(model: ConsolidatedHRCompanyReport): Result<ConsolidatedHRCompanyReport, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ConsolidatedHRCompanyReport>, Throwable>
}