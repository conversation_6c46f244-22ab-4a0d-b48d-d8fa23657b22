package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.CompanyScoreMagenta
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface CompanyScoreMagentaDataService : Service,
    Adder<CompanyScoreMagenta>,
    Getter<CompanyScoreMagenta>,
    Updater<CompanyScoreMagenta>,
    Finder<CompanyScoreMagentaDataService.FieldOptions, CompanyScoreMagentaDataService.OrderingOptions, CompanyScoreMagenta>,
    Deleter<CompanyScoreMagenta>
{
    override val namespace: String
        get() = "hr-core"
    override val serviceName: String
        get() = "company_score_magenta"


    class IdField : Field.UUIDField(CompanyScoreMagenta::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class CompanyIdField : Field.UUIDField(CompanyScoreMagenta::companyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ReferenceStartDate : Field.DateTimeField(CompanyScoreMagenta::referenceStartDate) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun eq(value: LocalDateTime) = Predicate.eq(this, value)
    }

    class ReferenceEndDate : Field.DateTimeField(CompanyScoreMagenta::referenceEndDate) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }


    class FieldOptions {
        val id = IdField()
        val companyId = CompanyIdField()
        val referenceStartDate = ReferenceStartDate()
        val referenceEndDate = ReferenceEndDate()
    }

    class OrderingOptions {
        val referenceStartDate = ReferenceStartDate()
        val referenceEndDate = ReferenceEndDate()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<CompanyScoreMagenta, Throwable>
    override suspend fun add(model: CompanyScoreMagenta): Result<CompanyScoreMagenta, Throwable>
    override suspend fun update(model: CompanyScoreMagenta): Result<CompanyScoreMagenta, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<CompanyScoreMagenta>, Throwable>
    override suspend fun delete(model: CompanyScoreMagenta): Result<Boolean, Throwable>
}
