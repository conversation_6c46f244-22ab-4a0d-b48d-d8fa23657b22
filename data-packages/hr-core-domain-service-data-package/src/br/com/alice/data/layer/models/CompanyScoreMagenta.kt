package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class CompanyScoreMagenta (
    override val id: UUID = RangeUUID.generate(),
    val companyId: UUID,
    val referenceStartDate: LocalDateTime,
    val referenceEndDate: LocalDateTime,
    val generalScore: Float,
    val eatingScore: Float,
    val sleepScore: Float,
    val habitsScore: Float,
    val lifeQualityScore: Float,
    val physicalActivityScore: Float,
    val mentalHealthScore: Float,
    val adhesion: Float,
    val answersQuantity: Int,
    val descriptionByKey: DescriptionByKeyList,

    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    ): Model

data class DescriptionByKeyList(
    val list: List<DescriptionByKey>,
) : JsonSerializable

data class DescriptionByKey(
    val excellent: Float,
    val good: Float,
    val bad: Float,
    val key: String,
) : JsonSerializable

