package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.data.layer.models.HaocPixRegistration
import com.github.kittinunf.result.Result

@RemoteService
interface HaocPixRegistrationDataService : Service, Adder<HaocPixRegistration> {

    override val namespace: String
        get() = "haoc_integration"
    override val serviceName: String
        get() = "pix_registration"

    override suspend fun add(model: HaocPixRegistration): Result<HaocPixRegistration, Throwable>
}
