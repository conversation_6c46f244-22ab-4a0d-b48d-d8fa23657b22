package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.serialization.JsonSerializable
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class HaocProntoAtendimentoResult(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val claimId: Int?,
    val caracterizacaoAtendimento: CaracterizacaoAtendimento,
    val motivoAtendimento: String,
    val atendimentos: List<HaocAtendimento>,
    val desfecho: HaocDesfecho,
    val haocDocumentId: UUID,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, PersonReference, ExternalHealthInformation

data class HaocDesfecho(
    val motivo: String,
    val dataSaida: LocalDateTime,
    val profissionalDeAlta: HaocProfissional
): JsonSerializable

data class CaracterizacaoAtendimento(
    val local: Unidade,
    val procedencia: String,
    val dataChegada: LocalDateTime,
): JsonSerializable

data class HaocAtendimento(
    val alergias: List<HaocAlergia> = emptyList(),
    val escalasDeAvaliacao: List<EscalaAvaliacao> = emptyList(),
    val exameFisico: HaocExameFisico? = null,
    val diagnosticos: List<HaocDiagnostico> = emptyList(),
    val medicamentos: HaocMedicamento? = null,
    val procedimentos: List<HaocProcedimento> = emptyList(),
    val planoCuidados: HaocPlanoCuidado? = null,
    val profissionalDoAtendimento: HaocProfissional
): JsonSerializable

data class HaocPlanoCuidado(
    val atividade: String? = null,
    val encaminhamento: List<HaocEncaminhamento> = emptyList(),
    val exames: List<String> = emptyList()
): JsonSerializable {
    fun fullPlanoCuidado() =
        """Atividade: $atividade
        Encaminhamentos: ${encaminhamento.joinToString(separator = "-") { "Especialidade: ${it.especialidade}, justificativa: ${it.justificativa}" }}
        Exames: $exames"""
}

data class HaocEncaminhamento(
    val especialidade: String,
    val justificativa: String
): JsonSerializable

data class HaocProcedimento(
    val nome: String,
    val data: LocalDateTime?,
    val estado: String
): JsonSerializable

data class HaocDiagnostico(
    val descricao: String,
    val cid: String,
    val categoria: String
): JsonSerializable {
    fun fullDiagnostico() = "$categoria - $cid - $descricao"
}

data class HaocMedicamento(
    val utilizadosNoPA: String? = null,
    val prescritos: String? = null
)

data class HaocProfissional(
    val nome: String,
    val ocupacao: String,
    val uf: String,
    val conselho: String,
    val numeroRegistro: String
): JsonSerializable

data class HaocExameFisico(
    val sinaisVitais: SinaisVitais? = null,
    val medicoes: HaocMedicoes? = null,
    val geral: HaocExameFisicoGeral? = null,
    val cabecaPescoco: String? = null,
    val torax: String? = null,
    val cardiovascular: HaocExameFisicoCardiovascular? = null,
    val abdominal: String? = null,
    val locomotor: String? = null,
): JsonSerializable

data class HaocExameFisicoCardiovascular(
    val PerfusaoCutanea: String? = null,
    val LivedoReticular: Boolean? = null,
    val RitmoCardiaco: String? = null,
    val Sopro: Boolean? = null
): JsonSerializable

data class HaocExameFisicoGeral(
    val estadoGeral: String? = null,
    val grauPalidez: String? = null,
    val presencaIctericia: String? = null,
    val presencaCianose: String? = null,
    val grauHidratacao: String? = null,
    val descricao: String? = null
): JsonSerializable

data class HaocMedicoes(
    val peso: BigDecimal? = null,
    val altura: BigDecimal? = null,
    val perimetroCefalico: BigDecimal? = null,
    val circunferenciaCintura: BigDecimal? = null
): JsonSerializable

data class SinaisVitais(
    val pressaoArterial: PressaoArterial? = null,
    val frequenciaCardiaca: BigDecimal? = null,
    val frequenciaRespiratoria: BigDecimal? = null,
    val temperaturaCorporal: BigDecimal? = null,
    val oximetria: BigDecimal? = null
): JsonSerializable

data class PressaoArterial(
    val sistolica: BigDecimal?,
    val diastolica: BigDecimal?,
    val posicaoIndividuo: String? = null,
    val localAfericao: String? = null
): JsonSerializable

data class HaocAlergia(
    val agente: String,
    val categoria: String,
    val manifestacao: String? = null,
    val grauCerteza: String? = null,
    val criticidade: String? = null,
    val dataHora: LocalDateTime? = null,
    val evolucao: String? = null
): JsonSerializable

data class EscalaAvaliacao(
    val classificacao: String,
    val classificador: String,
    val escalaDor: Int? = null,
    val escalaComaGlasgow: EscalaComaGlasgow? = null
): JsonSerializable

data class EscalaComaGlasgow(
    val respostaOcular: EscalaComaGlasgowValue,
    val respostaVerbal: EscalaComaGlasgowValue,
    val RespostaMotora: EscalaComaGlasgowValue,
    val pontuacaoTotal: Int
): JsonSerializable

data class EscalaComaGlasgowValue(
    val description: String,
    val value: Int
): JsonSerializable

enum class Unidade(val cnesCode: String) {
    VERGUEIRO("2078597"),
    PAULISTA("2076950")
}
