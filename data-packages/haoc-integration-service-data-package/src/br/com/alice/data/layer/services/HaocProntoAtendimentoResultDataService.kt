package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HaocProntoAtendimentoResult
import br.com.alice.data.layer.services.HaocProntoAtendimentoResultDataService.FieldOptions
import br.com.alice.data.layer.services.HaocProntoAtendimentoResultDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface HaocProntoAtendimentoResultDataService : Service,
    Finder<FieldOptions, OrderingOptions, HaocProntoAtendimentoResult>,
    Getter<HaocProntoAtendimentoResult>,
    Adder<HaocProntoAtendimentoResult> {

    override val namespace: String
        get() = "haoc_integration"
    override val serviceName: String
        get() = "pronto_atendimento_result"

    class PersonIdField : Field.TableIdField(HaocProntoAtendimentoResult::personId)
    class IdField : Field.UUIDField(HaocProntoAtendimentoResult::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val id = IdField()
    }

    class CreatedAt : Field.DateTimeField(HaocProntoAtendimentoResult::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<HaocProntoAtendimentoResult, Throwable>
    override suspend fun add(model: HaocProntoAtendimentoResult): Result<HaocProntoAtendimentoResult, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HaocProntoAtendimentoResult>, Throwable>
}
