package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class HaocClaimProcess(
    override val personId: PersonId,
    val status: HaocClaimStatus,
    val type: HaocClaimType,
    val unit: HaocUnit,
    val claimId: Int? = null,
    val healthEventId: UUID? = null,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now()
) : Model, PersonReference

enum class HaocClaimStatus {
    PENDING, DONE
}

enum class HaocClaimType {
    HOSPITALIZATION, HOSPITALIZATION_EXTENSION, EMERGENCY_CARE
}

fun HaocClaimType.haocDocumentType(): HaocDocumentType =
    if (this == HaocClaimType.EMERGENCY_CARE)
        HaocDocumentType.PRONTO_ATENDIMENTO
    else
        HaocDocumentType.SUMARIO_DE_ALTA

enum class HaocUnit {
    PAULISTA, VERGUEIRO
}
