package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HaocClaimProcess
import br.com.alice.data.layer.models.HaocClaimStatus
import br.com.alice.data.layer.models.HaocClaimType
import br.com.alice.data.layer.models.HaocUnit
import br.com.alice.data.layer.services.HaocClaimProcessDataService.FieldOptions
import br.com.alice.data.layer.services.HaocClaimProcessDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface HaocClaimProcessDataService : Service,
    Finder<FieldOptions, OrderingOptions, HaocClaimProcess>,
    Counter<FieldOptions, OrderingOptions, HaocClaimProcess>,
    Adder<HaocClaimProcess>,
    Updater<HaocClaimProcess>,
    Getter<HaocClaimProcess> {

    override val namespace: String
        get() = "haoc_integration"
    override val serviceName: String
        get() = "claim_process"

    class StatusField: Field.TextField(HaocClaimProcess::status) {
        fun eq(value: HaocClaimStatus) = Predicate.eq(this, value.name)
    }

    class ClaimTypeField: Field.TextField(HaocClaimProcess::type) {
        fun eq(value: HaocClaimType) = Predicate.eq(this, value.name)
    }

    class UnitField: Field.TextField(HaocClaimProcess::unit) {
        fun eq(value: HaocUnit) = Predicate.eq(this, value.name)
    }

    class PersonIdField: Field.UUIDField(HaocClaimProcess::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class ClaimIdField: Field.IntegerField(HaocClaimProcess::claimId) {
        fun isNull() = Predicate.isNull(this)
    }
    class HealthEventIdField: Field.UUIDField(HaocClaimProcess::healthEventId) {
        fun isNull() = Predicate.isNull(this)
    }

    class CreatedAtField: Field.DateTimeField(HaocClaimProcess::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class FieldOptions {
        val status = StatusField()
        val personId = PersonIdField()
        val claimId = ClaimIdField()
        val healthEventId = HealthEventIdField()
        val type = ClaimTypeField()
        val unit = UnitField()
        val createdAt = CreatedAtField()
    }
    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: HaocClaimProcess): Result<HaocClaimProcess, Throwable>
    override suspend fun update(model: HaocClaimProcess): Result<HaocClaimProcess, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HaocClaimProcess>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun get(id: UUID): Result<HaocClaimProcess, Throwable>
}
