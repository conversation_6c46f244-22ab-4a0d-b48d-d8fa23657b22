package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class HaocSumarioDeAltaResult(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val claimId: Int?,
    val caracterizacaoAtendimento: HaocCaracterizacaoChegada,
    val motivoAdmissao: HaocMotivoAdmissao,
    val restricoesFuncionais: List<String> = emptyList(),
    val procedimentos: List<HaocProcedimentoRealizado> = emptyList(),
    val evolucaoClinica: String,
    val alergias: List<HaocAlergia> = emptyList(),
    val medicamentos: List<String> = emptyList(),
    val planoCuidado: List<HaocPlanoCuidado> = emptyList(),
    val desfecho: HaocDesfechoInternacao,
    val haocDocumentId: UUID,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, PersonReference, ExternalHealthInformation

data class HaocCaracterizacaoChegada(
    val procedencia: String,
    val local: String,
    val caraterDaInternacao: String,
    val modalidateAssistencial: String? = null,
    val dataInternacao: LocalDateTime,
): JsonSerializable

data class HaocMotivoAdmissao(
    val diagnostico: List<HaocProblemaDiagnostico>,
    val categoria: String? = null,
    val indicador: String? = null,
    val estadoResolucao: String? = null
): JsonSerializable

data class HaocProblemaDiagnostico(
    val code: String,
    val diagnostico: String
): JsonSerializable {
    fun fullDiagnostico() = "$code - $diagnostico"
}

data class HaocProcedimentoRealizado(
    val nome: String,
    val data: LocalDateTime,
    val estado: String,
    val observacoes: String? = null
): JsonSerializable

data class HaocDesfechoInternacao(
    val motivo: String,
    val dataSaida: LocalDateTime,
    val diasUTI: Int,
    val encaminhamento: String? = null,
    val encaminhamentoEspecialidade: List<String> = emptyList(),
    val motivoEncaminhamento: String? = null,
    val encaminhamentoExames: List<String> = emptyList(),
    val profissionalDeAlta: HaocProfissional,
    val informacoesAdicionais: String? = null
): JsonSerializable
