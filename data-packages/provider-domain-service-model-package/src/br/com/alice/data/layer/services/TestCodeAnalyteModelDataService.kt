package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ProviderTestCode
import br.com.alice.data.layer.models.ProviderTestCodeModel
import br.com.alice.data.layer.models.TestCodeAnalyteModel
import br.com.alice.data.layer.services.TestCodeAnalyteModelDataService.FieldOptions
import br.com.alice.data.layer.services.TestCodeAnalyteModelDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface TestCodeAnalyteModelDataService : Service,
    Adder<TestCodeAnalyteModel>,
    Finder<FieldOptions, OrderingOptions, TestCodeAnalyteModel>,
    Updater<TestCodeAnalyteModel>,
    Deleter<TestCodeAnalyteModel>,
    Getter<TestCodeAnalyteModel> {

    override val namespace: String
        get() = "provider"
    override val serviceName: String
        get() = "test_code_analyte"

    class IdField : Field.UUIDField(TestCodeAnalyteModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class AnalyteIdField : Field.TextField(TestCodeAnalyteModel::analyteId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }
    class AnalyteNameField : Field.TextField(TestCodeAnalyteModel::analyteName)
    class CodeAliceField : Field.TextField(TestCodeAnalyteModel::codeAlice)
    class CodeItemField : Field.TextField(TestCodeAnalyteModel::codeItem) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class ProviderField : Field.TextField(TestCodeAnalyteModel::provider) {
        fun eq(value: ProviderTestCode.Brand) = Predicate.eq(this, value)
    }

    class SearchTokensField : Field.TextField(TestCodeAnalyteModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val analyteId = AnalyteIdField()
        val analyteName = AnalyteNameField()
        val codeAlice = CodeAliceField()
        val codeItem = CodeItemField()
        val provider = ProviderField()
        val searchTokens = SearchTokensField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: TestCodeAnalyteModel): Result<TestCodeAnalyteModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<TestCodeAnalyteModel>, Throwable>
    override suspend fun update(model: TestCodeAnalyteModel): Result<TestCodeAnalyteModel, Throwable>
    override suspend fun get(id: UUID): Result<TestCodeAnalyteModel, Throwable>
    override suspend fun delete(model: TestCodeAnalyteModel): Result<Boolean, Throwable>
}
