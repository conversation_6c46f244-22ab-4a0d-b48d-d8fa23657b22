package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ProviderTestCode
import br.com.alice.data.layer.models.ProviderTestCodeDataIntegrationModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProviderTestCodeDataIntegrationModelDataService : Service,
    Adder<ProviderTestCodeDataIntegrationModel>,
    Finder<ProviderTestCodeDataIntegrationModelDataService.FieldOptions, ProviderTestCodeDataIntegrationModelDataService.OrderingOptions, ProviderTestCodeDataIntegrationModel>,
    Getter<ProviderTestCodeDataIntegrationModel> {

    override val namespace: String
        get() = "provider_test_code"
    override val serviceName: String
        get() = "data_integration"

    class DataIntegrationId : Field.UUIDField(ProviderTestCodeDataIntegrationModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ProviderCode : Field.UUIDField(ProviderTestCodeDataIntegrationModel::provider) {
        fun eq(value: ProviderTestCode.Brand) = Predicate.eq(this, value)
        fun inList(value: List<ProviderTestCode.Brand>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val dataIntegrationId = DataIntegrationId()
        val providerCode = ProviderCode()
    }

    class OrderingOptions {
        val providerCode = ProviderCode()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: ProviderTestCodeDataIntegrationModel): Result<ProviderTestCodeDataIntegrationModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ProviderTestCodeDataIntegrationModel>, Throwable>
    override suspend fun get(id: UUID): Result<ProviderTestCodeDataIntegrationModel, Throwable>
}
