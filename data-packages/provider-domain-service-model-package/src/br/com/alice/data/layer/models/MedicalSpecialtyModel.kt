package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class MedicalSpecialtyModel(
    val name: String,
    val type: MedicalSpecialtyType,
    val parentSpecialtyId: UUID? = null,
    val requireSpecialist: Boolean = false,
    val generateGeneralistSubSpecialty: Boolean = false,
    val active: Boolean = true,
    val cboCode: CboCode? = null,
    val internal: Boolean = false,
    val urlSlug: String,
    val attentionLevel: AttentionLevel? = null,
    val isTherapy: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
    val isAdvancedAccess: Boolean = false,
    override val id: UUID = RangeUUID.generate()
) : Model {

    data class CboCode(
        val id: UUID,
        val code: String,
        val description: String
    ) : JsonSerializable

}
