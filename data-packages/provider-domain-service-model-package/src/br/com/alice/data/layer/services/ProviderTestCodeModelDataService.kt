package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ProviderTestCode
import br.com.alice.data.layer.models.ProviderTestCodeModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProviderTestCodeModelDataService : Service,
    Adder<ProviderTestCodeModel>,
    Finder<ProviderTestCodeModelDataService.FieldOptions, ProviderTestCodeModelDataService.OrderingOptions, ProviderTestCodeModel>,
    Counter<ProviderTestCodeModelDataService.FieldOptions, ProviderTestCodeModelDataService.OrderingOptions, ProviderTestCodeModel>,
    Updater<ProviderTestCodeModel>,
    Deleter<ProviderTestCodeModel>,
    Getter<ProviderTestCodeModel> {

    override val namespace: String
        get() = "provider"
    override val serviceName: String
        get() = "provider_test_code"

    class TestCodeId : Field.UUIDField(ProviderTestCodeModel::testCodeId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ProviderBrand : Field.TextField(ProviderTestCodeModel::providerBrand) {
        fun eq(value: ProviderTestCode.Brand) = Predicate.eq(this, value)
    }

    class ProviderId : Field.UUIDField(ProviderTestCodeModel::providerId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ProviderCode : Field.TextField(ProviderTestCodeModel::providerCode) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val testCodeId = TestCodeId()
        val providerId = ProviderId()
        val providerBrand = ProviderBrand()
        val providerCode = ProviderCode()
    }

    class OrderingOptions {
        val providerCode = ProviderCode()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: ProviderTestCodeModel): Result<ProviderTestCodeModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ProviderTestCodeModel>, Throwable>
    override suspend fun get(id: UUID): Result<ProviderTestCodeModel, Throwable>
    override suspend fun update(model: ProviderTestCodeModel): Result<ProviderTestCodeModel, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun delete(model: ProviderTestCodeModel): Result<Boolean, Throwable>

}
