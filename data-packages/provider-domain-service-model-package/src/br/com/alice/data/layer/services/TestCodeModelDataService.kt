package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.TestCodeModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface TestCodeModelDataService : Service,
    Finder<TestCodeModelDataService.FieldOptions, TestCodeModelDataService.OrderingOptions, TestCodeModel>,
    Updater<TestCodeModel>,
    Getter<TestCodeModel>,
    Counter<TestCodeModelDataService.FieldOptions, TestCodeModelDataService.OrderingOptions, TestCodeModel>,
    Adder<TestCodeModel> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "test_code"

    class Id: Field.UUIDField(TestCodeModel::id) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }
    class SearchTokens: Field.TextField(TestCodeModel::searchTokens) {
        fun search(value: String) = Predicate.search(this, value)
        fun rankedSearch(value: String) = Predicate.rankedSearch(this, value)
    }

    class Code: Field.TextField(TestCodeModel::code) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class Active : Field.BooleanField(TestCodeModel::active)

    class FieldOptions {
        val searchTokens = SearchTokens()
        val code = Code()
        val id = Id()
        val active = Active()
    }

    class OrderingOptions {
        val code = Code()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<TestCodeModel, Throwable>
    override suspend fun add(model: TestCodeModel): Result<TestCodeModel, Throwable>
    override suspend fun update(model: TestCodeModel): Result<TestCodeModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<TestCodeModel>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
}
