package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class TestCodeModel(
    val code: String,
    val description: String,
    val internalDescription: String,
    val priority: Boolean,
    val searchTokens: String? = null,
    val synonyms: List<String> = emptyList(),
    val preparationId: UUID? = null,
    val sensitiveResult: Boolean = false,
    val resultExpirationInDays: Int = 180,
    val active: Boolean = true,
    val version: Int = 0,
    override val id: UUID = RangeUUID.generate(),
    val createdAt: LocalDateTime = LocalDateTime.now()
) : Model {
    override fun sanitize(): Model = this.copy(
        code = code.trim(),
        description = description.trim()
    )
}
