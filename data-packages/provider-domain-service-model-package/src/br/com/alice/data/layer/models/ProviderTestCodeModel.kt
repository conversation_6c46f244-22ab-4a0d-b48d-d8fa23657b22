package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.util.UUID

data class ProviderTestCodeModel(
    override val id: UUID = RangeUUID.generate(),
    val providerId: UUID?,
    val testCodeId: UUID,
    val providerCode: String,
    val providerBrand: ProviderTestCode.Brand,
    val version: Int = 0,
    val dataIntegration: List<UUID> = emptyList(),
) : Model
