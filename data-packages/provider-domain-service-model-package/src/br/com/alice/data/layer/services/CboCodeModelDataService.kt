package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.CboCodeModel
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface CboCodeModelDataService : Service,
    Finder<CboCodeModelDataService.FieldOptions, CboCodeModelDataService.OrderingOptions, CboCodeModel>,
    Counter<CboCodeModelDataService.FieldOptions, CboCodeModelDataService.OrderingOptions, CboCodeModel>,
    Adder<CboCodeModel>,
    Getter<CboCodeModel> {

    override val namespace: String
        get() = "provider"
    override val serviceName: String
        get() = "cbo_code"

    class IdField : Field.UUIDField(CboCodeModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class CodeField : Field.TextField(CboCodeModel::code) {
        fun eq(value: String) = Predicate.eq(this, value)
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class DescriptionField : Field.TextField(CboCodeModel::description) {
        fun eq(value: String) = Predicate.eq(this, value)
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val code = CodeField()
        val description = DescriptionField()
    }

    class OrderingOptions {
        val code = CodeField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<CboCodeModel, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun add(model: CboCodeModel): Result<CboCodeModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<CboCodeModel>, Throwable>
}
