package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.extensions.onlyDigits
import java.util.UUID

data class CboCodeModel(
    override val id: UUID = RangeUUID.generate(),
    val code: String,
    val description: String,
    val version: Int = 0,
): Model {

    override fun sanitize() = this.copy(
        code = this.code.onlyDigits().trim()
    )
}
