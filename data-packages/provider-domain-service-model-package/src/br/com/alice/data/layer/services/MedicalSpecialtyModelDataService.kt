package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.MedicalSpecialtyModel
import br.com.alice.data.layer.models.MedicalSpecialtyType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MedicalSpecialtyModelDataService : Service,
    Finder<MedicalSpecialtyModelDataService.FieldOptions, MedicalSpecialtyModelDataService.OrderingOptions, MedicalSpecialtyModel>,
    Counter<MedicalSpecialtyModelDataService.FieldOptions, MedicalSpecialtyModelDataService.OrderingOptions, MedicalSpecialtyModel>,
    Adder<MedicalSpecialtyModel>,
    Getter<MedicalSpecialtyModel>,
    Updater<MedicalSpecialtyModel> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "medical_specialty"

    class CreatedAt: Field.DateTimeField(MedicalSpecialtyModel::createdAt)

    class Id : Field.UUIDField(MedicalSpecialtyModel::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Type: Field.TextField(MedicalSpecialtyModel::type) {
        fun eq(value: MedicalSpecialtyType) = Predicate.eq(this, value)
        fun inList(value: List<MedicalSpecialtyType>) = Predicate.inList(this, value)
    }

    class Name: Field.TextField(MedicalSpecialtyModel::name) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class ParentSpecialtyId: Field.UUIDField(MedicalSpecialtyModel::parentSpecialtyId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ActiveField: Field.BooleanField(MedicalSpecialtyModel::active)

    class Internal: Field.BooleanField(MedicalSpecialtyModel::internal)

    class CBOCode : Field.JsonbField(MedicalSpecialtyModel::cboCode) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: String) = Predicate.jsonSearch(this, "{\"code\":\"$value\"}")
    }

    class UrlSlug : Field.TextField(MedicalSpecialtyModel::urlSlug) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class IsTherapeuticSpecialtyField: Field.BooleanField(MedicalSpecialtyModel::isTherapy)

    class IsAdvancedAccessField: Field.BooleanField(MedicalSpecialtyModel::isAdvancedAccess)

    class FieldOptions {
        val id = Id()
        val type = Type()
        val name = Name()
        val parentSpecialtyId = ParentSpecialtyId()
        val active = ActiveField()
        val internal = Internal()
        val urlSlug = UrlSlug()
        val isTherapy = IsTherapeuticSpecialtyField()
        val cboCode = CBOCode()
        val isAdvancedAccess = IsAdvancedAccessField()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
        val name = Name()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<MedicalSpecialtyModel, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun add(model: MedicalSpecialtyModel): Result<MedicalSpecialtyModel, Throwable>
    override suspend fun update(model: MedicalSpecialtyModel): Result<MedicalSpecialtyModel, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<MedicalSpecialtyModel>, Throwable>
}
