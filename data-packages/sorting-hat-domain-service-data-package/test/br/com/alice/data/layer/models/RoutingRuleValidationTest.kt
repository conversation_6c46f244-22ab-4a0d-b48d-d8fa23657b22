package br.com.alice.data.layer.models

import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import kotlin.test.Test

class RoutingRuleValidationTest {

    @Test
    fun `#match for null returns false`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.EQUALS,
            value = 10
        )

        val result = validation.match(null)
        assertThat(result).isEqualTo(false)
    }

    @Test
    fun `#match for operator EQUALS for number`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.EQUALS,
            value = 10
        )

        val value = 10

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator EQUALS for String`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.EQUALS,
            value = "AA"
        )

        val value = "AA"

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator EQUALS for List string`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.EQUALS,
            value = listOf("A", "B")
        )

        val value = listOf("a", "b")

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator EQUALS for List number`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.EQUALS,
            value = listOf(1, 2)
        )

        val value = listOf(1, 2)

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator EQUALS for List Any`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.EQUALS,
            value = listOf(true, listOf(1))
        )

        val value = listOf(true, listOf(1))

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator EQUALS for Any`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.EQUALS,
            value = true
        )

        val value = true

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator GREATER`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.GREATER,
            value = 9
        )

        val value = 10

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator GREATER for string number`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.GREATER,
            value = "9.1"
        )

        val value = "10.2"

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator GREATER for invalid string type`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.GREATER,
            value = "true"
        )

        val value = 10

        assertThatThrownBy { validation.match(value) }
            .isInstanceOf(IllegalArgumentException::class.java)
            .hasMessage("Value true cannot be converted to double")
    }

    @Test
    fun `#match for operator GREATER for invalid type`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.GREATER,
            value = true
        )

        val value = 10

        assertThatThrownBy { validation.match(value) }
            .isInstanceOf(IllegalArgumentException::class.java)
            .hasMessage("Value true cannot be converted to double")
    }

    @Test
    fun `#match for operator GREATER_EQUALS for equals`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.GREATER_EQUALS,
            value = 10
        )

        val value = 10

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator GREATER_EQUALS for greater`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.GREATER_EQUALS,
            value = 10
        )

        val value = 11

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator LESS`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.LESS,
            value = 10
        )

        val value = 9

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator LESS_EQUALS for equals`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.LESS_EQUALS,
            value = 10
        )

        val value = 10

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator LESS_EQUALS for less`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.LESS_EQUALS,
            value = 10
        )

        val value = 9

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator CONTAINS for string`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.CONTAINS,
            value = listOf("A", "B")
        )

        val value = "A"

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator CONTAINS for Number`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.CONTAINS,
            value = listOf(10.1, 12.5)
        )

        val value = 12.5

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator CONTAINS for list`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.AGE,
            operator = RoutingRuleValidationOperator.CONTAINS,
            value = listOf(10.1, 12.5)
        )

        val value = listOf(12.5)

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }

    @Test
    fun `#match for operator CONTAINS for list with single value`() {
        val validation = RoutingRuleValidation(
            type = RoutingRuleValidationType.TAG,
            operator = RoutingRuleValidationOperator.CONTAINS,
            value = "A"
        )

        val value = listOf("A", "B")

        val result = validation.match(value)
        assertThat(result).isEqualTo(true)
    }
}
