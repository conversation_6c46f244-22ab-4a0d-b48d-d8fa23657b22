package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.extensions.containsAny
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class RoutingRule(
    override val id: UUID = RangeUUID.generate(),
    val name: String,
    val type: RoutingHistoryModel,
    val content: RoutingRuleContent,
    val active: Boolean = true,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model

data class RoutingRuleContent(
    val result: List<String>,
    val priority: Int,
    val validations: List<RoutingRuleValidation>
) : JsonSerializable

data class RoutingRuleValidation(
    val type: RoutingRuleValidationType,
    val operator: RoutingRuleValidationOperator,
    val value: Any
) : JsonSerializable {

    @Suppress("UNCHECKED_CAST")
    fun <T : Any?> match(checkValue: T) =
        if (checkValue == null) false
        else when (operator) {
            RoutingRuleValidationOperator.EQUALS -> {
                when (checkValue) {
                    is String -> checkValue.unFormat() == (value as String).unFormat()
                    is Number -> checkValue.toNumber() == (value as Number).toNumber()
                    is List<*> -> checkValue.map { (it as Any).baseFormat() } == (value as List<Any>).map { it.baseFormat() }
                    else -> checkValue == value as T
                }
            }
            RoutingRuleValidationOperator.GREATER -> checkValue.toNumber() > value.toNumber()
            RoutingRuleValidationOperator.GREATER_EQUALS -> checkValue.toNumber() >= value.toNumber()
            RoutingRuleValidationOperator.LESS -> checkValue.toNumber() < value.toNumber()
            RoutingRuleValidationOperator.LESS_EQUALS -> checkValue.toNumber() <= value.toNumber()
            RoutingRuleValidationOperator.CONTAINS -> {
                if (checkValue is String) (value as List<String>).map { it.unFormat() }.contains(checkValue.unFormat())
                else if (checkValue is Number) (value as List<Number>).map { it.toNumber() }.contains(checkValue.toNumber())
                else if (checkValue is List<*> && value !is List<*>) (checkValue as List<*>).contains(value)
                else if (checkValue is List<*>) (value as List<*>).containsAny(checkValue)
                else (value as List<T>).contains(checkValue)
            }
        }

    private fun Any.toNumber() =
        try {
            when (this) {
                is String -> toDouble()
                is Number -> toDouble()
                else -> throw IllegalArgumentException()
            }
        } catch (ex: Exception) {
            throw IllegalArgumentException("Value $this cannot be converted to double", ex)
        }

    private fun String.unFormat() = this.lowercase().unaccent()
    private fun Any.baseFormat() =
        when (this) {
            is String -> unFormat()
            is Number -> toNumber()
            else -> this
        }
}

enum class RoutingRuleValidationType(val description: String) {
    AGE("Idade"),
    CITY("Cidade"),
    TAG("Tag"),
    TIER("Tier"),
    BRAND("Brand")
}

enum class RoutingRuleValidationOperator(val description: String) {
    EQUALS("Igual (=)"),
    GREATER("Maior (>)"),
    GREATER_EQUALS("Maior Igual (≥)"),
    LESS("Menor (<)"),
    LESS_EQUALS("Menos Igual (≤)"),
    CONTAINS("Contém (⊆)")
}
