package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.RoutingHistory
import com.github.kittinunf.result.Result

@RemoteService
interface RoutingHistoryDataService : Service,
    Adder<RoutingHistory>,
    Finder<RoutingHistoryDataService.FieldOptions, RoutingHistoryDataService.OrderingOptions, RoutingHistory>
{

    override val namespace: String
        get() = "sorting_hat"
    override val serviceName: String
        get() = "routing_history"

    class InputIdField: Field.JsonbField(RoutingHistory::input) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun eq(value: String) = Predicate.jsonSearch(this, "{\"id\": \"$value\"}")
    }

    class CreatedAtField: Field.DateTimeField(RoutingHistory::createdAt)

    class FieldOptions {
        val inputId = InputIdField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )


    override suspend fun add(model: RoutingHistory): Result<RoutingHistory, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<RoutingHistory>, Throwable>
}
