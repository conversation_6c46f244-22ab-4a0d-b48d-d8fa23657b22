package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import java.time.LocalDateTime
import java.util.UUID

data class PersonTeamAssociation(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val healthcareTeamId: UUID,
    val healthcareAdditionalTeamId: UUID? = null,
    val risk: RiskDescription? = null,
    val status: PersonTeamAssociationStatus,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
): Model, PersonReference, HealthInformation

enum class PersonTeamAssociationStatus {
    ACTIVE, INACTIVE
}
