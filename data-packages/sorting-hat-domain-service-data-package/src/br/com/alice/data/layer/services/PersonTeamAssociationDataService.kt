package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.PersonTeamAssociation
import br.com.alice.data.layer.models.PersonTeamAssociationStatus
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface PersonTeamAssociationDataService  : Service,
    Finder<PersonTeamAssociationDataService.FieldOptions, PersonTeamAssociationDataService.OrderingOptions, PersonTeamAssociation>,
    Counter<PersonTeamAssociationDataService.FieldOptions, PersonTeamAssociationDataService.OrderingOptions, PersonTeamAssociation>,
    Getter<PersonTeamAssociation>,
    Adder<PersonTeamAssociation>,
    Updater<PersonTeamAssociation> {

    override val namespace: String get() = "sorting-hat"
    override val serviceName: String get() = "person-team-association"

    class PersonIdField: Field.UUIDField(PersonTeamAssociation::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class HealthcareTeamIdField: Field.UUIDField(PersonTeamAssociation::healthcareTeamId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class HealthcareAdditionalTeamIdField: Field.UUIDField(PersonTeamAssociation::healthcareAdditionalTeamId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class StatusField: Field.TextField(PersonTeamAssociation::status) {
        fun eq(value: PersonTeamAssociationStatus) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val healthcareTeamId = HealthcareTeamIdField()
        val healthcareAdditionalTeamId = HealthcareAdditionalTeamIdField()
        val status = StatusField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<PersonTeamAssociation>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun get(id: UUID): Result<PersonTeamAssociation, Throwable>
    override suspend fun add(model: PersonTeamAssociation): Result<PersonTeamAssociation, Throwable>
    override suspend fun update(model: PersonTeamAssociation): Result<PersonTeamAssociation, Throwable>
}
