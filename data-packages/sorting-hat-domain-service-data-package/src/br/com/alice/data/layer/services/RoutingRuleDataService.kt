package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.RoutingHistoryModel
import br.com.alice.data.layer.models.RoutingRule
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface RoutingRuleDataService : Service,
    Finder<RoutingRuleDataService.FieldOptions, RoutingRuleDataService.OrderingOptions, RoutingRule>,
    Counter<RoutingRuleDataService.FieldOptions, RoutingRuleDataService.OrderingOptions, RoutingRule>,
    Getter<RoutingRule>,
    Adder<RoutingRule>,
    Updater<RoutingRule>
{

    override val namespace: String
        get() = "sorting_hat"
    override val serviceName: String
        get() = "routing_rule"

    class IdField: Field.UUIDField(RoutingRule::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(values: List<UUID>) = Predicate.inList(this, values)
    }

    class TypeField: Field.TextField(RoutingRule::type) {
        fun eq(value: RoutingHistoryModel) = Predicate.eq(this, value)
    }

    class ActiveField: Field.BooleanField(RoutingRule::active)

    class NameField: Field.TextField(RoutingRule::name) {
        @LikePredicateUsage
        fun like(value: String) = Predicate.like(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val type = TypeField()
        val name = NameField()
        val active = ActiveField()
    }

    class OrderingOptions {
        val type = TypeField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<RoutingRule>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun get(id: UUID): Result<RoutingRule, Throwable>
    override suspend fun add(model: RoutingRule): Result<RoutingRule, Throwable>
    override suspend fun update(model: RoutingRule): Result<RoutingRule, Throwable>
    
}
