package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.MedicalSpecialtyType.SUBSPECIALTY
import java.time.LocalDateTime
import java.util.UUID

data class MedicalSpecialty(
    val name: String,
    val type: MedicalSpecialtyType,
    val parentSpecialtyId: UUID? = null,
    val requireSpecialist: Boolean = false,
    val generateGeneralistSubSpecialty: Boolean = false,
    val active: Boolean = true,
    val cboCode: CboCode? = null,
    val internal: Boolean = false,
    val urlSlug: String,
    val attentionLevel: AttentionLevel? = null,
    val isTherapy: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
    val id: UUID = RangeUUID.generate(),
    val isAdvancedAccess: Boolean = false,
) {

    data class CboCode(
        val id: UUID,
        val code: String,
        val description: String
    ) : JsonSerializable

    fun isOrphanSubSpecialty(): Boolean {
        return type == SUBSPECIALTY && parentSpecialtyId == null
    }

    fun isSecondaryAttentionLevelAndNotTherapy() =
        this.isSecondaryAttentionLevel() && !this.isTherapy

    fun isSecondaryAttentionLevelAndTherapy() =
        this.isSecondaryAttentionLevel() && this.isTherapy

    fun isSecondaryAttentionLevel() =
        this.attentionLevel != null && this.attentionLevel == AttentionLevel.SECONDARY

    fun getAppointmentName() = if (this.isTherapy) "Sessão de ${this.name}" else "Consulta de ${this.name}"

}


enum class MedicalSpecialtyType {
    SPECIALTY, SUBSPECIALTY
}

enum class AttentionLevel {
    PRIMARY, SECONDARY, TERTIARY
}
