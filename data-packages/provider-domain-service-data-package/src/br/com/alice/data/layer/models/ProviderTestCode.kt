package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import java.util.UUID

data class ProviderTestCode(
    val id: UUID = RangeUUID.generate(),
    val providerId: UUID?,
    val testCodeId: UUID,
    val providerCode: String,
    val providerBrand: Brand,
    val version: Int = 0,
    val dataIntegration: List<UUID> = emptyList(),
) {

    enum class Brand {
        DB, FLEURY, A_MAIS, DELBONI_AURIEMO, SALOMAO_ZOPPI, ALTA, LAVOISIER, DASA, LABI
    }

}
