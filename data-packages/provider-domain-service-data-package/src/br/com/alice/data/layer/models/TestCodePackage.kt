package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class TestCodePackage(
    val name: String,
    val testCodeIds: Set<UUID> = emptySet(),
    val searchTokens: String? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
    val id: UUID = RangeUUID.generate()
): JsonSerializable
