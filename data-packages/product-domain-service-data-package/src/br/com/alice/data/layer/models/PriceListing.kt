package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.extensions.money
import br.com.alice.common.serialization.JsonSerializable
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class PriceListing(
    val id: UUID = RangeUUID.generate(),
    val title: String,
    val items: List<PriceListingItem>,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
): JsonSerializable {

    fun getPrice(age: Int): BigDecimal? = items.getPrice(age)

    fun getPriceListItemTitle(age: Int): String? = items.find { it.minAge <= age && age <= it.maxAge }?.title
}

data class PriceListingItem(
    val minAge: Int,
    val maxAge: Int,
    val amount: BigDecimal,
    val priceAdjustment: BigDecimal? = 0.money,
): JsonSerializable {
    val title get() = if (maxAge >= Int.MAX_VALUE) "$minAge+ anos" else "De $minAge a $maxAge anos"
}

fun List<PriceListingItem>.getPrice(age: Int): BigDecimal? = find { it.minAge <= age && age <= it.maxAge }?.amount
