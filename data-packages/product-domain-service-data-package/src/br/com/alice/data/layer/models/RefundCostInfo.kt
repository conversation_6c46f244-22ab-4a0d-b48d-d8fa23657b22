package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class RefundCostInfo(
    val id: UUID = RangeUUID.generate(),
    val event: String,
    val description: String? = null,
    val type: RefundEventType,
    val prices: List<RefundTierPrice> = emptyList(),
    val index: Int = 0,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
)

enum class RefundEventType(val description: String) {
    APPOINTMENT("Consulta"),
    THERAPY("Terapia"),
    MEDICAL_FEES("Honorários médicos"),
}

data class RefundTierPrice(
    val tier: TierType,
    val value: BigDecimal,
)
