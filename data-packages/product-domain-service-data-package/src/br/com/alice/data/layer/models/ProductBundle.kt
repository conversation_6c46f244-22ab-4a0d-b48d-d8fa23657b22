package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.models.SpecialistTier
import java.time.LocalDateTime
import java.util.UUID

data class ProductBundle(
    val name: String,
    val providerIds: List<UUID> = emptyList(),
    val specialistIds: List<UUID> = emptyList(),
    val externalSpecialists: List<UUID> = emptyList(),
    val type: ProductBundleType,
    val specialtyTiers: List<SpecialtyTiers> = emptyList(),
    val imageUrl: String? = null,
    val priceScale: Int = 0,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val active: Boolean = false,
    var updatedBy: UpdatedBy? = null,
) {

    fun isHospitalBundle() =
        this.type == ProductBundleType.HOSPITAL

    fun isLaboratoryBundle() =
        this.type == ProductBundleType.LABORATORY

    fun isMaternityBundle() =
        this.type == ProductBundleType.MATERNITY

    fun isProviderBundle() =
        isHospitalBundle() || isLaboratoryBundle() || isMaternityBundle()

    fun isSpecialistBundle() =
        this.type == ProductBundleType.SPECIALIST

    fun isCassiSpecialistBundle() =
        this.type == ProductBundleType.CASSI_SPECIALIST

    fun isSpecialtyTiersBundle() =
        this.type == ProductBundleType.SPECIALITY_TIERS

    fun isClinicalCommunityBundle() =
        this.type == ProductBundleType.CLINICAL_COMMUNITY

    fun isVaccineBundle() =
        this.type == ProductBundleType.VACCINE
}

enum class ProductBundleType {
    HOSPITAL,
    LABORATORY,
    MATERNITY,
    SPECIALIST,
    SPECIALITY_TIERS,
    CLINICAL_COMMUNITY,
    CASSI_SPECIALIST,
    VACCINE;

    companion object {
        fun hospitalTypes(): List<ProductBundleType> = listOf(
            HOSPITAL,
            MATERNITY,
        )

        fun providerTypes(): List<ProductBundleType> = listOf(
            HOSPITAL,
            MATERNITY,
            LABORATORY
        )
    }
}

data class SpecialtyTiers(
    val tiers: List<SpecialistTier>,
    val specialtyId: UUID
)
