package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class CoveredGeoRegion(
    val name: String,
    val latitude: String,
    val longitude: String,
    val rangeInMeters: Int,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : JsonSerializable
