package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class CoPaymentCostInfo(
    val id: UUID = RangeUUID.generate(),
    val event: String,
    val description: String? = null,
    val chargeType: CoPaymentChargeType,
    val prices: List<CoPaymentTierPrice> = emptyList(),
    val index: Int = 0,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
)

enum class CoPaymentChargeType(val description: String) {
    FIXED_VALUE("Valor fixo"),
    BILLING_CEILING("Teto de cobrança")
}

data class CoPaymentTierPrice(
    val tier: TierType,
    val value: BigDecimal,
)
