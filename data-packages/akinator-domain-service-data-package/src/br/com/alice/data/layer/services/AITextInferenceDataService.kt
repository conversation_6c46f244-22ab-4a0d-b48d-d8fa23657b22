package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.data.layer.models.AITextInference
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AITextInferenceDataService : Service,
    Getter<AITextInference>,
    Adder<AITextInference> {

    override val namespace: String
        get() = "akinator"
    override val serviceName: String
        get() = "ai_text_inference"

    override suspend fun add(model: AITextInference): Result<AITextInference, Throwable>
    override suspend fun get(id: UUID): Result<AITextInference, Throwable>
}
