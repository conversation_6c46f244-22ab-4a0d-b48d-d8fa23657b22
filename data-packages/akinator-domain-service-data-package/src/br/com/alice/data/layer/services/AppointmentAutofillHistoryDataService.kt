package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.data.layer.models.AppointmentAutofillHistory
import com.github.kittinunf.result.Result

@RemoteService
interface AppointmentAutofillHistoryDataService : Service,
    Adder<AppointmentAutofillHistory> {

    override val namespace: String
        get() = "akinator"
    override val serviceName: String
        get() = "appointment_autofill_history"

    override suspend fun add(model: AppointmentAutofillHistory): Result<AppointmentAutofillHistory, Throwable>
}
