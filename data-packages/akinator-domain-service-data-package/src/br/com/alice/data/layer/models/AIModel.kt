package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class AIModel(
    val operationType: AIOperationType,
    val provider: String,
    val name: String,
    val params: List<AIModelParams> = listOf(),
    val apiUrl: String? = null,
    val internal: Boolean = false,
    val costPerKToken: BigDecimal? = null,
    val interfaceHash: String? = null,
    val interfaceContract: Map<String, Any>? = null,

    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val deletedAt: LocalDateTime? = null,
) : Model

data class AIModelParams (
    val name: String,
    val value: String,
    val type: String
) : JsonSerializable

enum class AIOperationType {
    TEXT_TEXT,
    DALYA
}
