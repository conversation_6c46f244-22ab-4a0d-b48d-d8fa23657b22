package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import kotlinx.serialization.Transient
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class AITextInference(
    val setupId: UUID,
    @Transient
    val setupName: String? = null,
    val context: String,
    val caller: String? = null,
    val result: AIInferenceResult,
    val tokensUsed: Int? = null,
    val estimatedCost: BigDecimal? = null,
    val outputs: List<AITextInferenceOutput>,
    val referenceModel: AITextInferenceReferenceModel? = null,

    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model

data class AITextInferenceOutput (
    val model: String,
    val result: AIInferenceResult,
    val cost: BigDecimal? = null,
    val output: String
) : JsonSerializable

enum class AIInferenceResult {
    SUCCESS,
    FAILURE
}

data class AITextInferenceReferenceModel(
    val id: String,
    val model: AITextInferenceReferenceModelOption
) : JsonSerializable

enum class AITextInferenceReferenceModelOption {
    CHANNEL
}
