package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AudioTranscriptionJob
import br.com.alice.data.layer.models.TranscriptionStatus
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AudioTranscriptionJobDataService : Service,
    Adder<AudioTranscriptionJob>,
    Getter<AudioTranscriptionJob>,
    Finder<AudioTranscriptionJobDataService.FieldOptions, AudioTranscriptionJobDataService.OrderingOptions, AudioTranscriptionJob>,
    Updater<AudioTranscriptionJob> {

    override val namespace: String
        get() = "akinator"
    override val serviceName: String
        get() = "audio_transcription_job"

    class IdField : Field.UUIDField(AudioTranscriptionJob::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class ExternalJobIdField : Field.TextField(AudioTranscriptionJob::externalJobId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class StatusField : Field.TextField(AudioTranscriptionJob::status) {
        fun eq(value: TranscriptionStatus) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val id = IdField()
        val externalJobId = ExternalJobIdField()
        val status = StatusField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: AudioTranscriptionJob): Result<AudioTranscriptionJob, Throwable>
    override suspend fun get(id: UUID): Result<AudioTranscriptionJob, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AudioTranscriptionJob>, Throwable>
    override suspend fun update(model: AudioTranscriptionJob): Result<AudioTranscriptionJob, Throwable>
}
