package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class AISetup(
    val name: String,
    val staffId: UUID,
    val operationType: AIOperationType,
    val prompt: String?,
    val models: List<AIModelInstance>,
    val interfaceHash: String? = null,

    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val deletedAt: LocalDateTime? = null,
) : Model


data class AIModelInstance (
    val provider: String,
    val name: String,
    val params: List<AIModelParams> = listOf(),
    val apiKeyEnvVar: String? = null,
) : JsonSerializable {
    fun getParam(name: String): AIModelParams? = params.find { it.name == name }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is AIModelInstance) return false

        if (provider != other.provider) return false
        if (name != other.name) return false

        return true
    }

    override fun hashCode(): Int {
        var result = provider.hashCode()
        result = 31 * result + name.hashCode()
        return result
    }

}
