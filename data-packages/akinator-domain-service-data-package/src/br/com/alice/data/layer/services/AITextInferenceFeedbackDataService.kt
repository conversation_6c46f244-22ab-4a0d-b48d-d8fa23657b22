package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AITextInferenceFeedback
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AITextInferenceFeedbackDataService : Service,
    Getter<AITextInferenceFeedback>,
    Adder<AITextInferenceFeedback>,
    Finder<AITextInferenceFeedbackDataService.FieldOptions, AITextInferenceFeedbackDataService.OrderingOptions, AITextInferenceFeedback>,
    Updater<AITextInferenceFeedback> {

    override val namespace: String
        get() = "akinator"
    override val serviceName: String
        get() = "ai_text_inference_feedback"

    class InferenceIdField : Field.UUIDField(AITextInferenceFeedback::inferenceId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val inferenceId = InferenceIdField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(model: AITextInferenceFeedback): Result<AITextInferenceFeedback, Throwable>
    override suspend fun update(model: AITextInferenceFeedback): Result<AITextInferenceFeedback, Throwable>
    override suspend fun get(id: UUID): Result<AITextInferenceFeedback, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AITextInferenceFeedback>, Throwable>
}
