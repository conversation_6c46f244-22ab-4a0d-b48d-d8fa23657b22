package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class AudioTranscriptionJob(
    val fileUri: String,
    val externalJobId: String,
    val status: TranscriptionStatus,
    val provider: TranscriptionProvider,
    val redactionLevel: RedactionLevel? = null,
    val transcriptionText: String? = null,
    val errorMessage: String? = null,
    val startedAt: LocalDateTime? = null,
    val completedAt: LocalDateTime? = null,
    val referenceModel: AudioTranscriptionJobReferenceModel? = null,

    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model

data class AudioTranscriptionJobReferenceModel(
    val id: String,
    val model: AudioTranscriptionJobReferenceModelOption
) : JsonSerializable

enum class AudioTranscriptionJobReferenceModelOption {
    APPOINTMENT
}

enum class TranscriptionStatus {
    QUEUED,
    PROCESSING,
    COMPLETED,
    FAILED,
    CANCELLED
}

enum class TranscriptionProvider {
    AWS_TRANSCRIBE
}

enum class RedactionLevel {
    MINIMAL,
    MODERATE,
    STRICT
}
