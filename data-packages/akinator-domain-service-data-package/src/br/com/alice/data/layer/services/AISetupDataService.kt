package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.SoftDeleter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AIModel
import br.com.alice.data.layer.models.AISetup
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AISetupDataService : Service,
    Adder<AISetup>,
    Get<PERSON><AISetup>,
    Updater<AISetup>,
    SoftDeleter<AISetup>,
    Finder<AISetupDataService.FieldOptions, AISetupDataService.OrderingOptions, AISetup> {

    override val namespace: String
        get() = "akinator"
    override val serviceName: String
        get() = "ai_setup"

    class StaffIdField : Field.UUIDField(AISetup::staffId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class OperationTypeField : Field.TextField(AISetup::operationType) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val staffId = StaffIdField()
        val operationType = OperationTypeField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun add(setup: AISetup): Result<AISetup, Throwable>
    override suspend fun update(setup: AISetup): Result<AISetup, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AISetup>, Throwable>
    override suspend fun get(id: UUID): Result<AISetup, Throwable>
    override suspend fun softDelete(setup: AISetup): Result<Boolean, Throwable>
}
