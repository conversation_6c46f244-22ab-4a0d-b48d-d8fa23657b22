package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.JsonSearchPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AliceTestResultBundle
import br.com.alice.data.layer.models.ProviderIntegration
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface AliceTestResultBundleDataService : Service,
    Finder<AliceTestResultBundleDataService.FieldOptions, AliceTestResultBundleDataService.OrderingOptions, AliceTestResultBundle>,
    Getter<AliceTestResultBundle>,
    Updater<AliceTestResultBundle>,
    Deleter<AliceTestResultBundle>,
    Adder<AliceTestResultBundle> {

    override val namespace: String
        get() = "test_result"

    override val serviceName: String
        get() = "alice_result_bundle"

    class IntegrationSourceField : Field.TextField(AliceTestResultBundle::integrationSource) {
        fun eq(value: ProviderIntegration) = Predicate.eq(this, value)
        fun inList(value: List<ProviderIntegration>) = Predicate.inList(this, value)
    }

    class PersonIdField : Field.TableIdField(AliceTestResultBundle::personId)
    class ExternalIdField : Field.TextField(AliceTestResultBundle::externalId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }
    class CreatedAt: Field.DateTimeField(AliceTestResultBundle::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }
    class CollectedAtField: Field.DateTimeField(AliceTestResultBundle::collectedAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }
    class ResultIdField: Field.JsonbField(AliceTestResultBundle::results) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun eq(value: UUID) = Predicate.jsonSearch(this, "[{\"id\":\"$value\"}]")
        fun inList(value: List<UUID>) = Predicate.inList(this, value.map {  "[{\"id\":\"$it\"}]"} )
    }

    class ResultCodeField: Field.JsonbField(AliceTestResultBundle::results) {
        @OptIn(Predicate.Companion.JsonSearchPredicateUsage::class)
        fun contains(value: String) = Predicate.jsonSearch(this, "[{\"procedure_id\":\"${value}\"}]")
        fun inList(value: List<String>) = Predicate.inList(this, value.map {  "[{\"procedure_id\":\"$it\"}]"} )
    }

    class FieldOptions {
        val integrationSource = IntegrationSourceField()
        val personId = PersonIdField()
        val externalId = ExternalIdField()
        val resultId = ResultIdField()
        val resultCode = ResultCodeField()
        val collectedAt = CollectedAtField()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
        val collectedAt = CollectedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<AliceTestResultBundle, Throwable>
    override suspend fun add(model: AliceTestResultBundle): Result<AliceTestResultBundle, Throwable>
    override suspend fun update(model: AliceTestResultBundle): Result<AliceTestResultBundle, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AliceTestResultBundle>, Throwable>
    override suspend fun delete(model: AliceTestResultBundle): Result<Boolean, Throwable>
}

