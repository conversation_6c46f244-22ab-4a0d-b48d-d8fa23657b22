package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.DischargeSummary
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.data.layer.services.DischargeSummaryDataService.FieldOptions
import br.com.alice.data.layer.services.DischargeSummaryDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface DischargeSummaryDataService :
    Service,
    Adder<DischargeSummary>,
    Getter<DischargeSummary>,
    Updater<DischargeSummary>,
    Finder<FieldOptions, OrderingOptions, DischargeSummary> {

    override val namespace: String get() = "interop"
    override val serviceName: String get() = "discharge_summary"

    class ExternalIdField : Field.TextField(DischargeSummary::externalId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class ProviderField : Field.TextField(DischargeSummary::provider) {
        fun eq(value: ProviderIntegration) = Predicate.eq(this, value)
    }

    class PersonIdField : Field.TableIdField(DischargeSummary::personId)

    class CreatedAt : Field.DateTimeField(DischargeSummary::createdAt)

    class FieldOptions {
        val externalId = ExternalIdField()
        val personId = PersonIdField()
        val provider = ProviderField()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder(): QueryBuilder<FieldOptions, OrderingOptions> = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )


    override suspend fun add(model: DischargeSummary): Result<DischargeSummary, Throwable>
    override suspend fun get(id: UUID): Result<DischargeSummary, Throwable>
    override suspend fun update(model: DischargeSummary): Result<DischargeSummary, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<DischargeSummary>, Throwable>
}
