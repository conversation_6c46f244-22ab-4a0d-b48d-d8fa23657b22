package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AliceExamReference
import br.com.alice.data.layer.services.AliceExamReferenceDataService.FieldOptions
import br.com.alice.data.layer.services.AliceExamReferenceDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AliceExamReferenceDataService :
    Service,
    Getter<AliceExamReference>,
    Adder<AliceExamReference>,
    Updater<AliceExamReference>,
    Finder<FieldOptions, OrderingOptions, AliceExamReference> {
    override val namespace: String get() = "interop"
    override val serviceName: String get() = "alice_exam_reference"

    class CodeAliceField : Field.TextField(AliceExamReference::codeAlice) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class CreatedAtField : Field.DateTimeField(AliceExamReference::createdAt)

    class FieldOptions {
        val codeAlice = CodeAliceField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<AliceExamReference, Throwable>
    override suspend fun add(model: AliceExamReference): Result<AliceExamReference, Throwable>
    override suspend fun update(model: AliceExamReference): Result<AliceExamReference, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AliceExamReference>, Throwable>
}
