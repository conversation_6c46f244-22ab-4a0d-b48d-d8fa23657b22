package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class AliceExamReference(
    val codeAlice: String,
    val indexReferences: List<IndexReferences>,
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model

data class IndexReferences(
    val name: String,
    val unit: String,
    val labels: List<String>,
    val references: ReferenceBaseValues
): JsonSerializable

data class ReferenceBaseValues(
    val lower: Double? = null,
    val higher: Double? = null,
): JsonSerializable
