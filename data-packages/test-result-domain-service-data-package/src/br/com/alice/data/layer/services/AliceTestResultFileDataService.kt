package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AliceTestResultFile
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AliceTestResultFileDataService : Service,
    Finder<AliceTestResultFileDataService.FieldOptions, AliceTestResultFileDataService.OrderingOptions, AliceTestResultFile>,
    Getter<AliceTestResultFile>,
    Updater<AliceTestResultFile>,
    Adder<AliceTestResultFile> {

    override val namespace: String
        get() = "test_result"

    override val serviceName: String
        get() = "alice_result_file"

    class PersonIdField : Field.TableIdField(AliceTestResultFile::personId)
    class ReferenceModelId : Field.UUIDField(AliceTestResultFile::referencedModelId){
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val referenceModelId = ReferenceModelId()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<AliceTestResultFile, Throwable>
    override suspend fun add(model: AliceTestResultFile): Result<AliceTestResultFile, Throwable>
    override suspend fun update(model: AliceTestResultFile): Result<AliceTestResultFile, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AliceTestResultFile>, Throwable>
}

