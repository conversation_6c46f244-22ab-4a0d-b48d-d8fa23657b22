package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class DischargeSummary(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val externalId: String,
    val provider: ProviderIntegration,
    val dischargeItem: DischargeSummaryItem
) : Model, HealthInformation, PersonReference

data class DischargeSummaryItem(
    val kind: DischargeSummaryItemKind,
    val location: String?,
    val admittedAt: LocalDateTime,
    val dischargedAt: LocalDateTime,
    val practitionerSpecialty: List<DischargeSummaryPractitioner>?,
    val diagnostic: List<String>?,
    val risk: List<DischargeSummaryRisk>?,
    val clinicalImpression: List<DischargeSummaryImpression>?,
    val vitalSigns: List<DischargeSummaryVitalSigns>?,
    val carePlan: List<DischargeSummaryImpression>?,
    val condition: List<String>?,
    val procedures: List<DischargeSummaryProcedure>? = emptyList(),
    val medicines: List<String>? = emptyList(),
    val structuredMedicine: List<DischargeSummaryMedicines>? = emptyList(),
) : JsonSerializable

enum class DischargeSummaryItemKind {
    EMERGENCY, HOSPITALIZATION;

    fun toTertiaryType(): TertiaryIntentionType = when (this) {
        EMERGENCY -> TertiaryIntentionType.TIT_EMERGENCY
        HOSPITALIZATION -> TertiaryIntentionType.TIT_HOSPITALIZATION
    }

    fun toPersonHealthEventCategory(): PersonHealthEventCategory = when (this) {
        EMERGENCY -> PersonHealthEventCategory.SUMMARY_EMERGENCY
        HOSPITALIZATION -> PersonHealthEventCategory.SUMMARY_HOSPITALIZATION
    }

}

data class DischargeSummaryPractitioner(
    val name: String,
    val councilType: String?,
    val councilValue: String?,
    val qualification: String?,
)

data class DischargeSummaryRisk(
    val codeType: String,
    val codeValue: String,
)

data class DischargeSummaryImpression(
    val description: String,
    val summary: String
)

data class DischargeSummaryVitalSigns(
    val code: String,
    val display: String,
    val value: String
)

data class DischargeSummaryMedicines(
    val code: String?,
    val method: String?,
    val system: String?,
    val value: String?,
    val orientation: String?
)

data class DischargeSummaryProcedure(
    val value: String?
)
