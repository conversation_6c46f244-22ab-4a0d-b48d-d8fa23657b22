package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import java.time.LocalDateTime
import java.util.UUID

data class BillingAccountableParty(
    val id: UUID = RangeUUID.generate(),
    val firstName: String,
    val lastName: String,
    val type: BillingAccountablePartyType,
    val nationalId: String,
    val email: String,
    val invoiceEmail: String? = null,
    val address: Address? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    var updatedBy: UpdatedBy? = null,
) {
    val fullName get() = "$firstName $lastName"
    val fullNameConsideringType get() = if (type == BillingAccountablePartyType.NATURAL_PERSON) fullName else firstName
    val isNaturalPerson = this.type == BillingAccountablePartyType.NATURAL_PERSON
    val isLegalPerson = this.type == BillingAccountablePartyType.LEGAL_PERSON
}

enum class BillingAccountablePartyType(val description: String) {
    NATURAL_PERSON("PF"),
    LEGAL_PERSON("PJ"),
}
