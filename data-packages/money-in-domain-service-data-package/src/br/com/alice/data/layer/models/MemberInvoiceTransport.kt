package br.com.alice.data.layer.models

import br.com.alice.common.PaymentMethod
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class MemberInvoiceResponse(
    val id: String,
    val referenceDate: LocalDate,
    val status: InvoiceStatus,
    val paidAt: LocalDateTime? = null,
    val totalAmount: BigDecimal,
    val payments: List<InvoicePaymentResponse> = emptyList(),
    val cancelledReason: List<CancellationReason>? = null,
    val dueDate: String,
    val invoiceItems: List<InvoiceItemResponse>? = null,
    val type: MemberInvoiceType? = null,
)

data class InvoicePaymentResponse(
    val amount: BigDecimal,
    val approvedAt: LocalDateTime? = null,
    val status: InvoicePaymentStatus,
    val method: PaymentMethod,
    val canceledReason: CancellationReason? = null,
    val externalId: String? = null,
    val source: InvoicePaymentSource? = null,
    val failReason: String? = null,
    val id: UUID,
    val paymentDetail: PaymentDetail? = null,
    val portalUrl: String? = null,
)

data class MemberInvoicesResponse(
    val memberInvoices: List<MemberInvoiceResponse> = emptyList()
)

data class CreateMemberInvoiceRequest(
    val referenceDate: String,
    val totalAmount: BigDecimal,
    val dueDate: String
)

data class CreatePaymentRequest(
    val payment: PaymentRequest,
)

data class PaymentRequest(
    val method: PaymentMethod,
    val amount: BigDecimal? = null,
    val externalId: String? = null,
    val dueDate: String? = null,
    val paymentUrl: String? = null,
    val memberInvoiceId: UUID? = null,
    val reason: PaymentReason? = PaymentReason.FIRST_PAYMENT,
)

data class CreatePaymentResponse(
    val payment: InvoicePayment,
)

data class ApprovePaymentResponse(
    val payment: InvoicePayment,
)

data class CancelPaymentRequest(
    val reason: CancellationReason,
)

data class CancelPaymentResponse(
    val payment: InvoicePayment,
)
