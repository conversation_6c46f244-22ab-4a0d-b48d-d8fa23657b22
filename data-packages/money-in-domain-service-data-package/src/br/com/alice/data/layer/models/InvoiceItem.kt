package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.money
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class InvoiceItem(
    val id: UUID = RangeUUID.generate(),
    val referenceDate: LocalDate,
    val operation: InvoiceItemOperation,
    val type: InvoiceItemType,
    val notes: String? = null,
    val status: InvoiceItemStatus,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val percentageValue: BigDecimal? = null,
    val absoluteValue: BigDecimal? = null,
    var resolvedValue: BigDecimal? = null,
    val personId: PersonId? = null,
    val companyId: UUID? = null,
    val companySubcontractId: UUID? = null,
    val externalId: String? = null,
    val invoicedDays: Int? = null,
    val referenceValue: BigDecimal? = null,
) {

    val value = percentageValue ?: absoluteValue!!

    val unit = if (percentageValue != null) InvoiceItemValueUnit.PERCENTAGE else InvoiceItemValueUnit.ABSOLUTE

    private val isDiscount = operation == InvoiceItemOperation.DISCOUNT

    fun cancel() = copy(
        status = InvoiceItemStatus.CANCELED,
    )

    fun resolveValue(full: BigDecimal): BigDecimal {
        val operator = (if (isDiscount) -1 else 1).money

        val result = if (absoluteValue != null)
            operator * absoluteValue
        else
            operator * full * percentageValue!! / 100.money

        this.resolvedValue = result.money
        return this.resolvedValue!!
    }

}

enum class InvoiceItemOperation {
    CHARGE,
    DISCOUNT
}

enum class InvoiceItemGroupType {
    PRODUCT,
    PRORATION,
    RESULT
}

enum class InvoiceItemType(val groupType: InvoiceItemGroupType, val description: String) {
    PROMO_CODE(InvoiceItemGroupType.PRODUCT, "Código Promocional"),
    PRORATION(InvoiceItemGroupType.PRORATION, "Pró-rata"),
    EXIT_PRORATION(InvoiceItemGroupType.PRORATION, "Devolução Retroativa de Mensalidade (devido a cancelamento ou troca de plano)"),
    COPAY(InvoiceItemGroupType.RESULT, "Coparticipação"),
    PRODUCT_CHANGE(InvoiceItemGroupType.RESULT, "Mudança de Produto"),
    SALES(InvoiceItemGroupType.PRODUCT, "Desconto de Vendas"),
    PRODUCT_PRICE(InvoiceItemGroupType.PRODUCT, "Preço do Produto"),
    SALES_RESULT(InvoiceItemGroupType.RESULT, "Desconto de Vendas"),
    PROMO_CODE_RESULT(InvoiceItemGroupType.RESULT, "Código Promocional"),
    PLAN_READJUSTMENT(InvoiceItemGroupType.PRODUCT, "Reajuste do plano"),
    OPERATIONAL_ADJUSTMENT(InvoiceItemGroupType.PRODUCT, "Ajuste operacional"),
    FEES_FINES(InvoiceItemGroupType.PRODUCT, "Juros e Multas"),
    INTEREST_OR_FINE(InvoiceItemGroupType.PRODUCT, "Acréscimo/Desconto de juros ou multas"),
    MONTHLY_ORGANIC_INCLUSION(InvoiceItemGroupType.PRODUCT, "Cobrança/Desconto Mensalidade Inclusão Orgânica"),
    RETROACTIVE_MONTHLY_FEE_BILLING(InvoiceItemGroupType.PRODUCT, "Cobrança Retroativa de Mensalidade"),
    OTHERS(InvoiceItemGroupType.PRODUCT, "Outros"),
}

enum class InvoiceItemStatus {
    ACTIVE,
    PENDING,
    FAILED,
    CANCELED,
}

enum class InvoiceItemValueUnit {
    ABSOLUTE,
    PERCENTAGE
}
