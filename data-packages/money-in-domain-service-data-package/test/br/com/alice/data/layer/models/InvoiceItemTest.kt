package br.com.alice.data.layer.models

import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import java.math.BigDecimal
import kotlin.test.Test

class InvoiceItemTest {

    @Test
    fun `#resolveValue when it is a absolute discount value`() {
        val invoiceItem = TestModelFactory.buildInvoiceItem(
            operation = InvoiceItemOperation.DISCOUNT,
            absoluteValue = BigDecimal(1000),
            percentageValue = null
        )

        val expected = BigDecimal("-1000.00")

        val result = invoiceItem.resolveValue(BigDecimal.ZERO)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#resolveValue when it is a absolute charge value`() {
        val invoiceItem = TestModelFactory.buildInvoiceItem(
            operation = InvoiceItemOperation.CHARGE,
            absoluteValue = BigDecimal(1000),
            percentageValue = null
        )

        val expected = BigDecimal("1000.00")

        val result = invoiceItem.resolveValue(BigDecimal.ZERO)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#resolveValue when it is a percentage discount value`() {
        val invoiceItem = TestModelFactory.buildInvoiceItem(
            operation = InvoiceItemOperation.DISCOUNT,
            absoluteValue = null,
            percentageValue = BigDecimal(10)
        )

        val expected = BigDecimal("-87.90")

        val result = invoiceItem.resolveValue(BigDecimal(879))

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#resolveValue when it is a percentage charge value`() {
        val invoiceItem = TestModelFactory.buildInvoiceItem(
            operation = InvoiceItemOperation.CHARGE,
            absoluteValue = null,
            percentageValue = 12.5.toBigDecimal()
        )

        val expected = 125.0

        val result = invoiceItem.resolveValue(BigDecimal(1000))

        assertThat(result.toDouble()).isEqualTo(expected)
    }
}
