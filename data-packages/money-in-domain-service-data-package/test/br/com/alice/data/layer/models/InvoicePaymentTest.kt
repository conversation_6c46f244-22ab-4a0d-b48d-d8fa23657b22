package br.com.alice.data.layer.models

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class InvoicePaymentTest {

    @Test
    fun `#approve - return model approved on date`() {
        val date = LocalDateTime.of(1993, 4, 1, 11, 0, 0)
        val model = TestModelFactory.buildInvoicePayment(status = InvoicePaymentStatus.PENDING)
        val expected = model.copy(status = InvoicePaymentStatus.APPROVED, approvedAt = date)

        val result = model.approve(date)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#decline - return model declined`() {
        val model = TestModelFactory.buildInvoicePayment(status = InvoicePaymentStatus.PENDING)
        val expected = model.copy(status = InvoicePaymentStatus.DECLINED)

        val result = model.decline()

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#cancel - return model canceled with canceled reason`() {
        val reason = CancellationReason.OVERDUE
        val model = TestModelFactory.buildInvoicePayment(status = InvoicePaymentStatus.PENDING)
        val expected = model.copy(
            status = InvoicePaymentStatus.CANCELED,
            canceledReason = reason
        )

        val result = model.cancel(reason)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#fail - return model failed with failed reason`() {
        val model = TestModelFactory.buildInvoicePayment(status = InvoicePaymentStatus.PENDING)
        val expected = model.copy(status = InvoicePaymentStatus.FAILED, failReason = "reason")

        val result = model.fail("reason")

        assertThat(result).isEqualTo(expected)

    }

    @Test
    fun `#withPaymentDetail - return model with payment detail with correct paymentId`() {
        val paymentDetail = BoletoPaymentDetail(
            paymentId = RangeUUID.generate(),
            dueDate = LocalDateTime.of(2023, 4, 1, 11, 0, 0),
            barcode = "bar-code",
            paymentUrl = "payment-url",
            externalId = "external-id",
        )
        val model = TestModelFactory.buildInvoicePayment(status = InvoicePaymentStatus.PENDING, paymentDetail = null)
        val correctPaymentDetail = paymentDetail.copy(paymentId = model.id)

        val result = model.withPaymentDetail(paymentDetail)

        assertThat(result.paymentDetail).isEqualTo(correctPaymentDetail)
        assertThat(result.paymentDetail!!.method).isEqualTo(PaymentMethod.BOLETO)
    }
}
