package br.com.alice.data.layer.models

import br.com.alice.data.layer.helpers.TestModelFactory
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.Test

class MemberInvoiceTest {

    private val memberInvoice = TestModelFactory.buildMemberInvoice()

    @Test
    fun `#wasCanceled must return true when status is CANCELED`() {
        val canceledInvoice = memberInvoice.copy(status = InvoiceStatus.CANCELED)
        assertThat(canceledInvoice.wasCanceled).isTrue()
    }

    @Test
    fun `#wasCanceled must return false when status is OPEN`() {
        val openInvoice = memberInvoice.copy(status = InvoiceStatus.OPEN)
        assertThat(openInvoice.wasCanceled).isFalse()
    }

    @Test
    fun `#alreadyPaid must return false when status is OPEN`() {
        val openInvoice = memberInvoice.copy(status = InvoiceStatus.OPEN)
        assertThat(openInvoice.alreadyPaid).isFalse()
    }

    @Test
    fun `#alreadyPaid must return true when status is PAID and paid date is not null`() {
        val paidInvoice = memberInvoice.copy(status = InvoiceStatus.PAID, paidAt = LocalDateTime.now())
        assertThat(paidInvoice.alreadyPaid).isTrue()
    }

    @Test
    fun `#isOverdue must return true when dueDate was yesterday and status is OPEN`() {
        val overdueInvoice = memberInvoice.copy(
            status = InvoiceStatus.OPEN,
            dueDate = LocalDateTime.now().minusDays(1)
        )

        assertThat(overdueInvoice.isOverdue).isTrue()
    }

    @Test
    fun `#isOverdue must return false when dueDate was yesterday but status is PAID`() {
        val overdueInvoice = memberInvoice.copy(
            status = InvoiceStatus.PAID,
            paidAt = LocalDateTime.now(),
            dueDate = LocalDateTime.now().minusDays(1)
        )

        assertThat(overdueInvoice.isOverdue).isFalse()
    }

    @Test
    fun `#isOverdue must return false when dueDate is today`() {
        val overdueInvoice = memberInvoice.copy(
            status = InvoiceStatus.OPEN,
            dueDate = LocalDateTime.now()
                .withHour(23)
                .withMinute(59)
                .withSecond(59)
        )

        assertThat(overdueInvoice.isOverdue).isFalse()
    }

    @Test
    fun `#markAsPaid must change the status to PAID and paid at date`() {
        val date = LocalDateTime.now()
        val paidInvoice = memberInvoice.markAsPaid(date)
        assertThat(paidInvoice.status).isEqualTo(InvoiceStatus.PAID)
        assertThat(paidInvoice.paidAt).isEqualTo(date)
    }

    @Nested
    inner class ChangeReferenceDate {
        @Test
        fun `#should change the reference date when it is a first payment`() {
            val date = LocalDate.now().withDayOfMonth(1)
            val invoice =
                memberInvoice.copy(
                    type = MemberInvoiceType.FIRST_PAYMENT,
                    referenceDate = date.minusMonths(2)
                )
                    .changeReferenceDate(date.atStartOfDay())

            assertThat(invoice.referenceDate).isEqualTo(date)
        }

        @Test
        fun `#should not change the reference date when it is not a first payment`() {
            val date = LocalDate.now().withDayOfMonth(1)
            val invoice =
                memberInvoice.copy(
                    type = MemberInvoiceType.B2B_REGULAR_PAYMENT,
                    referenceDate = date.minusMonths(2)
                )
                    .changeReferenceDate(date.atStartOfDay())

            assertThat(invoice.referenceDate).isEqualTo(date.minusMonths(2))
        }

    }

}
