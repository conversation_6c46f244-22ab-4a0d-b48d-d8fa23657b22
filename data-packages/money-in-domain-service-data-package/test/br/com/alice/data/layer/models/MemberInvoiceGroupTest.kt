package br.com.alice.data.layer.models

import br.com.alice.common.core.extensions.money
import br.com.alice.data.layer.helpers.TestModelFactory
import java.math.BigDecimal
import kotlin.test.Test
import kotlin.test.assertEquals

class MemberInvoiceGroupTest {

    @Test
    fun `#calculateTotalAmount should calculate totalAmount with globalItems`() {
        val globalItems = listOf(
            TestModelFactory.buildInvoiceItem(operation = InvoiceItemOperation.CHARGE, resolvedValue = BigDecimal(5)),
            TestModelFactory.buildInvoiceItem(operation = InvoiceItemOperation.CHARGE, resolvedValue = BigDecimal(5)),
            TestModelFactory.buildInvoiceItem(
                operation = InvoiceItemOperation.DISCOUNT,
                resolvedValue = BigDecimal(-10)
            ),
            TestModelFactory.buildInvoiceItem(
                operation = InvoiceItemOperation.DISCOUNT,
                absoluteValue = BigDecimal(10)
            ),
            TestModelFactory.buildInvoiceItem(
                operation = InvoiceItemOperation.DISCOUNT,
                percentageValue = BigDecimal(10),
                absoluteValue = null
            ),
            TestModelFactory.buildInvoiceItem(
                operation = InvoiceItemOperation.CHARGE,
                percentageValue = BigDecimal(5),
                absoluteValue = null
            )
        )
        val memberInvoices = listOf(
            TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(500)),
            TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(500))
        )
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
            memberInvoiceIds = memberInvoices.map { it.id },
            globalItems = globalItems
        ).copy(totalAmount = null)
        val expectedResult = memberInvoiceGroup.copy(totalAmount = BigDecimal("940.00").money)

        val result = memberInvoiceGroup.calculateTotalAmount(memberInvoices)

        assertEquals(expectedResult, result)
    }

    @Test
    fun `#calculateTotalAmount should calculate totalAmount without globalItems`() {
        val memberInvoices = listOf(
            TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(500)),
            TestModelFactory.buildMemberInvoice(totalAmount = BigDecimal(500))
        )
        val memberInvoiceGroup = TestModelFactory.buildMemberInvoiceGroup(
            memberInvoiceIds = memberInvoices.map { it.id },
            globalItems = null
        ).copy(totalAmount = null)
        val expectedResult = memberInvoiceGroup.copy(totalAmount = BigDecimal("1000").money)

        val result = memberInvoiceGroup.calculateTotalAmount(memberInvoices)

        assertEquals(expectedResult, result)
    }
}
