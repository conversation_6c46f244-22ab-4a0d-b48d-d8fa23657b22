package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentEvent(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val name: String,
    val clinicalRecordName: String,
    val referenceModel: AppointmentEventReferenceModel,
    val referenceModelId: String,
    val referenceModelDate: LocalDateTime,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

): Model, PersonReference, HealthInformation

enum class AppointmentEventReferenceModel {
    CHANNEL,
    SCHEDULING,
    TEST_RESULT,
    WANDA_TASK,
    HEALTH_PLAN_TASK
}
