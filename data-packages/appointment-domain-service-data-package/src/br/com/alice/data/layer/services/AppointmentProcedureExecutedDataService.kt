package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.SoftDeleter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AppointmentProcedureExecuted
import br.com.alice.data.layer.services.AppointmentProcedureExecutedDataService.FieldOptions
import br.com.alice.data.layer.services.AppointmentProcedureExecutedDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AppointmentProcedureExecutedDataService :
    Service,
    Finder<FieldOptions, OrderingOptions, AppointmentProcedureExecuted>,
    Counter<FieldOptions, OrderingOptions, AppointmentProcedureExecuted>,
    Adder<AppointmentProcedureExecuted>,
    Getter<AppointmentProcedureExecuted>,
    SoftDeleter<AppointmentProcedureExecuted>,
    Updater<AppointmentProcedureExecuted> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "appointment_procedure_executed"

    class AppointmentId : Field.UUIDField(AppointmentProcedureExecuted::appointmentId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Id : Field.UUIDField(AppointmentProcedureExecuted::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Name : Field.TextField(AppointmentProcedureExecuted::name) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun isNull() = Predicate.isNull(this)
    }

    class FieldOptions {
        val appointmentId = AppointmentId()
        val id = Id()
        val name = Name()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<AppointmentProcedureExecuted, Throwable>
    override suspend fun add(model: AppointmentProcedureExecuted): Result<AppointmentProcedureExecuted, Throwable>
    override suspend fun update(model: AppointmentProcedureExecuted): Result<AppointmentProcedureExecuted, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AppointmentProcedureExecuted>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun softDelete(model: AppointmentProcedureExecuted): Result<Boolean, Throwable>
}
