package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AppointmentFollowUp
import br.com.alice.data.layer.models.AppointmentFollowUpType
import br.com.alice.data.layer.services.AppointmentFollowUpDataService.FieldOptions
import br.com.alice.data.layer.services.AppointmentFollowUpDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AppointmentFollowUpDataService : Service,
    Finder<FieldOptions, OrderingOptions, AppointmentFollowUp>,
    Updater<AppointmentFollowUp>,
    Getter<AppointmentFollowUp>,
    Adder<AppointmentFollowUp>,
    Counter<FieldOptions, OrderingOptions, AppointmentFollowUp> {

    override val namespace: String
        get() = "digital_care"
    override val serviceName: String
        get() = "follow_up"

    class AppointmentIdField: Field.UUIDField(AppointmentFollowUp::appointmentId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class TypeField: Field.TextField(AppointmentFollowUp::type) {
        fun eq(value: AppointmentFollowUpType) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val appointmentId = AppointmentIdField()
        val type = TypeField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<AppointmentFollowUp, Throwable>
    override suspend fun add(model: AppointmentFollowUp): Result<AppointmentFollowUp, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AppointmentFollowUp>, Throwable>
    override suspend fun update(model: AppointmentFollowUp): Result<AppointmentFollowUp, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
