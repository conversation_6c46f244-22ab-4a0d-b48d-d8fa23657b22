package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class Timeline(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val staffId: UUID? = null,
    val providerUnitId: UUID? = null,
    val title: String? = null,
    val description: String,
    val type: TimelineType,
    val status: TimelineStatus? = null,
    val draftGroup: List<UUID> = emptyList(),
    val channelIds: List<String> = emptyList(),
    val specialtyId: UUID? = null,
    val evolutions: List<TimelineEvolution> = emptyList(),
    val referencedLinks: List<ReferencedLink> = emptyList(),
    val referencedModelId: UUID,
    val referencedModelDate: LocalDateTime = LocalDateTime.now(),
    val referencedModelClass: TimelineReferenceModel,
    val hasSpecialistOpinion: Boolean? = false,
    val appendages: List<TimelineAppendage> = emptyList(),
    val aiSummary: String? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, HealthInformation, PersonReference {
    data class ReferencedLink(
        val id: UUID,
        val model: ReferenceLinkModel
    ) : JsonSerializable

    enum class ReferenceLinkModel {
        SPECIALIST_OPINION
    }
}

enum class TimelineReferenceModel {
    HAOC_PRONTO_ATENDIMENTO_RESULT,
    HAOC_SUMMARIO_DE_ALTA_RESULT,
    EINSTEIN_ATENDIMENTO,
    FHIR_BUNDLE,
    APPOINTMENT,
    TERTIARY_INTENTION_TOUCH_POINT,
    COUNTER_REFERRAL,
    HEALTH_FORM_ANSWER_GROUP,
    REFUND_COUNTER_REFERRAL,
}

enum class TimelineStatus {
    FINISHED,
    DISCARDED_NO_SHOW,
}

data class TimelineEvolution(
    val id: UUID,
    val staffId: UUID,
    val description: String,
    val createdAt: LocalDateTime,
)

data class TimelineAppendage(
    val id: UUID,
    val type: TimelineAppendagesType,
    val title: String,
    val category: TimelineAppendagesCategory,
): JsonSerializable

enum class TimelineAppendagesType {
    PERSON_CASE,
    HEALTH_PLAN_TASK,
    APPOINTMENT_PROCEDURE_EXECUTED
}

enum class TimelineAppendagesCategory {
    CID_10,
    CIAP_2,
    FREE_TEXT,
    GOAL,
    CIPE,
    SYMPTOM,
    PRESCRIPTION,
    EATING,
    PHYSICAL_ACTIVITY,
    SLEEP,
    MOOD,
    OTHERS,
    TEST_REQUEST,
    REFERRAL,
    SURGERY_PRESCRIPTION,
    EMERGENCY,
    SCHEDULING,
    PROCEDURE_EXECUTED,
    FOLLOW_UP_REQUEST
}
