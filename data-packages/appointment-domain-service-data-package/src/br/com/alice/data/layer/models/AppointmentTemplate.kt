package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.models.Sex
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentTemplate(
    override val id: UUID = RangeUUID.generate(),
    val type: AppointmentType,
    val title: String,
    val description: String,
    val content: Map<String, Any>? = emptyMap(),
    val attachments: List<Attachment> = emptyList(),
    val active: Boolean = true,
    val biologicalSexes: List<Sex> = listOf(
        Sex.FEMALE, Sex.INTERSEX, Sex.MALE
    ),
    override var updatedBy: UpdatedBy? = null,
    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model, UpdatedByReference {

    fun apply(staff: Staff, personId: PersonId) = Appointment(
        staffId = staff.id,
        personId = personId,
        description = this.description,
        guidance = "",
        excuseNotes = emptyList(),
        startedAt = LocalDateTime.now(),
        attachments = this.attachments,
        status = AppointmentStatus.DRAFT,
        type = this.type
    )
}
