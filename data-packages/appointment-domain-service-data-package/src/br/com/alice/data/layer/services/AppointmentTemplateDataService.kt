package br.com.alice.data.layer.services

import br.com.alice.common.models.Sex
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AppointmentTemplate
import br.com.alice.data.layer.models.AppointmentType
import br.com.alice.data.layer.services.AppointmentTemplateDataService.FieldOptions
import br.com.alice.data.layer.services.AppointmentTemplateDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface AppointmentTemplateDataService :
    Service,
    Adder<AppointmentTemplate>,
    Finder<FieldOptions, OrderingOptions, AppointmentTemplate>,
    Getter<AppointmentTemplate>,
    Updater<AppointmentTemplate>,
    Deleter<AppointmentTemplate> {

    override val namespace: String get() = "ehr"
    override val serviceName: String get() = "appointment_template"

    override suspend fun get(id: UUID): Result<AppointmentTemplate, Throwable>

    override suspend fun findByQuery(query: Query): Result<List<AppointmentTemplate>, Throwable>

    override suspend fun add(model: AppointmentTemplate): Result<AppointmentTemplate, Throwable>

    override suspend fun update(model: AppointmentTemplate): Result<AppointmentTemplate, Throwable>

    override suspend fun delete(model: AppointmentTemplate): Result<Boolean, Throwable>

    class TypeField : Field.TextField(AppointmentTemplate::type) {
        fun eq(value: AppointmentType) = Predicate.eq(this, value)

        fun inList(value: List<AppointmentType>) = Predicate.inList(this, value)
    }

    class TitleField : Field.TextField(AppointmentTemplate::title) {
        fun eq(value: String) = Predicate.eq(this, value)

        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class DescriptionField : Field.TextField(AppointmentTemplate::description) {
        fun eq(value: String) = Predicate.eq(this, value)

        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class ActiveField : Field.BooleanField(AppointmentTemplate::active) {
        fun inList(value: List<Boolean>) = Predicate.inList(this, value)
    }

    class BiologicalSexesField : Field.TextField(AppointmentTemplate::biologicalSexes) {
        fun inList(value: List<Sex>) = Predicate.inList(this, value)
    }

    class FieldOptions {
        val type = TypeField()
        val title = TitleField()
        val biologicalSexes = BiologicalSexesField()
        val description = DescriptionField()
        val active = ActiveField()
    }

    class CreatedAt : Field.DateTimeField(AppointmentTemplate::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() =
        QueryBuilder(FieldOptions(), OrderingOptions())
}
