package br.com.alice.data.layer.models

enum class TreatedBy(val description: String) {
    SCREENING_NURSE("Enfermeiro(a) Acolhimento"),
    CLINICAL_NURSE("Enfermeiro(a) Clínico"),
    NURSE_SHARED_WITH_PHYSICIAN("Enfermeiro(a) + compartilhamento do caso com médico(a) Alice Agora"),
    NURSE_CONFERENCE_WITH_PHYSICIAN("Enfermeiro(a) + teleconsulta com médico(a) Alice Agora"),
    PHYSICIAN("Somente médico(a)"),
    NURSE_AND_HEALTH_COMMUNITY("Enfermeiro(a) + matriciamento ou discussão do caso com especialista"),
    NURSE_PHYSICIAN_AND_HEALTH_COMMUNITY(
        "Enfermeiro(a) + médico(a) Alice Agora + matriciamento ou discussão do caso com especialista"
    );
}

enum class Outcome(val severity: Int, val description: String, val appointmentType: AppointmentType? = null) {
    // old outcomes for digital care
    GUIDANCE_BY_NURSES(1, "Orientação pelos Enfermeiros AA"),
    ATTENDANCE_OR_DIGITAL_PRESCRIPTION(2, "Atendimento ou receita médica digital"),
    GUIDANCE_MORE_CONDUCT_OF_DIGITAL_SPECIALIST(3, "Orientação + conduta de especialista digital"),
    PHYSICAL_ASSISTANCE_AT_HOME(4, "Atendimento físico, para examinar ou coletar exames em Domicílio"),
    PHYSICAL_ASSISTANCE_AT_CASA_ALICE(5, "Atendimento físico, para examinar ou coletar exames na Casa Alice"),
    PHYSICAL_ATTENDANCE_SPECIALISTS_OFFICE(6, "Atendimento físico, para examinar Consultório Especialista"),
    DC_EMERGENCY_UNIT_BY_OWN_MEANS(6, "Encaminhamento ao PS por meios próprios"),
    EMERGENCY_HOSPITAL_CARE(7, "Atendimento hospitalar de urgência"),
    DC_EMERGENCY_UNIT_REMOVAL(7, "Encaminhamento ao PS com remoção"),
    EMERGENCY_WITH_POSSIBLE_AMBULANCE_ACTIVATION(
        8,
        "Atendimento hospitalar imediato (emergência) com possível acionamento de ambulância"
    ),

    // new outcomes for digital care
    DC_DISCHARGED(1, "Alta", AppointmentType.ASSISTANCE_CARE),
    DC_FOLLOW_UP(2, "Seguimento no AA", AppointmentType.ASSISTANCE_CARE),
    DC_ON_SITE_REFERRAL(3, "Encaminhado para OnSite", AppointmentType.ASSISTANCE_CARE),
    DC_HEALTH_TEAM_REFERRAL(4, "Encaminhamento para o Time de Saúde", AppointmentType.ASSISTANCE_CARE),
    DC_SPECIALIST_REFERRAL(5, "Encaminhamento para Especialista", AppointmentType.ASSISTANCE_CARE),
    DC_EMERGENCY_UNIT_BY_CLINICAL_MEANS(
        6,
        "Encaminhamento ao PS por indicação clínica (meios próprios)",
        AppointmentType.ASSISTANCE_CARE
    ),
    DC_EMERGENCY_UNIT_BY_MEMBER_EXPERIENCE(
        7,
        "Encaminhamento ao PS por experiência do membro (meios próprios)",
        AppointmentType.ASSISTANCE_CARE
    ),
    DC_EMERGENCY_UNIT_BY_AMBULANCE(8, "Encaminhamento ao PS por ambulância (APH)", AppointmentType.ASSISTANCE_CARE),


    // old outcomes for on_site
    DISCHARGED(1, "Alta do episódio"),
    MEDICAL_PROCEDURE(2, "Procedimento médico"),
    COMPLEMENTARY_EXAM_REQUEST(3, "Solicitação de exame complementar"),
    SPECIALIST_REFERRAL(4, "Encaminhamento para especialista"),
    RETURN_WITHOUT_MEDICAL_CARE(5, "Devolutiva para TdS sem conduta imediata"),
    RETURN_WITH_MEDICAL_CARE(6, "Devolutiva para TdS com conduta imediata"),

    //new outcomes for on_site
    NO_FOLLOW_UP_NEEDED_ON_SITE(1, "Sem necessidade de seguimento posterior", AppointmentType.ON_SITE),
    FOLLOW_UP_WITH_AA_ON_SITE(2, "Seguimento com AA", AppointmentType.ON_SITE),
    FOLLOW_UP_WITH_TDS_ON_SITE(3, "Seguimento com TdS", AppointmentType.ON_SITE),
    MATRICULATION_WITH_HC_ON_SITE(4, "Matriciamento com HC ", AppointmentType.ON_SITE),
    FORWARDED_TO_HC_ON_SITE(5, "Encaminhado para HC", AppointmentType.ON_SITE),
    FORWARDED_TO_PS_ON_SITE(6, "Encaminhado para o PS", AppointmentType.ON_SITE),
    MED_AA_INTERCONSULTATION_ON_SITE(7, "Interconsulta Med AA", AppointmentType.ON_SITE),
    MED_GEST_INTERCONSULTATION_ON_SITE(8, "Interconsulta Med Gest", AppointmentType.ON_SITE),
    SHARED_CONSULTATION_WITH_DOCTOR_ON_SITE(9, "Consulta compartilhada com médico(a) ", AppointmentType.ON_SITE);
}

enum class AppointmentType(val description: String, val isInternal: Boolean) {
    IMMERSION("Imersão", false),
    FOLLOW_UP_VISIT("Retorno", false),
    ASSISTANCE_CARE("Atendimento Alice Agora", false),
    HEALTH_PLAN_CARE("Atendimento Enfermeiras Gestoras", false),
    STATEMENT_OF_HEALTH("Declaração de Saúde", false),
    ANNOTATION("Anotação", true),
    ANNOTATION_HEALTH_COMMUNITY("Matriciamento", true),
    DEFAULT("Consulta", false),
    ON_SITE("On Site", false),
    TEST_COLLECT("Atendimento Casa Alice", false),
    SOAP("Consulta", false),
    COUNTER_REFERRAL("Consulta", false),
    FREE_TEXT("Consulta", false);

    fun toTimeline(): TimelineType = when (this) {
        ANNOTATION -> TimelineType.APPOINTMENT_ANNOTATION
        ANNOTATION_HEALTH_COMMUNITY -> TimelineType.APPOINTMENT_ANNOTATION_HEALTH_COMMUNITY
        ASSISTANCE_CARE -> TimelineType.APPOINTMENT_ASSISTANCE_CARE
        DEFAULT -> TimelineType.APPOINTMENT_DEFAULT
        FOLLOW_UP_VISIT -> TimelineType.APPOINTMENT_FOLLOW_UP_VISIT
        IMMERSION -> TimelineType.APPOINTMENT_IMMERSION
        HEALTH_PLAN_CARE -> TimelineType.APPOINTMENT_HEALTH_PLAN_CARE
        ON_SITE -> TimelineType.APPOINTMENT_ON_SITE
        STATEMENT_OF_HEALTH -> TimelineType.APPOINTMENT_STATEMENT_OF_HEALTH
        TEST_COLLECT -> TimelineType.APPOINTMENT_TEST_COLLECT
        SOAP -> TimelineType.CLINICAL_RECORD
        FREE_TEXT -> TimelineType.CLINICAL_RECORD
        COUNTER_REFERRAL -> TimelineType.CLINICAL_RECORD
    }
}

