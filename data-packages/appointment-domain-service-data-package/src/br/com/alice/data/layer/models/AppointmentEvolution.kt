package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.models.HealthInformation
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentEvolution(
    override val id: UUID = RangeUUID.generate(),
    val staffId: UUID,
    val appointmentId: UUID,
    val description: String,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val version: Int = 0,
) : Model, HealthInformation
