package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Predicate.Companion.JsonSearchPredicateUsage
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.Appointment
import br.com.alice.data.layer.models.Appointment.ReferencedLink
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.AppointmentEventReferenceModel
import br.com.alice.data.layer.models.AppointmentStatus
import br.com.alice.data.layer.models.AppointmentType
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface AppointmentDataService :
    Service,
    Finder<AppointmentDataService.FieldOptions, AppointmentDataService.OrderingOptions, Appointment>,
    Counter<AppointmentDataService.FieldOptions, AppointmentDataService.OrderingOptions, Appointment>,
    Adder<Appointment>,
    Getter<Appointment>,
    Updater<Appointment> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "appointment"

    class PersonIdField : Field.TableIdField(Appointment::personId) {
        fun inList(value: List<PersonId>) = Predicate.inList(this, value)
    }

    class StaffId : Field.UUIDField(Appointment::staffId) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class AppointmentTypeField : Field.TextField(Appointment::type) {
        fun inList(value: List<AppointmentType>) = Predicate.inList(this, value)
        fun eq(value: AppointmentType) = Predicate.eq(this, value)
    }

    class CreatedAt : Field.DateTimeField(Appointment::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class StartedAt : Field.DateTimeField(Appointment::startedAt) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class AppointmentDate : Field.DateField(Appointment::appointmentDate) {
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class Type : Field.TextField(Appointment::type) {
        fun eq(value: AppointmentType) = Predicate.eq(this, value)
    }

    class UpdatedAt : Field.DateTimeField(Appointment::updatedAt) {
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class CompletedAt : Field.DateTimeField(Appointment::completedAt) {
        fun isNull() = Predicate.isNull(this)
    }

    class StatusField : Field.TextField(Appointment::status) {
        fun eq(value: AppointmentStatus) = Predicate.eq(this, value)
        fun inList(values: List<AppointmentStatus>) = Predicate.inList(this, values)
    }
    class DiscardedType : Field.TextField(Appointment::discardedType) {
        fun eq(value: AppointmentDiscardedType) = Predicate.eq(this, value)
    }

    class ReferencedLinksField : Field.JsonbField(Appointment::referencedLinks) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun jsonSearch(value: ReferencedLink) = Predicate.jsonSearch(
            this,
            "[{\"id\": \"${value.id}\", \"model\":\"${value.model}\"}]"
        )
    }

    class IdField : Field.UUIDField(Appointment::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class ChannelIdField : Field.TextField(Appointment::channelId) {
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class EventField : Field.JsonbField(Appointment::event) {
        @OptIn(JsonSearchPredicateUsage::class)
        fun jsonSearch(referenceModelId: String, referenceModel: AppointmentEventReferenceModel) =
            Predicate.jsonSearch(
                this,
                "{\"reference_model_id\": \"$referenceModelId\", \"reference_model\":\"$referenceModel\"}"
            )
        @OptIn(JsonSearchPredicateUsage::class)
        fun jsonSearch(referenceModel: AppointmentEventReferenceModel) =
            Predicate.jsonSearch(
                this,
                "{\"reference_model\":\"$referenceModel\"}"
            )
    }

    class FieldOptions {
        val id = IdField()
        val channelId = ChannelIdField()
        val personId = PersonIdField()
        val staffId = StaffId()
        val appointmentType = AppointmentTypeField()
        val createdAt = CreatedAt()
        val updatedAt = UpdatedAt()
        val completedAt = CompletedAt()
        val status = StatusField()
        val discardedType = DiscardedType()
        val referencedLinks = ReferencedLinksField()
        val event = EventField()
        val startedAt = StartedAt()
        val appointmentDate = AppointmentDate()
        val type = Type()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<Appointment, Throwable>
    override suspend fun add(model: Appointment): Result<Appointment, Throwable>
    override suspend fun update(model: Appointment): Result<Appointment, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<Appointment>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
