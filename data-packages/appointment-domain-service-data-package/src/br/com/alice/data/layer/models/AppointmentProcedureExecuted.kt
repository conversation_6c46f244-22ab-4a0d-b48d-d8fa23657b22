package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentProcedureExecuted(
    override val id: UUID = RangeUUID.generate(),
    val appointmentId: UUID,
    val status: ProcedureExecutedStatus = ProcedureExecutedStatus.DRAFT,
    val tussCode: String,
    val healthSpecialistResourceBundleCode: String? = null,
    val isPriced: Boolean = false,
    val typeOfService: TypeOfService? = null,
    val name: String,
    override var updatedBy: UpdatedBy? = null,

    override val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model, UpdatedByReference

enum class ProcedureExecutedStatus {
    DRAFT,
    DONE
}

enum class TypeOfService {
    SURGICAL_PROCEDURE,
    OUTPATIENT_PROCEDURE,
    EXAM,
    CONSULTATION
}
