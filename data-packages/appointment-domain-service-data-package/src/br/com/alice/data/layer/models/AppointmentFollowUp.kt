package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.serialization.JsonSerializable
import java.util.UUID

data class AppointmentFollowUp(
    val appointmentId: UUID,
    val ticketId: Long,
    val type: AppointmentFollowUpType,
    override val id: UUID = RangeUUID.generate()
): Model, JsonSerializable

enum class AppointmentFollowUpType(val description: String) {
    TEST("Exame"),
    REFERRAL("Encaminhamento"),
    PRESCRIPTION("Receita");
}
