package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentMacro(
    override val id: UUID = RangeUUID.generate(),
    val title: String,
    val content: String,
    val componentType: Appointment.ComponentType,
    val active: Boolean = true,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
): Model
