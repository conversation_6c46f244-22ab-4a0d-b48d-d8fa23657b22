package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.AppointmentEvolution
import br.com.alice.data.layer.services.AppointmentEvolutionDataService.FieldOptions
import br.com.alice.data.layer.services.AppointmentEvolutionDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface AppointmentEvolutionDataService : Service,
    Finder<FieldOptions, OrderingOptions, AppointmentEvolution>,
    Adder<AppointmentEvolution>,
    Getter<AppointmentEvolution>,
    Updater<AppointmentEvolution> {

    override val namespace: String
        get() = "ehr"
    override val serviceName: String
        get() = "appointment_evolution"

    class IdField : Field.TableIdField(AppointmentEvolution::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class AppointmentIdField: Field.UUIDField(AppointmentEvolution::appointmentId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class CreatedAtField: Field.DateTimeField(AppointmentEvolution::createdAt)
    class UpdatedAtField: Field.DateTimeField(AppointmentEvolution::updatedAt)

    class FieldOptions {
        val id = IdField()
        val appointmentId = AppointmentIdField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
        val appointmentId = AppointmentIdField()
        val updatedAt = UpdatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions(),
    )

    override suspend fun get(id: UUID): Result<AppointmentEvolution, Throwable>
    override suspend fun add(model: AppointmentEvolution): Result<AppointmentEvolution, Throwable>
    override suspend fun update(model: AppointmentEvolution): Result<AppointmentEvolution, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AppointmentEvolution>, Throwable>
}
