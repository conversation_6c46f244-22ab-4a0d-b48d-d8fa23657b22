package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.Appointment.ComponentType
import br.com.alice.data.layer.models.AppointmentMacro
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface AppointmentMacroDataService :
    Service,
    Finder<AppointmentMacroDataService.FieldOptions, AppointmentMacroDataService.OrderingOptions, AppointmentMacro>,
    Counter<AppointmentMacroDataService.FieldOptions, AppointmentMacroDataService.OrderingOptions, AppointmentMacro>,
    Adder<AppointmentMacro>,
    Getter<AppointmentMacro>,
    Updater<AppointmentMacro> {

    override val namespace: String
        get() = "appointment"
    override val serviceName: String
        get() = "appointment_macro"

    class TitleField: Field.TextField(AppointmentMacro::title) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class ComponentTypeField: Field.TextField(AppointmentMacro::componentType) {
        fun inList(value: List<ComponentType>) = Predicate.inList(this, value)
        fun eq(value: ComponentType) = Predicate.eq(this, value)
    }

    class ActiveField: Field.BooleanField(AppointmentMacro::active) {
        override fun eq(value: Boolean) = Predicate.eq(this, value)
    }

    class CreatedAtField : Field.DateTimeField(AppointmentMacro::createdAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class UpdatedAtField : Field.DateTimeField(AppointmentMacro::updatedAt) {
        fun less(value: LocalDateTime) = Predicate.less(this, value)
        fun greater(value: LocalDateTime) = Predicate.greater(this, value)
    }

    class FieldOptions {
        val title = TitleField()
        val componentType = ComponentTypeField()
        val active = ActiveField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
        val updatedAt = UpdatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<AppointmentMacro, Throwable>
    override suspend fun add(model: AppointmentMacro): Result<AppointmentMacro, Throwable>
    override suspend fun update(model: AppointmentMacro): Result<AppointmentMacro, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<AppointmentMacro>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
