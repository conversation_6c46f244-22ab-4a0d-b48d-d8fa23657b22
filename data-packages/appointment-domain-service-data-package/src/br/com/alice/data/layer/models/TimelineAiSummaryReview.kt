package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class TimelineAiSummaryReview(
    override val id: UUID = RangeUUID.generate(),
    val timelineId: UUID,
    val staffId: UUID,
    val score: TimelineAiSummaryReviewScore = TimelineAiSummaryReviewScore.NOT_REVIEWED,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model

enum class TimelineAiSummaryReviewScore {
    POSITIVE,
    NEGATIVE,
    NOT_REVIEWED,
}
