package br.com.alice.data.layer.models

import br.com.alice.common.Disease
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.data.layer.models.AppointmentType.DEFAULT
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class AppointmentTest {

    @Test
    fun `sanitize values`() {
        val appointment = Appointment(
            staffId = RangeUUID.generate(),
            personId = PersonId(),
            description = "O paciente estava com problemas estomacais",
            guidance = "Coma mais fibras",
            excuseNotes = listOf(ExcuseNote(" presente no período da manhã para consulta "), ExcuseNote(" ")),
            completedAt = LocalDateTime.now(),
            type = DEFAULT
        )

        val sanitizedAppointment = appointment.sanitize()
        assertThat(sanitizedAppointment.excuseNotes).isEqualTo(listOf(ExcuseNote("presente no período da manhã para consulta")))
    }

    @Test
    fun `#getStaffIds return all staff id in appointment`() {
        val appointment = Appointment(
            staffId = RangeUUID.generate(),
            personId = PersonId(),
            description = "",
            guidance = "",
            excuseNotes = emptyList(),
            completedAt = LocalDateTime.now(),
            type = DEFAULT,
            ownerStaffIds = setOf(RangeUUID.generate()),
            draftGroupStaffIds = listOf(RangeUUID.generate())
        )

        val expected = appointment.ownerStaffIds
            .plus(appointment.staffId)
            .plus(appointment.draftGroupStaffIds!!)
            .distinct()

        val result = appointment.getStaffIds()
        assertThat(result).isEqualTo(expected)

        val expected2 = appointment.ownerStaffIds
            .plus(appointment.staffId)
            .distinct()

        val result2 = appointment.copy(draftGroupStaffIds = null).getStaffIds()
        assertThat(result2).isEqualTo(expected2)

        val expected3 = appointment.ownerStaffIds
            .plus(appointment.staffId)
            .distinct()

        val result3 = appointment.copy(draftGroupStaffIds = emptyList()).getStaffIds()
        assertThat(result3).isEqualTo(expected3)
    }

    @Test
    fun `#getConsolidatedInfo should return text with appointment info`() {
        val caseRecordDetails = listOf(CaseRecordDetails(
            caseId = RangeUUID.generate(),
            description = DiseaseDetails(
                type = Disease.Type.FREE_TEXT,
                value = "Dor de cabeça persistente há mais de 2 dias"
            ),
            severity = CaseSeverity.COMPENSATED
        ))
        val appointment = Appointment(
            staffId = RangeUUID.generate(),
            personId = PersonId(),
            description = "O paciente estava com problemas estomacais",
            guidance = "Coma mais fibras",
            objective = "Paciente com febre",
            subjective = "Paciente com dor de cabeça",
            plan = "Tomar remédio",
            type = DEFAULT,
            clinicalEvaluation = "Dor de cabeça persistente",
            caseRecordDetails = caseRecordDetails,
            staffRole = Role.CHIEF_DIGITAL_CARE_PHYSICIAN,
        )

        val result = appointment.getConsolidatedInfo()
        assertThat(result).isEqualTo("""
            DEFAULT;
            O paciente estava com problemas estomacais;
            Paciente com dor de cabeça;
            Paciente com febre;
            Tomar remédio;
            Dor de cabeça persistente;
            $caseRecordDetails;
            CHIEF_DIGITAL_CARE_PHYSICIAN
            """.trimIndent()
        )
    }

}
