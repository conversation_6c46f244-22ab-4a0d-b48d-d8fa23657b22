package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.data.layer.models.GenerateExternalAttendancePa
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface GenerateExternalAttendancePaDataService : Service,
    Adder<GenerateExternalAttendancePa>, Getter<GenerateExternalAttendancePa> {
    override val namespace: String
        get() = "generate_external_attendance"
    override val serviceName: String
        get() = "pa"

    override suspend fun add(model: GenerateExternalAttendancePa): Result<GenerateExternalAttendancePa, Throwable>
    override suspend fun get(id: UUID): Result<GenerateExternalAttendancePa, Throwable>
}
