package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.models.ExternalHealthInformation
import br.com.alice.common.models.HealthInformation
import java.time.LocalDateTime
import java.util.UUID

data class PersonEligibilityDuquesa(
    override val id: UUID = RangeUUID.generate(),
    override val personId: PersonId,
    val externalPersonId: String,
    val brand: PersonEligibilityBrand,
    val version: Int = 0,
    val startDate: LocalDateTime,
    val dueDate: LocalDateTime,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, HealthInformation, PersonReference, ExternalHealthInformation

enum class PersonEligibilityBrand {
    CIA, EINSTEIN
}
