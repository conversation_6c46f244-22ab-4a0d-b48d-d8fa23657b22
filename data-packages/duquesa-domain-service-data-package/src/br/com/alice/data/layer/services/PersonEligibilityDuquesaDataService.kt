package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.PersonEligibilityBrand
import br.com.alice.data.layer.models.PersonEligibilityDuquesa
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface PersonEligibilityDuquesaDataService :
    Service,
    Finder<PersonEligibilityDuquesaDataService.FieldOptions, PersonEligibilityDuquesaDataService.OrderingOptions, PersonEligibilityDuquesa>,
    Adder<PersonEligibilityDuquesa>,
    Updater<PersonEligibilityDuquesa>,
    Getter<PersonEligibilityDuquesa> {

    override val namespace: String get() = "duquesa"
    override val serviceName: String get() = "person_eligibility"

    class PersonIdField : Field.TableIdField(PersonEligibilityDuquesa::personId)

    class BrandField : Field.TextField(PersonEligibilityDuquesa::brand) {
        fun eq(value: PersonEligibilityBrand) = Predicate.eq(this, value)
    }

    class CreatedAt : Field.DateTimeField(PersonEligibilityDuquesa::createdAt)
    class FieldOptions {
        val personId = PersonIdField()
        val brand = BrandField()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<PersonEligibilityDuquesa, Throwable>
    override suspend fun add(model: PersonEligibilityDuquesa): Result<PersonEligibilityDuquesa, Throwable>
    override suspend fun update(model: PersonEligibilityDuquesa): Result<PersonEligibilityDuquesa, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<PersonEligibilityDuquesa>, Throwable>
}
