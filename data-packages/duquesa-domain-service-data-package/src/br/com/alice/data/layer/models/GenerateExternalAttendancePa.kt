package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.util.UUID

data class GenerateExternalAttendancePa(
    val link: String,
    val origin: ExternalPaOrigin,
    val externalId: String,
    override val personId: PersonId,
    override val id: UUID = RangeUUID.generate()
) : Model, PersonReference

enum class ExternalPaOrigin {
    EINSTEIN,
    CIA,
    FLEURY
}
