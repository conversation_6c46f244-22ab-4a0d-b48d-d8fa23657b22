package br.com.alice.data.layer.models

import br.com.alice.authentication.UserType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentScheduleOption(
    val id: UUID = RangeUUID.generate(),
    val title: String,
    val description: String? = null,
    val imageUrl: String,
    val type: AppointmentScheduleType,
    val calendarUrl: String? = null,
    val staffId: UUID? = null,
    val version: Int = 0,
    val active: Boolean,
    val specialtyId: UUID? = null,
    val subSpecialtyIds: List<UUID>? = emptyList(),
    val healthcareModelType: HealthcareModelType,
    val specialistId: UUID? = null,
    val showOnApp: Boolean,
    val availabilityLevel: Int = 0,
    val userType: UserType = UserType.MEMBER,
    val calendarProviderUnits: List<CalendarProviderUnit>? = emptyList(),
    val shouldUseInternalScheduler: Boolean = false,
    val appointmentScheduleEventTypeId: UUID? = null,
    val forChildren: Boolean = false,
    val ageRating: AgeRatingType = AgeRatingType.BOTH,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

data class CalendarProviderUnit(
    val providerUnitId: UUID,
    val calendarUrl: String = "",
    // Computed attributes
    val address: String? = null,
    val name: String? = null,
)
