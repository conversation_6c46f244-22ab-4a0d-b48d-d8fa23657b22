package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class ExternalCalendarEvent(
    val externalEventId: String,
    val staffId: UUID,
    val healthProfessionalId: UUID? = null,
    val appointmentScheduleId: UUID? = null,
    val status: ExternalEventStatus,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime,
    val provider: ExternalCalendarProvider,
    val externalUpdatedAt: LocalDateTime,
    val externalCalendarRecurrentEventId: UUID? = null,
    val transparency: ExternalEventTransparency,
    val isStaffActive: Boolean = true,
    val summary: String? = null,
    val responseStatus: ExternalEventResponseStatus = ExternalEventResponseStatus.ACCEPTED,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    fun canCancelAppointmentSchedule(): Boolean =
        appointmentScheduleId != null && status == ExternalEventStatus.CANCELLED

    fun cancel() = copy(
        status = ExternalEventStatus.CANCELLED,
        externalUpdatedAt = LocalDateTime.now(),
    )
}

enum class ExternalEventStatus(val value: String) {
    CONFIRMED("confirmed"),
    TENTATIVE("tentative"),
    CANCELLED("cancelled"),
}

enum class ExternalEventResponseStatus(val value: String) {
    NEEDS_ACTION("needsAction"),
    DECLINED("declined"),
    TENTATIVE("tentative"),
    ACCEPTED("accepted");

    companion object {
        fun get(value: String?): ExternalEventResponseStatus {
            return ExternalEventResponseStatus.values().find {
                it.value.equals(value, ignoreCase = true)
            } ?: ACCEPTED
        }
    }
}

enum class ExternalCalendarProvider(val value: String) {
    GOOGLE("google"),
}

enum class ExternalEventTransparency(val value: String) {
    BUSY("opaque"),
    FREE("transparent");

    companion object {
        fun get(value: String?): ExternalEventTransparency? {
            return values().find {
                it.value.equals(value, ignoreCase = true)
            }
        }
    }
}
