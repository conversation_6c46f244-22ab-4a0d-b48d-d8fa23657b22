package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.Status
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class EventTypeProviderUnit(
    val appointmentScheduleEventTypeId: UUID,
    val providerUnitId: UUID? = null,
    val duration: Int = 30,
    val minimumTimeToScheduleBeforeAppointmentTime: Int = 1,
    val numberOfDaysFromNowToAllowScheduling: Int = 90,
    val availableWeekDays: List<Weekday> = Weekday.values().toList(),
    val status: Status = Status.ACTIVE,
    val availabilityStartTime: LocalTime? = null,
    val availabilityEndTime: LocalTime? = null,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)
