package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.Status
import java.time.LocalDateTime
import java.util.UUID

data class StaffSchedulePreference(
    val staffId: UUID,
    val schedulePreferenceId: UUID,
    val staffName: String,
    val staffRole: String,
    val staffEmail: String,
    val staffPhoto: String? = null,
    val hasStaffSchedules: Boolean,
    val searchTokens: String? = null,
    val status: Status = Status.ACTIVE,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)
