package br.com.alice.data.layer.models

import br.com.alice.authentication.UserType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.Status
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentScheduleEventType(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val title: String,
    val specialtyId: UUID? = null,
    val subSpecialtyIds: List<UUID>? = emptyList(),
    val showOnApp: Boolean,
    val category: AppointmentScheduleType,
    val duration: Int,
    val locationType: AppointmentScheduleEventTypeLocation,
    val healthcareModelType: HealthcareModelType,
    val description: String? = null,
    val userType: UserType = UserType.MEMBER,
    val status: Status = Status.ACTIVE,
    val searchTokens: String? = null,
    val minimumTimeToScheduleBeforeAppointmentTime: Int = 1,
    val isMultiProfessionalReferral: Boolean = false,
    val numberOfDaysFromNowToAllowScheduling: Int = 90,
    val internalObservation: String? = null,
    val membersRisk: List<AppointmentScheduleEventTypeMemberRisk> = emptyList(),
    val groupByType: AppointmentScheduleType = AppointmentScheduleType.OTHER,
    val availableWeekDays: List<Weekday> = Weekday.values().toList(),
    val forChildren: Boolean = false,
    val ageRating: AgeRatingType = AgeRatingType.BOTH,
    val lastUpdatedBy: UUID? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {

    fun isActive() = status == Status.ACTIVE

}

enum class AppointmentScheduleEventTypeLocation {
    REMOTE, ON_SITE, HYBRID, HOME_CARE
}

enum class AppointmentScheduleEventTypeMemberRisk {
    LOW_RISK, MEDIUM_RISK, HIGH_RISK, NO_RISK
}

enum class AgeRatingType {
    CHILDREN, ADULT, BOTH
}
