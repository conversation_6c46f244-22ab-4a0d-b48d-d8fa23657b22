package br.com.alice.data.layer.models

import br.com.alice.authentication.UserType
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.models.AppointmentScheduleStatus.CANCELED
import br.com.alice.data.layer.models.AppointmentScheduleStatus.COMPLETED
import br.com.alice.data.layer.models.AppointmentScheduleStatus.NO_SHOW
import br.com.alice.data.layer.models.AppointmentScheduleStatus.SCHEDULED
import com.google.gson.annotations.Expose
import java.time.Duration
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentSchedule(
    @Expose
    override val personId: PersonId,
    val eventId: String?,
    val eventName: String,
    val location: String?,
    val status: AppointmentScheduleStatus,
    val healthcareTeamId: UUID? = null,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime? = null,
    @Expose
    val type: AppointmentScheduleType,
    val staffId: UUID? = null,
    val healthPlanTaskId: UUID? = null,
    val personTaskId: UUID? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val currentUserType: UserType? = null,
    val eventUuid: UUID?,
    val referencedLinks: List<ReferencedLink> = emptyList(),
    val scheduledByInternalScheduler: Boolean = false,
    val appointmentScheduleEventTypeId: UUID? = null,
    val cancelledByType: AppointmentScheduleCancelledByType? = null,
    val providerUnitId: UUID? = null,
    val createdInternally: Boolean = false,
    val screeningNavigationId: UUID? = null,
    val hubspotMeetingId: String? = null,
    val id: UUID = RangeUUID.generate()
) : PersonReference {
    val isScheduled get() = status == SCHEDULED
    val isCompleted get() = status == COMPLETED
    val isCanceled get() = status == CANCELED
    val wasNoShow get() = status == NO_SHOW

    fun cancel() = copy(status = CANCELED)
    fun markAsNoShow() = copy(status = NO_SHOW)
    fun complete() = copy(status = COMPLETED)

    fun canChangeStatus(baseDate: LocalDateTime = LocalDateTime.now()): Boolean {
        if (isCanceled) return false
        if (startTime.isAfter(baseDate)) return false
        return true
    }

    fun completedInMinutes(baseDate: LocalDateTime): Long {
        val duration = Duration.between(createdAt, baseDate)
        return duration.toMinutes()
    }

    fun getPreAppointmentStartTime(): LocalDateTime {
        return startTime.minusMinutes(10)
    }

    fun getPreAppointmentEndTime(): LocalDateTime {
        return startTime
    }

    fun getDuration(): Duration? = endTime?.let { endTime -> Duration.between(startTime, endTime) }

    fun getActionPlanTaskId() = healthPlanTaskId

    companion object {
        fun multiTeamTypes() = listOf(
            AppointmentScheduleType.NUTRITIONIST,
            AppointmentScheduleType.PSYCHOLOGIST,
            AppointmentScheduleType.PHYSICAL_EDUCATOR,
            AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST,
            AppointmentScheduleType.FOLLOW_UP_PHYSICAL_EDUCATOR,
        )

        fun testTypes() = listOf(
            AppointmentScheduleType.TEST,
            AppointmentScheduleType.PROC_ONSITE,
            AppointmentScheduleType.PROC_NURSE,
        )
    }

    data class ReferencedLink(
        val id: UUID,
        val model: ReferenceLinkModel
    ) : JsonSerializable

    enum class ReferenceLinkModel {
        SCHEDULE_SCREENING_CONTEXT_ID
    }
}

enum class AppointmentScheduleStatus {
    SCHEDULED,
    COMPLETED,
    CANCELED,
    NO_SHOW
}

enum class AppointmentScheduleType(val description: String) {
    COMMUNITY("Comunidade"),
    CONNECTION("Conexão"),
    FOLLOW_UP("Retorno"),
    HEALTHCARE_TEAM("Time de saúde"),
    HEALTH_DECLARATION("Declaração de saúde"),
    IMMERSION("Imersão"),
    NUTRITIONIST("Nutricionista"),
    OTHER("Outro"),
    PHYSICAL_EDUCATOR("Preparador físico"),
    PHYSIOTHERAPIST("Fisioterapeuta"),
    PSYCHOLOGIST("Psicólogo"),
    TEST("Exame"),
    HOME_TEST("Exame domiciliar"),
    NURSE("Enfermeiro(a)"),
    PHYSICIAN_ONSITE("Médico(a) presencial"),
    PROC_ONSITE("Procedimento"),
    PROC_NURSE("Procedimento de enfermagem"),
    NURSE_ONSITE("Enfermeiro(a) no local"),
    FOLLOW_UP_NUTRITIONIST("Retorno nutricionista"),
    FOLLOW_UP_PHYSICAL_EDUCATOR("Retorno preparador físico"),
}

enum class AppointmentScheduleCancelledByType {
    ALICE,
    CALENDLY_ADMIN,
    CHURN,
    HOST,
    MEMBER,
    RESCHEDULE,
    UNKNOWN,
    WANDA_TASK
}
