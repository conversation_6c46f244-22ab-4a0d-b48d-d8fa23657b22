package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentScheduleCheckIn(
    override val personId: PersonId,
    val appointmentScheduleId: UUID,
    val status: AppointmentScheduleCheckInStatus = AppointmentScheduleCheckInStatus.SENT,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val id: UUID = RangeUUID.generate()
): PersonReference {

    fun isAwaitingForAppointmentDate() =
        status == AppointmentScheduleCheckInStatus.SENT || status == AppointmentScheduleCheckInStatus.CONFIRMED
}

enum class AppointmentScheduleCheckInStatus {
    SENT,
    CONFIRMED,
    DROPPED_BY_CANCEL,
    DROPPED_BY_RESCHEDULE
}
