package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID

data class AppointmentReminder(
    val appointmentScheduleId: UUID,
    val provider: AppointmentReminderProvider,
    val notificationTime: AppointmentReminderNotificationTime,
    val id: UUID = RangeUUID.generate(),
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)


enum class AppointmentReminderProvider {
    TREBLE,
}

enum class AppointmentReminderNotificationTime {
    ONE_DAY_BEFORE,
    TWO_DAYS_BEFORE,
    THREE_DAYS_BEFORE,
    ONE_HOUR_BEFORE,
    TWO_HOURS_BEFORE,
    FIFTEEN_MINUTES_BEFORE
}
