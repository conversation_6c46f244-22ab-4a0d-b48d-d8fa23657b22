package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.logging.logger
import java.time.LocalDateTime
import java.util.UUID

data class ExternalCalendarRecurrentEvent(
    val externalEventId: String,
    val staffId: UUID,
    val status: ExternalEventStatus,
    val provider: ExternalCalendarProvider,
    val recurrence: List<String>,
    val byDay: List<Weekday> = emptyList(),
    val untilDateTime: LocalDateTime? = null,
    val externalUpdatedAt: LocalDateTime? = null,
    val transparency: ExternalEventTransparency,
    val staffScheduleId: UUID? = null,
    val isStaffActive: Boolean = true,
    val firstEventStartTime: LocalDateTime? = null,
    val summary: String? = null,
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) {
    fun notExpired(): Boolean {
        val untilDateTimeIsAfterNow = this.untilDateTime == null || this.untilDateTime.isAfter(LocalDateTime.now().minusDays(1))
        val externalStatusIsConfirmed = this.status == ExternalEventStatus.CONFIRMED
        return untilDateTimeIsAfterNow && externalStatusIsConfirmed
    }

    fun cancel() = copy(
        status = ExternalEventStatus.CANCELLED,
        externalUpdatedAt = LocalDateTime.now(),
    )
}

enum class Weekday(val initials: String) {
    MONDAY("MO"),
    TUESDAY("TU"),
    WEDNESDAY("WE"),
    THURSDAY("TH"),
    FRIDAY("FR"),
    SATURDAY("SA"),
    SUNDAY("SU");

    companion object {
        fun get(initials: String): Weekday? {
            return Regex("[A-Z]{2}").find(initials)?.value?.let {
                values().find { day ->
                    day.initials == it
                }
            } ?: run {
                logger.info(
                    "ExternalCalendarRecurrentEvent::Weekday::get day not found",
                    "initials" to initials
                )
                null
            }
        }
    }
}
