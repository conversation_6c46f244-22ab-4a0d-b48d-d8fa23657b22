package br.com.alice.data.layer.models

import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentScheduleStatus.CANCELED
import org.assertj.core.api.Assertions.assertThat
import java.time.Duration
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class AppointmentScheduleTest {

    @Test
    fun `#canChangeStatus should return false when start time is in the future`() {
        val baseDate = LocalDateTime.now()

        val appointmentSchedule = TestModelFactory
            .buildAppointmentSchedule()
            .copy(startTime = baseDate.plusDays(10))

        assertThat(appointmentSchedule.canChangeStatus(baseDate)).isFalse()
    }

    @Test
    fun `#canChangeStatus should return false when schedule is canceled`() {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(status = CANCELED)
        assertThat(appointmentSchedule.canChangeStatus()).isFalse()
    }

    @Test
    fun `#completedInMinutes should return a duration between completed time and created at date`() {
        val completedDate = LocalDateTime.now()
        val expectedMinutes = 40L

        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule().copy(
            createdAt = completedDate.minusMinutes(expectedMinutes)
        )

        assertThat(appointmentSchedule.completedInMinutes(completedDate)).isEqualTo(expectedMinutes)
    }

    @Test
    fun `getDuration should return the duration between start and end time`() {
        val timeNow = LocalDateTime.now()
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(
            startTime = timeNow,
            endTime = timeNow.plusMinutes(30)
        )

        assertEquals(appointmentSchedule.getDuration(), Duration.of(30, ChronoUnit.MINUTES))
    }

    @Test
    fun `getDuration return null when end time is null`() {
        val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(endTime = null)
        assertNull(appointmentSchedule.getDuration())
    }

}
