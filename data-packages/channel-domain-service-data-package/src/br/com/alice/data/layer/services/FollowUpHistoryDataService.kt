package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.FollowUpHistory
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface FollowUpHistoryDataService : Service,
    Finder<FollowUpHistoryDataService.FieldOptions, FollowUpHistoryDataService.OrderingOptions, FollowUpHistory>,
    Adder<FollowUpHistory>,
    Getter<FollowUpHistory>,
    Updater<FollowUpHistory> {

    override val namespace: String
        get() = "channel"
    override val serviceName: String
        get() = "follow_up_history"

    class PersonIdField: Field.UUIDField(FollowUpHistory::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class ChannelIdField: Field.TextField(FollowUpHistory::channelId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class WandaTaskIdField: Field.UUIDField(FollowUpHistory::wandaTaskId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class FollowUpIdField: Field.UUIDField(FollowUpHistory::followUpId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
    }

    class SentAtField: Field.DateTimeField(FollowUpHistory::sentAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
    }

    class AnswerAtField : Field.DateTimeField(FollowUpHistory::answerAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class SkipAtField : Field.DateTimeField(FollowUpHistory::skipAt) {
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun isNull() = Predicate.isNull(this)
        fun isNotNull() = Predicate.isNotNull(this)
    }

    class AnswerField : Field.TextField(FollowUpHistory::answer) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val personId = PersonIdField()
        val channelId = ChannelIdField()
        val wandaTaskId = WandaTaskIdField()
        val followUpId = FollowUpIdField()
        val sentAt = SentAtField()
        val answerAt = AnswerAtField()
        val skipAt = SkipAtField()
        val answer = AnswerField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<FollowUpHistory, Throwable>
    override suspend fun add(model: FollowUpHistory): Result<FollowUpHistory, Throwable>
    override suspend fun update(model: FollowUpHistory): Result<FollowUpHistory, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<FollowUpHistory>, Throwable>
}
