package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.VideoCall
import br.com.alice.data.layer.models.VideoCallSourceType
import br.com.alice.data.layer.models.VideoCallStatus
import br.com.alice.data.layer.models.VideoCallType
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface VideoCallDataService: Service,
    Finder<VideoCallDataService.FieldOptions, VideoCallDataService.OrderingOptions, VideoCall>,
    Adder<VideoCall>,
    Getter<VideoCall>,
    Updater<VideoCall> {

    override val namespace: String
        get() = "channels"
    override val serviceName: String
        get() = "video_call"

    class ExternalIdField: Field.UUIDField(VideoCall::externalId) {
        fun eq(value: UUID) = Predicate.eq(this, value) 
    }

    class PersonIdField: Field.UUIDField(VideoCall::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class ChannelIdField: Field.TextField(VideoCall::channelId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class StatusField: Field.TextField(VideoCall::status) {
        fun eq(value: VideoCallStatus) = Predicate.eq(this, value)
        fun inList(values: List<VideoCallStatus>) = Predicate.inList(this, values)
    }

    class TypeField: Field.TextField(VideoCall::type) {
        fun eq(value: VideoCallType) = Predicate.eq(this, value)
    }

    class SourceIdField: Field.TextField(VideoCall::sourceId) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class SourceTypeField: Field.TextField(VideoCall::sourceType) {
        fun eq(value: VideoCallSourceType) = Predicate.eq(this, value)
    }

    class CreatedAtField: Field.DateTimeField(VideoCall::createdAt)

    class FieldOptions {
        val externalId = ExternalIdField()
        val personId = PersonIdField()
        val channelId = ChannelIdField()
        val status = StatusField()
        val type = TypeField()
        val sourceId = SourceIdField()
        val sourceType = SourceTypeField()
    }
    
    class OrderingOptions {
        val createdAt = CreatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<VideoCall, Throwable>
    override suspend fun add(model: VideoCall): Result<VideoCall, Throwable>
    override suspend fun update(model: VideoCall): Result<VideoCall, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<VideoCall>, Throwable>
}
