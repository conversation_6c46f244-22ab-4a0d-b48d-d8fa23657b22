package br.com.alice.data.layer.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelHistory
import br.com.alice.data.layer.models.ChannelType
import br.com.alice.data.layer.services.ChannelHistoryDataService.FieldOptions
import br.com.alice.data.layer.services.ChannelHistoryDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.time.LocalDateTime
import java.util.UUID

@RemoteService
interface ChannelHistoryDataService : Service,
    Finder<FieldOptions, OrderingOptions, ChannelHistory>,
    Adder<ChannelHistory>,
    Getter<ChannelHistory>,
    Deleter<ChannelHistory>,
    Updater<ChannelHistory>,
    Counter<FieldOptions, OrderingOptions, ChannelHistory> {

    override val namespace: String
        get() = "channel"
    override val serviceName: String
        get() = "history"

    class TypeField : Field.TextField(ChannelHistory::type) {
        fun eq(value: ChannelType) = Predicate.eq(this, value)
    }
    class ActionField : Field.TextField(ChannelHistory::action) {
        fun eq(value: ChannelChangeAction) = Predicate.eq(this, value)
    }
    class ChannelIdField : Field.TextField(ChannelHistory::channelId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }
    class CreatedAtField : Field.DateTimeField(ChannelHistory::createdAt) {
        fun greaterEq(value: LocalDateTime) = Predicate.greaterEq(this, value)
        fun lessEq(value: LocalDateTime) = Predicate.lessEq(this, value)
    }
    class UpdatedAtField : Field.DateTimeField(ChannelHistory::updatedAt)

    class PersonIdField : Field.UUIDField(ChannelHistory::personId) {
        fun eq(value: PersonId) = Predicate.eq(this, value)
    }

    class EventDateField : Field.DateTimeField(ChannelHistory::eventDate) {
        fun isNull() = Predicate.isNull(this)
        fun eq(value: LocalDateTime) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val type = TypeField()
        val action = ActionField()
        val createdAt = CreatedAtField()
        val channelId = ChannelIdField()
        val personId = PersonIdField()
        val eventDate = EventDateField()
    }

    class OrderingOptions {
        val createdAt = CreatedAtField()
        val updatedAt = UpdatedAtField()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ChannelHistory, Throwable>
    override suspend fun add(model: ChannelHistory): Result<ChannelHistory, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ChannelHistory>, Throwable>
    override suspend fun update(model: ChannelHistory): Result<ChannelHistory, Throwable>
    override suspend fun delete(model: ChannelHistory): Result<Boolean, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>

}
