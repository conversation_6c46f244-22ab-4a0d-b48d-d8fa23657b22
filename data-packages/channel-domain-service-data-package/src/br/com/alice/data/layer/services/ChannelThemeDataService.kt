package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ChannelTheme
import br.com.alice.data.layer.services.ChannelThemeDataService.FieldOptions
import br.com.alice.data.layer.services.ChannelThemeDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ChannelThemeDataService : Service,
    Finder<FieldOptions, OrderingOptions, ChannelTheme>,
    Counter<FieldOptions, OrderingOptions, ChannelTheme>,
    Adder<ChannelTheme>,
    Getter<ChannelTheme> {

    override val namespace: String
        get() = "channel"
    override val serviceName: String
        get() = "theme"

    class ChannelIdField : Field.TextField(ChannelTheme::channelId) {
        fun eq(value: String) = Predicate.eq(this, value)
        fun inList(value: List<String>) = Predicate.inList(this, value)
    }

    class CreatedAt : Field.DateTimeField(ChannelTheme::createdAt)
    class UpdatedAt : Field.DateTimeField(ChannelTheme::updatedAt)

    class FieldOptions {
        val channelId = ChannelIdField()
    }

    class OrderingOptions {
        val channelId = ChannelIdField()
        val createdAt = CreatedAt()
        val updatedAt = UpdatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ChannelTheme, Throwable>
    override suspend fun add(model: ChannelTheme): Result<ChannelTheme, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ChannelTheme>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
