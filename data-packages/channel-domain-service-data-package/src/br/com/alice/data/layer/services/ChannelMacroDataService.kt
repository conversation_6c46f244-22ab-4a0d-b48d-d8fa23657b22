package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Deleter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ChannelMacro
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ChannelMacroDataService: Service,
    Finder<ChannelMacroDataService.FieldOptions, ChannelMacroDataService.OrderingOptions, ChannelMacro>,
    Counter<ChannelMacroDataService.FieldOptions, ChannelMacroDataService.OrderingOptions, ChannelMacro>,
    Adder<ChannelMacro>,
    Deleter<ChannelMacro>,
    Getter<ChannelMacro>,
    Updater<ChannelMacro> {

    override val namespace: String
        get() = "channel"
    override val serviceName: String
        get() = "macro"

    class ShortcutField: Field.TextField(ChannelMacro::shortcut) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class UpdatedAt: Field.DateTimeField(ChannelMacro::updatedAt)

    class FieldOptions {
        val shortcut = ShortcutField()
    }

    class OrderingOptions {
        val shortcut = ShortcutField()
        val updatedAt = UpdatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ChannelMacro, Throwable>
    override suspend fun add(model: ChannelMacro): Result<ChannelMacro, Throwable>
    override suspend fun update(model: ChannelMacro): Result<ChannelMacro, Throwable>
    override suspend fun delete(model: ChannelMacro): Result<Boolean, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ChannelMacro>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
