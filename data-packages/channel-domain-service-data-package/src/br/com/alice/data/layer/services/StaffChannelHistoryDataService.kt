package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.data.layer.models.StaffChannelHistory
import com.github.kittinunf.result.Result

@RemoteService
interface StaffChannelHistoryDataService : Service,
    Adder<StaffChannelHistory> {

    override val namespace: String
        get() = "channels"
    override val serviceName: String
        get() = "staff_channel_history"

    override suspend fun add(model: StaffChannelHistory): Result<StaffChannelHistory, Throwable>
}
