package br.com.alice.data.layer.services

import br.com.alice.common.core.Status
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.LikePredicateUsage
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ChannelFup
import br.com.alice.data.layer.services.ChannelFupDataService.FieldOptions
import br.com.alice.data.layer.services.ChannelFupDataService.OrderingOptions
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ChannelFupDataService : Service,
    Finder<FieldOptions, OrderingOptions, ChannelFup>,
    Counter<FieldOptions, OrderingOptions, ChannelFup>,
    Adder<ChannelFup>,
    Updater<ChannelFup>,
    Getter<ChannelFup> {

    override val namespace: String
        get() = "channel"
    override val serviceName: String
        get() = "fup"

    class IdField : Field.UUIDField(ChannelFup::id) {
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class NameField : Field.TextField(ChannelFup::name) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class QuestionField : Field.TextField(ChannelFup::question) {
        @OptIn(LikePredicateUsage::class)
        fun like(value: String) = Predicate.like(this, value)
    }

    class StatusField : Field.TextField(ChannelFup::status) {
        fun eq(value: Status) = Predicate.eq(this, value)
        fun inList(value: List<Status>) = Predicate.inList(this, value)
    }

    class ListedField : Field.BooleanField(ChannelFup::listed) {
        override fun eq(value: Boolean) = Predicate.eq(this, value)
    }

    class CreatedAt : Field.DateTimeField(ChannelFup::createdAt)
    class UpdatedAt : Field.DateTimeField(ChannelFup::updatedAt)

    class FieldOptions {
        val id = IdField()
        val name = NameField()
        val question = QuestionField()
        val status = StatusField()
        val listed = ListedField()
    }

    class OrderingOptions {
        val createdAt = CreatedAt()
        val updatedAt = UpdatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<ChannelFup, Throwable>
    override suspend fun add(model: ChannelFup): Result<ChannelFup, Throwable>
    override suspend fun update(model: ChannelFup): Result<ChannelFup, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<ChannelFup>, Throwable>
    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>
    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>
}
