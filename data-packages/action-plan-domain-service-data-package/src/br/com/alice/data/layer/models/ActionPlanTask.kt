package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import br.com.alice.common.core.extensions.asMap
import br.com.alice.common.core.extensions.toLocalDate
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.logging.logger
import br.com.alice.common.models.HealthInformation
import br.com.alice.common.serialization.JsonSerializable
import br.com.alice.data.layer.services.UpdateRequest
import kotlinx.serialization.Transient
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.reflect.KClass
import kotlin.reflect.KParameter
import kotlin.reflect.full.createType
import kotlin.reflect.full.isSubclassOf
import kotlin.reflect.full.primaryConstructor
import kotlin.reflect.full.withNullability
import kotlin.reflect.jvm.jvmErasure

const val BASE_S3_PATH = "https://alice-member-app-assets.s3.amazonaws.com/health_plan_tasks/v2/"
val FINAL_STATUS = listOf(
    ActionPlanTaskStatus.SCHEDULED,
    ActionPlanTaskStatus.EXPIRED,
    ActionPlanTaskStatus.DONE,
    ActionPlanTaskStatus.DELETED,
    ActionPlanTaskStatus.DELETED_BY_MEMBER,
    ActionPlanTaskStatus.DELETED_BY_STAFF
)

val STATUS_PERMITTED_BEFORE_SCHEDULED = listOf(
    ActionPlanTaskStatus.ACTIVE,
    ActionPlanTaskStatus.ACTIVE_ON_GOING,
    ActionPlanTaskStatus.SCHEDULED,
    ActionPlanTaskStatus.OVERDUE,
)

open class ActionPlanTask(
    override val personId: PersonId,
    val healthPlanId: UUID? = null,
    val appointmentId: UUID? = null,
    val title: String? = null,
    val description: String? = null,
    val content: Map<String, Any>? = null,
    val dueDate: LocalDate? = null,
    val scheduledAt: LocalDateTime? = null,
    val appointmentScheduleId: UUID? = null,
    val status: ActionPlanTaskStatus = ActionPlanTaskStatus.ACTIVE,
    val lastRequesterStaffId: UUID,
    val requestersStaffIds: Set<UUID> = emptySet(),
    val type: ActionPlanTaskType,
    val releasedAt: LocalDateTime? = null,
    val finishedAt: LocalDateTime? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    val originalUpdatedAt: LocalDateTime,
    val acknowledgedAt: LocalDateTime? = null,
    val frequency: Frequency? = null,
    val deadline: Deadline? = null,
    val start: Start? = null,
    val attachments: List<Attachment> = emptyList(),
    val groupId: UUID? = null,
    val originTaskId: UUID? = null,
    val releasedByStaffId: UUID? = null,
    val createdBy: ActionPlanTaskUpdatedBy? = null,
    val finishedBy: ActionPlanTaskUpdatedBy? = null,
    val initiatedByMemberAt: LocalDateTime? = null,
    val favorite: Boolean,
    val caseRecordDetails: List<CaseRecordDetails> = emptyList(),
    override val version: Int = 0,
    override val id: UUID = RangeUUID.generate(),
    override var updatedBy: UpdatedBy? = null,
) : Model, HealthInformation, PersonReference, UpdatedByReference, SentenceReference, UpdateRequest {

    @Transient
    private val statusAllowedSchedule = listOf(
        ActionPlanTaskStatus.ACTIVE,
        ActionPlanTaskStatus.ACTIVE_ON_GOING,
        ActionPlanTaskStatus.OVERDUE,
    )

    @Transient
    private val statusAllowedConclusion = listOf(
        ActionPlanTaskStatus.ACTIVE,
        ActionPlanTaskStatus.ACTIVE_ON_GOING,
        ActionPlanTaskStatus.OVERDUE,
        ActionPlanTaskStatus.SCHEDULED,
    )

    constructor(task: ActionPlanTask) : this(
        personId = task.personId,
        healthPlanId = task.healthPlanId,
        appointmentId = task.appointmentId,
        title = task.title,
        description = task.description,
        content = task.content,
        dueDate = task.dueDate,
        status = task.status,
        lastRequesterStaffId = task.lastRequesterStaffId,
        requestersStaffIds = task.requestersStaffIds,
        type = task.type,
        releasedAt = task.releasedAt,
        finishedAt = task.finishedAt,
        createdAt = task.createdAt,
        updatedAt = task.updatedAt,
        acknowledgedAt = task.acknowledgedAt,
        version = task.version,
        id = task.id,
        updatedBy = task.updatedBy,
        frequency = task.frequency,
        deadline = task.deadline,
        start = task.start,
        attachments = task.attachments,
        groupId = task.groupId,
        originTaskId = task.originTaskId,
        releasedByStaffId = task.releasedByStaffId,
        createdBy = task.createdBy,
        finishedBy = task.finishedBy,
        initiatedByMemberAt = task.initiatedByMemberAt,
        favorite = task.favorite,
        caseRecordDetails = task.caseRecordDetails,
        originalUpdatedAt = task.originalUpdatedAt,
        scheduledAt = task.scheduledAt,
        appointmentScheduleId = task.appointmentScheduleId
    )

    fun isInIntermediateStatus() = this.isScheduled() || this.isActiveOnGoing() || this.isOverdue()
    fun isFinalStatus() = this.isDone() || this.isExpired() || this.isDeleted()
    fun isActive() = status == ActionPlanTaskStatus.ACTIVE
    fun isDone() = status == ActionPlanTaskStatus.DONE
    fun isToDo() = isActive() || isInIntermediateStatus()
    fun isNotAvailable() = !isToDo()
    fun isScheduled() = status == ActionPlanTaskStatus.SCHEDULED
    fun isExpired() = status == ActionPlanTaskStatus.EXPIRED
    fun isActiveOnGoing() = status == ActionPlanTaskStatus.ACTIVE_ON_GOING
    fun isOverdue() = status == ActionPlanTaskStatus.OVERDUE
    fun isReferral() = type == ActionPlanTaskType.REFERRAL
    private fun isFollowUpRequest() = type == ActionPlanTaskType.FOLLOW_UP_REQUEST
    fun isTestRequest() = type == ActionPlanTaskType.TEST_REQUEST
    fun isSchedulable() = isTestRequest() || isReferral() || isFollowUpRequest()
    fun isDeleted() =
        status == ActionPlanTaskStatus.DELETED_BY_MEMBER || status == ActionPlanTaskStatus.DELETED || status == ActionPlanTaskStatus.DELETED_BY_STAFF

    fun taskAcknowledgedButNotScheduled() = !isScheduled() && acknowledgedAt != null

    fun isEmergency() = this.type == ActionPlanTaskType.EMERGENCY

    fun canChangeStatusToSchedule() = STATUS_PERMITTED_BEFORE_SCHEDULED.contains(this.status)

    fun shouldNotEdit() = FINAL_STATUS.contains(status)

    fun shouldShowCopayAlert() = this.isOverdue() || this.isExpired()

    fun isAllowedSchedule() = status in statusAllowedSchedule

    fun isAllowedConclusion() = status in statusAllowedConclusion

    open fun getViewDescription(): String = this.description ?: ""

    fun getSpecialtyIdIfExists(): UUID? {
        val specialtyNode = this.content?.get("specialty") as? Map<*, *>
        return specialtyNode?.get("id")?.toString()?.toUUID()
    }

    @Suppress("UNCHECKED_CAST")
    fun <T : ActionPlanTask> specialize(): T {
        val constructor = this.type.kClass.constructors.first()

        try {
            val parameters = processParameters(constructor.parameters, this.content)
            return constructor.callBy(parameters) as T
        } catch (e: Exception) {
            logger.error(
                "Error on ActionPlanTask.specialize",
                "action_plan_task_id" to id,
                "action_plan_task_type" to type,
                "content" to this.content?.map { it.key to it.value },
                e
            )
            throw e
        }
    }

    private fun processParameters(
        parameters: List<KParameter>,
        mapValues: Map<String, Any?>?,
        handlePrescriptionQuantity: Boolean = false
    ): Map<KParameter, Any?> =
        parameters.associateWith { param ->
            try {
                // TODO We need to make these parsers more generic
                when {
                    // TODO: PrescriptionMedicine quantity was Int and now is String. This handling is to convert correctly.
                    handlePrescriptionQuantity && param.name == PrescriptionMedicine::quantity.name ->
                        mapValues?.get(param.name)?.let {
                            if (it is Double) it.toInt().toString()
                            else it.toString()
                        }

                    param.name == "task" -> this
                    param.type.jvmErasure.isData -> parserNested(param, mapValues?.get(param.name)?.asMap())
                    isEnum(param) -> parserEnum(param, mapValues?.get(param.name)?.toString())
                    param.type.classifier == Int::class -> mapValues?.get(param.name)?.toString()?.toDouble()?.toInt()
                    param.type.classifier == Float::class -> mapValues?.get(param.name)?.toString()?.toFloat()
                    param.type.classifier == UUID::class -> mapValues?.get(param.name)?.toString()?.toUUID()
                    param.type.classifier == LocalDateTime::class -> mapValues?.get(param.name)?.toString()
                        ?.toLocalDateTime()

                    param.type.classifier == LocalDate::class -> mapValues?.get(param.name)?.toString()?.toLocalDate()
                    else -> mapValues?.get(param.name)
                }
            } catch (e: Exception) {
                logger.error("Error on ActionPlanTask.processParameters: parameter=${param.name}", e)
                throw e
            }
        }

    private fun parserNested(parameter: KParameter, objectMap: Map<String, Any?>?): Any? {
        if (objectMap == null) return null

        val constructor = parameter.type.jvmErasure.primaryConstructor!!
        val parameters = processParameters(
            constructor.parameters,
            objectMap,
            parameter.type == PrescriptionMedicine::class.createType().withNullability(true)
        )

        try {
            return constructor.callBy(parameters)
        } catch (e: Exception) {
            logger.error("Error on ActionPlanTask.parserNested: health_plan_task_id=$id parameter=$parameters", e)
            throw e
        }
    }

    private fun isEnum(parameter: KParameter): Boolean =
        parameter.type.classifier!!.createType().jvmErasure.isSubclassOf(Enum::class)

    private fun parserEnum(parameter: KParameter, value: Any?): Any? =
        value?.let { someValue ->
            parameter.type.classifier!!.createType().jvmErasure.members.find { it.name == "valueOf" }!!.call(someValue)
        }

    fun getStaffIds() =
        (this.requestersStaffIds + listOfNotNull(lastRequesterStaffId, releasedByStaffId, finishedBy?.id)).distinct()

    override fun fullSentence() =
        SentenceReference.fullSentence(
            title = this.title,
            frequency = this.frequency,
            deadline = this.deadline,
            start = this.start
        )

    override fun compactSentence() = ""
}

fun ActionPlanTask.copy(
    personId: PersonId = this.personId,
    healthPlanId: UUID? = this.healthPlanId,
    appointmentId: UUID? = this.appointmentId,
    title: String? = this.title,
    description: String? = this.description,
    content: Map<String, Any>? = this.content,
    dueDate: LocalDate? = this.dueDate,
    status: ActionPlanTaskStatus = this.status,
    scheduledAt: LocalDateTime? = this.scheduledAt,
    appointmentScheduleId: UUID? = this.appointmentScheduleId,
    lastRequesterStaffId: UUID = this.lastRequesterStaffId,
    requestersStaffIds: Set<UUID> = this.requestersStaffIds,
    type: ActionPlanTaskType = this.type,
    releasedAt: LocalDateTime? = this.releasedAt,
    finishedAt: LocalDateTime? = this.finishedAt,
    createdAt: LocalDateTime = this.createdAt,
    updatedAt: LocalDateTime = this.updatedAt,
    acknowledgedAt: LocalDateTime? = this.acknowledgedAt,
    version: Int = this.version,
    id: UUID = this.id,
    updatedBy: UpdatedBy? = this.updatedBy,
    frequency: Frequency? = this.frequency,
    deadline: Deadline? = this.deadline,
    start: Start? = this.start,
    attachments: List<Attachment> = this.attachments,
    groupId: UUID? = this.groupId,
    originTaskId: UUID? = this.originTaskId,
    releasedByStaffId: UUID? = this.releasedByStaffId,
    createdBy: ActionPlanTaskUpdatedBy? = this.createdBy,
    finishedBy: ActionPlanTaskUpdatedBy? = this.finishedBy,
    initiatedByMemberAt: LocalDateTime? = this.initiatedByMemberAt,
    favorite: Boolean = this.favorite,
    caseRecordDetails: List<CaseRecordDetails> = this.caseRecordDetails,
    originalUpdatedAt: LocalDateTime = this.originalUpdatedAt,
): ActionPlanTask =
    ActionPlanTask(
        personId = personId,
        healthPlanId = healthPlanId,
        appointmentId = appointmentId,
        title = title,
        description = description,
        content = content,
        dueDate = dueDate,
        status = status,
        scheduledAt = scheduledAt,
        appointmentScheduleId = appointmentScheduleId,
        lastRequesterStaffId = lastRequesterStaffId,
        requestersStaffIds = requestersStaffIds,
        type = type,
        releasedAt = releasedAt,
        finishedAt = finishedAt,
        createdAt = createdAt,
        updatedAt = updatedAt,
        acknowledgedAt = acknowledgedAt,
        version = version,
        id = id,
        updatedBy = updatedBy,
        frequency = frequency,
        deadline = deadline,
        start = start,
        attachments = attachments,
        groupId = groupId,
        originTaskId = originTaskId,
        releasedByStaffId = releasedByStaffId,
        createdBy = createdBy,
        finishedBy = finishedBy,
        initiatedByMemberAt = initiatedByMemberAt,
        favorite = favorite,
        caseRecordDetails = caseRecordDetails,
        originalUpdatedAt = originalUpdatedAt
    )

data class ActionPlanTaskUpdatedBy(
    val id: UUID? = null,
    val source: ActionPlanTaskSourceType
) : JsonSerializable

enum class ActionPlanTaskSourceType(val description: String) {
    STAFF("Staff"),
    MEMBER("Membro"),
    SYSTEM("Sistema"),
    TEMPLATE("Modelo de Tarefa"),
    COUNTER_REFERRAL("Tarefas automáticas geradas por contra referência"),
    EXTERNAL_REFERRAL("Tarefas automáticas geradas por encaminhamento externo"),
}

enum class ActionPlanTaskStatus(
    val priority: Int,
    val icon: String? = null
) {
    SCHEDULED(0, "${BASE_S3_PATH}schedule_icon.svg"),
    OVERDUE(1),
    ACTIVE(2),
    ACTIVE_ON_GOING(2),
    DONE(3, "${BASE_S3_PATH}done_icon.svg"),
    DELETED(3, "${BASE_S3_PATH}task-deleted.svg"),
    DELETED_BY_MEMBER(3, "${BASE_S3_PATH}task-deleted.svg"),
    DELETED_BY_STAFF(3, "${BASE_S3_PATH}task-deleted.svg"),
    EXPIRED(3, "${BASE_S3_PATH}expired_icon.svg"),
}

enum class ActionPlanTaskType(
    val kClass: KClass<out ActionPlanTask>,
    val icon: String,
    val filterIcon: String,
    val filterName: String,
    val cardImage: String,
) {
    PRESCRIPTION(
        PrescriptionNew::class,
        "${BASE_S3_PATH}prescription_icon.svg",
        "pill",
        "Medicamentos",
        "${BASE_S3_PATH}pill.svg",
    ),
    EATING(
        GenericTaskNew::class,
        "${BASE_S3_PATH}food_icon.svg",
        "food",
        "Alimentação",
        "${BASE_S3_PATH}food.svg",
    ),
    PHYSICAL_ACTIVITY(
        GenericTaskNew::class,
        "${BASE_S3_PATH}physical_activity_icon.svg",
        "sport",
        "Atividade Física",
        "${BASE_S3_PATH}sport.svg",
    ),
    SLEEP(
        GenericTaskNew::class,
        "${BASE_S3_PATH}sleep_icon.svg",
        "night",
        "Sono",
        "${BASE_S3_PATH}sleep.svg",
    ),
    MOOD(
        GenericTaskNew::class,
        "${BASE_S3_PATH}mood_icon.svg",
        "mindfulness",
        "Hábitos",
        "${BASE_S3_PATH}mindfulness.svg",
    ),
    OTHERS(
        GenericTaskNew::class,
        "${BASE_S3_PATH}referral_icon.svg",
        "hashtag",
        "Outros",
        "${BASE_S3_PATH}app_touch.svg",
    ),
    TEST_REQUEST(
        TestRequestNew::class,
        "${BASE_S3_PATH}schedule_icon.svg",
        "lab",
        "Exames",
        "${BASE_S3_PATH}lab.svg",
    ),
    REFERRAL(
        ReferralNew::class,
        "${BASE_S3_PATH}schedule_icon.svg",
        "doctor",
        "Encaminhamentos",
        "${BASE_S3_PATH}calendar.svg",
    ),
    EMERGENCY(
        EmergencyNew::class,
        "${BASE_S3_PATH}referral_icon.svg",
        "calendar",
        "Pronto Socorro",
        "${BASE_S3_PATH}app_touch.svg",
    ),
    QUESTIONNAIRE(
        QuestionnaireNew::class,
        "${BASE_S3_PATH}referral_icon.svg",
        "edit",
        "Questionários",
        "${BASE_S3_PATH}calendar.svg",
    ),
    FOLLOW_UP_REQUEST(
        FollowUpRequestNew::class,
        "${BASE_S3_PATH}referral_icon.svg",
        "calendar",
        "Retorno",
        "${BASE_S3_PATH}calendar.svg",
    ),
    SURGERY_PRESCRIPTION(
        SurgeryPrescriptionNew::class,
        "${BASE_S3_PATH}referral_icon.svg",
        "vaccine",
        "Cirurgia",
        "${BASE_S3_PATH}calendar.svg",
    )
}

data class PrescriptionNew(
    val dose: Dose?,
    val action: ActionType?,
    val routeOfAdministration: RouteOfAdministration?,
    val medicine: PrescriptionMedicine?,
    val packing: Int,
    val shortId: String? = null,
    val token: String? = null,
    val task: ActionPlanTask,
    val sentenceEdited: Boolean? = null,
    val medicineEndAt: LocalDateTime? = null,
    val digitalPrescription: DigitalPrescription? = null,
) : ActionPlanTask(task) {
    override fun fullSentence() =
        SentenceReference.fullSentence(
            action = this.action,
            dose = this.dose?.quantity,
            unit = this.dose?.unit,
            routeOfAdministration = this.routeOfAdministration,
            frequency = this.frequency,
            deadline = this.deadline,
            start = this.start,
            description = this.task.description
        )

    override fun compactSentence() =
        SentenceReference.compactSentence(
            dose = this.dose?.quantity,
            action = this.action,
            unit = this.dose?.unit,
            frequency = this.frequency,
        )

    fun hasDigitalPrescription() = digitalPrescription != null
}

data class GenericTaskNew(
    val task: ActionPlanTask
) : ActionPlanTask(task)

data class TestRequestNew(
    val code: String?,
    val task: ActionPlanTask,
    val shortId: String? = null,
    val token: String? = null,
    val cityId: String? = null,
    val memberGuidance: String? = null,
) : ActionPlanTask(task) {
    fun shouldNotSchedule() = FINAL_STATUS.contains(status)
}

data class TestRequestPreparation(
    val title: String,
    val description: String
)

// MedicalSpecialty.name goes on ActionPlanTask.title
data class ReferralNew(
    val suggestedSpecialist: SuggestedSpecialist? = null,
    val diagnosticHypothesis: String?,
    val referenceLetterSentDate: LocalDateTime? = null,
    val genericReferral: Boolean? = null,
    val specialty: ReferralSpecialty? = null,
    val subSpecialty: ReferralSpecialty? = null,
    val cityId: String? = null,
    val sessionsQuantity: Int? = null,
    val followUpMaxQuantity: Int? = null,
    val task: ActionPlanTask,
    val isAdvancedAccess: Boolean? = false,
) : ActionPlanTask(task) {
    fun shouldNotSchedule() = FINAL_STATUS.contains(status)

    fun hasManySessions() = getSessions() > 1

    fun getSessions() = followUpMaxQuantity ?: sessionsQuantity ?: 1

    fun hasInfoToCheckAttentionLevel() = this.hasSuggestedSpecialistAndTypeIsStaff() || this.hasSpecialty()

    fun hasSuggestedSpecialistAndTypeIsStaff() =
        this.suggestedSpecialist != null && this.suggestedSpecialist.type == SpecialistType.STAFF

    override fun getViewDescription(): String {
        val hasSpecialistInTitle = suggestedSpecialist?.name?.let { specialistName ->
            title?.contains(specialistName, ignoreCase = true) == true
        } == true

        val specialistSuffix = suggestedSpecialist?.name?.takeUnless { hasSpecialistInTitle } ?: ""
        val hasReferralInTitle = title?.lowercase()?.contains(REFERRAL_TEXT, ignoreCase = true) == true

        return ("$title $specialistSuffix".takeIf { hasReferralInTitle }
            ?: REFERRAL_DESCRIPTION_FORMAT.format(title, specialistSuffix))
            .trim()
    }

    private fun hasSpecialty() = this.specialty != null && this.specialty.id != null

    private companion object {
        const val REFERRAL_TEXT = "Encaminhamento"
        const val REFERRAL_DESCRIPTION_FORMAT = "Encaminhamento para %s %s"
    }
}

data class EmergencyNew(
    val diagnosticHypothesis: String?,
    val referenceLetterSentDate: LocalDateTime? = null,
    val specialty: EmergencySpecialty? = null,
    val cityId: String? = null,
    val task: ActionPlanTask
) : ActionPlanTask(task) {

    override fun getViewDescription(): String {
        val needFillEmergencySuffix = task.title?.contains(EMERGENCY_ROOM_TEXT) == false
        val description = task.title?.takeUnless { needFillEmergencySuffix }
            ?: (task.title + EMERGENCY_SUFFIX)
        return description
    }

    private companion object {
        const val EMERGENCY_ROOM_TEXT = "Pronto Socorro"
        const val EMERGENCY_SUFFIX = " - $EMERGENCY_ROOM_TEXT"
    }
}

data class QuestionnaireNew(
    val healthFormAnswerSource: HealthFormAnswerSource?,
    val questionnaireKey: String?,
    val task: ActionPlanTask,
) : ActionPlanTask(task)

data class FollowUpRequestNew(
    val task: ActionPlanTask,
) : ActionPlanTask(task)

data class SurgeryPrescriptionNew(
    val task: ActionPlanTask,
) : ActionPlanTask(task)
