package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.HealthConditionGroup
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface HealthConditionGroupDataService : Service,
    Finder<HealthConditionGroupDataService.FieldOptions, HealthConditionGroupDataService.OrderingOptions, HealthConditionGroup>,
    Getter<HealthConditionGroup>,
    Updater<HealthConditionGroup>,
    Adder<HealthConditionGroup> {

    override val namespace: String
        get() = "action_plan"
    override val serviceName: String
        get() = "health_condition_group"

    class HealthConditionIdsField : Field.JsonbField(HealthConditionGroup::healthConditionIds) {
        @OptIn(Predicate.Companion.ContainsPredicateUsage::class)
        fun contains(value: UUID) = Predicate.contains(this, value)
        @OptIn(Predicate.Companion.ContainsAnyPredicateUsage::class)
        fun containsAny(value: List<UUID>) = Predicate.containsAny(this, value)
    }

    class NameField : Field.TextField(HealthConditionGroup::name) {
        fun eq(value: String) = Predicate.eq(this, value)
    }

    class FieldOptions {
        val healthConditionIds = HealthConditionIdsField()
        val name = NameField()
    }

    class OrderingOptions

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun get(id: UUID): Result<HealthConditionGroup, Throwable>
    override suspend fun add(model: HealthConditionGroup): Result<HealthConditionGroup, Throwable>
    override suspend fun findByQuery(query: Query): Result<List<HealthConditionGroup>, Throwable>
    override suspend fun update(model: HealthConditionGroup): Result<HealthConditionGroup, Throwable>
}
