package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.models.State
import java.time.LocalDateTime
import java.util.UUID

data class HippocratesHealthcareProfessional(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val fullName: String,
    val councilName: HippocratesHealthProfessionalCouncil,
    val councilState: State,
    val councilNumber: String,
    val searchTokens: String? = null,
    val specialties: List<String> = emptyList()
)

enum class HippocratesHealthProfessionalCouncil {
    CRBM, CREF, COREN, CREFITO, CREFONO, CRM, CRN, CRO, CRP, CRMV, CRF, CRBIO
}
