plugins {
    kotlin("jvm")
}

// Override the Kotlin version for this module only and allow Ktor 3.x
configurations.all {
    resolutionStrategy.eachDependency {
        if (requested.group == "org.jetbrains.kotlin") {
            useVersion(kotlin2Version)
            because("common-mcp requires Kotlin $kotlin2Version for MCP SDK compatibility")
        }
        // Allow Ktor 3.x for this module to match MCP SDK requirements
        if (requested.group == "io.ktor" && requested.name.startsWith("ktor-")) {
            useVersion(ktor3Version)
            because("common-mcp uses Ktor $ktor3Version to match MCP SDK requirements")
        }
    }
}

group = "br.com.alice.common-mcp"
version = "0.0.1"

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

dependencies {
    implementation(project(":common"))

    api("io.modelcontextprotocol:kotlin-sdk:0.5.0")
    implementation("org.jetbrains.kotlin:kotlin-stdlib:$kotlin2Version")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")

    api("io.insert-koin:koin-ktor:$koin3Version")
    api("io.ktor:ktor-server-content-negotiation:$ktor3Version")

    // YAML configuration support
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.15.2")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.2")

    test2Dependencies()
}
