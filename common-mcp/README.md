# Common MCP Module

A comprehensive Kotlin library for building Model Context Protocol (MCP) servers with minimal boilerplate code. This module provides middleware, annotations, and transport classes to simplify the creation of MCP tools, prompts, and resources.

## Support

For questions and support:
- **Team**: Platform Team 🚀
- **Slack**: `#eng-platform`

## Overview

The `common-mcp` module streamlines MCP server development by providing:

- **Annotation-driven tool configuration** with automatic schema generation
- **Middleware for common patterns** like authentication, logging, and parameter handling
- **Transport classes** for encapsulating MCP capabilities
- **YAML-based configuration** with placeholder resolution
- **Type-safe parameter mapping** with automatic conversion
- **Integration with Koin DI** and Alice authentication system

## Key Features

### 🚀 Zero-Boilerplate Tool Creation
Transform service methods into MCP tools with simple annotations:

```kotlin
@MCPTool(
    name = "process_data",
    description = "Processes data with the given parameters"
)
suspend fun processData(
    @MCPParameter(description = "Data to process") data: String,
    @MCPParameter(required = false, defaultValue = "10") limit: Int
) = dataService.process(data, limit)
```

### 🔧 Automatic Schema Generation
Input schemas are automatically generated from method signatures and parameter annotations.

### 🔐 Built-in Authentication
Automatic integration with <PERSON>'s authentication system using `withRootServicePolicy` and `withUnauthenticatedTokenWithKey`.

### 📝 YAML Configuration
Externalize tool configurations with placeholder support:

```yaml
tools:
  processData:
    name: "process_data"
    description: "Processes data with the given parameters"
    successMessage: "Data processed successfully!"
```

### 🎯 Type Safety
Automatic parameter conversion for common types: `String`, `Int`, `Long`, `Boolean`, `UUID`.

## Quick Start

### 1. Add Dependencies

```kotlin
dependencies {
    implementation(project(":common-mcp"))
}
```

### 2. Create a Service with MCP Tools

```kotlin
class DataProcessingService(
    private val dataClient: DataClient
) {
    @MCPTool(
        name = "process_data",
        description = "Processes data with specified parameters"
    )
    suspend fun processData(
        @MCPParameter(description = "Input data to process") data: String,
        @MCPParameter(description = "Processing limit", required = false, defaultValue = "100") limit: Int
    ): ProcessingResult {
        return dataClient.process(data, limit)
    }
}
```

### 3. Configure MCP Server

```kotlin
// Set service name for authentication
MCPToolMiddleware.SERVICE_NAME = "data-processing-service"

// Create tool transport
val processingTool = MCPToolTransportFactory.createToolTransport<DataProcessingService>(
    DataProcessingService::processData
)

// Configure server capabilities
val capabilities = MCPCapabilities(
    tools = listOf(processingTool)
)

// Start server
runSseMcpServerUsingKtorPlugin(
    port = 8080,
    mcpCapabilities = capabilities,
    dependencyInjectionModules = listOf(dataProcessingModule)
)
```

## Architecture

### Core Components

#### MCPToolTransportFactory (Recommended)
The highest-level API for creating complete `ToolTransport` objects:

```kotlin
val toolTransport = MCPToolTransportFactory.createToolTransport<MyService>(
    MyService::myMethod
)
```

#### MCPToolMiddleware (Lower-level)
Core middleware handling method invocation with authentication and error handling:

```kotlin
val handler = MCPToolMiddleware.createHandler<MyService>(MyService::myMethod)
```

#### Transport Classes
Encapsulate MCP capabilities for easy server configuration:

- `ToolTransport` - For MCP tools
- `PromptTransport` - For MCP prompts
- `ResourceTransport` - For MCP resources
- `MCPCapabilities` - Container for all capabilities

#### Annotations
- `@MCPTool` - Configures tool metadata
- `@MCPParameter` - Configures parameter behavior

### Data Flow

1. **Request Reception** - MCP client sends `CallToolRequest`
2. **Parameter Extraction** - `ParameterMapper` extracts and converts parameters
3. **Authentication** - Middleware applies authentication policies
4. **Method Invocation** - Service method called via reflection
5. **Result Handling** - Response formatted as `CallToolResult`

## Detailed Usage

### Tool Configuration with @MCPTool

```kotlin
@MCPTool(
    name = "user_search",                    // Tool identifier
    description = "Search for users",       // Tool description
    successMessage = "Found {result} users", // Success message template
    errorMessage = "Search failed",         // Error message prefix
    serializeResult = true                  // JSON serialize result
)
suspend fun searchUsers(
    @MCPParameter(description = "Search query") query: String,
    @MCPParameter(description = "Maximum results", required = false, defaultValue = "10") limit: Int
): List<User> {
    return userService.search(query, limit)
}
```

### Parameter Configuration with @MCPParameter

```kotlin
suspend fun createUser(
    @MCPParameter(
        name = "user_name",              // Parameter name in request
        description = "User's full name", // Schema description
        required = true                   // Required parameter
    ) name: String,

    @MCPParameter(
        description = "User's email address",
        required = false,                 // Optional parameter
        defaultValue = "<EMAIL>" // Default if not provided
    ) email: String
): User {
    return userService.create(name, email)
}
```

### YAML Configuration

Create `mcp-tools.yaml` in your resources:

```yaml
tools:
  userSearch:
    name: "user_search"
    description: "Search for users in the system"
    successMessage: "Found {result} users matching criteria"
    parameters:
      query:
        description: "Search query string"
      limit:
        description: "Maximum number of results to return"

  createUser:
    name: "user_create"
    description: "Create a new user account"
    successMessage: "User created with ID: {result}"
```

Load configuration in your application:

```kotlin
MCPToolConfigLoader.loadConfig("mcp-tools.yaml")
```

Use placeholders in annotations:

```kotlin
@MCPTool(
    name = "{tools.userSearch.name}",
    description = "{tools.userSearch.description}",
    successMessage = "{tools.userSearch.successMessage}"
)
suspend fun searchUsers(
    @MCPParameter(description = "{tools.userSearch.parameters.query.description}") query: String
): List<User> = userService.search(query)
```

### Working with Prompts and Resources

#### Creating Prompt Transports

```kotlin
val promptTransport = PromptTransport(
    name = "code_review",
    description = "Generate code review comments",
    arguments = listOf(
        PromptArgument(name = "code", description = "Code to review", required = true)
    )
) { request ->
    val code = request.arguments["code"] ?: ""
    GetPromptResult(
        description = "Code review for provided code",
        messages = listOf(
            PromptMessage(
                role = PromptMessage.Role.USER,
                content = TextContent("Please review this code: $code")
            )
        )
    )
}
```

#### Creating Resource Transports

```kotlin
val resourceTransport = ResourceTransport(
    uri = "file://logs/application.log",
    name = "Application Logs",
    description = "Current application log file",
    mimeType = "text/plain"
) { request ->
    val logContent = File("logs/application.log").readText()
    ReadResourceResult(
        contents = listOf(TextResourceContents(
            uri = request.uri,
            text = logContent,
            mimeType = "text/plain"
        ))
    )
}
```

## API Reference

### Annotations

#### @MCPTool
Configures MCP tool behavior for methods.

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `name` | String | Tool name (supports placeholders) | "" |
| `description` | String | Tool description (supports placeholders) | "" |
| `successMessage` | String | Success message template | "" |
| `errorMessage` | String | Error message prefix | "" |
| `serializeResult` | Boolean | Whether to JSON serialize result | true |

#### @MCPParameter
Configures parameter extraction and validation.

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `name` | String | Parameter name in request | (uses parameter name) |
| `description` | String | Parameter description for schema | "" |
| `required` | Boolean | Whether parameter is required | true |
| `defaultValue` | String | Default value when not provided | "" |

### Core Classes

#### MCPToolTransportFactory
Factory for creating complete `ToolTransport` objects.

```kotlin
object MCPToolTransportFactory {
    inline fun <reified T : Any> createToolTransport(
        methodReference: KFunction<*>
    ): ToolTransport
}
```

#### MCPToolMiddleware
Core middleware for handling MCP tool invocation.

```kotlin
object MCPToolMiddleware {
    var SERVICE_NAME: String

    inline fun <reified T : Any> createHandler(
        methodReference: KFunction<*>
    ): suspend (CallToolRequest) -> CallToolResult
}
```

#### MCPCapabilities
Container for all MCP server capabilities.

```kotlin
data class MCPCapabilities(
    val prompts: List<PromptTransport> = emptyList(),
    val resources: List<ResourceTransport> = emptyList(),
    val tools: List<ToolTransport> = emptyList()
)
```

### Transport Classes

#### ToolTransport
Encapsulates tool configuration and handler.

```kotlin
data class ToolTransport(
    val name: String,
    val description: String,
    val inputSchema: Tool.Input,
    val handler: suspend (CallToolRequest) -> CallToolResult
) {
    fun addToServer(server: Server)
}
```

#### PromptTransport
Encapsulates prompt configuration and handler.

```kotlin
data class PromptTransport(
    val name: String,
    val description: String,
    val arguments: List<PromptArgument> = emptyList(),
    val handler: suspend (GetPromptRequest) -> GetPromptResult
) {
    fun addToServer(server: Server)
}
```

#### ResourceTransport
Encapsulates resource configuration and handler.

```kotlin
data class ResourceTransport(
    val uri: String,
    val name: String,
    val description: String,
    val mimeType: String,
    val handler: suspend (ReadResourceRequest) -> ReadResourceResult
) {
    fun addToServer(server: Server)
}
```

## Testing

### Unit Testing MCP Tools

```kotlin
@Test
fun `test MCP tool with middleware`() = runTest {
    // Setup
    MCPToolMiddleware.SERVICE_NAME = "test-service"
    MCPToolConfigLoader.loadConfig("mcp-tools.yaml")

    // Create tool transport
    val toolTransport = MCPToolTransportFactory.createToolTransport<TestService>(
        TestService::testMethod
    )

    // Create request
    val request = CallToolRequest(
        name = "test_tool",
        arguments = buildJsonObject {
            put("name", "test")
            put("value", 42)
        }
    )

    // Execute
    val result = toolTransport.handler(request)

    // Verify
    assertThat(result.isError).isFalse()
    assertThat(result.content).hasSize(1)
}
```

### Integration Testing

```kotlin
@Test
fun `test complete MCP server integration`() = runTest {
    // Setup server
    val capabilities = MCPCapabilities(
        tools = listOf(
            MCPToolTransportFactory.createToolTransport<MyService>(MyService::myMethod)
        )
    )

    val server = configureServer(capabilities)

    // Test tool listing
    val tools = server.listTools()
    assertThat(tools.tools).hasSize(1)
    assertThat(tools.tools[0].name).isEqualTo("my_tool")
}
```

## Dependencies and Compatibility

### Version Compatibility

- **Kotlin**: 2.x (required for MCP SDK compatibility)
- **Ktor**: 3.x (required for MCP SDK compatibility)
- **Koin**: 3.4+
- **Java**: 17+

> **Note**: This module uses Kotlin 2.x and Ktor 3.x specifically to maintain compatibility with the MCP Kotlin SDK. Version resolution strategies are configured in the build file to ensure proper dependency alignment.

## Examples

### Complete Service Implementation

```kotlin
// Service interface
interface UserManagementService {
    suspend fun findUser(id: String): User?
    suspend fun createUser(name: String, email: String): User
    suspend fun listUsers(limit: Int): List<User>
}

// Service implementation with MCP tools
class UserManagementServiceImpl(
    private val userRepository: UserRepository
) : UserManagementService {

    @MCPTool(
        name = "{tools.findUser.name}",
        description = "{tools.findUser.description}",
        successMessage = "{tools.findUser.successMessage}"
    )
    override suspend fun findUser(
        @MCPParameter(description = "{tools.findUser.parameters.id.description}") id: String
    ): User? {
        return userRepository.findById(id.toUUID())
    }

    @MCPTool(
        name = "{tools.createUser.name}",
        description = "{tools.createUser.description}",
        successMessage = "User created: {result}"
    )
    override suspend fun createUser(
        @MCPParameter(description = "User's full name") name: String,
        @MCPParameter(description = "User's email address") email: String
    ): User {
        return userRepository.create(User(name = name, email = email))
    }

    @MCPTool(
        name = "user_list",
        description = "List all users with optional limit"
    )
    override suspend fun listUsers(
        @MCPParameter(description = "Maximum users to return", required = false, defaultValue = "50") limit: Int
    ): List<User> {
        return userRepository.findAll(limit)
    }
}
```

### Server Configuration

```kotlin
// Application main function
suspend fun main() {
    // Load MCP tool configuration
    MCPToolConfigLoader.loadConfig("mcp-tools.yaml")

    // Set service name for authentication
    MCPToolMiddleware.SERVICE_NAME = "user-management-service"

    // Create tool transports
    val userTools = listOf(
        MCPToolTransportFactory.createToolTransport<UserManagementService>(
            UserManagementService::findUser
        ),
        MCPToolTransportFactory.createToolTransport<UserManagementService>(
            UserManagementService::createUser
        ),
        MCPToolTransportFactory.createToolTransport<UserManagementService>(
            UserManagementService::listUsers
        )
    )

    // Configure capabilities
    val capabilities = MCPCapabilities(tools = userTools)

    // Start server
    runSseMcpServerUsingKtorPlugin(
        port = 8080,
        mcpCapabilities = capabilities,
        dependencyInjectionModules = listOf(userManagementModule)
    )
}

// Koin module
val userManagementModule = module {
    single<UserRepository> { UserRepositoryImpl() }
    single<UserManagementService> { UserManagementServiceImpl(get()) }
}
```

### Configuration File Example

```yaml
# mcp-tools.yaml
tools:
  findUser:
    name: "user_find"
    description: "Find a user by their unique identifier"
    successMessage: "User found: {result}"
    parameters:
      id:
        description: "The unique identifier of the user to find"

  createUser:
    name: "user_create"
    description: "Create a new user account in the system"
    successMessage: "User created successfully with ID: {result}"
    parameters:
      name:
        description: "The full name of the user"
      email:
        description: "The email address for the user account"

  listUsers:
    name: "user_list"
    description: "Retrieve a list of all users in the system"
    parameters:
      limit:
        description: "Maximum number of users to return (default: 50)"
```

## Best Practices

### 1. Service Design
- Keep service methods focused and single-purpose
- Use descriptive parameter names and types
- Implement proper error handling in service methods
- Follow Alice's SOLID principles

### 2. Tool Configuration
- Use YAML configuration for externalized tool metadata
- Provide clear, descriptive tool and parameter descriptions
- Use meaningful tool names that follow naming conventions
- Configure appropriate success and error messages

### 3. Parameter Handling
- Use type-safe parameters (String, Int, Long, Boolean, UUID)
- Provide sensible default values for optional parameters
- Validate input parameters in service methods
- Use descriptive parameter descriptions for better UX

### 4. Authentication and Security
- Always set `MCPToolMiddleware.SERVICE_NAME` before using tools
- Ensure service methods don't expose sensitive information
- Follow Alice's authentication patterns
- Validate user permissions in service implementations

### 5. Testing
- Write unit tests for service methods independently
- Test MCP tool integration with middleware
- Use real MCP clients in integration tests
- Test error scenarios and edge cases

### 6. Error Handling
- Return meaningful error messages to users
- Log errors appropriately for debugging
- Use Result types or proper exception handling
- Configure custom error messages in @MCPTool annotations

## Migration Guide

### From Manual Tool Implementation

**Before (Manual Implementation):**
```kotlin
private fun createUserTool(): suspend (CallToolRequest) -> CallToolResult = { request ->
    val name = request.arguments.get("name")?.toString()?.replace("\"", "")
        ?: throw IllegalArgumentException("name is required")
    val email = request.arguments.get("email")?.toString()?.replace("\"", "")
        ?: "<EMAIL>"

    logger.info("Creating user with name: $name, email: $email")

    val result = withRootServicePolicy("user-service") {
        withUnauthenticatedTokenWithKey("user-service") {
            val service = getKoin().get<UserService>()
            service.createUser(name, email)
        }
    }

    CallToolResult(
        content = listOf(TextContent(gson.toJson(result))),
        isError = false
    )
}
```

**After (Using common-mcp):**
```kotlin
@MCPTool(
    name = "user_create",
    description = "Create a new user account"
)
suspend fun createUser(
    @MCPParameter(description = "User's full name") name: String,
    @MCPParameter(description = "User's email", required = false, defaultValue = "<EMAIL>") email: String
): User = userService.createUser(name, email)

// Tool creation
val tool = MCPToolTransportFactory.createToolTransport<UserService>(UserService::createUser)
```

## Troubleshooting

### Common Issues

#### 1. "Method must be annotated with @MCPTool"
Ensure your service method has the `@MCPTool` annotation with required `name` and `description` fields.

#### 2. "SERVICE_NAME not set"
Set the service name before creating tools:
```kotlin
MCPToolMiddleware.SERVICE_NAME = "your-service-name"
```

#### 3. "Configuration placeholder not found"
Ensure your YAML configuration file is loaded and contains the referenced placeholders:
```kotlin
MCPToolConfigLoader.loadConfig("mcp-tools.yaml")
```

#### 4. Parameter conversion errors
Verify parameter types are supported (String, Int, Long, Boolean, UUID) and provide proper validation.

#### 5. Koin dependency resolution errors
Ensure all required dependencies are registered in your Koin modules and the modules are provided to the server.

### Debug Tips

1. **Enable debug logging** to see parameter extraction and method invocation details
2. **Check YAML configuration** loading with `MCPToolConfigLoader.isConfigLoaded()`
3. **Verify tool registration** by listing tools on the configured server
4. **Test service methods independently** before wrapping them as MCP tools
5. **Use integration tests** with real MCP clients to validate end-to-end functionality

## Contributing

When contributing to the common-mcp module:

1. Follow Alice's coding standards and SOLID principles
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure backward compatibility when possible
5. Use meaningful commit messages and PR descriptions

---
