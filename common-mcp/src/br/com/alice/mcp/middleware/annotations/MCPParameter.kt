package br.com.alice.mcp.middleware.annotations

/**
 * Annotation to mark parameters that should be extracted from MCP CallToolRequest arguments.
 *
 * @param name The name of the parameter in the request arguments. If not specified, uses the parameter name.
 * @param description The parameter description for the tool schema (auto-generated if not provided).
 * @param required Whether this parameter is required. If true and parameter is missing, throws IllegalArgumentException.
 * @param defaultValue Default value to use if parameter is not provided (only used when required = false).
 */
@Target(AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
annotation class MCPParameter(
    val name: String = "",
    val description: String = "",
    val required: Boolean = true,
    val defaultValue: String = ""
)
