package br.com.alice.mcp.middleware.annotations

/**
 * Annotation to configure MCP tool behavior for methods.
 *
 * @param name The tool name (e.g., "limbo_reprocess-dlq"). Can use placeholder syntax like "{tools.reprocessDLQ.name}".
 * @param description The tool description. Can use placeholder syntax like "{tools.reprocessDLQ.description}".
 * @param successMessage Message to return on successful execution. Can use {result} placeholder for result value.
 * @param errorMessage Message prefix to use on error. The actual error message will be appended.
 * @param serializeResult Whether to serialize the result as JSON. If false, uses toString() or successMessage.
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class MCPTool(
    val name: String = "",
    val description: String = "",
    val successMessage: String = "",
    val errorMessage: String = "Error executing operation",
    val serializeResult: Boolean = true
)
