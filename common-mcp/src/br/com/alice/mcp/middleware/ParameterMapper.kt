package br.com.alice.mcp.middleware

import br.com.alice.common.core.extensions.toUUID
import br.com.alice.mcp.middleware.annotations.MCPParameter
import br.com.alice.mcp.middleware.config.MCPToolConfigLoader
import io.modelcontextprotocol.kotlin.sdk.CallToolRequest
import java.util.UUID
import kotlin.reflect.KParameter
import kotlin.reflect.KType
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.jvm.jvmErasure

/**
 * Utility class for mapping MCP request parameters to function parameters using reflection.
 */
object ParameterMapper {
    
    /**
     * Extracts and converts parameters from CallToolRequest to match the target function signature.
     * 
     * @param request The MCP CallToolRequest containing the arguments
     * @param parameters List of function parameters (excluding the first 'this' parameter for instance methods)
     * @return Array of converted parameter values ready for function invocation
     */
    fun extractParameters(request: CallToolRequest, parameters: List<KParameter>): Array<Any?> {
        return parameters.map { param ->
            val annotation = param.findAnnotation<MCPParameter>()
            val rawParamName = annotation?.name?.takeIf { it.isNotEmpty() } ?: param.name ?:
                throw IllegalArgumentException("Parameter name not available for ${param}")

            // Resolve placeholder in parameter name
            val paramName = MCPToolConfigLoader.resolvePlaceholder(rawParamName)
            val rawValue = request.arguments.get(paramName)?.toString()?.replace("\"", "")
            
            when {
                rawValue == null -> handleMissingParameter(param, annotation)
                else -> convertParameter(rawValue, param.type, param.name ?: paramName)
            }
        }.toTypedArray()
    }
    
    /**
     * Handles missing parameters based on annotation configuration.
     */
    private fun handleMissingParameter(param: KParameter, annotation: MCPParameter?): Any? {
        return when {
            annotation?.required == false -> {
                convertParameter(annotation.defaultValue, param.type, param.name ?: "unknown")
            }
            param.isOptional -> null
            else -> throw IllegalArgumentException("${param.name} is required")
        }
    }
    
    /**
     * Converts a string parameter value to the target type.
     */
    private fun convertParameter(value: String, targetType: KType, paramName: String): Any? {
        if (value.isEmpty() && targetType.isMarkedNullable) {
            return null
        }

        return when (targetType.jvmErasure) {
            String::class -> value
            Int::class -> value.toIntOrNull() ?: throw IllegalArgumentException("Invalid integer value for $paramName: $value")
            Long::class -> value.toLongOrNull() ?: throw IllegalArgumentException("Invalid long value for $paramName: $value")
            Boolean::class -> value.toBooleanStrictOrNull() ?: throw IllegalArgumentException("Invalid boolean value for $paramName: $value")
            UUID::class -> try {
                value.toUUID()
            } catch (e: Exception) {
                throw IllegalArgumentException("Invalid UUID value for $paramName: $value", e)
            }
            else -> {
                // For other types, try to use the string value directly
                // This can be extended to support more complex types as needed
                if (targetType.isMarkedNullable && value.isEmpty()) {
                    null
                } else {
                    value
                }
            }
        }
    }
}
