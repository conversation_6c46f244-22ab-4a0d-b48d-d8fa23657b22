package br.com.alice.mcp

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.common.controllers.HealthCheckerController
import br.com.alice.common.logging.logger
import br.com.alice.common.ops.health.setupHealthCheckModule
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.mcp.transports.MCPCapabilities
import com.google.gson.FieldNamingPolicy
import io.ktor.server.application.install
import io.ktor.server.auth.Authentication
import io.ktor.server.cio.CIO
import io.ktor.server.engine.embeddedServer
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import io.modelcontextprotocol.kotlin.sdk.Implementation
import io.modelcontextprotocol.kotlin.sdk.ServerCapabilities
import io.modelcontextprotocol.kotlin.sdk.server.Server
import io.modelcontextprotocol.kotlin.sdk.server.ServerOptions
import io.modelcontextprotocol.kotlin.sdk.server.mcp
import org.koin.core.module.Module
import org.koin.ktor.ext.inject
import org.koin.ktor.plugin.Koin

fun configureServer(mcpCapabilities: MCPCapabilities): Server {
    val server = Server(
        Implementation(
            name = "mcp-kotlin test server",
            version = "0.0.1"
        ),
        ServerOptions(
            capabilities = ServerCapabilities(
                prompts = ServerCapabilities.Prompts(listChanged = true),
                resources = ServerCapabilities.Resources(subscribe = true, listChanged = true),
                tools = ServerCapabilities.Tools(listChanged = true),
            )
        )
    )

    mcpCapabilities.resources.forEach { it.addToServer(server) }
    mcpCapabilities.prompts.forEach { it.addToServer(server) }
    mcpCapabilities.tools.forEach { it.addToServer(server) }

    return server
}

/**
 * Starts an SSE (Server Sent Events) MCP server using the Ktor framework and the specified port.
 *
 * The url can be accessed in the MCP inspector at [http://localhost:$port]
 *
 * @param port The port number on which the SSE MCP server will listen for client connections.
 * @return Unit This method does not return a value.
 */
suspend fun runSseMcpServerUsingKtorPlugin(port: Int,
                                           mcpCapabilities: MCPCapabilities,
                                           dependencyInjectionModules: List<Module> = emptyList()): Unit {
    logger.info("Starting sse server on port $port")
    logger.info("Use inspector to connect to the http://localhost:$port/sse")

    embeddedServer(CIO, host = "0.0.0.0", port = port) {
        install(Authentication) {
            firebase()
        }
        authenticationBootstrap()

        install(Koin) {
            modules(setupHealthCheckModule() + dependencyInjectionModules)
        }

        install(ContentNegotiation) {
            simpleGson {
                setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
            }
        }

        routing {
            route("/ops") {
                val healthCheckerController by inject<HealthCheckerController>()

                get("/live") {
                    val response = healthCheckerController.isLive()
                    call.respond(response.status, response.message)
                }
                get("/ready") {
                    val response = healthCheckerController.isReady()
                    call.respond(response.status, response.message)
                }
            }
        }

        mcp {
            return@mcp configureServer(mcpCapabilities)
        }
    }.start(wait = true)
}
