package br.com.alice.mcp.transports

import io.modelcontextprotocol.kotlin.sdk.GetPromptRequest
import io.modelcontextprotocol.kotlin.sdk.GetPromptResult
import io.modelcontextprotocol.kotlin.sdk.PromptArgument
import io.modelcontextprotocol.kotlin.sdk.server.Server

/**
 * Transport class for encapsulating prompt data to be passed to server.addPrompt function.
 *
 * This class provides a convenient way to package all the necessary data for creating a prompt
 * and includes a helper method to add the prompt to a server.
 *
 * @param name The unique name/identifier of the prompt
 * @param description A human-readable description of what the prompt does
 * @param arguments List of arguments that the prompt accepts (optional)
 * @param handler Function that processes the prompt request and returns a GetPromptResult.
 *                The request parameter is of type GetPromptRequest from the MCP SDK.
 */
data class PromptTransport(
    val name: String,
    val description: String,
    val arguments: List<PromptArgument> = emptyList(),
    val handler: suspend (request: GetPromptRequest) -> GetPromptResult
) {
    /**
     * Convenience method to add this prompt to a server.
     * This method calls server.addPrompt with the encapsulated data.
     */
    fun addToServer(server: Server) {
        server.addPrompt(
            name = name,
            description = description,
            arguments = arguments,
            promptProvider = handler
        )
    }
}
