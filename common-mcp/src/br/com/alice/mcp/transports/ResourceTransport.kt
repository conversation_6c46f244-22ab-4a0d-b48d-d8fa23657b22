package br.com.alice.mcp.transports

import io.modelcontextprotocol.kotlin.sdk.ReadResourceRequest
import io.modelcontextprotocol.kotlin.sdk.ReadResourceResult
import io.modelcontextprotocol.kotlin.sdk.server.Server

/**
 * Transport class for encapsulating resource data to be passed to server.addResource function.
 *
 * This class provides a convenient way to package all the necessary data for creating a resource
 * and includes a helper method to add the resource to a server.
 *
 * @param uri The unique URI identifier for the resource
 * @param name A human-readable name for the resource
 * @param description A human-readable description of what the resource provides
 * @param mimeType The MIME type of the resource content (e.g., "text/html", "application/json")
 * @param handler Function that processes the resource request and returns a ReadResourceResult.
 *                The request parameter is of type ReadResourceRequest from the MCP SDK.
 */
data class ResourceTransport(
    val uri: String,
    val name: String,
    val description: String,
    val mimeType: String,
    val handler: suspend (request: ReadResourceRequest) -> ReadResourceResult
) {
    /**
     * Convenience method to add this resource to a server.
     * This method calls server.addResource with the encapsulated data.
     */
    fun addToServer(server: Server) {
        server.addResource(
            uri = uri,
            name = name,
            description = description,
            mimeType = mimeType,
            readHandler = handler
        )
    }
}
