package br.com.alice.mcp.transports

import io.modelcontextprotocol.kotlin.sdk.CallToolRequest
import io.modelcontextprotocol.kotlin.sdk.CallToolResult
import io.modelcontextprotocol.kotlin.sdk.Tool
import io.modelcontextprotocol.kotlin.sdk.server.Server

/**
 * Transport class for encapsulating tool data to be passed to server.addTool function.
 *
 * This class provides a convenient way to package all the necessary data for creating a tool
 * and includes a helper method to add the tool to a server.
 *
 * @param name The unique name/identifier of the tool
 * @param description A human-readable description of what the tool does
 * @param inputSchema The input schema defining the tool's parameters and validation rules
 * @param handler Function that processes the tool request and returns a CallToolResult.
 *                The request parameter is of type CallToolRequest from the MCP SDK.
 */
data class ToolTransport(
    val name: String,
    val description: String,
    val inputSchema: Tool.Input,
    val handler: suspend (request: CallToolRequest) -> CallToolResult
) {
    /**
     * Convenience method to add this tool to a server.
     * This method calls server.addTool with the encapsulated data.
     */
    fun addToServer(server: Server) {
        server.addTool(
            name = name,
            description = description,
            inputSchema = inputSchema,
            handler = handler
        )
    }
}
