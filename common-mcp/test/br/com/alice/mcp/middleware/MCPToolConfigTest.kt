package br.com.alice.mcp.middleware

import br.com.alice.mcp.middleware.config.MCPToolConfigLoader
import br.com.alice.mcp.middleware.test.MCPMiddlewareTestBase
import br.com.alice.mcp.middleware.test.TestService
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class MCPToolConfigTest : MCPMiddlewareTestBase() {

    @Test
    fun `should resolve placeholders from YAML configuration`() {
        val toolTransport = MCPToolTransportFactory.createToolTransport<TestService>(
            methodReference = TestService::reprocessDLQ
        )

        // Verify that placeholders were resolved
        val inputSchema = toolTransport.inputSchema
        val properties = inputSchema.properties.jsonObject

        // Should have event_id property with resolved name and description
        assertTrue(properties.containsKey("event_id"))
        val eventIdProperty = properties["event_id"]!!.jsonObject
        assertEquals("string", eventIdProperty["type"]!!.jsonPrimitive.content)
        assertEquals("The ID of the event to reprocess", eventIdProperty["description"]!!.jsonPrimitive.content)
        assertEquals("uuid", eventIdProperty["format"]!!.jsonPrimitive.content)
    }

    @Test
    fun `should resolve placeholders for listDLQs parameters`() {
        val toolTransport = MCPToolTransportFactory.createToolTransport<TestService>(
            methodReference = TestService::listDLQs
        )

        val inputSchema = toolTransport.inputSchema
        val properties = inputSchema.properties.jsonObject

        // Should have producer property with resolved description
        assertTrue(properties.containsKey("producer"))
        val producerProperty = properties["producer"]!!.jsonObject
        assertEquals("The DLQ producer", producerProperty["description"]!!.jsonPrimitive.content)

        // Should have topic property with resolved description
        assertTrue(properties.containsKey("topic"))
        val topicProperty = properties["topic"]!!.jsonObject
        assertEquals("The DLQ original topic", topicProperty["description"]!!.jsonPrimitive.content)
    }

    @Test
    fun `should resolve placeholder values correctly`() {
        assertEquals("The DLQ producer", MCPToolConfigLoader.resolvePlaceholder("{tools.listDLQs.parameters.producer.description}"))
        assertEquals("event_id", MCPToolConfigLoader.resolvePlaceholder("{tools.reprocessDLQ.parameters.eventId.name}"))
        assertEquals("DLQ reprocessed successfully!", MCPToolConfigLoader.resolvePlaceholder("{tools.reprocessDLQ.successMessage}"))
        
        // Should return original value if not a placeholder
        assertEquals("regular text", MCPToolConfigLoader.resolvePlaceholder("regular text"))
        
        // Should return original placeholder if not found
        assertEquals("{nonexistent.placeholder}", MCPToolConfigLoader.resolvePlaceholder("{nonexistent.placeholder}"))
    }

    @Test
    fun `should resolve test tool placeholders`() {
        assertEquals("test_tool", MCPToolConfigLoader.resolvePlaceholder("{tools.testTool.name}"))
        assertEquals("A test tool for middleware testing", MCPToolConfigLoader.resolvePlaceholder("{tools.testTool.description}"))
        assertEquals("Test operation completed successfully!", MCPToolConfigLoader.resolvePlaceholder("{tools.testTool.successMessage}"))
        assertEquals("The name parameter", MCPToolConfigLoader.resolvePlaceholder("{tools.testTool.parameters.name.description}"))
        assertEquals("The value parameter", MCPToolConfigLoader.resolvePlaceholder("{tools.testTool.parameters.value.description}"))
    }
}
