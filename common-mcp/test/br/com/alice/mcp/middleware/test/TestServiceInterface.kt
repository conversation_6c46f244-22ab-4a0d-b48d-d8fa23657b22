package br.com.alice.mcp.middleware.test

import com.github.kittinunf.result.Result
import java.util.UUID

/**
 * Test service interface for middleware testing.
 */
interface TestServiceInterface {
    suspend fun reprocess(id: UUID): Result<Boolean, Throwable>
    suspend fun findByFilters(producer: String, topic: String, range: IntRange): Result<List<String>, Throwable>
    suspend fun performOperation(name: String, value: Int): String
}
