package br.com.alice.mcp.middleware

import br.com.alice.mcp.middleware.annotations.MCPParameter
import br.com.alice.mcp.middleware.annotations.MCPTool
import br.com.alice.mcp.middleware.test.MCPMiddlewareTestBase
import br.com.alice.mcp.middleware.test.TestService
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class MCPToolTransportFactoryTest : MCPMiddlewareTestBase() {

    @Test
    fun `factory should generate correct tool transport for reprocessDLQ`() {
        val toolTransport = MCPToolTransportFactory.createToolTransport<TestService>(
            methodReference = TestService::reprocessDLQ
        )

        // Verify basic properties - should read from annotation and resolve placeholders
        assertEquals("limbo_reprocess-dlq", toolTransport.name)
        assertEquals("Use this to reprocess a DLQ by event id", toolTransport.description)

        // Verify input schema
        val inputSchema = toolTransport.inputSchema
        val properties = inputSchema.properties.jsonObject

        // Should have event_id property
        assertTrue(properties.containsKey("event_id"))
        val eventIdProperty = properties["event_id"]!!.jsonObject
        assertEquals("string", eventIdProperty["type"]!!.jsonPrimitive.content)
        assertEquals("The ID of the event to reprocess", eventIdProperty["description"]!!.jsonPrimitive.content)
        assertEquals("uuid", eventIdProperty["format"]!!.jsonPrimitive.content)

        // Should have event_id as required
        assertEquals(listOf("event_id"), inputSchema.required)
    }

    @Test
    fun `factory should generate correct tool transport for listDLQs`() {
        val toolTransport = MCPToolTransportFactory.createToolTransport<TestService>(
            methodReference = TestService::listDLQs
        )

        // Verify basic properties - should read from annotation and resolve placeholders
        assertEquals("limbo_list-dlq", toolTransport.name)
        assertEquals("Use this to list DLQs by topic and producer", toolTransport.description)

        // Verify input schema
        val inputSchema = toolTransport.inputSchema
        val properties = inputSchema.properties.jsonObject

        // Should have producer property
        assertTrue(properties.containsKey("producer"))
        val producerProperty = properties["producer"]!!.jsonObject
        assertEquals("string", producerProperty["type"]!!.jsonPrimitive.content)
        assertEquals("The DLQ producer", producerProperty["description"]!!.jsonPrimitive.content)

        // Should have topic property
        assertTrue(properties.containsKey("topic"))
        val topicProperty = properties["topic"]!!.jsonObject
        assertEquals("string", topicProperty["type"]!!.jsonPrimitive.content)
        assertEquals("The DLQ original topic", topicProperty["description"]!!.jsonPrimitive.content)

        // Should have no required parameters (both are optional)
        assertTrue(inputSchema.required?.isEmpty() ?: true)
    }

    @Test
    fun `factory should generate default descriptions for parameters without description`() {
        // Create a test implementation with parameter without description
        class TestImpl {
            @MCPTool(
                name = "test_tool",
                description = "Test tool"
            )
            suspend fun testMethod(
                @MCPParameter someParameter: String
            ) = "test"
        }

        val toolTransport = MCPToolTransportFactory.createToolTransport<TestImpl>(
            methodReference = TestImpl::testMethod
        )

        val properties = toolTransport.inputSchema.properties.jsonObject
        val paramProperty = properties["someParameter"]!!.jsonObject
        assertEquals("The someParameter parameter", paramProperty["description"]!!.jsonPrimitive.content)
    }

    @Test
    fun `factory should throw exception when annotation is missing name`() {
        class TestImpl {
            @MCPTool(description = "Test tool")
            suspend fun testMethod() = "test"
        }

        val exception = assertThrows<IllegalArgumentException> {
            MCPToolTransportFactory.createToolTransport<TestImpl>(
                methodReference = TestImpl::testMethod
            )
        }
        assertTrue(exception.message!!.contains("must specify a name"))
    }

    @Test
    fun `factory should throw exception when annotation is missing description`() {
        class TestImpl {
            @MCPTool(name = "test_tool")
            suspend fun testMethod() = "test"
        }

        val exception = assertThrows<IllegalArgumentException> {
            MCPToolTransportFactory.createToolTransport<TestImpl>(
                methodReference = TestImpl::testMethod
            )
        }
        assertTrue(exception.message!!.contains("must specify a description"))
    }
}
