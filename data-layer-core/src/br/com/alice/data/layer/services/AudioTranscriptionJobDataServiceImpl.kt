package br.com.alice.data.layer.services

import br.com.alice.data.layer.models.AudioTranscriptionJob
import br.com.alice.data.layer.pipelines.DatabasePipelineFactory
import br.com.alice.data.layer.tables.AudioTranscriptionJobTable

class AudioTranscriptionJobDataServiceImpl internal constructor(factory: DatabasePipelineFactory) :
    BaseDataServiceImpl<AudioTranscriptionJob>(
        factory.get(
            AudioTranscriptionJob::class,
            AudioTranscriptionJobTable::class
        )
    ),
    AudioTranscriptionJobDataService
